#!/bin/bash

# Production Build Script - Build images and push to Docker Hub
# This script builds all services with production configuration and pushes them to Docker Hub

set -e

# Color variables
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}===== Production Build & Push Script =====${NC}"
echo -e "${YELLOW}This script will build all services with production configuration and push to Docker Hub${NC}"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Error: Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Login to Docker Hub
echo -e "${YELLOW}Logging in to Docker Hub...${NC}"
if [ -z "${DOCKER_USERNAME}" ] || [ -z "${DOCKER_PASSWORD}" ]; then
    echo -e "${YELLOW}Docker credentials not found in environment variables.${NC}"
    docker login
else
    echo ${DOCKER_PASSWORD} | docker login -u ${DOCKER_USERNAME} --password-stdin
fi

# Stop any running build containers
echo -e "${YELLOW}Stopping any running build containers...${NC}"
docker compose -f docker-compose.build.yml down 2>/dev/null || true

# Build all services with production configuration
echo -e "${GREEN}Building all services with production configuration...${NC}"
docker compose -f docker-compose.build.yml build --parallel

echo -e "${GREEN}Build completed successfully!${NC}"

# Push all images to Docker Hub
echo -e "${BLUE}Pushing images to Docker Hub...${NC}"

# Function to push image
push_image() {
    local SERVICE=$1
    local IMAGE_NAME="prasanthats/redfyn:${SERVICE}"
    
    echo -e "${YELLOW}Pushing ${SERVICE} image...${NC}"
    docker push ${IMAGE_NAME}
    echo -e "${GREEN}${SERVICE} pushed successfully!${NC}"
}

# Push all custom built images
push_image "spot_frontend"
push_image "spot_backend"
push_image "solana"
push_image "trading_panel"
push_image "limit_orders"

echo -e "${GREEN}===== All images built and pushed successfully! =====${NC}"
echo -e "${BLUE}Images available on Docker Hub:${NC}"
echo -e "  - prasanthats/redfyn:spot_frontend"
echo -e "  - prasanthats/redfyn:spot_backend"
echo -e "  - prasanthats/redfyn:solana"
echo -e "  - prasanthats/redfyn:trading_panel"
echo -e "  - prasanthats/redfyn:limit_orders"
echo ""
echo -e "${YELLOW}To deploy these images on production, run: ./scripts/deploy.sh${NC}"