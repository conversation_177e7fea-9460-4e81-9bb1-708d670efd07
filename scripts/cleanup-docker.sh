#!/bin/bash

# Docker Cleanup Script for RedFyn Spot Trading Platform
# This script stops and removes all Docker containers, images, volumes, and networks

set -e  # Exit on any error

echo "🧹 Starting Docker cleanup for RedFyn Spot..."
echo "================================================"

# Function to check if docker-compose.prod.yml exists
check_compose_file() {
    if [ ! -f "docker-compose.prod.yml" ]; then
        echo "❌ Error: docker-compose.prod.yml not found in current directory"
        echo "Please run this script from the project root directory"
        exit 1
    fi
}

# Function to stop and remove containers, images, volumes, and networks
cleanup_production() {
    echo "🔍 Checking for running production containers..."
    
    # Check if containers are running
    RUNNING_CONTAINERS=$(docker compose -f docker-compose.prod.yml ps -q 2>/dev/null || true)
    
    if [ ! -z "$RUNNING_CONTAINERS" ]; then
        echo "🛑 Stopping production containers..."
        docker compose -f docker-compose.prod.yml stop
        echo "✅ Production containers stopped"
    else
        echo "ℹ️  No running production containers found"
    fi
    
    echo "🗑️  Removing production containers, images, volumes, and networks..."
    docker compose -f docker-compose.prod.yml down --rmi all --volumes --remove-orphans
    echo "✅ Production cleanup completed"
}

# Function to clean up development environment (optional)
cleanup_development() {
    if [ -f "docker-compose.yml" ]; then
        echo "🔍 Checking for running development containers..."
        
        # Check if containers are running
        RUNNING_CONTAINERS=$(docker compose -f docker-compose.yml ps -q 2>/dev/null || true)
        
        if [ ! -z "$RUNNING_CONTAINERS" ]; then
            echo "🛑 Stopping development containers..."
            docker compose -f docker-compose.yml stop
            echo "✅ Development containers stopped"
        else
            echo "ℹ️  No running development containers found"
        fi
        
        echo "🗑️  Removing development containers, images, volumes, and networks..."
        docker compose -f docker-compose.yml down --rmi all --volumes --remove-orphans
        echo "✅ Development cleanup completed"
    else
        echo "ℹ️  No development docker-compose.yml found, skipping..."
    fi
}

# Function to clean up build environment (optional)
cleanup_build() {
    if [ -f "docker-compose.build.yml" ]; then
        echo "🔍 Checking for running build containers..."
        
        # Check if containers are running
        RUNNING_CONTAINERS=$(docker compose -f docker-compose.build.yml ps -q 2>/dev/null || true)
        
        if [ ! -z "$RUNNING_CONTAINERS" ]; then
            echo "🛑 Stopping build containers..."
            docker compose -f docker-compose.build.yml stop
            echo "✅ Build containers stopped"
        else
            echo "ℹ️  No running build containers found"
        fi
        
        echo "🗑️  Removing build containers, images, volumes, and networks..."
        docker compose -f docker-compose.build.yml down --rmi all --volumes --remove-orphans
        echo "✅ Build cleanup completed"
    else
        echo "ℹ️  No build docker-compose.build.yml found, skipping..."
    fi
}

# Function to remove dangling images and volumes
cleanup_dangling() {
    echo "🧽 Cleaning up dangling images and volumes..."
    
    # Remove dangling images
    DANGLING_IMAGES=$(docker images -f "dangling=true" -q)
    if [ ! -z "$DANGLING_IMAGES" ]; then
        docker rmi $DANGLING_IMAGES
        echo "✅ Removed dangling images"
    else
        echo "ℹ️  No dangling images found"
    fi
    
    # Remove dangling volumes
    DANGLING_VOLUMES=$(docker volume ls -f "dangling=true" -q)
    if [ ! -z "$DANGLING_VOLUMES" ]; then
        docker volume rm $DANGLING_VOLUMES
        echo "✅ Removed dangling volumes"
    else
        echo "ℹ️  No dangling volumes found"
    fi
}

# Function to show Docker system usage
show_usage() {
    echo "📊 Docker system usage:"
    docker system df
}

# Main execution
main() {
    check_compose_file
    
    # Parse command line arguments
    case "${1:-all}" in
        "prod"|"production")
            cleanup_production
            ;;
        "dev"|"development")
            cleanup_development
            ;;
        "build")
            cleanup_build
            ;;
        "all")
            cleanup_production
            cleanup_development
            cleanup_build
            cleanup_dangling
            ;;
        "dangling")
            cleanup_dangling
            ;;
        "help"|"--help"|"h")
            echo "Usage: $0 [option]"
            echo "Options:"
            echo "  prod, production  - Clean up production environment only"
            echo "  dev, development  - Clean up development environment only"
            echo "  build            - Clean up build environment only"
            echo "  all              - Clean up all environments (default)"
            echo "  dangling         - Clean up dangling images and volumes only"
            echo "  help, --help, h  - Show this help message"
            exit 0
            ;;
        *)
            echo "❌ Unknown option: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
    
    show_usage
    echo "🎉 Docker cleanup completed successfully!"
}

# Run main function with all arguments
main "$@"