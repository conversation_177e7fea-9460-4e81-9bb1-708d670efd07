#!/bin/bash

# Production Deploy Script - Pull images from Docker Hub and deploy
# This script pulls pre-built images from Docker Hub and deploys them in production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}===== Production Deploy Script =====${NC}"
echo -e "${YELLOW}This script will pull images from Docker Hub and deploy them in production${NC}"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Error: Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

# Check if docker-compose.prod.yml exists
if [ ! -f "docker-compose.prod.yml" ]; then
    echo -e "${RED}Error: docker-compose.prod.yml not found. Please ensure you're in the project root directory.${NC}"
    exit 1
fi

# Stop any running production containers
echo -e "${YELLOW}Stopping any running production containers...${NC}"
docker compose -f docker-compose.prod.yml down 2>/dev/null || true

# Pull latest images from Docker Hub
echo -e "${GREEN}Pulling latest images from Docker Hub...${NC}"
docker compose -f docker-compose.prod.yml pull

echo -e "${GREEN}Images pulled successfully!${NC}"
echo ""

# Deploy the application
echo -e "${GREEN}Deploying application in production mode...${NC}"
docker compose -f docker-compose.prod.yml up -d

echo -e "${GREEN}Deployment completed successfully!${NC}"
echo ""

# Show running containers
echo -e "${BLUE}Running containers:${NC}"
docker compose -f docker-compose.prod.yml ps
echo ""

# Show application URLs
echo -e "${GREEN}Application deployed successfully!${NC}"
echo -e "${YELLOW}Services are available at:${NC}"
echo -e "  - Frontend: http://localhost:3000"
echo -e "  - Spot Backend API: http://localhost:5000"
echo -e "  - Solana API: http://localhost:5001"
echo -e "  - Trading Panel API: http://localhost:5002"
echo -e "  - Limit Orders API: http://localhost:5003"
echo -e "  - Redis: localhost:6379"
echo ""
echo -e "${YELLOW}To view logs: docker compose -f docker-compose.prod.yml logs -f${NC}"
echo -e "${YELLOW}To stop services: docker compose -f docker-compose.prod.yml down${NC}"