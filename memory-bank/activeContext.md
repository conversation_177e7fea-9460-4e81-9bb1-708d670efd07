# Active Context

## Current Work Focus
The primary focus is monitoring the newly deployed production environment at `https://redfyn.crypfi.io` and ensuring its stability. Further testing of all core features, especially API interactions routed through Nginx, is required.

### Current Task
- ✅ Deploy application to production using Docker and Nginx.
- ✅ Configure SSL certificates using Certbot.
- ✅ Resolve deployment issues (port conflicts, Redis connection, Nginx routing).
- 🔄 Monitor application logs and server health.
- 🔄 Perform thorough testing of all application features in the production environment.

## Recent Changes

### 1. Bundled Transactions Implementation
- Implemented `useBundledTransactions` hook for managing multi-step/multi-DEX transactions.
- Created infrastructure for sequencing trades across different DEXes and protocols.
- Added support for atomic execution patterns for improved trading efficiency.
- Implemented safeguards for partial transaction success/failure.
- Created `BundledSwapExample` component to demonstrate the bundled transaction flow.
- Added cross-protocol support between Ethereum-based DEXes (Uniswap, SushiSwap) and Solana-based services (PumpFun).

### 2. Production Deployment
- Configured `docker-compose.prod.yml` for production services (frontend, backend, liquidity_pool, redis).
- Created `nginx/conf/redfyn.conf` for the host Nginx reverse proxy.
- Set up Nginx to handle SSL termination for `redfyn.crypfi.io`.
- Implemented routing rules in Nginx to direct traffic to appropriate containers based on URL paths (`/`, `/api/`, `/lp/`, `/liquidity-api/`).
- Created `deploy.sh` script to automate the deployment process, including Nginx configuration and Docker Compose startup.
- Added `DEPLOYMENT.md` documentation.

### 2. Deployment Issue Resolution
- **Port Conflict**: Resolved conflict with existing host Nginx by using the host Nginx instead of a containerized one for the reverse proxy.
- **Redis Connection**: Fixed `ECONNREFUSED` errors in the liquidity pool service by setting the correct `REDIS_URL`, `REDIS_HOST`, and `REDIS_PORT` environment variables in `docker-compose.prod.yml` to point to the `redis` service name within the Docker network.
- **Nginx Routing (API/LP Paths)**: Corrected 404 errors for API endpoints by ensuring Nginx `proxy_pass` directives did not strip necessary path prefixes (`/api/`, `/lp/`). Removed trailing slashes from `proxy_pass` URLs.
- **Nginx Routing (Liquidity API Path)**: Corrected 404 errors for `/liquidity-api/*` endpoints by adding a rewrite rule in Nginx to map them to the liquidity pool service's `/api/*` path (`rewrite ^/liquidity-api/(.*) /api/$1 break;`).

### 3. TradingPanel Component Optimization
- Fixed "Maximum update depth exceeded" error in the TradingPanel component
- Implemented refs to track initialization state and avoid unnecessary re-renders
- Fixed improper dependency arrays in useEffect hooks that were causing infinite updates
- Added proper wallet type detection with getActualWalletType function
- Created a memoized WalletSelector component to prevent unnecessary re-renders
- Added debug logging to diagnose wallet connectivity issues

### 4. Multi-Service Development Setup ✅
- **Unified Development Workflow**: Created root-level package.json with concurrent service management
- **Service Architecture**: Implemented 4-service architecture (frontend, spot_backend, liquidity_pool, solana)
- **Development Scripts**: Added comprehensive npm scripts for individual and combined service management
- **Dependency Management**: Installed dependencies for all services with proper permissions
- **Documentation Updates**: Updated README.md and memory-bank to reflect current multi-service architecture
- **Service Integration**: All services running successfully with proper port management:
  - Frontend: http://localhost:4001
  - Spot Backend: http://localhost:5001
  - Liquidity Pool: http://localhost:3047
  - Solana Service: http://localhost:6001

### 4. Wallet Integration Improvements
- Added support for multiple wallet types (Ethereum, Solana)
- Implemented visual differentiation between wallet types in the UI
- Added auto-selection of the first connected wallet when available
- Created a robust wallet selection mechanism with proper state management
- Fixed issues with wallet detection and connection status tracking
- Implemented proper cleanup to prevent memory leaks

### 5. Token Selection Hook Implementation
- Created and implemented `useTokenSelection` custom hook
- Provides shared selection functionality across components
- Handles localStorage persistence
- Manages event propagation and UI state

### 6. HighlightsTable Component Updates
- Added token selection button functionality
- Implemented click handling for navigation to trade page
- Added visual feedback for selected tokens
- Used the `useTokenSelection` hook for consistent behavior

### 7. Market Component Updates
- Updated token selection to use the shared hook
- Added proper event propagation prevention
- Implemented the correct navigation pattern to trade page with handleTokenClick
- All components should reference Market.jsx for the correct token navigation pattern

### 8. Radar Component Updates
- Replaced Set-based selection with array-based selection from hook
- Updated row click behavior to navigate to trade page
- Fixed event propagation to prevent unwanted clicks
- Added token selection UI that's consistent with other components

### 9. Backend Universal Network Handling
- Implemented better error handling for universal network data
- Added processing for universal token radar data
- Improved caching mechanism for network-specific data

### 10. Project Intelligence Capture
- Created `.cursorrules` file to store project-specific guidelines and patterns.

### 11. PumpFun Service Enhancements
- Implemented logic to differentiate between active bonding curve tokens and graduated tokens.
- Integrated DEX price fetching (via DexScreener) for graduated PumpFun tokens, with fallback mechanisms.
- Improved bonding curve PDA discovery using multiple seed phrases.
- Refined bonding curve pricing logic with dynamic scaling for specific tokens.
- Added caching for PumpFun bonding curve data and graduated token prices.
- Configured optional Chainstack RPC usage with authentication.

### 12. Uniswap Service Refactoring (BSC Split)
- Separated Uniswap V2 logic for Binance Smart Chain (BSC) into `uniswapV2BSCService.ts`.
- Updated `uniswapController.ts` to route requests based on `chainId` (56 for BSC, others to main `UniswapService`).
- Main `UniswapService` continues to use Alpha Router for Ethereum/testnets (V2/V3).
- Implemented improved fallback logic in `UniswapService` for failed protocol-specific quote requests.
- Standardized fee calculation logic in both Uniswap services based on environment variables.

## Next Steps

### 1. Production Monitoring & Stability
- Continuously monitor server resources (CPU, memory, disk).
- Monitor Nginx and application logs for errors.
- Verify SSL certificate auto-renewal is functioning.
- Perform load testing (optional, if needed).

### 2. Feature Testing in Production
- Test all core user flows (token selection, different views).
- Verify all API endpoints are working correctly through Nginx.
- Test trading quote functionality.
- Test wallet connection and balance fetching.

### 3. Complete Trading Functionality
- Implement actual swap execution with wallet signatures.
- Add transaction history tracking.
- Implement slippage protection.
- Add gas estimation for Ethereum-based swaps.
- Create transaction confirmation and receipt UI.

### 4. Error Handling and Resilience
- Implement robust error handling for failed trades.
- Add fallback DEX options when primary DEX is unavailable.
- Create better user feedback for network congestion or transaction failures.
- Implement retry logic for failed transactions.

### 5. Performance Optimizations
- Further optimize rendering performance for TradingPanel.
- Implement more efficient wallet state management.
- Consider using Web Workers for heavy computation tasks.
- Optimize quote fetching to reduce API calls.

### 6. Testing & Validation
- Test token selection across all components.
- Ensure selections persist between different views.
- Verify that selected tokens correctly appear in the trade view.
- Test trading functionality across different wallet types and networks.

### 7. UI/UX Refinements
- Add visual feedback for token selection actions.
- Consider adding a "clear all selections" feature.
- Ensure consistent styling across all components.
- Improve loading states and error messages for better user experience.

### 8. Liquidity Pool Integration
- Develop backend services for fetching and processing liquidity pool data.
- Create frontend components for displaying pool information.
- Implement analytics calculations for key metrics (APY, impermanent loss, etc.)
- Add token pair selection mechanism for pool exploration.
- Design and implement pool comparison feature.

### 9. Documentation & Project Rules
- Add further relevant rules and patterns to `.cursorrules` as they emerge.
- Keep Memory Bank files updated with significant changes.

## Active Decisions & Considerations

### 1. Deployment Strategy
**Decision**: Use Docker Compose for application services and host Nginx for reverse proxy/SSL.
**Rationale**: Resolved port 80 conflict with existing Nginx installation on the server.
**Implementation**: Modified `deploy.sh` to configure host Nginx, removed Nginx service from `docker-compose.prod.yml`.

### 2. Nginx Path Routing
**Decision**: Preserve path prefixes (`/api/`, `/lp/`) in `proxy_pass` and use `rewrite` for `/liquidity-api/`.
**Rationale**: Ensures requests reach the correct endpoints within the backend and liquidity pool services, which expect these prefixes.
**Implementation**: Removed trailing slashes from `proxy_pass` URLs and added `rewrite ^/liquidity-api/(.*) /api/$1 break;`.

### 3. Redis Configuration in Docker
**Decision**: Use Docker service name (`redis`) in connection URLs (`REDIS_URL=redis://redis:6379`) and environment variables (`REDIS_HOST=redis`).
**Rationale**: Allows services within the Docker network to resolve the Redis container correctly.
**Implementation**: Added `environment` section to `liquidity_pool` service in `docker-compose.prod.yml`.

### 4. Wallet Selection Strategy
**Decision**: Using refs to track initialization state and avoid unnecessary re-renders  
**Rationale**: Prevents infinite update loops caused by state updates during rendering  
**Implementation**: 
```javascript
// Track initialization state with a ref instead of triggering re-renders
const isInitializedRef = useRef(false);
const walletUpdateTimeoutRef = useRef(null);

// Auto-select first wallet with timeout to break render cycle
useEffect(() => {
  if (hasAutoSelectedRef.current || selectedWallet || !connectedWallets.length) {
    return;
  }
  
  hasAutoSelectedRef.current = true;
  
  // Delay to break the render cycle
  const timeoutId = setTimeout(() => {
    onSelectWallet(connectedWallets[0]);
  }, 0);
  
  return () => clearTimeout(timeoutId);
}, [connectedWallets, selectedWallet, onSelectWallet]);
```

### 5. Wallet Type Detection
**Decision**: Implementing a comprehensive wallet type detection function  
**Rationale**: Different wallets have different properties and detection methods  
**Implementation**:
```javascript
const getActualWalletType = useCallback((wallet) => {
  if (!wallet) return "unknown";
  
  // First, check if this is a Solana wallet by examining the address format
  const isSolanaAddress = wallet.address && !wallet.address.startsWith('0x');
  
  // Check for Phantom wallet specifically
  const isPhantomWallet = 
    wallet.walletClientType === "phantom" ||
    (isSolanaAddress && window.phantom && window.phantom.solana) ||
    (wallet.connector && wallet.connector.name === "phantom");
  
  if (isPhantomWallet) {
    return "phantom";
  }
  
  // For other wallet types...
  
  return wallet.walletClientType || "external";
}, []);
```

### 6. Token Identification Strategy
**Decision**: Using token IDs as the primary identifier, with symbol as fallback  
**Rationale**: IDs are more unique than symbols, which can be duplicated across networks  
**Implementation**: `const tokenId = token.id || token.symbol;`

### 7. Selection Data Structure
**Decision**: Using array-based storage in localStorage  
**Rationale**: Simpler serialization/deserialization than Set objects  
**Implementation**: Converting Sets to Arrays when persisting to localStorage

### 8. Event Propagation
**Decision**: Explicitly stopping event propagation in selection handlers  
**Rationale**: Prevents row clicks when selecting tokens, avoiding navigation when toggling selection  
**Implementation**: `e.stopPropagation()` in event handlers

### 9. Trade Page Navigation
**Decision**: Passing selected token data via React Router state  
**Rationale**: Provides immediate access to token data without additional API calls  
**Implementation**: 
```javascript
navigate('/trade', { 
  state: { 
    selectedTokens: [token], 
    activeToken: token 
  } 
});
```

### 10. Liquidity Pool Data Handling
**Decision**: Using a normalized data model for different protocol types  
**Rationale**: Creates a consistent interface for pools from various DEXes (Uniswap, PancakeSwap, SushiSwap, Raydium, Meteora)  
**Implementation**: 
```javascript
// Normalized pool data structure
const normalizePoolData = (rawData, protocol) => {
  return {
    id: generateUniqueId(rawData),
    protocol: protocol,
    tokenA: normalizeToken(rawData.token0 || rawData.coinA),
    tokenB: normalizeToken(rawData.token1 || rawData.coinB),
    liquidity: calculateLiquidity(rawData, protocol),
    volume24h: extractVolume(rawData),
    apy: calculateAPY(rawData, protocol),
    // Additional normalized fields
  };
};
```

### 11. Supported Liquidity Pools
**Decision**: Supporting five key DEX protocols across multiple chains  
**Rationale**: Provides coverage of the most popular liquidity sources while maintaining maintainable codebase  
**Implementation**:  
- **Uniswap**: Ethereum-based DEX (V2 and V3 support)
- **PancakeSwap**: BNB Chain (BSC)-based DEX
- **SushiSwap**: Multi-chain DEX (Ethereum, Polygon)
- **Raydium**: Solana-based DEX
- **Meteora**: Solana-based DEX

### 12. Chain-Specific Protocol Routing
**Decision**: Dynamically routing users to supported DEXes based on selected network  
**Rationale**: Different chains support different DEX protocols  
**Implementation**:
```javascript
// Chain-to-DEX mapping
const supportedDexesByChain = {
  // EVM chains
  'ethereum': ['uniswap', 'sushiswap'],
  'bsc': ['pancakeswap'],
  'polygon': ['sushiswap'],
  // Non-EVM chains
  'solana': ['raydium', 'meteora'],
};

// Get DEXes supported for the current chain
const supportedDexesForChain = supportedDexesByChain[chainId.toLowerCase()] || [];
```