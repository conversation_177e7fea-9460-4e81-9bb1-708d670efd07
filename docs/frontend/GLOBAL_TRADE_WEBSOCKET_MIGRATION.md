# Global Trade WebSocket Migration Guide

## Overview

This migration guide explains how to replace individual WebSocket connections with a global connection pooling solution to eliminate multiple WebSocket connections to the trading panel service.

## Problem

Previously, each component using `useBackendTradeData` created its own WebSocket connection, leading to:
- Multiple connections to the same trading panel service
- Resource waste and potential connection limits
- Inconsistent data across components
- Complex connection management

## Solution

The new global connection pooling system:
- **Single WebSocket Connection**: One shared connection for all components
- **Pool-based Subscriptions**: Multiple pool addresses supported on one connection
- **Automatic Cleanup**: Connections closed when no subscribers remain
- **Data Sharing**: All components receive the same real-time data

## Migration Steps

### 1. Replace Hook Import

**Before:**
```typescript
import { useBackendTradeData } from '@/hooks/useBackendTradeData';
```

**After:**
```typescript
import { useGlobalTradeData } from '@/hooks/useGlobalTradeData';
// or
import useGlobalTradeData from '@/hooks/useGlobalTradeData';
```

### 2. Update Hook Usage

The API is identical, so no changes needed:

```typescript
// This works exactly the same
const { trades, isLoading, isConnected, error, lastUpdate, latestRawTrade } = useGlobalTradeData(poolAddress);
```

### 3. Update Type Imports (if needed)

**Before:**
```typescript
import { FormattedTrade } from '@/hooks/useBackendTradeData';
```

**After:**
```typescript
import { FormattedTrade } from '@/hooks/useGlobalTradeData';
// or
import { FormattedTrade } from '@/services/globalTradeWebSocketService';
```

## Components to Update

### Priority 1: Main Connection Sources
1. **PulseTrade.tsx** (line 27)
2. **Pulse_Token.tsx** (line 65)
3. **Tables/Traders.tsx** (via tradeData prop)

### Priority 2: Any other components using useBackendTradeData

## Testing the Migration

### 1. Use the Test Component

Add the test component to verify connection pooling:

```typescript
import GlobalTradeDataTest from '@/components/GlobalTradeDataTest';

// Add to your route or temporarily to a page
<GlobalTradeDataTest />
```

### 2. Browser DevTools Verification

1. Open DevTools → Network tab → Filter by "WS"
2. Refresh the page
3. **Expected**: Only ONE `trading-panel-ws` connection
4. **Before**: Multiple `trading-panel-ws` connections

### 3. Console Log Verification

Look for these log patterns:
```
🔌 [GLOBAL] Subscribing to pool: [pool-address] (subscriber: [id])
✅ [GLOBAL] Connected to trading WebSocket server
📊 [HOOK] Received trade data update for pool: [pool-address]
```

## Key Features

### Connection Pooling
- Single WebSocket connection shared across all components
- Automatic connection management
- Exponential backoff for reconnections

### Pool Subscription Management
- Multiple pool addresses supported
- Automatic subscription/unsubscription
- Data isolation per pool

### Cleanup and Memory Management
- Automatic cleanup when components unmount
- Connection closed when no active subscriptions
- Prevents memory leaks

## Debugging

### Connection Count
Monitor WebSocket connections in browser DevTools:
- Network tab → WS filter
- Should see only ONE `trading-panel-ws` connection

### Console Logs
Enable detailed logging by checking console for:
- `[GLOBAL]` - Global service logs
- `[HOOK]` - Hook-level logs
- Connection status changes
- Subscription management

### Common Issues

1. **Multiple Connections Still Appearing**
   - Check if old `useBackendTradeData` is still being used
   - Verify all components are updated

2. **Data Not Updating**
   - Check pool address is correct
   - Verify WebSocket connection is established
   - Check console for error messages

3. **Connection Drops**
   - Check network connectivity
   - Verify backend service is running
   - Check for rate limiting

## Rollback Plan

If issues occur, you can quickly rollback by:

1. Revert hook imports back to `useBackendTradeData`
2. Remove global service imports
3. The old individual connection system will resume

## Performance Benefits

- **Reduced Resource Usage**: One connection vs multiple
- **Faster Data Updates**: Shared connection means faster propagation
- **Better Error Handling**: Centralized error management
- **Improved Reliability**: Single connection point reduces failure modes

## Next Steps

After migration:
1. Monitor connection count in production
2. Verify data consistency across components
3. Check for any performance improvements
4. Consider removing old `useBackendTradeData` hook after verification
