# Assets Display Flow in Portfolio Spot Tab

## Navigation Path
1. **Main App** → Portfolio page (`/portfolio`)
2. **Portfolio Page** → Tabs component 
3. **Tabs Component** → Spot tab (default active)
4. **Spot Component** → Assets/Trades toggle → **Assets Table**

## File Structure
```
spot_frontend/src/
├── Portfolio/
│   ├── Portfolio.tsx     (Main container)
│   ├── Tabs.tsx         (Tab navigation - Overview, Spot, Wallet)
│   └── Spot.tsx         (Contains Assets & Trades tables)
```

## Assets Display Location in Spot.tsx

The assets are displayed in the **bottom-right section** of the Spot tab, specifically:

### Layout Structure:
```
Spot Tab Layout:
┌─────────────────────────────────────────────────────────┐
│  Top Row (3 columns)                                    │
│  ┌─────────────┬────────────────────────────────────┐  │
│  │ Balance Card│  Profit & Loss Chart               │  │
│  │ - Total Value│  (Line chart with timeframes)      │  │
│  │ - Unrealized │                                    │  │
│  │ - Available  │                                    │  │
│  └─────────────┴────────────────────────────────────┘  │
│                                                         │
│  Bottom Row (3 columns)                                 │
│  ┌─────────────┬────────────────────────────────────┐  │
│  │ Left Side   │  ASSETS TABLE (2 columns wide)    │  │
│  │ - Performance│  ┌─────────────────────────────┐  │  │
│  │   History   │  │ Assets | Trades (toggle)    │  │  │
│  │ - Filtered  │  ├─────────────────────────────┤  │  │
│  │   Totals    │  │ Token | Price | Balance... │  │  │
│  │             │  ├─────────────────────────────┤  │  │
│  │             │  │ [Token rows displayed here] │  │  │
│  │             │  └─────────────────────────────┘  │  │
│  └─────────────┴────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────┘
```

## Code Location (Line Numbers in Spot.tsx)

### 1. **Assets/Trades Toggle Buttons** (Lines 427-437)
```tsx
<button onClick={() => setActiveView("Assets")}>Assets</button>
<button onClick={() => setActiveView("Trades")}>Trades</button>
```

### 2. **Assets Table Header** (Lines 456-462)
```tsx
<div className="grid grid-cols-5">
  <div>Token</div>
  <div>Price</div>
  <div>Cost</div>
  <div>Balance</div>
  <div>Value</div>
</div>
```

### 3. **Assets Display States** (Lines 463-525)
- **Loading State** (Lines 463-467): Shows spinner while fetching
- **Error State** (Lines 468-477): Shows error message with retry button
- **Assets List** (Lines 478-516): Maps through assets array and displays each token
- **Empty State** (Lines 517-524): Shows "No Assets Yet" message

### 4. **Individual Asset Row** (Lines 486-513)
Each asset is displayed as a grid row with:
- **Column 1**: Token logo, symbol, and name
- **Column 2**: Price (currently shows '-' if no price data)
- **Column 3**: Cost (currently shows '-')
- **Column 4**: Token balance amount
- **Column 5**: USD value

## Data Flow

1. **Wallet Connection**: Get wallet address from Privy
   ```tsx
   const { wallets } = useWallets();
   const solanaWallet = wallets.find(wallet => wallet.walletClientType === 'solana');
   const walletAddress = solanaWallet?.address;
   ```

2. **API Call** (Lines 82-164): 
   - Triggers when activeView is "Assets" and wallet is connected
   - POST request to `http://localhost:4001/api/wallet/solana-token-balance`
   - Sends `{ walletAddress }` in request body

3. **Data Processing** (Lines 107-127):
   - Receives token array from API
   - Adds SOL balance as first item if present
   - Stores in `assets` state

4. **Display** (Lines 480-515):
   - Maps through `assets` array
   - Calculates UI amount from raw balance
   - Renders each token in a table row

## Current Issues & Solutions

### Issue: Assets not showing even though API returns data
**Possible Causes:**
1. API response structure mismatch
2. Rate limiting (429 errors)
3. Rendering conditions not met

**To Debug:**
1. Check browser console for logs showing:
   - "Fetching assets for wallet: [address]"
   - "Full API Response: [data]"
   - "Found X tokens"

2. Verify wallet is connected (check if walletAddress exists)

3. Ensure you're on the "Spot" tab with "Assets" view selected

4. Check Network tab in browser DevTools to see actual API response

**Quick Test:**
Open browser console and check if these values exist:
- `walletAddress` should show your wallet address
- `assets` array should contain token data after API call
- `activeView` should be "Assets"