# Limit Order Price Monitoring Fixes

## Overview
Fixed the limit order price monitoring system to properly use Mobula's WebSocket API with fallback URL support and proper API key integration.

## Issues Fixed

### 1. WebSocket URL Configuration
**Problem**: The service was hardcoded to use `wss://api.mobula.io` without fallback support.

**Solution**: 
- Added support for primary and fallback WebSocket URLs
- Primary: `wss://api.mobula.io`
- Fallback: `wss://api-prod.mobula.io`
- Automatic fallback switching when primary URL fails

### 2. API Key Integration
**Problem**: API key was loaded but needed proper integration with Mobula's market data WebSocket API.

**Solution**:
- Ensured `MOBULA_API_KEY` is properly loaded from environment variables
- Integrated API key into WebSocket subscription messages according to Mobula documentation
- Added proper error handling for missing API key

### 3. Environment Configuration
**Problem**: Missing environment variables for WebSocket URL configuration.

**Solution**: Added new environment variables in `.env`:
```bash
# Mobula WebSocket URLs for limit order monitoring
MOBULA_MARKET_WSS_PRIMARY=wss://api.mobula.io
MOBULA_MARKET_WSS_FALLBACK=wss://api-prod.mobula.io
```

## Implementation Details

### WebSocket Message Format
Following Mobula's documentation, the service now sends properly formatted subscription messages:

```json
{
  "type": "market",
  "authorization": "YOUR-API-KEY",
  "payload": {
    "assets": [
      {
        "address": "token_address",
        "blockchain": "blockchain_id"
      }
    ],
    "interval": 15
  }
}
```

### Fallback Logic
- Service starts with primary URL (`wss://api.mobula.io`)
- If connection fails after max attempts, switches to fallback URL (`wss://api-prod.mobula.io`)
- Resets reconnection attempts when switching URLs
- Provides detailed logging for debugging

### Enhanced Status Reporting
The service now provides comprehensive status information including:
- Current WebSocket URL being used
- Primary and fallback URL configuration
- API key presence verification
- Connection state and statistics

## Files Modified

1. **`src/services/limitOrderMonitoringService.ts`**:
   - Added fallback URL support
   - Enhanced connection logic with proper error handling
   - Improved status reporting

2. **`.env`**:
   - Added `MOBULA_MARKET_WSS_PRIMARY` and `MOBULA_MARKET_WSS_FALLBACK` variables

3. **`src/test/testLimitOrderMonitoring.ts`** (new):
   - Test script to verify the monitoring service functionality

## Testing

Run the test script to verify the fixes:

```bash
cd backend/spot_backend
npm run build
node dist/test/testLimitOrderMonitoring.js
```

The test will:
1. Check configuration and API key presence
2. Initialize the monitoring service
3. Verify WebSocket connection
4. Test order refresh functionality
5. Report final status

## Monitoring and Debugging

The service provides detailed logging for:
- WebSocket connection attempts and status
- URL fallback switching
- API key validation
- Subscription message sending
- Market data reception and processing

Check logs for messages prefixed with:
- `🔌` - Connection events
- `📡` - Subscription events
- `🎯` - Order execution events
- `⚠️` - Warnings
- `❌` - Errors

## Next Steps

1. **Test with Real Orders**: Create test limit orders to verify price monitoring
2. **Performance Monitoring**: Monitor WebSocket connection stability
3. **Error Handling**: Add additional error handling for edge cases
4. **Rate Limiting**: Implement rate limiting if needed based on Mobula API limits

## API Documentation Reference

- Mobula WebSocket Market Feed: https://docs.mobula.io/indexing-stream/stream/websocket/wss-market-data
- Primary URL: `wss://api.mobula.io`
- Fallback URL: `wss://api-prod.mobula.io`
- API Key: Required in `authorization` field of subscription message
