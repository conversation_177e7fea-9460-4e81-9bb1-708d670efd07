# Wallet Delegation Management System

## Overview

This document describes the comprehensive wallet delegation management system implemented for Privy wallets in the RedFyn application. The system ensures proper wallet selection, handles delegation status checking, provides user prompts for enabling delegation, and fixes issues with temporary wallet selection.

## Key Features

### 1. **Delegation Status Checking**
- Automatically checks if the current default wallet has `delegated: true`
- Compares localStorage wallet ID with API response from `/api/wallet/privy-solana-info`
- Identifies temporary wallets (IDs starting with "temp_") and avoids selecting them as default

### 2. **Smart Wallet Selection Logic**
- Prioritizes properly delegated wallets over imported/temporary ones
- Filters out temporary wallets from automatic selection
- Falls back to non-imported wallets when no delegated wallets are available

### 3. **User Delegation Prompts**
- Shows clear prompts when delegation is needed
- Provides options to enable delegation or switch to an already-delegated wallet
- Handles imported wallets that cannot be delegated

### 4. **Enhanced Error Handling**
- Comprehensive error handling for API calls
- Specialized toast system for delegation-related feedback
- Deduplication system to prevent multiple identical toasts

## Implementation Components

### Core Files

#### `utils/walletDelegationManager.ts`
- **`checkWalletDelegationStatus()`**: Checks current wallet delegation status
- **`showDelegationPrompt()`**: Shows user prompts for delegation decisions
- **`handleWalletDelegationWorkflow()`**: Manages the complete delegation workflow
- **`updateStoredDefaultWallet()`**: Updates localStorage with selected wallet

#### `hooks/useWalletDelegation.ts`
- **`useWalletDelegation()`**: React hook for delegation management
- **`useAutoWalletDelegation()`**: Auto-triggers delegation workflow on mount
- Provides state management and actions for delegation operations

#### `utils/delegationToast.ts`
- **`DelegationToasts`**: Predefined toast messages for common scenarios
- **`showDelegationToast()`**: Specialized toast system for delegation feedback
- **`handleDelegationError()`**: Error handling with appropriate toast messages

#### `utils/walletUtils.ts` (Enhanced)
- **`getDefaultSolanaWalletAddress()`**: Enhanced to avoid temporary wallets
- **`getDefaultSolanaWalletInfoEnhanced()`**: Uses delegation management system
- Improved wallet selection logic with delegation awareness

### UI Components

#### `components/WalletDelegationTest.tsx`
- Test component for debugging delegation functionality
- Provides comprehensive testing interface
- Shows current delegation status and available wallets

#### Enhanced TradingPanel
- Integrated delegation status indicator
- Shows delegation prompts when needed
- Uses enhanced wallet selection logic

## API Integration

### Privy Delegation API
The system uses Privy's `useHeadlessDelegatedActions` hook:

```typescript
import { useHeadlessDelegatedActions } from '@privy-io/react-auth';

const { delegateWallet } = useHeadlessDelegatedActions();

// Enable delegation for a Solana wallet
await delegateWallet({
  address: walletAddress,
  chainType: 'solana'
});
```

### Backend API Response
Expected response from `/api/wallet/privy-solana-info`:

```json
{
  "success": true,
  "data": {
    "walletId": "wxt0u7nc4naactq9kt4cbjdw",
    "address": "594bYtjAQtFQx2dBSmLZCFMeneYXzRca9EbHeRDKC4qX",
    "delegated": true,
    "imported": false,
    "allSolanaWallets": [
      {
        "id": "wxt0u7nc4naactq9kt4cbjdw",
        "address": "594bYtjAQtFQx2dBSmLZCFMeneYXzRca9EbHeRDKC4qX",
        "delegated": true,
        "imported": false
      },
      {
        "id": "temp_g935C6veQ53oxSjc5LTHMhsZMNNzJH8rodioR9JnBzJ",
        "address": "g935C6veQ53oxSjc5LTHMhsZMNNzJH8rodioR9JnBzJ",
        "delegated": false,
        "imported": true
      }
    ]
  }
}
```

## Usage Examples

### Basic Usage in Components

```typescript
import { useWalletDelegation } from '../hooks/useWalletDelegation';

const MyComponent = () => {
  const walletDelegation = useWalletDelegation();

  // Check if delegation is needed
  if (walletDelegation.needsDelegation) {
    // Show delegation prompt or handle automatically
    walletDelegation.handleDelegationWorkflow();
  }

  return (
    <div>
      {walletDelegation.needsDelegation && (
        <div className="delegation-warning">
          Wallet delegation required for seamless trading
          <button onClick={() => walletDelegation.handleDelegationWorkflow()}>
            Enable
          </button>
        </div>
      )}
    </div>
  );
};
```

### Manual Delegation Check

```typescript
import { checkWalletDelegationStatus } from '../utils/walletDelegationManager';

const checkDelegation = async (userId: string) => {
  const result = await checkWalletDelegationStatus(userId);
  
  if (result.success && result.needsDelegation) {
    console.log('Delegation needed for:', result.currentWallet?.address);
    
    if (result.recommendedWallet) {
      console.log('Recommended wallet:', result.recommendedWallet.address);
    }
  }
};
```

## Configuration

### Privy Configuration
Ensure delegation is enabled in your Privy configuration:

```typescript
// main.tsx
const privyConfig = {
  embeddedWallets: {
    createOnLogin: 'all-users',
    delegateWalletsByDefault: true // Enable delegation by default
  }
};
```

### localStorage Structure
The system uses the following localStorage structure:

```json
{
  "defaultWallets": {
    "solana": "594bYtjAQtFQx2dBSmLZCFMeneYXzRca9EbHeRDKC4qX"
  }
}
```

## Error Handling

### Common Error Scenarios
1. **Imported Wallets**: Cannot be delegated, system suggests switching to delegated wallet
2. **Network Errors**: Handled with retry logic and user feedback
3. **API Failures**: Graceful fallback to standard wallet selection
4. **User Cancellation**: Proper cleanup and state management

### Toast Messages
The system provides specialized toast messages for:
- Delegation status checking
- Enabling delegation process
- Success/failure notifications
- Wallet switching confirmations
- Error states and warnings

## Testing

Use the `WalletDelegationTest` component to test the system:

1. Import and add to your component tree
2. Run delegation tests to verify functionality
3. Test different wallet scenarios (delegated, imported, temporary)
4. Verify toast system and error handling

## Benefits

1. **Seamless User Experience**: Automatic delegation management reduces friction
2. **Smart Wallet Selection**: Avoids problematic temporary wallets
3. **Clear User Feedback**: Comprehensive toast system keeps users informed
4. **Robust Error Handling**: Graceful handling of edge cases and errors
5. **Flexible Integration**: Easy to integrate into existing components

## Future Enhancements

1. **Batch Delegation**: Enable delegation for multiple wallets simultaneously
2. **Delegation Preferences**: Allow users to set delegation preferences
3. **Advanced Filtering**: More sophisticated wallet filtering options
4. **Analytics**: Track delegation success rates and user behavior
