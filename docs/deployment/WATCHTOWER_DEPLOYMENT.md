# Docker Watchtower Deployment Guide

This guide explains how to set up automatic deployments using Docker Watchtower for the Redfyn platform.

## What is Watchtower?

Watchtower is a container that monitors running Docker containers and watches for changes to their images. When Watchtower detects that an image has changed, it automatically restarts the container using the new image.

## Setup Overview

We've configured a complete continuous deployment system:

1. Docker images are built and pushed to a registry
2. Watchtower on the production server monitors for new images
3. When new images are detected, Watchtower automatically updates the containers

## Pre-requisites

- Docker and Docker Compose installed on both development and production servers
- A Docker registry account (Docker Hub, GitLab Container Registry, etc.)
- SSH access to the production server

## Configuration Files

The following files have been set up for the deployment process:

1. **docker-compose.yml** - Updated with Watchtower configuration and container labels
2. **docker-compose.prod.yml** - Production-specific Docker Compose configuration
3. **scripts/docker-env.sh** - Environment variables for the Docker registry and deployment
4. **scripts/deploy-to-production.sh** - Deployment script to build, push and deploy images

## Step 1: Configure Environment Variables

Edit the `scripts/docker-env.sh` file with your Docker registry and production server details:

```bash
# Docker registry URL (e.g., docker.io/username or your private registry)
export REGISTRY_URL="docker.io/yourorganization"

# Docker registry credentials (optional)
export DOCKER_USERNAME="your-username"
export DOCKER_PASSWORD="your-password"

# Production server SSH details
export PROD_SERVER="<EMAIL>"
export SSH_KEY_PATH="/path/to/your/ssh/key"

# The domain name for your application
export DOMAIN_NAME="redfyn.crypfi.io"
```

## Step 2: Deploy Initial Setup to Production Server

1. Source the environment variables:

```bash
source ./scripts/docker-env.sh
```

2. Deploy the full setup to the production server:

```bash
./scripts/deploy-to-production.sh
```

This script will:
- Build Docker images for all services
- Push them to your Docker registry
- Connect to your production server and pull the images
- Start the containers with Watchtower monitoring

## Step 3: Continuous Deployment Process

Once the initial setup is complete, the continuous deployment process works as follows:

1. Make changes to your code and commit them
2. Build and push new Docker images with new tags:

```bash
source ./scripts/docker-env.sh
./scripts/deploy-to-production.sh
```

3. The script will push new images to your Docker registry
4. Watchtower on the production server will detect the new images and automatically update the containers

## Services Overview

The following services are configured for automatic deployment:

1. **frontend** - The Redfyn frontend application (React)
2. **spot-backend** - The main backend API service
3. **solana** - The Solana blockchain integration service
4. **trading-panel** - The trading panel service
5. **limit-orders** - The limit orders management service

## Managing Watchtower

### Check Watchtower Logs

SSH into your production server and check Watchtower logs:

```bash
docker logs redfyn-watchtower
```

### Manual Container Updates

If you need to force a container update:

```bash
docker-compose -f docker-compose.prod.yml up -d --force-recreate <service-name>
```

### Disabling Updates for Specific Containers

To exclude a container from automatic updates, remove the `com.centurylinklabs.watchtower.enable=true` label from its configuration.

## Watchtower Configuration Options

The Watchtower container is configured with the following options:

- `--interval 300`: Check for updates every 300 seconds (5 minutes)
- `--cleanup`: Remove old images after updating
- `--label-enable`: Only watch containers with a specific label
- `--debug`: Enable verbose logging for troubleshooting

You can adjust these settings in the `docker-compose.prod.yml` file.

## Notifications (Optional)

To receive notifications when Watchtower updates containers, set the `WATCHTOWER_NOTIFICATION_URL` environment variable:

```bash
export WATCHTOWER_NOTIFICATION_URL="slack://webhook-url"
```

Watchtower supports various notification channels including Slack, Discord, Email, and more.

## Security Considerations

1. Use private Docker repositories for production images
2. Configure Docker registry authentication on your production server
3. Use SSH keys for secure server access
4. Store secrets in environment variables, not in Docker Compose files

## Troubleshooting

### Watchtower not updating containers

Check the following:
- Ensure containers have the `com.centurylinklabs.watchtower.enable=true` label
- Verify Docker registry authentication works on the production server
- Check Watchtower logs for any errors

### Registry authentication issues

If Watchtower can't pull new images due to authentication problems:

```bash
# On the production server
docker login your-registry-url
```

This will create/update credentials in `/root/.docker/config.json` which is mounted to the Watchtower container.

## Rollback Procedure

To roll back to a previous version:

1. Specify the previous image tag in your deployment:

```bash
export TAG=previous-version
docker-compose -f docker-compose.prod.yml up -d
```

2. Or pull and run a specific image version:

```bash
docker pull ${REGISTRY_URL}/redfyn-frontend:specific-tag
# Update tag in docker-compose.prod.yml
docker-compose -f docker-compose.prod.yml up -d
``` 