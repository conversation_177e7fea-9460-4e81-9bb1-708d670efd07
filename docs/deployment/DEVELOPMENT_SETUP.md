# Redfyn Development Environment Setup

This guide explains how to set up and run the Redfyn microservices application in development mode.

## Overview

The development environment differs from production in the following ways:
- **No SSL/HTTPS** - All services run on HTTP
- **Solana Devnet** - Uses Solana devnet instead of mainnet
- **Debug Mode** - All services run with NODE_ENV=development
- **Direct Port Access** - Each service is accessible on its own port
- **Traefik Dashboard** - Enabled without authentication

## Service Ports

| Service | Port | URL |
|---------|------|-----|
| Frontend (React) | 3000 | http://localhost:3000 |
| Backend API | 5001 | http://localhost:5001 |
| Solana Service | 6001 | http://localhost:6001 |
| Trading Panel | 5003 | http://localhost:5003 |
| Limit Orders | 5002 | http://localhost:5002 |
| Redis | 6379 | redis://localhost:6379 |
| Traefik Proxy | 80 | http://localhost |
| Traefik Dashboard | 8080 | http://localhost:8080 |

## Prerequisites

1. <PERSON><PERSON> and <PERSON><PERSON> Compose installed
2. Access to Docker Hub (to pull prasanthats/redfyn images)
3. Basic understanding of Docker and microservices

## Quick Start

### 1. Set Up Environment Variables

Copy the development environment file:
```bash
cp .env.development .env
```

Edit `.env` and add your specific values:
- Database connection string
- API keys (Mobula, CoinGecko, etc.)
- Any other service-specific credentials

### 2. Start Development Environment

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up -d

# View logs for all services
docker-compose -f docker-compose.dev.yml logs -f

# View logs for specific service
docker-compose -f docker-compose.dev.yml logs -f backend
```

### 3. Verify Services

Check that all services are running:
```bash
docker-compose -f docker-compose.dev.yml ps
```

Test service endpoints:
```bash
# Frontend
curl http://localhost:3000

# Backend API health check
curl http://localhost:5001/api/health

# Solana service
curl http://localhost:6001/solana-api/health

# Trading panel
curl http://localhost:5003/trading-panel-api/health

# Limit orders
curl http://localhost:5002/limit-orders-api/health
```

### 4. Access Traefik Dashboard

Open http://localhost:8080 in your browser to see:
- Service routing configuration
- Health status of all services
- Real-time request metrics

## Development Workflow

### Making Changes

Since the development environment uses pre-built Docker images from Docker Hub, you have several options for development:

1. **Hot Reload with Volume Mounts** (Recommended for active development):
   ```yaml
   # Add to any service in docker-compose.dev.yml
   volumes:
     - ./backend/spot_backend:/app
   ```

2. **Build Local Images**:
   ```bash
   # Build and run with local changes
   docker-compose -f docker-compose.build.yml build
   docker-compose -f docker-compose.dev.yml up -d
   ```

3. **Use Development Scripts**:
   ```bash
   # Switch to development mode
   ./scripts/dev-mode.sh

   # Switch back to production mode
   ./scripts/prod-mode.sh
   ```

### Database Migrations

For database changes in development:
```bash
# Run migrations
docker-compose -f docker-compose.dev.yml exec backend npm run migrate

# Create new migration
docker-compose -f docker-compose.dev.yml exec backend npm run migrate:create
```

### Testing Solana Integration

The development environment uses Solana devnet:
1. Get devnet SOL from https://solfaucet.com/
2. Use devnet wallet addresses for testing
3. All transactions are on devnet (no real money)

## Switching Between Environments

### From Production to Development
```bash
# Stop production services
docker-compose down

# Start development services
docker-compose -f docker-compose.dev.yml up -d
```

### From Development to Production
```bash
# Stop development services
docker-compose -f docker-compose.dev.yml down

# Start production services
docker-compose up -d
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :3000
   
   # Kill process
   kill -9 <PID>
   ```

2. **Redis Connection Issues**
   ```bash
   # Check Redis is running
   docker-compose -f docker-compose.dev.yml ps redis
   
   # Test Redis connection
   docker-compose -f docker-compose.dev.yml exec redis redis-cli ping
   ```

3. **Service Not Starting**
   ```bash
   # Check service logs
   docker-compose -f docker-compose.dev.yml logs <service-name>
   
   # Restart specific service
   docker-compose -f docker-compose.dev.yml restart <service-name>
   ```

### Debug Mode

To enable verbose logging:
1. Set `LOG_LEVEL=debug` in environment variables
2. Check service logs for detailed debug output

### Clean Start

For a completely fresh start:
```bash
# Stop all services and remove volumes
docker-compose -f docker-compose.dev.yml down -v

# Remove all images
docker rmi $(docker images 'prasanthats/redfyn*' -q)

# Start fresh
docker-compose -f docker-compose.dev.yml up -d
```

## Development Best Practices

1. **Use Devnet Wallets** - Never use mainnet wallets in development
2. **Monitor Logs** - Keep logs open while developing
3. **Test API Changes** - Use Postman or curl to test endpoints
4. **Check Traefik** - Use dashboard to verify routing
5. **Commit .env.example** - Never commit actual .env files

## Additional Resources

- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Traefik Documentation](https://doc.traefik.io/traefik/)
- [Solana Devnet Guide](https://docs.solana.com/clusters#devnet)
- [Redis Documentation](https://redis.io/documentation)