# Redfyn Build Server Setup

This server is configured exclusively for building and pushing Docker images to Docker Hub.

## Quick Start

1. **Set up environment (if needed):**
   ```bash
   ./setup_env.sh
   ```

2. **Build and push all images:**
   ```bash
   ./scripts/build-and-push-compose.sh
   ```

## What Gets Built

The following services are built and pushed to `prasanthats/redfyn`:

- **frontend** - React frontend application
- **backend** - Spot backend service
- **solana** - Solana blockchain service
- **trading-panel** - Trading panel service
- **limit-orders** - Limit orders service
- **redis** - Redis cache (official image retagged)
- **traefik** - Reverse proxy (official image retagged)
- **watchtower** - Auto-updater (official image retagged)

## Image Tags

Each image is pushed with two tags:
- `latest` - For automatic updates via Watchtower
- `<git-commit-hash>` - For specific version deployment

Example:
- `prasanthats/redfyn:frontend-latest`
- `prasanthats/redfyn:frontend-a1b2c3d`

## Docker Hub Authentication

You can authenticate in two ways:

1. **Interactive login:**
   The script will prompt for Docker Hub credentials

2. **Environment variables:**
   ```bash
   export DOCKER_USERNAME="prasanthats"
   export DOCKER_PASSWORD="your-password"
   ```

## Files Structure

```
/www/wwwroot/redfyn-spot/
├── docker-compose.build.yml     # Build configuration
├── scripts/
│   └── build-and-push-compose.sh  # Main build script
├── backend/
│   ├── spot_backend/
│   ├── solana/
│   ├── trading_panel/
│   └── limit_orders/
└── spot_frontend/
```

## Build Process

1. Builds all services in parallel using `docker-compose.build.yml`
2. Tags each image with both `latest` and git commit hash
3. Pushes all images to Docker Hub
4. Pulls and retags third-party images (Redis, Traefik, Watchtower)

## Production Deployment

After building and pushing images, the production server can:
- Pull images using the `latest` tag for automatic updates
- Pull images using the git commit hash for fixed versions
- Use Watchtower for automatic container updates