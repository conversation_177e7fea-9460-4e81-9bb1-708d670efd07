# Redfyn Production Deployment Guide

## Overview

This guide covers deploying the Redfyn application stack to a production server using Docker images from Docker Hub.

## Prerequisites

1. **Production Server Requirements:**
   - Linux server (Ubuntu 20.04+ recommended)
   - Docker and Docker Compose installed
   - Minimum 4GB RAM, 2 CPU cores
   - Port 80 and 443 open for web traffic
   - Domain name pointed to server IP

2. **Docker Hub Access:**
   - Images are hosted at: `prasanthats/redfyn`
   - Repository: https://hub.docker.com/r/prasanthats/redfyn

## Production Server Setup

### 1. Install Docker (if not already installed)

```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### 2. Create Project Directory

```bash
sudo mkdir -p /www/wwwroot/redfyn-spot
cd /www/wwwroot/redfyn-spot
```

### 3. Create Production Docker Compose File

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  # Traefik reverse proxy with automatic SSL
  traefik:
    image: prasanthats/redfyn:traefik-latest
    container_name: redfyn-traefik
    restart: unless-stopped
    command:
      - --api.dashboard=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=${ADMIN_EMAIL}
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard (optional, remove in production)
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-ssl-certs:/letsencrypt
    networks:
      - app-network

  # Watchtower for automatic updates
  watchtower:
    image: prasanthats/redfyn:watchtower-latest
    container_name: redfyn-watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command: --interval 300 --cleanup --label-enable
    environment:
      - WATCHTOWER_LABEL_ENABLE=true
    labels:
      - "com.centurylinklabs.watchtower.enable=false"
    networks:
      - app-network

  # Redis service
  redis:
    image: prasanthats/redfyn:redis-latest
    container_name: redfyn-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - app-network
    command: redis-server --appendonly yes --maxclients 10000
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # Solana Service
  solana:
    image: prasanthats/redfyn:solana-latest
    container_name: redfyn-solana
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=6001
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    env_file:
      - ./backend/solana/.env.production
    networks:
      - app-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:6001/health"]
      interval: 15s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.solana.rule=Host(`${DOMAIN_NAME}`) && PathPrefix(`/solana-api`)"
      - "traefik.http.routers.solana.entrypoints=websecure"
      - "traefik.http.routers.solana.tls.certresolver=letsencrypt"
      - "traefik.http.services.solana.loadbalancer.server.port=6001"
      - "traefik.http.middlewares.solana-stripprefix.stripprefix.prefixes=/solana-api"
      - "traefik.http.routers.solana.middlewares=solana-stripprefix"
      - "com.centurylinklabs.watchtower.enable=true"

  # Spot Backend Service
  spot-backend:
    image: prasanthats/redfyn:backend-latest
    container_name: redfyn-spot-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5001
      - REDIS_URL=redis://redis:6379
    env_file:
      - ./backend/spot_backend/.env.production
    networks:
      - app-network
    depends_on:
      - redis
      - solana
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`${DOMAIN_NAME}`) && (PathPrefix(`/api`) || PathPrefix(`/socket.io/`))"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.backend.loadbalancer.server.port=5001"
      - "com.centurylinklabs.watchtower.enable=true"

  # Trading Panel Service
  trading-panel:
    image: prasanthats/redfyn:trading-panel-latest
    container_name: redfyn-trading-panel
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5003
    env_file:
      - ./backend/trading_panel/.env.production
    networks:
      - app-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.trading-panel.rule=Host(`${DOMAIN_NAME}`) && (PathPrefix(`/trading-panel-api`) || PathPrefix(`/trading-panel-ws`))"
      - "traefik.http.routers.trading-panel.entrypoints=websecure"
      - "traefik.http.routers.trading-panel.tls.certresolver=letsencrypt"
      - "traefik.http.services.trading-panel.loadbalancer.server.port=5003"
      - "com.centurylinklabs.watchtower.enable=true"

  # Limit Orders Service
  limit-orders:
    image: prasanthats/redfyn:limit-orders-latest
    container_name: redfyn-limit-orders
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5002
    env_file:
      - ./backend/limit_orders/.env.production
    networks:
      - app-network
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.limit-orders.rule=Host(`${DOMAIN_NAME}`) && PathPrefix(`/limit-orders-api`)"
      - "traefik.http.routers.limit-orders.entrypoints=websecure"
      - "traefik.http.routers.limit-orders.tls.certresolver=letsencrypt"
      - "traefik.http.services.limit-orders.loadbalancer.server.port=5002"
      - "traefik.http.middlewares.limit-orders-stripprefix.stripprefix.prefixes=/limit-orders-api"
      - "traefik.http.routers.limit-orders.middlewares=limit-orders-stripprefix"
      - "com.centurylinklabs.watchtower.enable=true"

  # Frontend Service
  frontend:
    image: prasanthats/redfyn:frontend-latest
    container_name: redfyn-frontend
    restart: unless-stopped
    env_file:
      - ./spot_frontend/.env.production
    networks:
      - app-network
    depends_on:
      - spot-backend
      - solana
      - trading-panel
      - limit-orders
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`${DOMAIN_NAME}`)"
      - "traefik.http.routers.frontend.entrypoints=websecure"
      - "traefik.http.routers.frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"
      # HTTP to HTTPS redirect
      - "traefik.http.routers.frontend-http.rule=Host(`${DOMAIN_NAME}`)"
      - "traefik.http.routers.frontend-http.entrypoints=web"
      - "traefik.http.routers.frontend-http.middlewares=redirect-to-https"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
      - "com.centurylinklabs.watchtower.enable=true"

networks:
  app-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
  traefik-ssl-certs:
    driver: local
```

### 4. Create Environment Files

Create the following directory structure and .env files:

```bash
mkdir -p backend/spot_backend
mkdir -p backend/solana
mkdir -p backend/trading_panel
mkdir -p backend/limit_orders
mkdir -p spot_frontend
```

Create `.env` file in root:
```bash
DOMAIN_NAME=your-domain.com
ADMIN_EMAIL=<EMAIL>
```

Create production env files for each service:
- `backend/spot_backend/.env.production`
- `backend/solana/.env.production`
- `backend/trading_panel/.env.production`
- `backend/limit_orders/.env.production`
- `spot_frontend/.env.production`

### 5. Deploy the Application

```bash
# Login to Docker Hub
docker login

# Pull all images
docker compose -f docker-compose.prod.yml pull

# Start all services
docker compose -f docker-compose.prod.yml up -d

# Check status
docker compose -f docker-compose.prod.yml ps

# View logs
docker compose -f docker-compose.prod.yml logs -f
```

## Deployment Options

### Automatic Updates (Recommended)

The configuration above uses `:latest` tags. Watchtower will automatically update containers when new images are pushed to Docker Hub.

### Manual Updates

To manually update to latest images:
```bash
docker compose -f docker-compose.prod.yml pull
docker compose -f docker-compose.prod.yml up -d
```

**Note:** Only `latest` tags are available. Version-specific tags are not pushed to keep Docker Hub clean.

## SSL Certificate Setup

Traefik automatically handles SSL certificates via Let's Encrypt. Ensure:
1. Domain DNS is properly configured
2. Ports 80 and 443 are accessible
3. Valid email is set in ADMIN_EMAIL

## Monitoring and Maintenance

### Check Service Status
```bash
docker compose -f docker-compose.prod.yml ps
```

### View Logs
```bash
# All services
docker compose -f docker-compose.prod.yml logs -f

# Specific service
docker compose -f docker-compose.prod.yml logs -f spot-backend
```

### Restart Services
```bash
# All services
docker compose -f docker-compose.prod.yml restart

# Specific service
docker compose -f docker-compose.prod.yml restart spot-backend
```

### Update Services
```bash
# Pull latest images
docker compose -f docker-compose.prod.yml pull

# Recreate containers
docker compose -f docker-compose.prod.yml up -d
```

## Backup

### Redis Data Backup
```bash
docker exec redfyn-redis redis-cli BGSAVE
docker cp redfyn-redis:/data/dump.rdb ./redis-backup-$(date +%Y%m%d).rdb
```

### Full Backup Script
```bash
#!/bin/bash
BACKUP_DIR="/backups/redfyn/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup Redis
docker exec redfyn-redis redis-cli BGSAVE
docker cp redfyn-redis:/data/dump.rdb $BACKUP_DIR/redis-dump.rdb

# Backup env files
cp -r backend $BACKUP_DIR/
cp -r spot_frontend $BACKUP_DIR/
cp docker-compose.prod.yml $BACKUP_DIR/
```

## Troubleshooting

### Service Won't Start
```bash
# Check logs
docker compose -f docker-compose.prod.yml logs service-name

# Check container status
docker ps -a
```

### SSL Certificate Issues
```bash
# Check Traefik logs
docker logs redfyn-traefik

# Reset certificates
docker compose -f docker-compose.prod.yml down
docker volume rm redfyn-spot_traefik-ssl-certs
docker compose -f docker-compose.prod.yml up -d
```

### Performance Issues
```bash
# Check resource usage
docker stats

# Scale services (if using Docker Swarm)
docker service scale redfyn_spot-backend=3
```

## Security Recommendations

1. **Firewall Configuration:**
   ```bash
   sudo ufw allow 80/tcp
   sudo ufw allow 443/tcp
   sudo ufw allow 22/tcp
   sudo ufw enable
   ```

2. **Remove Traefik Dashboard in Production:**
   Remove port 8080 mapping from traefik service

3. **Use Strong Passwords:**
   Ensure all .env files contain strong, unique passwords

4. **Regular Updates:**
   Keep Docker and system packages updated

5. **Monitor Logs:**
   Set up log aggregation and monitoring

## Support

For issues or questions:
1. Check service logs: `docker compose logs -f`
2. Verify environment variables are set correctly
3. Ensure all required ports are open
4. Check Docker Hub for latest images at https://hub.docker.com/r/prasanthats/redfyn