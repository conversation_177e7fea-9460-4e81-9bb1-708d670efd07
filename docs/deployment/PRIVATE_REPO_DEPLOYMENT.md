# Private Repository Deployment Guide

This document outlines how to deploy the Redfyn application using a private Docker repository.

## Repository Structure

All Docker images are now stored in a private repository under:

```
prasanthats/redfyn
```

Each service is tagged with its service name followed by a version:

- `prasanthats/redfyn:frontend-latest`
- `prasanthats/redfyn:backend-latest`
- `prasanthats/redfyn:solana-latest`
- `prasanthats/redfyn:trading-panel-latest`
- `prasanthats/redfyn:limit-orders-latest`
- `prasanthats/redfyn:redis-latest`
- `prasanthats/redfyn:traefik-latest`
- `prasanthats/redfyn:watchtower-latest`

## Building and Pushing Images

To build and push all images to the private repository, run:

```bash
sudo bash scripts/build-and-push.sh
```

This script will:
1. Build all service images from their respective Dockerfiles
2. Tag them with the appropriate service name
3. Push them to the private repository
4. Pull, tag, and push third-party images (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>tower)

## Deployment

The `docker-compose.prod.yml` file has been updated to use images from the private repository. To deploy:

1. Ensure you have Docker and Docker Compose installed on your production server
2. Log in to Docker Hub on your production server:
   ```bash
   sudo docker login
   ```
3. Pull the latest code:
   ```bash
   git pull origin main
   ```
4. Deploy using the production compose file:
   ```bash
   sudo docker-compose -f docker-compose.prod.yml up -d
   ```

## Automated Deployment

For automated deployment with commit-specific versioning:

```bash
sudo bash scripts/deploy-to-production.sh
```

This script will:
1. Build and tag images with both the git commit hash and `latest` tag
2. Push all images to the private repository
3. Optionally connect to the production server and update the deployment

## Watchtower Auto-Updates

Watchtower is configured to automatically update containers when new images are pushed to the repository. The update process:

1. New images are pushed to the repository
2. Watchtower detects new images and pulls them
3. Containers are gracefully restarted with the new images

## Security Considerations

- The private repository provides an additional layer of security by restricting access to your Docker images
- Ensure Docker Hub credentials are securely stored on your build and production servers
- Consider using Docker Hub access tokens instead of your main account password

## Troubleshooting

If you encounter issues with image pulls on the production server:

1. Verify Docker Hub login status:
   ```bash
   sudo docker login
   ```
2. Check if the images exist in your repository:
   ```bash
   sudo docker search prasanthats/redfyn
   ```
3. Manually pull an image to test connectivity:
   ```bash
   sudo docker pull prasanthats/redfyn:frontend-latest
   ```
4. Check Docker daemon logs:
   ```bash
   sudo journalctl -u docker
   ``` 