# Private Repository Setup Summary

## Overview

All Docker images for the Redfyn application have been successfully moved to a private Docker Hub repository. This provides better security and organization of your container images.

## Repository Structure

All images are now stored in:

```
prasanthats/redfyn
```

Each service is tagged with its service name:

| Service | Image Tag |
|---------|-----------|
| Frontend | `prasanthats/redfyn:frontend-latest` |
| Backend | `prasanthats/redfyn:backend-latest` |
| Solana | `prasanthats/redfyn:solana-latest` |
| Trading Panel | `prasanthats/redfyn:trading-panel-latest` |
| Limit Orders | `prasanthats/redfyn:limit-orders-latest` |
| Redis | `prasanthats/redfyn:redis-latest` |
| Traefik | `prasanthats/redfyn:traefik-latest` |
| Watchtower | `prasanthats/redfyn:watchtower-latest` |

## Available Scripts

The following scripts have been created to help manage your private repository:

1. **build-and-push.sh**
   - Builds all service images from their respective Dockerfiles
   - Tags them with the appropriate service name
   - Pushes them to the private repository
   - Usage: `sudo bash scripts/build-and-push.sh`

2. **check-private-repo.sh**
   - Checks if all required images exist in your private repository
   - Usage: `sudo bash scripts/check-private-repo.sh`

3. **verify-private-repo.sh**
   - Verifies your local Docker setup and configuration
   - Checks if docker-compose.prod.yml is properly configured
   - Usage: `sudo bash scripts/verify-private-repo.sh`

4. **deploy-with-env.sh**
   - Deploys the application using environment variables from deploy.env
   - Pulls the latest images from your private repository
   - Usage: `sudo bash scripts/deploy-with-env.sh`

5. **deploy-to-production.sh**
   - Comprehensive deployment script with versioning based on git commit hash
   - Can optionally deploy to a remote server
   - Usage: `sudo bash scripts/deploy-to-production.sh`

## Configuration Files

1. **docker-compose.prod.yml**
   - Updated to use images from your private repository
   - Includes default values for environment variables
   - Configured with Traefik for automatic SSL and routing

2. **deploy.env.example**
   - Template for deployment environment variables
   - Copy to deploy.env and customize for your deployment

## Deployment Instructions

### Local Deployment

1. Copy the environment template:
   ```bash
   cp deploy.env.example deploy.env
   ```

2. Edit deploy.env with your settings:
   ```bash
   nano deploy.env
   ```

3. Run the deployment script:
   ```bash
   sudo bash scripts/deploy-with-env.sh
   ```

### Remote Server Deployment

1. Set up SSH access to your production server

2. Run the production deployment script:
   ```bash
   sudo bash scripts/deploy-to-production.sh
   ```

3. Follow the prompts to complete the deployment

## Watchtower Auto-Updates

Watchtower is configured to automatically update containers when new images are pushed to the repository. The update process:

1. Build and push new images with the build-and-push.sh script
2. Watchtower on the production server detects new images and pulls them
3. Containers are gracefully restarted with the new images

## Troubleshooting

If you encounter issues:

1. Check Docker Hub login status:
   ```bash
   sudo docker login
   ```

2. Verify your images exist in the repository:
   ```bash
   sudo bash scripts/check-private-repo.sh
   ```

3. Validate your docker-compose configuration:
   ```bash
   sudo docker compose -f docker-compose.prod.yml config
   ```

4. Check container logs:
   ```bash
   sudo docker logs redfyn-frontend
   sudo docker logs redfyn-traefik
   ``` 