# Available Scripts

This document provides an overview of the available scripts in the `scripts/` directory and their purposes.

## Deployment Scripts

### 1. `build-and-push.sh`

**Purpose:** Builds all service Docker images and pushes them to your private Docker Hub repository.

**Usage:**
```bash
sudo bash scripts/build-and-push.sh
```

**What it does:**
- Builds Docker images for all services (frontend, backend, solana, trading_panel, limit_orders)
- Tags them with appropriate names in your private repository
- Pushes the images to Docker Hub
- Also pulls, tags, and pushes third-party images (Redis, Traefik, Watchtower)

### 2. `deploy-to-production.sh`

**Purpose:** Comprehensive deployment script with versioning based on git commit hash.

**Usage:**
```bash
sudo bash scripts/deploy-to-production.sh
```

**What it does:**
- Builds and tags images with both git commit hash and `latest` tag
- Pushes all images to your private repository
- Can optionally connect to a remote production server and update the deployment
- Updates image tags in docker-compose.prod.yml on the production server
- Restarts Watchtower to trigger automatic updates of all containers

### 3. `deploy-with-env.sh`

**Purpose:** Deploys the application using environment variables from a deploy.env file.

**Usage:**
```bash
sudo bash scripts/deploy-with-env.sh
```

**What it does:**
- Loads environment variables from deploy.env
- Pulls the latest images from your private repository
- Deploys services using docker-compose.prod.yml

### 4. `verify-deployment.sh`

**Purpose:** Verifies that your deployment configuration is valid.

**Usage:**
```bash
sudo bash scripts/verify-deployment.sh
```

**What it does:**
- Checks if all required Docker images exist locally
- Verifies that docker-compose.prod.yml is configured to use your private repository
- Creates a sample deploy.env file if it doesn't exist
- Validates the docker-compose.prod.yml syntax

## Utility Scripts

### 5. `cleanup.sh`

**Purpose:** Removes temporary or redundant scripts that are no longer needed.

**Usage:**
```bash
sudo bash scripts/cleanup.sh
```

**What it does:**
- Removes temporary scripts used during the migration process
- Keeps only essential deployment scripts

### 6. `check-port.sh`

**Purpose:** Checks if a specific port is in use.

**Usage:**
```bash
bash scripts/check-port.sh <port_number>
```

**What it does:**
- Checks if the specified port is already in use
- Useful for troubleshooting port conflicts

### 7. `check-ports.sh`

**Purpose:** Checks multiple ports used by the application services.

**Usage:**
```bash
bash scripts/check-ports.sh
```

**What it does:**
- Checks all ports used by the application services
- Helps identify port conflicts before deployment

### 8. `create-docker-repos.sh`

**Purpose:** Creates Docker repositories for your services.

**Usage:**
```bash
sudo bash scripts/create-docker-repos.sh
```

**What it does:**
- Creates Docker repositories for your services on Docker Hub
- Note: This script is primarily used during initial setup

### 9. `docker-env.sh`

**Purpose:** Sets up environment variables for Docker.

**Usage:**
```bash
source scripts/docker-env.sh
```

**What it does:**
- Sets up environment variables for Docker
- Useful for configuring Docker in different environments

## How to Use These Scripts

### For Initial Deployment:

1. Verify your deployment configuration:
   ```bash
   sudo bash scripts/verify-deployment.sh
   ```

2. Create a deploy.env file with your settings:
   ```bash
   cp deploy.env.example deploy.env
   nano deploy.env
   ```

3. Deploy the application:
   ```bash
   sudo bash scripts/deploy-with-env.sh
   ```

### For Updates and Continuous Deployment:

1. Build and push updated images:
   ```bash
   sudo bash scripts/build-and-push.sh
   ```

2. Deploy to production:
   ```bash
   sudo bash scripts/deploy-to-production.sh
   ```

### For Troubleshooting:

1. Check port conflicts:
   ```bash
   bash scripts/check-ports.sh
   ```

2. Verify deployment configuration:
   ```bash
   sudo bash scripts/verify-deployment.sh
   ``` 