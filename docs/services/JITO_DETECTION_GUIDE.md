# How to Detect Jito-Enabled Transactions

## 🎯 Overview

There are several ways to identify if a transaction was executed through Jito MEV protection. This guide covers all detection methods from simple API response checks to on-chain analysis.

## 🔍 Detection Methods

### Method 1: API Response Analysis (Recommended)

The easiest way is to check the API response fields:

```javascript
function detectJitoFromResponse(response) {
    if (!response.success) return { type: 'failed', reason: 'Transaction failed' };
    
    const data = response.data;
    
    // ✅ CONFIRMED JITO EXECUTION
    if (data.bundleId) {
        return {
            type: 'jito',
            bundleId: data.bundleId,
            jitoUrl: data.jitoUrl,
            tipAmount: data.tipAmount
        };
    }
    
    // ⚠️ MEV ATTEMPTED (fell back to regular execution)
    if (data.mevProtected && data.tipAmount > 0) {
        return {
            type: 'mev_attempted',
            tipAmount: data.tipAmount,
            executionMethod: data.executionMethod,
            reason: 'Jito attempted but fell back to regular execution'
        };
    }
    
    // ❌ REGULAR EXECUTION
    return {
        type: 'regular',
        reason: 'No MEV protection requested or applied'
    };
}
```

### Method 2: Response Field Indicators

Check these specific fields in the API response:

#### ✅ **Confirmed Jito Execution**
```json
{
  "success": true,
  "data": {
    "bundleId": "abc123...",           // ← Jito bundle ID (primary indicator)
    "jitoUrl": "https://explorer.jito.wtf/bundle/abc123...",
    "executionMethod": "jito",         // ← Execution method
    "mevProtected": true,
    "tipAmount": 750000,
    "tipAmountSol": 0.00075
  }
}
```

#### ⚠️ **MEV Attempted (Fallback)**
```json
{
  "success": true,
  "data": {
    "signature": "xyz789...",          // ← Regular transaction signature
    "executionMethod": "privy",        // ← Fell back to Privy
    "mevProtected": true,              // ← MEV logic was applied
    "tipAmount": 750000,               // ← Tip was calculated
    "tipAmountSol": 0.00075
  }
}
```

#### ❌ **Regular Execution**
```json
{
  "success": true,
  "data": {
    "signature": "xyz789...",
    "executionMethod": "privy",
    "mevProtected": false,             // ← No MEV protection
    "tipAmount": 0,                    // ← No tip
    "tipAmountSol": 0
  }
}
```

### Method 3: On-Chain Transaction Analysis

Analyze the actual transaction on Solana blockchain:

```javascript
async function detectJitoFromTransaction(signature) {
    const connection = new Connection(RPC_URL);
    const transaction = await connection.getTransaction(signature);
    
    if (!transaction) return { jitoEnabled: false };
    
    // Check for transfers to Jito tip accounts
    const JITO_TIP_ACCOUNTS = [
        '96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5',
        'HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe',
        'Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY',
        // ... more tip accounts
    ];
    
    const accountKeys = transaction.transaction.message.accountKeys;
    const instructions = transaction.transaction.message.instructions;
    
    for (const instruction of instructions) {
        if (instruction.programIdIndex === 0) { // System program
            const toAccount = accountKeys[instruction.accounts[1]];
            if (JITO_TIP_ACCOUNTS.includes(toAccount.toString())) {
                return { 
                    jitoEnabled: true, 
                    tipRecipient: toAccount.toString() 
                };
            }
        }
    }
    
    return { jitoEnabled: false };
}
```

### Method 4: Request Parameters Check

Check if the request parameters would trigger MEV protection:

```javascript
function wouldTriggerMEV(requestParams) {
    const { priorityLevel, mevProtection, amount } = requestParams;
    
    // Explicit MEV protection
    if (mevProtection === true) return true;
    
    // High priority levels trigger MEV
    if (priorityLevel === 'high' || priorityLevel === 'veryHigh') return true;
    
    // Large trades might auto-trigger MEV
    if (amount > 1.0) return true; // > 1 SOL
    
    return false;
}
```

## 📊 Detection Examples

### Example 1: Successful Jito Execution
```javascript
const response = {
    "success": true,
    "data": {
        "bundleId": "2igJYENjB9iSqQE1puXigjf7cbWYpNGrwFS2DfZXnYbJ",
        "jitoUrl": "https://explorer.jito.wtf/bundle/2igJYENjB9iSqQE1puXigjf7cbWYpNGrwFS2DfZXnYbJ",
        "executionMethod": "jito",
        "mevProtected": true,
        "tipAmount": 750000,
        "tipAmountSol": 0.00075
    }
};

const detection = detectJitoFromResponse(response);
// Result: { type: 'jito', bundleId: '2igJ...', tipAmount: 750000 }
```

### Example 2: MEV Attempted (Fallback)
```javascript
const response = {
    "success": true,
    "data": {
        "signature": "xyz789...",
        "executionMethod": "privy",
        "mevProtected": true,
        "tipAmount": 750000,
        "tipAmountSol": 0.00075
    }
};

const detection = detectJitoFromResponse(response);
// Result: { type: 'mev_attempted', tipAmount: 750000, reason: 'Jito attempted but fell back...' }
```

### Example 3: Regular Execution
```javascript
const response = {
    "success": true,
    "data": {
        "signature": "abc123...",
        "executionMethod": "privy",
        "mevProtected": false,
        "tipAmount": 0
    }
};

const detection = detectJitoFromResponse(response);
// Result: { type: 'regular', reason: 'No MEV protection requested or applied' }
```

## 🎯 Quick Reference

### Primary Indicators (100% Reliable)
- ✅ `bundleId` field present → **Jito execution confirmed**
- ✅ `executionMethod: "jito"` → **Jito execution confirmed**
- ✅ `jitoUrl` field present → **Jito execution confirmed**

### Secondary Indicators (MEV Attempted)
- ⚠️ `mevProtected: true` + `tipAmount > 0` → **MEV attempted**
- ⚠️ `executionMethod: "privy"` + `mevProtected: true` → **Fallback execution**

### Request Triggers (Would Enable MEV)
- 🎯 `priorityLevel: "high"` or `"veryHigh"` → **Auto-enables MEV**
- 🎯 `mevProtection: true` → **Explicitly enables MEV**
- 🎯 Large trade amounts → **May auto-enable MEV**

## 🔧 Implementation Examples

### Frontend Detection
```javascript
// In your frontend after API call
function handleSwapResponse(response) {
    const detection = detectJitoFromResponse(response);
    
    switch (detection.type) {
        case 'jito':
            showSuccess(`🎉 Jito MEV Protection Active! Bundle: ${detection.bundleId}`);
            break;
        case 'mev_attempted':
            showInfo(`⚠️ MEV Protection Applied (Fallback execution)`);
            break;
        case 'regular':
            showInfo(`ℹ️ Regular execution (no MEV protection)`);
            break;
    }
}
```

### Backend Logging
```javascript
// In your backend after transaction execution
function logTransactionType(response) {
    const detection = detectJitoFromResponse(response);
    
    console.log(`Transaction Type: ${detection.type}`);
    if (detection.bundleId) {
        console.log(`Jito Bundle: ${detection.bundleId}`);
    }
    if (detection.tipAmount) {
        console.log(`MEV Tip: ${detection.tipAmount} lamports`);
    }
}
```

## 📈 Monitoring & Analytics

### Track MEV Usage
```javascript
function trackMEVUsage(response) {
    const detection = detectJitoFromResponse(response);
    
    // Analytics tracking
    analytics.track('transaction_executed', {
        type: detection.type,
        mev_protected: detection.type !== 'regular',
        jito_successful: detection.type === 'jito',
        tip_amount: detection.tipAmount || 0
    });
}
```

### Success Rate Monitoring
```javascript
function calculateJitoSuccessRate(transactions) {
    const jitoAttempts = transactions.filter(tx => 
        tx.mevProtected && tx.tipAmount > 0
    );
    
    const jitoSuccesses = transactions.filter(tx => 
        tx.bundleId || tx.executionMethod === 'jito'
    );
    
    return {
        attemptedCount: jitoAttempts.length,
        successfulCount: jitoSuccesses.length,
        successRate: jitoSuccesses.length / jitoAttempts.length * 100
    };
}
```

## 🎉 Summary

**Easiest Detection Method:**
```javascript
const isJito = response.data.bundleId ? 'jito' : 
               response.data.mevProtected ? 'mev_attempted' : 'regular';
```

**Most Reliable Indicators:**
1. `bundleId` field → Confirmed Jito execution
2. `mevProtected: true` + `tipAmount > 0` → MEV attempted
3. `executionMethod: "jito"` → Jito execution method used

Use these methods to track MEV protection usage, monitor Jito success rates, and provide appropriate user feedback!
