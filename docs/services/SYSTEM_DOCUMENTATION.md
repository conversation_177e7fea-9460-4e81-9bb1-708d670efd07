# TP/SL and Limit Order System - Complete Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture Design](#architecture-design)
3. [Database Schema](#database-schema)
4. [API Endpoints](#api-endpoints)
5. [Order Flow Diagrams](#order-flow-diagrams)
6. [Price Monitoring System](#price-monitoring-system)
7. [Order Execution Engine](#order-execution-engine)
8. [Security & Validation](#security--validation)
9. [Performance Optimization](#performance-optimization)
10. [Error Handling](#error-handling)
11. [Deployment Guide](#deployment-guide)
12. [Troubleshooting](#troubleshooting)

---

## System Overview

### Purpose
The TP/SL (Take Profit/Stop Loss) and Limit Order system provides automated trading capabilities for Solana tokens, enabling users to:
- Set limit buy/sell orders that execute when target prices are reached
- Create take profit orders to automatically sell at profit targets
- Set stop loss orders to limit losses
- Monitor real-time price feeds and execute orders with minimal latency

### Key Features
- **Real-time execution** (50-200ms latency via WebSocket)
- **Multi-table database design** for data integrity
- **Atomic transactions** for order safety
- **Hybrid monitoring** (WebSocket + polling backup)
- **MEV protection** through fast execution
- **Comprehensive validation** and security measures

---

## Architecture Design

### High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │   API Gateway   │    │  Limit Orders   │
│                 │◄──►│                 │◄──►│    Service      │
│ Order Creation  │    │ Rate Limiting   │    │  (Port 5002)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Mobula WebSocket│    │  Redis Cache    │    │   Supabase DB   │
│                 │◄──►│                 │◄──►│                 │
│ Real-time Prices│    │ Price + Orders  │    │ Order Storage   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Solana Service │    │   BullMQ Queue  │    │ Price Monitor   │
│                 │◄──►│                 │◄──►│                 │
│ Token Swaps     │    │ Order Execution │    │ Order Checking  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Service Communication
- **Frontend → API**: HTTP/HTTPS requests for order management
- **API → Database**: Supabase client for data persistence
- **Price Monitor → WebSocket**: Real-time price feeds from Mobula
- **Order Execution → Solana**: HTTP requests for token swaps
- **Services → Redis**: Caching and job queue management

---

## Database Schema

### Multi-Table Design Overview
```sql
-- Main TP/SL Orders Table
CREATE TABLE tpsl_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL,
    wallet_id TEXT NOT NULL,
    wallet_address TEXT NOT NULL,
    token_address TEXT NOT NULL,
    token_name TEXT NOT NULL,
    token_symbol TEXT NOT NULL,
    token_image TEXT,
    pool_address TEXT NOT NULL,
    exchange_name TEXT DEFAULT 'pumpfun',
    current_price DECIMAL NOT NULL,
    amount DECIMAL NOT NULL,
    total_token DECIMAL,
    txn TEXT,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'executed', 'cancelled')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Take Profit Sub-Orders
CREATE TABLE tp_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tpsl_order_id UUID REFERENCES tpsl_orders(id) ON DELETE CASCADE,
    target_price DECIMAL NOT NULL,
    percentage DECIMAL NOT NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'executed', 'cancelled')),
    executed_at TIMESTAMPTZ,
    execution_txn TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Stop Loss Sub-Orders
CREATE TABLE sl_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tpsl_order_id UUID REFERENCES tpsl_orders(id) ON DELETE CASCADE,
    target_price DECIMAL NOT NULL,
    percentage DECIMAL NOT NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'executed', 'cancelled')),
    executed_at TIMESTAMPTZ,
    execution_txn TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Limit Orders Table
CREATE TABLE limit_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL,
    wallet_id TEXT NOT NULL,
    wallet_address TEXT NOT NULL,
    token_address TEXT NOT NULL,
    token_name TEXT NOT NULL,
    token_symbol TEXT NOT NULL,
    pool_address TEXT NOT NULL,
    exchange_name TEXT DEFAULT 'pumpfun',
    order_type TEXT NOT NULL CHECK (order_type IN ('buy', 'sell')),
    direction TEXT NOT NULL CHECK (direction IN ('buy', 'sell')),
    target_price DECIMAL NOT NULL,
    amount DECIMAL NOT NULL,
    slippage DECIMAL DEFAULT 0.02,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'executed', 'cancelled', 'expired')),
    expires_at TIMESTAMPTZ,
    current_price DECIMAL,
    triggered_at TIMESTAMPTZ,
    execution_txn TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Database Indexes for Performance
```sql
-- Price monitoring performance indexes
CREATE INDEX idx_tpsl_orders_status_token ON tpsl_orders(status, token_address);
CREATE INDEX idx_tp_orders_status_tpsl ON tp_orders(status, tpsl_order_id);
CREATE INDEX idx_sl_orders_status_tpsl ON sl_orders(status, tpsl_order_id);
CREATE INDEX idx_limit_orders_status_token ON limit_orders(status, token_address);
CREATE INDEX idx_limit_orders_user_status ON limit_orders(user_id, status);

-- User query optimization
CREATE INDEX idx_tpsl_orders_user_status ON tpsl_orders(user_id, status);
CREATE INDEX idx_orders_created_at ON tpsl_orders(created_at DESC);
CREATE INDEX idx_limit_orders_created_at ON limit_orders(created_at DESC);
```

### Atomic Transaction Functions
```sql
-- Create TP/SL Order Atomically
CREATE OR REPLACE FUNCTION create_tpsl_order_atomic(
    p_user_id TEXT,
    p_wallet_id TEXT,
    p_wallet_address TEXT,
    p_token_address TEXT,
    p_token_name TEXT,
    p_token_symbol TEXT,
    p_token_image TEXT,
    p_pool_address TEXT,
    p_exchange_name TEXT,
    p_current_price DECIMAL,
    p_amount DECIMAL,
    p_total_token DECIMAL,
    p_txn TEXT,
    p_tp_percentage DECIMAL,
    p_sl_percentage DECIMAL
) RETURNS JSON AS $$
DECLARE
    v_order_id UUID;
    v_tp_id UUID;
    v_sl_id UUID;
    v_tp_target_price DECIMAL;
    v_sl_target_price DECIMAL;
BEGIN
    -- Calculate target prices
    IF p_tp_percentage IS NOT NULL THEN
        v_tp_target_price := p_current_price * (1 + p_tp_percentage / 100);
    END IF;
    
    IF p_sl_percentage IS NOT NULL THEN
        v_sl_target_price := p_current_price * (1 - p_sl_percentage / 100);
    END IF;

    -- Create main order
    INSERT INTO tpsl_orders (
        user_id, wallet_id, wallet_address, token_address, token_name,
        token_symbol, token_image, pool_address, exchange_name,
        current_price, amount, total_token, txn
    ) VALUES (
        p_user_id, p_wallet_id, p_wallet_address, p_token_address, p_token_name,
        p_token_symbol, p_token_image, p_pool_address, p_exchange_name,
        p_current_price, p_amount, p_total_token, p_txn
    ) RETURNING id INTO v_order_id;

    -- Create TP order if specified
    IF p_tp_percentage IS NOT NULL THEN
        INSERT INTO tp_orders (tpsl_order_id, target_price, percentage)
        VALUES (v_order_id, v_tp_target_price, p_tp_percentage)
        RETURNING id INTO v_tp_id;
    END IF;

    -- Create SL order if specified
    IF p_sl_percentage IS NOT NULL THEN
        INSERT INTO sl_orders (tpsl_order_id, target_price, percentage)
        VALUES (v_order_id, v_sl_target_price, p_sl_percentage)
        RETURNING id INTO v_sl_id;
    END IF;

    RETURN json_build_object(
        'success', true,
        'order_id', v_order_id,
        'tp_id', v_tp_id,
        'sl_id', v_sl_id
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM,
            'error_code', 'TRANSACTION_FAILED'
        );
END;
$$ LANGUAGE plpgsql;
```

---

## API Endpoints

### TP/SL Order Endpoints

#### Create Enhanced TP/SL Order
```http
POST /api/enhanced-tpsl/orders
Content-Type: application/json

{
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "wallet_id": "cbq2lb54zo7rtzv14i5sp75j",
  "wallet_address": "********************************************",
  "current_price": 0.000004347011262905622,
  "amount": 0.0001,
  "token_address": "6LBkLu4HgNhhPUpoPWHD1Uum2UazW57EUmrQmXiLpump",
  "pool_address": "FHGt6ecwhjKaKm55x2SMaaeSGi5uAZNQw6mZ2UfxXpp2",
  "token_name": "Old Websites Religion",
  "token_symbol": "Zoroastism",
  "exchange_name": "PumpFun",
  "tp_percentage": 10,
  "sl_percentage": 5
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "order_id": "uuid-here",
    "tp_id": "uuid-here",
    "sl_id": "uuid-here",
    "main_order": { /* order details */ },
    "tp_orders": [ /* tp order details */ ],
    "sl_orders": [ /* sl order details */ ]
  }
}
```

#### Get User Orders
```http
GET /api/enhanced-tpsl/orders?user_id=did:privy:user_id&status=active&limit=50&offset=0
```

#### Cancel Order
```http
POST /api/enhanced-tpsl/orders/{orderId}/cancel
Content-Type: application/json

{
  "user_id": "did:privy:user_id"
}
```

#### Remove Take Profit
```http
POST /api/enhanced-tpsl/orders/{orderId}/remove-tp
Content-Type: application/json

{
  "user_id": "did:privy:user_id"
}
```

#### Remove Stop Loss
```http
POST /api/enhanced-tpsl/orders/{orderId}/remove-sl
Content-Type: application/json

{
  "user_id": "did:privy:user_id"
}
```

### Simple TP/SL Order Endpoints

#### Create Simple TP/SL Order (Legacy)
```http
POST /api/simple-tpsl/orders
Content-Type: application/json

{
  "user_id": "did:privy:user_id",
  "wallet_id": "wallet_id",
  "wallet_address": "wallet_address",
  "current_price": 0.00001,
  "amount": 0.001,
  "token_address": "token_address",
  "pool_address": "pool_address",
  "token_name": "Token Name",
  "token_symbol": "SYMBOL",
  "tp_enabled": true,
  "tp_target_price": 0.000011,
  "tp_percentage": 10,
  "sl_enabled": true,
  "sl_target_price": 0.000009,
  "sl_percentage": 10
}
```

### Limit Order Endpoints

#### Create Limit Order
```http
POST /api/limit-orders/orders
Content-Type: application/json

{
  "user_id": "did:privy:user_id",
  "wallet_id": "wallet_id",
  "wallet_address": "wallet_address",
  "token_address": "token_address",
  "token_name": "Token Name",
  "token_symbol": "SYMBOL",
  "pool_address": "pool_address",
  "order_type": "buy",
  "direction": "buy",
  "target_price": 0.00001,
  "amount": 0.001,
  "slippage": 0.02,
  "expires_at": "2024-12-31T23:59:59Z"
}
```

### Rate Limiting
```javascript
const RATE_LIMITS = {
  CREATE_ORDER: { windowMs: 60000, max: 10 }, // 10 orders per minute
  GET_ORDERS: { windowMs: 60000, max: 100 },  // 100 requests per minute
  CANCEL_ORDER: { windowMs: 60000, max: 20 }  // 20 cancellations per minute
};
```

---

## Order Flow Diagrams

### TP/SL Order Creation Flow
```mermaid
sequenceDiagram
    participant U as User
    participant API as API Gateway
    participant DB as Supabase DB
    participant SOL as Solana Service
    participant PM as Price Monitor
    participant WS as Mobula WebSocket

    U->>API: POST /api/simple-tpsl/orders
    API->>API: Validate request data
    API->>SOL: Execute token purchase
    SOL-->>API: Purchase successful + txn hash
    API->>DB: Create main order record
    API->>DB: Create TP sub-order (if enabled)
    API->>DB: Create SL sub-order (if enabled)
    DB-->>API: Orders created successfully
    API->>PM: Add orders to monitoring
    PM->>WS: Subscribe to token price feeds
    API-->>U: Order creation response
    
    Note over PM: Real-time monitoring starts
    WS->>PM: Price update received
    PM->>PM: Check order trigger conditions
    PM->>SOL: Execute sell order (if triggered)
```

### Limit Order Creation Flow
```mermaid
sequenceDiagram
    participant U as User
    participant API as API Gateway
    participant DB as Supabase DB
    participant PM as Price Monitor
    participant WS as Mobula WebSocket
    participant SOL as Solana Service

    U->>API: POST /api/limit-orders/orders
    API->>API: Validate request data
    API->>DB: Store limit order
    DB-->>API: Order stored successfully
    API->>PM: Add order to monitoring
    PM->>WS: Subscribe to token price feeds
    API-->>U: Order creation response
    
    Note over PM: Wait for price trigger
    WS->>PM: Price update received
    PM->>PM: Check if target price reached
    PM->>SOL: Execute buy/sell order (if triggered)
    SOL-->>PM: Execution result
    PM->>DB: Update order status
```

### Real-Time Price Monitoring Flow
```mermaid
sequenceDiagram
    participant WS as Mobula WebSocket
    participant R as Redis Cache
    participant PM as Price Monitor
    participant Q as BullMQ Queue
    participant SOL as Solana Service
    participant DB as Database

    WS->>R: Store price update (60s TTL)
    WS->>PM: Trigger real-time callback
    PM->>PM: Check all orders for token
    
    alt Order trigger condition met
        PM->>DB: Update order status to 'triggered'
        PM->>Q: Add order to execution queue
        Q->>SOL: Execute token swap
        SOL-->>Q: Execution result
        Q->>DB: Update order with execution data
        PM->>PM: Remove order from monitoring
    end
    
    Note over PM: Backup polling every 5 seconds
    PM->>R: Get cached price (backup check)
    PM->>PM: Check orders (if not recently checked)
```

---

## Price Monitoring System

### Hybrid Architecture
The price monitoring system uses a hybrid approach combining real-time WebSocket updates with polling backup:

#### Primary: Real-Time WebSocket Monitoring
```typescript
// Real-time price update handler
mobulaWebSocketService.onPriceUpdate(tokenAddress, async (newPrice: number) => {
  await this.checkOrdersForToken(tokenAddress, newPrice);
});
```

**Characteristics:**
- **Latency**: 50-200ms
- **Trigger**: WebSocket price updates from Mobula
- **Coverage**: All monitored tokens
- **Reliability**: High (with reconnection logic)

#### Secondary: Polling Backup System
```typescript
// Backup polling every 5 seconds
setInterval(async () => {
  await this.checkAllOrders(); // Only checks if not recently checked via WebSocket
}, 5000);
```

**Characteristics:**
- **Latency**: 0-5 seconds
- **Trigger**: Timer-based (every 5 seconds)
- **Purpose**: Safety net for WebSocket failures
- **Smart logic**: Skips tokens recently checked via WebSocket

### Price Data Sources

#### Mobula WebSocket Configuration
```typescript
const MOBULA_WS_URL = 'wss://production-feed.mobula.io';
const MOBULA_API_KEY = process.env.MOBULA_API_KEY;

// Subscription message format
const subscribeMessage = {
  type: 'feed',
  authorization: MOBULA_API_KEY,
  kind: 'address',
  tokens: tokens.map(address => ({
    blockchain: 'solana',
    address
  }))
};
```

#### Redis Price Caching
```typescript
// Price storage with 60-second TTL
await redis.setex(
  `price:${tokenAddress}`,
  60,
  JSON.stringify({
    price: priceData.price,
    timestamp: priceData.timestamp,
    volume24h: priceData.volume24h,
    priceUSD: priceData.extra.priceUSD
  })
);
```

### Order Trigger Logic

#### Take Profit Trigger
```typescript
if (order_type === 'take_profit') {
  // TP orders are always sells (taking profit on purchased tokens)
  // Trigger when current price >= target price (price went up)
  return currentPrice >= trigger_price;
}
```

#### Stop Loss Trigger
```typescript
if (order_type === 'stop_loss') {
  // SL orders are always sells (limiting losses on purchased tokens)
  // Trigger when current price <= target price (price went down)
  return currentPrice <= trigger_price;
}
```

#### Limit Order Trigger
```typescript
if (order_type === 'limit') {
  if (direction === 'buy') {
    // Buy when price drops to or below target
    return currentPrice <= trigger_price;
  } else {
    // Sell when price rises to or above target
    return currentPrice >= trigger_price;
  }
}
```

---

## Order Execution Engine

### Execution Queue System
The system uses BullMQ for reliable order execution:

```typescript
// Add order to execution queue
await orderExecutionQueue.add('execute-order', {
  orderId: order.id,
  orderType: order.order_type,
  tokenAddress: order.token_address,
  poolAddress: order.pool_address,
  direction: order.direction,
  amount: order.amount,
  triggerPrice: order.trigger_price,
  currentPrice: currentPrice,
  walletAddress: order.wallet_address,
  walletId: order.wallet_id,
  slippage: order.slippage || 0.02,
  userId: order.user_id,
  exchange_name: order.dex_type
}, {
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 2000
  },
  removeOnComplete: false,
  removeOnFail: false
});
```

### Solana Service Integration

#### Swap Request Format
```typescript
interface SolanaSwapRequest {
  tokenAddress: string;
  poolAddress: string;
  dexType: 'pumpfun' | 'pumpswap' | 'jupiter';
  amount: number;
  direction: 'buy' | 'sell';
  slippage: number;
  walletAddress: string;
  walletId?: string;
  priorityFee?: number;
  bribeAmount?: number;
  mevProtection?: boolean;
  priorityLevel?: 'low' | 'medium' | 'high' | 'veryHigh';
}
```

#### Default Execution Parameters
```typescript
const swapRequest = {
  // ... order details
  slippage: 0.15,           // 15% default slippage
  mevProtection: false,     // MEV protection disabled
  bribeAmount: 0.0001,      // 0.0001 SOL Jito tip
  priorityLevel: "low",     // Low priority level
  priorityFee: 0.0002      // 0.0002 SOL priority fee
};
```

### Execution Flow States

#### Order Status Transitions
```
pending/active → triggered → executing → executed/failed
                     ↓
                  cancelled (user action)
```

#### Database Updates During Execution
```typescript
// 1. Mark as triggered
await supabase
  .from(tableName)
  .update({
    current_price: currentPrice,
    triggered_at: new Date().toISOString()
  })
  .eq('id', order.id);

// 2. Execute via Solana service
const result = await solanaService.executeSwap(swapData);

// 3. Update with execution result
await supabase
  .from(tableName)
  .update({
    status: result.success ? 'executed' : 'failed',
    execution_txn: result.signature,
    executed_at: new Date().toISOString()
  })
  .eq('id', order.id);
```

---

## Security & Validation

### Input Validation

#### TP/SL Order Validation
```typescript
const VALIDATION_LIMITS = {
  MIN_AMOUNT: 0.001,        // Minimum 0.001 SOL
  MAX_AMOUNT: 1000,         // Maximum 1000 SOL
  MIN_PERCENTAGE: 0.1,      // Minimum 0.1%
  MAX_PERCENTAGE: 1000,     // Maximum 1000%
  MIN_PRICE: 0.000000001,   // Minimum price
  MAX_ORDERS_PER_USER: 100  // Maximum orders per user
};

// User ID validation
if (!data.user_id.startsWith('did:privy:')) {
  throw new TPSLError('INVALID_USER_ID_FORMAT', 'User ID must be a valid Privy DID');
}

// Wallet address validation
if (!/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(data.wallet_address)) {
  throw new TPSLError('INVALID_WALLET_ADDRESS', 'Invalid Solana wallet address format');
}
```

#### Rate Limiting & Security Headers
```typescript
// Security headers middleware
const validateRequest = (req: Request, res: Response, next: any) => {
  // Basic security headers
  res.header('X-Content-Type-Options', 'nosniff');
  res.header('X-Frame-Options', 'DENY');
  res.header('X-XSS-Protection', '1; mode=block');
  
  // Validate content type for POST requests
  if (req.method === 'POST' && !req.get('Content-Type')?.includes('application/json')) {
    return res.status(400).json({
      success: false,
      error: 'Content-Type must be application/json',
      error_code: 'INVALID_CONTENT_TYPE'
    });
  }
  
  next();
};
```

### Row Level Security (RLS)
```sql
-- Enable RLS on all tables
ALTER TABLE tpsl_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE tp_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE sl_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE limit_orders ENABLE ROW LEVEL SECURITY;

-- Users can only access their own orders
CREATE POLICY user_orders_policy ON tpsl_orders
FOR ALL USING (user_id = current_setting('app.user_id'));

CREATE POLICY user_tp_orders_policy ON tp_orders
FOR ALL USING (
  tpsl_order_id IN (
    SELECT id FROM tpsl_orders WHERE user_id = current_setting('app.user_id')
  )
);
```

### Error Handling

#### Custom Error Classes
```typescript
export class TPSLError extends Error {
  constructor(
    public code: string,
    message: string,
    public context?: any
  ) {
    super(message);
    this.name = 'TPSLError';
  }
}
```

#### Comprehensive Error Responses
```typescript
// API Error Response Format
{
  "success": false,
  "error": "Human-readable error message",
  "error_code": "MACHINE_READABLE_CODE",
  "context": { /* Additional error context */ }
}
```

---

## Performance Optimization

### Caching Strategy

#### Redis Cache Layers
```typescript
// Price caching (60-second TTL)
await redis.setex(`price:${tokenAddress}`, 60, priceData);

// Order caching (5-minute TTL)
await redis.setex(`orders:token:${tokenAddress}`, 300, ordersData);

// User notification caching
await redis.lpush(`notifications:${userId}`, notificationData);
await redis.ltrim(`notifications:${userId}`, 0, 99); // Keep last 100
```

#### Database Query Optimization
```sql
-- Composite indexes for common queries
CREATE INDEX idx_orders_monitoring ON tpsl_orders(status, token_address) 
WHERE status = 'active';

CREATE INDEX idx_orders_user_lookup ON tpsl_orders(user_id, status, created_at DESC);

-- Partial indexes for active orders only
CREATE INDEX idx_tp_orders_active ON tp_orders(tpsl_order_id, target_price) 
WHERE status = 'active';
```

### Connection Management

#### Redis Connection Pool
```typescript
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true
});
```

#### WebSocket Reconnection Strategy
```typescript
const RECONNECT_DELAY = 5000;
const MAX_RECONNECT_ATTEMPTS = 10;

private attemptReconnect(): void {
  if (this.reconnectAttempts < this.MAX_RECONNECT_ATTEMPTS) {
    this.reconnectAttempts++;
    setTimeout(() => {
      this.connect();
    }, this.RECONNECT_DELAY);
  }
}
```

### Resource Management

#### Worker Concurrency
```typescript
// BullMQ worker configuration
const worker = new Worker('order-execution', async (job) => {
  return await executeOrder(job.data);
}, {
  connection: redis,
  concurrency: 5,  // Process 5 orders simultaneously
  removeOnComplete: 100,
  removeOnFail: 50
});
```

#### Memory Management
```typescript
// Order group cleanup when no orders remain
if (orderGroup.orders.length === 0) {
  this.orderGroups.delete(tokenAddress);
  mobulaWebSocketService.unsubscribeFromTokens([tokenAddress]);
  await redis.del(`orders:token:${tokenAddress}`);
}
```

---

## Deployment Guide

### Environment Configuration

#### Required Environment Variables
```bash
# Database Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-service-role-key

# External Services
SOLANA_SERVICE_URL=http://localhost:6001
MOBULA_API_KEY=your-mobula-api-key

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password

# Service Configuration
PORT=5002
NODE_ENV=production
```

#### Docker Configuration
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY dist/ ./dist/
COPY .env ./

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5002/health || exit 1

EXPOSE 5002

CMD ["node", "dist/index.js"]
```

#### Docker Compose Setup
```yaml
version: '3.8'

services:
  limit-orders:
    build: .
    ports:
      - "5002:5002"
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - SOLANA_SERVICE_URL=http://solana-service:6001
    depends_on:
      - redis
      - solana-service
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  solana-service:
    image: your-solana-service:latest
    ports:
      - "6001:6001"
    restart: unless-stopped

volumes:
  redis_data:
```

### Database Setup

#### Migration Scripts
```sql
-- 1. Create tables
\i migrations/001_create_tpsl_tables.sql

-- 2. Create indexes
\i migrations/002_create_indexes.sql

-- 3. Create functions
\i migrations/003_create_functions.sql

-- 4. Enable RLS
\i migrations/004_enable_rls.sql
```

#### Service Account Setup
```sql
-- Create service user for the application
CREATE USER limit_orders_service WITH PASSWORD 'secure_password';

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO limit_orders_service;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO limit_orders_service;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO limit_orders_service;
```

### Monitoring Setup

#### Health Check Endpoints
```typescript
// Service health check
app.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    service: 'limit-orders-service',
    timestamp: new Date().toISOString(),
    priceMonitoring: enhancedPriceMonitorService.getMonitoringStats(),
    redis: redis.status === 'ready' ? 'connected' : 'disconnected',
    database: 'connected' // Add actual DB health check
  };
  
  res.json(health);
});

// Enhanced health check
app.get('/api/enhanced-tpsl/health', async (req, res) => {
  const systemHealth = await enhancedTpslService.getSystemHealth();
  res.status(systemHealth.status === 'healthy' ? 200 : 503).json({
    success: true,
    data: systemHealth
  });
});
```

#### Logging Configuration
```typescript
// Winston logger setup
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

---

## Troubleshooting

### Common Issues

#### WebSocket Connection Problems
```bash
# Check WebSocket connectivity
curl -i -N -H "Connection: Upgrade" \
  -H "Upgrade: websocket" \
  -H "Sec-WebSocket-Key: test" \
  -H "Sec-WebSocket-Version: 13" \
  wss://production-feed.mobula.io

# Check Mobula API key
echo $MOBULA_API_KEY

# Monitor WebSocket logs
docker logs -f limit-orders | grep -i websocket
```

#### Redis Connection Issues
```bash
# Test Redis connectivity
redis-cli -h localhost -p 6379 ping

# Check Redis memory usage
redis-cli info memory

# Monitor Redis logs
redis-cli monitor
```

#### Database Connection Problems
```bash
# Test Supabase connection
curl -H "apikey: $SUPABASE_KEY" \
  "$SUPABASE_URL/rest/v1/tpsl_orders?limit=1"

# Check RLS policies
psql -c "SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
         FROM pg_policies WHERE tablename IN ('tpsl_orders', 'tp_orders', 'sl_orders');"
```

#### Order Execution Failures
```bash
# Check Solana service availability
curl http://localhost:6001/health

# Monitor order execution queue
redis-cli keys "bull:order-execution:*"

# Check failed jobs
redis-cli lrange "bull:order-execution:failed" 0 -1
```

### Performance Debugging

#### Monitor Order Processing Time
```typescript
// Add performance monitoring
const startTime = Date.now();
await processOrder(orderData);
const processingTime = Date.now() - startTime;

logger.info('Order processing performance', {
  orderId: orderData.id,
  processingTime: `${processingTime}ms`,
  orderType: orderData.order_type
});
```

#### Price Monitoring Statistics
```typescript
// Get monitoring stats
const stats = enhancedPriceMonitorService.getMonitoringStats();
console.log({
  isMonitoring: stats.isMonitoring,
  totalOrders: stats.totalOrders,
  tokenCount: stats.tokenCount,
  ordersByType: stats.ordersByType,
  webSocketConnected: stats.webSocketConnected
});
```

#### Database Query Performance
```sql
-- Check slow queries
SELECT query, mean_exec_time, calls, total_exec_time
FROM pg_stat_statements
WHERE query LIKE '%tpsl_orders%'
ORDER BY mean_exec_time DESC
LIMIT 10;

-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
WHERE tablename IN ('tpsl_orders', 'tp_orders', 'sl_orders', 'limit_orders')
ORDER BY idx_scan DESC;
```

### Error Recovery Procedures

#### WebSocket Recovery
```typescript
// Manual WebSocket reconnection
await mobulaWebSocketService.disconnect();
await mobulaWebSocketService.connect();
await enhancedPriceMonitorService.refreshTokenOrders('all');
```

#### Queue Recovery
```typescript
// Clear stuck jobs
await orderExecutionQueue.obliterate({ force: true });

// Restart worker
await orderExecutionWorker.close();
await orderExecutionWorker.start();
```

#### Database Recovery
```sql
-- Check for stuck transactions
SELECT pid, state, query_start, query 
FROM pg_stat_activity 
WHERE state = 'active' AND query_start < NOW() - INTERVAL '5 minutes';

-- Kill stuck transactions (if necessary)
SELECT pg_terminate_backend(pid) FROM pg_stat_activity 
WHERE state = 'active' AND query_start < NOW() - INTERVAL '10 minutes';
```

---

## Support and Maintenance

### Regular Maintenance Tasks

#### Daily Tasks
- Monitor system health endpoints
- Check error logs for anomalies
- Verify WebSocket connection stability
- Monitor order execution success rate

#### Weekly Tasks
- Review database performance metrics
- Clean up old log files
- Check Redis memory usage
- Update price monitoring statistics

#### Monthly Tasks
- Review and optimize database indexes
- Analyze order execution patterns
- Update rate limiting configurations
- Security audit of API endpoints

### Contact Information
- **System Administrator**: [<EMAIL>]
- **Development Team**: [<EMAIL>]
- **Emergency Contact**: [<EMAIL>]

---

*This documentation is maintained by the development team and should be updated whenever system changes are made.*