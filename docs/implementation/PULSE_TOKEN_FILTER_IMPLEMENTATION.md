# Pulse Trade Token Filter Implementation

## Overview
Added token filtering capability to the user trades view in the bottom panel (Tables/Traders.tsx) to match the functionality already present in the side panel.

## Changes Made

### 1. Updated Imports
- Added `Filter` icon from lucide-react
- Added `useActiveToken` hook import from ActiveTokenContext

### 2. Added State and Hook Usage
- Added `filterByActiveToken` state to track filter status
- Added `useActiveToken` hook to get the current active token

### 3. Updated Data Fetching
- Modified `useUserTradeData` hook call to include token address when filter is active
- Token filtering only applies when:
  - View is 'you' (user trades)
  - Filter is toggled on
  - Active token exists

### 4. Added Filter Toggle UI
- Added filter button that appears only when:
  - In 'you' view
  - Active token exists
- But<PERSON> shows:
  - Blue background when filter is active
  - Gray background when inactive
  - Active token symbol when filtering
  - "All Tokens" when showing all trades

## Implementation Details

```typescript
// Token filter state
const [filterByActiveToken, setFilterByActiveToken] = useState(false);

// Get active token
const { activeToken } = useActiveToken();

// Pass token filter to hook
const userTradeData = useUserTradeData(
  view === 'you' ? solanaWalletAddress : null,
  view === 'you' && filterByActiveToken && activeToken ? activeToken.address : null
);

// Filter button UI
{view === 'you' && activeToken && (
  <button
    onClick={() => setFilterByActiveToken(!filterByActiveToken)}
    className={`flex items-center text-xs font-medium px-3 py-1 rounded-full transition-colors ${
      filterByActiveToken 
        ? 'bg-blue-600 text-white' 
        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
    }`}
    title={filterByActiveToken ? `Showing only ${activeToken.symbol} trades` : `Show only ${activeToken.symbol} trades`}
  >
    <Filter size={12} className="mr-1" />
    {filterByActiveToken ? activeToken.symbol : 'All Tokens'}
  </button>
)}
```

## Consistency with Side Panel

Both the side panel (Trades.tsx) and bottom panel (Traders.tsx) now have:
- Same filter button design and behavior
- Same filtering logic through useUserTradeData hook
- Same visual indicators (blue when active, gray when inactive)
- Same size and styling for consistency

## Testing Recommendations

1. **Basic Functionality**
   - Navigate to Pulse Trade page
   - Connect wallet
   - Click on "YOU" tab in bottom panel
   - Verify filter button appears when a token is active
   - Toggle filter and verify trades are filtered

2. **Edge Cases**
   - Test with no active token (filter button should not appear)
   - Test with wallet not connected
   - Test switching between tokens
   - Test filter persistence when switching tabs

3. **Consistency Check**
   - Open side panel trades view
   - Enable filter in side panel
   - Check that bottom panel filter works independently
   - Verify both panels can filter to same token

## Benefits

- **Feature Parity**: Both trade views now have the same filtering capabilities
- **User Experience**: Users can filter trades by specific token in either view
- **Flexibility**: Filters work independently in each panel
- **Visual Consistency**: Same UI patterns across the application