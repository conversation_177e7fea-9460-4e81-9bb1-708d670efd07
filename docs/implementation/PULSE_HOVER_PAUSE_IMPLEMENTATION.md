# Pulse Page Hover-to-Pause Implementation

## Overview
This feature allows users to pause token list updates when hovering over buy buttons on the Pulse page, making it easier to click on buy buttons without the list updating and moving tokens around.

## Implementation Details

### 1. **Event-Based Pause System**
- When hovering over a buy button, a `pulsePauseCategory` event is emitted
- When leaving the buy button, a `pulseResumeCategory` event is emitted
- Events include the category name (new, bonding, or bonded)

### 2. **Modified Components**

#### CryptoCard Component (`/src/Pulse/Pulse/Pulse.tsx`)
- Added `handleBuyButtonEnter` and `handleBuyButtonLeave` functions
- These emit pause/resume events when hovering over buy buttons
- Events include the token's category to pause only that specific list

#### useWebSocketPulseData Hook (`/src/hooks/useWebSocketPulseData.ts`)
- Added global state for tracking paused categories and queued updates
- Added event listeners for pause/resume events
- Modified data update logic to queue updates for paused categories
- When resumed, queued updates are applied immediately

#### VirtualizedCategoryColumn Component (`/src/Pulse/components/VirtualizedCategoryColumn.tsx`)
- Added visual indicator showing "Updates Paused" when category is paused
- Changes from green "Live" indicator to amber "Updates Paused" indicator
- Shows "Hover paused" instead of "Updated now" in the status text

### 3. **Key Features**

#### Independent Category Pausing
- Each category (New, Bonding, Bonded) can be paused independently
- Only the category containing the hovered buy button is paused

#### Update Queueing
- Updates are not lost during pause - they're queued
- When resuming, the latest queued data is applied
- Prevents outdated data from being shown

#### Auto-Resume Protection
- Categories automatically resume after 10 seconds if still paused
- Prevents accidental permanent pausing if user forgets to move mouse

#### Visual Feedback
- Clear visual indicator when updates are paused
- Amber color scheme distinguishes paused state from live state
- Smooth transitions between states

### 4. **How It Works**

1. User hovers over a buy button in any token card
2. Buy button emits `pulsePauseCategory` event with category name
3. WebSocket handler catches event and starts queueing updates for that category
4. Visual indicator changes to show "Updates Paused"
5. User moves mouse away from buy button
6. Buy button emits `pulseResumeCategory` event
7. Queued updates are applied and normal updates resume
8. Visual indicator returns to "Live" state

### 5. **Benefits**

- **Improved UX**: Token lists stop moving when trying to click buy buttons
- **No Data Loss**: Updates are queued, not discarded
- **Category Isolation**: Other categories continue updating normally
- **Safety**: Auto-resume prevents permanent pausing
- **Clear Feedback**: Users can see when updates are paused

## Testing

To test the implementation:

1. Navigate to the Pulse page
2. Wait for tokens to load in any category
3. Hover over any buy button
4. Observe that:
   - The category header shows "Updates Paused" in amber
   - New tokens stop appearing in that category
   - Other categories continue updating normally
5. Move mouse away from buy button
6. Observe that updates resume and any queued tokens appear

## Future Enhancements

Possible improvements for the future:
- Add user preference to disable hover-pause feature
- Adjust auto-resume timeout based on user preference
- Add animation when queued updates are applied
- Show count of queued updates during pause