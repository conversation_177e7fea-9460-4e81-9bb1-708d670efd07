can you fix this issue
Request URL
] 📊 VolumeAggregationService initialized with memory limits:
[]       - Max trades per pool: 1000
[]       - Max total memory: 100MB
[]       - Cleanup interval: 120s
[] 📊 Started connection monitoring for resource optimization
[] 🔧 Multi-Pool WebSocket Server initialized
[] 📊 Configuration:
[]       - Max pools per service: 50
[]       - Pool cleanup interval: 300s
[]       - Pool idle timeout: 600s
[]       - Client disconnect grace period: 30s
[] 🚀 Trading Panel service running on port 5003
[] 📡 WebSocket server available at ws://localhost:5003
[] 🌐 HTTP API available at http://localhost:5003/api/trading-panel
[] 
[] 4:55:49 PM - Found 0 errors. Watching for file changes.
[] Platform private key not found in environment variables
[] Derived PumpFun event authority PDA: Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1
[] 2025-06-30 16:55:49 info: 🚀 Limit Orders Service running on port 5002 {
[]   "service": "limit-orders-service"
[] }
[] info: 🚀 Limit Orders Service running on port 5002 {"service":"limit-orders-service","timestamp":"2025-06-30 16:55:49"}
[] 2025-06-30 16:55:49 info: 📝 API Documentation: http://localhost:5002/api-docs {
[]   "service": "limit-orders-service"
[] }
[] info: 📝 API Documentation: http://localhost:5002/api-docs {"service":"limit-orders-service","timestamp":"2025-06-30 16:55:49"}
[] 2025-06-30 16:55:49 info: 🔍 Health Check: http://localhost:5002/health {
[]   "service": "limit-orders-service"
[] }
[] info: 🔍 Health Check: http://localhost:5002/health {"service":"limit-orders-service","timestamp":"2025-06-30 16:55:49"}
[] 2025-06-30 16:55:49 info: Starting enhanced price monitoring service... {
[]   "service": "limit-orders-service"
[] }
[] info: Starting enhanced price monitoring service... {"service":"limit-orders-service","timestamp":"2025-06-30 16:55:49"}
[] 2025-06-30 16:55:49 info: Redis client connected {
[]   "service": "limit-orders-service"
[] }
[] info: Redis client connected {"service":"limit-orders-service","timestamp":"2025-06-30 16:55:49"}
[] 2025-06-30 16:55:49 info: Redis client ready {
[]   "service": "limit-orders-service"
[] }
[] info: Redis client ready {"service":"limit-orders-service","timestamp":"2025-06-30 16:55:49"}
[] 2025-06-30 16:55:50 error: Mobula WebSocket error: getaddrinfo ENOTFOUND price-feed.mobula.io {
[]   "service": "limit-orders-service",
[]   "errno": -3008,
[]   "code": "ENOTFOUND",
[]   "syscall": "getaddrinfo",
[]   "hostname": "price-feed.mobula.io",
[]   "stack": "Error: getaddrinfo ENOTFOUND price-feed.mobula.io\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)"
[] }
[] error: Mobula WebSocket error: getaddrinfo ENOTFOUND price-feed.mobula.io {"code":"ENOTFOUND","errno":-3008,"hostname":"price-feed.mobula.io","service":"limit-orders-service","stack":"Error: getaddrinfo ENOTFOUND price-feed.mobula.io\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)","syscall":"getaddrinfo","timestamp":"2025-06-30 16:55:50"}
[] 2025-06-30 16:55:50 warn: Mobula WebSocket closed. Code: 1006, Reason:  {
[]   "service": "limit-orders-service"
[] }
[] warn: Mobula WebSocket closed. Code: 1006, Reason:  {"service":"limit-orders-service","timestamp":"2025-06-30 16:55:50"}
[] 2025-06-30 16:55:50 error: Failed to start enhanced price monitoring: getaddrinfo ENOTFOUND price-feed.mobula.io {
[]   "service": "limit-orders-service",
[]   "errno": -3008,
[]   "code": "ENOTFOUND",
[]   "syscall": "getaddrinfo",
[]   "hostname": "price-feed.mobula.io",
[]   "stack": "Error: getaddrinfo ENOTFOUND price-feed.mobula.io\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)"
[] }
[] error: Failed to start enhanced price monitoring: getaddrinfo ENOTFOUND price-feed.mobula.io {"code":"ENOTFOUND","errno":-3008,"hostname":"price-feed.mobula.io","service":"limit-orders-service","stack":"Error: getaddrinfo ENOTFOUND price-feed.mobula.io\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)","syscall":"getaddrinfo","timestamp":"2025-06-30 16:55:50"}
[] 2025-06-30 16:55:50 error: Failed to start services: getaddrinfo ENOTFOUND price-feed.mobula.io {
[]   "service": "limit-orders-service",
[]   "errno": -3008,
[]   "code": "ENOTFOUND",
[]   "syscall": "getaddrinfo",
[]   "hostname": "price-feed.mobula.io",
[]   "stack": "Error: getaddrinfo ENOTFOUND price-feed.mobula.io\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)"
[] }
[] error: Failed to start services: getaddrinfo ENOTFOUND price-feed.mobula.io {"code":"ENOTFOUND","errno":-3008,"hostname":"price-feed.mobula.io","service":"limit-orders-service","stack":"Error: getaddrinfo ENOTFOUND price-feed.mobula.io\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)","syscall":"getaddrinfo","timestamp":"2025-06-30 16:55:50"}