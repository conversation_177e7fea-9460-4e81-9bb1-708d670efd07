{"compilerOptions": {"target": "es2020", "module": "commonjs", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node", "transpileOnly": true}}