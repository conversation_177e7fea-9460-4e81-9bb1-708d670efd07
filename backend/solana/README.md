# Solana Liquidity Pool

A Solana-based liquidity pool implementation with Privy wallet integration for secure transaction signing.

## Features

- Privy wallet integration for secure transaction signing
- PumpFun program interaction
- Token swapping functionality
- Comprehensive error handling

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Solana CLI (for local development)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/solana-liquidity-pool.git
   cd solana-liquidity-pool
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn
   ```

3. Set up environment variables:
   Create a `.env` file in the root directory with the following variables:
   ```
   PRIVY_APP_ID=your_privy_app_id
   PRIVY_APP_SECRET=your_privy_app_secret
   PRIVY_SIGNING_KEY=your_privy_signing_key
   SOLANA_RPC_URL=your_solana_rpc_url
   ```

## Usage

### Development

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm test
```

## Project Structure

```
src/
  ├── services/           # Service layer
  │   ├── privy/          # Privy wallet integration
  │   └── pumpFun/        # PumpFun program integration
  ├── utils/              # Utility functions
  └── types/              # TypeScript type definitions
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Swap API Monitoring and Logging

The service now includes comprehensive error monitoring and logging for swap operations:

- All logs go to `logs/combined.log`
- Error logs go to `logs/error.log`
- Swap-specific error logs go to `logs/swap_error.log`

### Viewing Logs

```bash
# View all logs
cat logs/combined.log

# View error logs
cat logs/error.log

# View swap-specific error logs
cat logs/swap_error.log

# Monitor logs in real-time
tail -f logs/combined.log

# Monitor error logs in real-time
tail -f logs/error.log

# Monitor swap error logs in real-time
tail -f logs/swap_error.log
```

### Setting API Timeouts

To set a 10-second (or other time period) timeout for swap API endpoints, you can modify the Express route handlers in the appropriate controller files:

1. Open `src/controllers/pumpFun/pump.controller.ts`
2. Add a timeout middleware to the routes in `src/routes/pumpFun/pump.route.ts`:

```typescript
// src/routes/pumpFun/pump.route.ts
import { Router } from 'express';
import { getQuote, executeSwap } from '../../controllers/pumpFun/pump.controller';

// Timeout middleware
const timeout = (ms: number) => {
  return (req: any, res: any, next: any) => {
    res.setTimeout(ms, () => {
      res.status(408).json({ 
        success: false, 
        error: `Request timeout after ${ms}ms` 
      });
    });
    next();
  };
};

const router = Router();

// Apply timeout middleware (10 seconds)
router.post('/quote', timeout(10000), getQuote);
router.post('/execute', timeout(10000), executeSwap);

export default router;
```

### Testing Error Logging

You can test the error logging functionality using the provided test utility:

```bash
cd backend/solana
npx ts-node src/utils/test-logger.ts
```

This will generate a test error and log it to both the general error log and the swap-specific error log.
