import { Router } from 'express';
import { executeTPSLWebhook, executeLimitOrderWebhook } from '../../controllers/webhook/webhook.controller';
import { swapLogger } from '../../utils/logger';

// Webhook signature validation middleware
const validateWebhookSignature = (req: any, res: any, next: any) => {
  const signature = req.headers['x-webhook-signature'];
  const eventType = req.headers['x-webhook-event'];
  
  // Log webhook received
  swapLogger.info('Webhook received', {
    eventType,
    hasSignature: !!signature,
    ip: req.ip,
    timestamp: new Date().toISOString()
  });
  
  // For now, we'll proceed without signature validation
  // In production, implement HMAC signature validation here
  req.webhookEvent = eventType;
  next();
};

const router = Router();

/**
 * @route   POST /api/webhook/tpsl-triggered
 * @desc    Handle TP/SL order triggered webhook
 * @access  Internal webhook
 */
router.post('/tpsl-triggered', validateWebhookSignature, executeTPSLWebhook);

/**
 * @route   POST /api/webhook/limit-order-triggered
 * @desc    Handle limit order triggered webhook
 * @access  Internal webhook
 */
router.post('/limit-order-triggered', validateWebhookSignature, executeLimitOrderWebhook);

export default router;