import { Router } from 'express';
import { queueService } from '../../services/queue/swap-queue.service';
import { cryptoWorkerPool } from '../../services/workers/crypto-worker-pool';
import { swapLogger } from '../../utils/logger';

const router = Router();

/**
 * @route   GET /api/queue/stats
 * @desc    Get queue statistics
 * @access  Public
 */
router.get('/stats', async (req, res) => {
  try {
    const queueStats = await queueService.getQueueStats();
    const workerStats = cryptoWorkerPool.getPoolStats();
    
    res.json({
      success: true,
      data: {
        queues: queueStats,
        workers: workerStats,
        timestamp: Date.now()
      }
    });
  } catch (error) {
    swapLogger.error('Error getting queue stats', { error: error instanceof Error ? error.message : 'Unknown error' });
    res.status(500).json({
      success: false,
      error: 'Failed to get queue statistics'
    });
  }
});

/**
 * @route   GET /api/queue/job/:jobId
 * @desc    Get job status
 * @access  Public
 */
router.get('/job/:jobId', async (req: any, res: any) => {
  try {
    const { jobId } = req.params;
    const jobStatus = await queueService.getJobStatus(jobId);
    
    if (!jobStatus) {
      return res.status(404).json({
        success: false,
        error: 'Job not found'
      });
    }
    
    res.json({
      success: true,
      data: jobStatus
    });
  } catch (error) {
    swapLogger.error('Error getting job status', { 
      jobId: req.params.jobId,
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    res.status(500).json({
      success: false,
      error: 'Failed to get job status'
    });
  }
});

/**
 * @route   POST /api/queue/test
 * @desc    Test queue functionality
 * @access  Public
 */
router.post('/test', async (req, res) => {
  try {
    const { type = 'quote', priority = 0 } = req.body;
    
    // Test quote job
    if (type === 'quote') {
      const testQuoteRequest = {
        tokenAddress: '11111111111111111111111111111112', // System program (for testing)
        poolAddress: '11111111111111111111111111111112',
        dexType: 'pumpfun' as any,
        amount: 1,
        direction: 'buy' as any
      };
      
      const jobId = await queueService.addQuoteJob(testQuoteRequest, priority);
      
      res.json({
        success: true,
        data: {
          jobId,
          type: 'quote',
          message: 'Test quote job queued successfully'
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Invalid test type. Use "quote"'
      });
    }
  } catch (error) {
    swapLogger.error('Error testing queue', { error: error instanceof Error ? error.message : 'Unknown error' });
    res.status(500).json({
      success: false,
      error: 'Failed to test queue'
    });
  }
});

/**
 * @route   POST /api/queue/cleanup
 * @desc    Manually trigger queue cleanup
 * @access  Public
 */
router.post('/cleanup', async (req, res) => {
  try {
    await queueService.cleanupJobs();
    
    res.json({
      success: true,
      message: 'Queue cleanup completed successfully'
    });
  } catch (error) {
    swapLogger.error('Error cleaning up queues', { error: error instanceof Error ? error.message : 'Unknown error' });
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup queues'
    });
  }
});

export default router;