import { Router } from 'express';
import pumpRoutes from './pumpFun/pump.route';
import launchLabRoutes from './launchlab/launchlab.route';
import tokenRoutes from './token.routes';
import webhookRoutes from './webhook/webhook.routes';
import queueRoutes from './queue/queue.routes';

const router = Router();

// API Routes - Mount with explicit path
router.use('/api/pump', pumpRoutes);
router.use('/api/launchlab', launchLabRoutes);
router.use('/api/token', tokenRoutes);
router.use('/api/webhook', webhookRoutes);
router.use('/api/queue', queueRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
    console.log('Health check endpoint hit');
    res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Debug route
router.get('/debug', (req, res) => {
    console.log('Debug endpoint hit');
    res.status(200).json({ message: 'Debug endpoint working' });
});

export default router;
