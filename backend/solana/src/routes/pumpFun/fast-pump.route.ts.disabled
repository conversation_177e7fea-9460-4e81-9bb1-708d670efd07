import { Router } from 'express';
import timeout from 'express-timeout-handler';
import { executeEnhancedSwap, getEnhancedQuote } from '../../controllers/pumpFun/enhanced-pump.controller.js';
import { MemoryOptimizerService } from '../../services/transaction/memory-optimizer.service.js';
import { logger } from '../../utils/logger.js';

const router = Router();

// Memory monitoring middleware
router.use((req, res, next) => {
  const usage = MemoryOptimizerService.getMemoryUsage();
  
  // Log memory usage for monitoring
  logger.debug('Request memory check', {
    endpoint: req.path,
    heapUsedMB: usage.heapUsedMB.toFixed(2),
    activeContexts: usage.activeContexts
  });

  // Detect potential memory issues
  MemoryOptimizerService.detectMemoryLeaks();
  
  next();
});

// Production timeout configuration
const timeoutOptions = {
  timeout: 20000, // 20 seconds for production
  onTimeout: (req: any, res: any) => {
    logger.error('Request timeout', {
      endpoint: req.path,
      method: req.method,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
    
    res.status(408).json({
      success: false,
      error: 'Request timeout - transaction took too long to process',
      technicalDetails: 'Service timeout after 20 seconds',
      category: 'NETWORK_ERROR',
      retryable: true,
      suggestions: [
        'Try again with higher priority fees',
        'Check network status',
        'Use smaller trade amounts during high congestion'
      ]
    });
  },
  onDelayedResponse: (req: any, method: string, args: any) => {
    logger.warn('Delayed response detected', {
      endpoint: req.path,
      method: method,
      responseTime: Date.now() - req.startTime
    });
    return `Request processing took longer than expected: ${method}`;
  }
};

/**
 * Enhanced quote endpoint with fast execution optimizations
 * GET /api/fast-pump/quote
 */
router.post('/quote', timeout.handler(timeoutOptions), async (req, res) => {
  const contextId = `quote_${Date.now()}_${Math.random().toString(36).substring(7)}`;
  
  await MemoryOptimizerService.executeWithCleanup(contextId, async (context) => {
    // Add request start time for monitoring
    req.startTime = Date.now();
    
    await getEnhancedQuote(req, res);
    
    // Log performance metrics
    const executionTime = Date.now() - req.startTime;
    logger.info('Quote request completed', {
      contextId,
      executionTime,
      success: res.statusCode === 200
    });
  });
});

/**
 * Enhanced swap endpoint with all production optimizations
 * POST /api/fast-pump/swap
 */
router.post('/swap', timeout.handler(timeoutOptions), async (req, res) => {
  const contextId = `swap_${Date.now()}_${Math.random().toString(36).substring(7)}`;
  
  await MemoryOptimizerService.executeWithCleanup(contextId, async (context) => {
    // Add request start time for monitoring
    req.startTime = Date.now();
    
    // Log swap initiation
    logger.info('Fast swap initiated', {
      contextId,
      tokenAddress: req.body.tokenAddress,
      amount: req.body.amount,
      direction: req.body.direction,
      exchangeType: req.body.dexType,
      walletId: req.body.walletId?.substring(0, 8)
    });
    
    await executeEnhancedSwap(req, res);
    
    // Log completion metrics
    const executionTime = Date.now() - req.startTime;
    logger.info('Fast swap completed', {
      contextId,
      executionTime,
      success: res.statusCode === 200,
      memoryUsage: MemoryOptimizerService.getMemoryUsage().heapUsedMB.toFixed(2) + 'MB'
    });
  });
});

/**
 * Health check endpoint with memory and performance metrics
 * GET /api/fast-pump/health
 */
router.get('/health', (req, res) => {
  const usage = MemoryOptimizerService.getMemoryUsage();
  
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    memory: {
      heapUsedMB: parseFloat(usage.heapUsedMB.toFixed(2)),
      heapTotalMB: parseFloat(usage.heapTotalMB.toFixed(2)),
      externalMB: parseFloat(usage.externalMB.toFixed(2)),
      activeContexts: usage.activeContexts
    },
    performance: {
      uptime: process.uptime(),
      nodeVersion: process.version,
      platform: process.platform
    },
    features: {
      transactionSimulation: true,
      circuitBreaker: true,
      errorRecovery: true,
      priceProtection: true,
      memoryOptimization: true,
      fastExecution: true
    }
  };

  // Determine health status
  if (usage.heapUsedMB > 512) {
    health.status = 'warning';
  }
  
  if (usage.heapUsedMB > 768 || usage.activeContexts > 50) {
    health.status = 'critical';
  }

  const statusCode = health.status === 'healthy' ? 200 : 
                    health.status === 'warning' ? 200 : 503;

  res.status(statusCode).json(health);
});

/**
 * Performance metrics endpoint
 * GET /api/fast-pump/metrics
 */
router.get('/metrics', (req, res) => {
  const usage = MemoryOptimizerService.getMemoryUsage();
  
  // Force garbage collection for accurate metrics
  MemoryOptimizerService.forceGarbageCollection();
  
  const postGCUsage = MemoryOptimizerService.getMemoryUsage();
  
  const metrics = {
    timestamp: new Date().toISOString(),
    memory: {
      beforeGC: {
        heapUsedMB: usage.heapUsedMB,
        heapTotalMB: usage.heapTotalMB,
        externalMB: usage.externalMB,
        rssMB: usage.rssMB
      },
      afterGC: {
        heapUsedMB: postGCUsage.heapUsedMB,
        heapTotalMB: postGCUsage.heapTotalMB,
        externalMB: postGCUsage.externalMB,
        rssMB: postGCUsage.rssMB
      },
      freedMB: usage.heapUsedMB - postGCUsage.heapUsedMB,
      activeContexts: postGCUsage.activeContexts
    },
    process: {
      uptime: process.uptime(),
      cpuUsage: process.cpuUsage(),
      pid: process.pid,
      version: process.version
    }
  };

  res.json(metrics);
});

/**
 * Force cleanup endpoint (for admin use)
 * POST /api/fast-pump/cleanup
 */
router.post('/cleanup', (req, res) => {
  try {
    // Force garbage collection
    MemoryOptimizerService.forceGarbageCollection();
    
    const usage = MemoryOptimizerService.getMemoryUsage();
    
    logger.info('Manual cleanup triggered', {
      heapUsedMB: usage.heapUsedMB.toFixed(2),
      activeContexts: usage.activeContexts
    });

    res.json({
      success: true,
      message: 'Cleanup completed successfully',
      memoryUsage: {
        heapUsedMB: usage.heapUsedMB,
        activeContexts: usage.activeContexts
      }
    });
  } catch (error) {
    logger.error('Manual cleanup failed', error);
    
    res.status(500).json({
      success: false,
      error: 'Cleanup failed',
      details: (error as Error).message
    });
  }
});

export default router;