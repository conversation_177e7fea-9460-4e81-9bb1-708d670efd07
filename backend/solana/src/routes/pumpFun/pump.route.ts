import { Router } from 'express';
import { getQuote, executeSwap } from '../../controllers/pumpFun/pump.controller';
import { swapLogger } from '../../utils/logger';

// Timeout middleware to limit API response time
const timeout = (ms: number) => {
  return (req: any, res: any, next: any) => {
    // Save the original send function
    const send = res.send;
    let isTimedOut = false;
    
    // Set a timeout
    const timer = setTimeout(() => {
      isTimedOut = true;
      // Log the timeout event
      swapLogger.error('API Timeout Exceeded', {
        route: req.originalUrl,
        method: req.method,
        timeoutMs: ms
      });
      
      res.status(408).json({ 
        success: false, 
        error: `Request timeout after ${ms}ms` 
      });
    }, ms);
    
    // Override the send function to clear the timeout
    res.send = function(body: any) {
      if (!isTimedOut) {
        clearTimeout(timer);
        send.call(this, body);
      }
    };
    
    next();
  };
};

const router = Router();

/**
 * @route   POST /api/pump/quote
 * @desc    Get a quote for a potential swap
 * @access  Public
 */
router.post('/quote', timeout(10000), getQuote);

/**
 * @route   POST /api/pump/swap
 * @desc    Execute a swap
 * @access  Public
 */
router.post('/swap', timeout(10000), executeSwap);

export default router;
