import { Request, Response } from 'express';
import { PublicKey } from '@solana/web3.js';
import {
  LaunchLabQuoteRequest,
  LaunchLabQuoteResponse,
  LaunchLabSwapRequest,
  LaunchLabTradeResponse,
  LaunchLabPoolInfo,
  ApiResponse,
  LaunchLabErrorCode
} from '../../types/launchlab.types';
import { launchLabService } from '../../services/launchlab/launchlab.service';
import { tradeHistoryService } from '../../services/tradeHistory/tradeHistory.service';
import { notificationService } from '../../services/notification/notification.service';

/**
 * LaunchLab Controller
 * Handles HTTP requests for LaunchLab operations
 */

/**
 * Get quote for LaunchLab trading
 * @route POST /api/launchlab/quote
 */
export const getQuote = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('LaunchLab quote request received:', req.body);

    const { poolId, amount, direction, walletAddress } = req.body;

    // Validate required fields
    if (!poolId || !amount || !direction) {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.INVALID_AMOUNT,
          message: 'Missing required fields: poolId, amount, direction'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabQuoteResponse>);
      return;
    }

    // Validate direction
    if (direction !== 'buy' && direction !== 'sell') {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.INVALID_DIRECTION,
          message: 'Direction must be either "buy" or "sell"'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabQuoteResponse>);
      return;
    }

    // Validate amount
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.INVALID_AMOUNT,
          message: 'Amount must be a positive number'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabQuoteResponse>);
      return;
    }

    // Validate poolId format
    try {
      new PublicKey(poolId);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.POOL_NOT_FOUND,
          message: 'Invalid pool ID format'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabQuoteResponse>);
      return;
    }

    const quoteRequest: LaunchLabQuoteRequest = {
      poolId,
      amount: amount.toString(),
      direction,
      walletAddress: walletAddress || undefined
    };

    console.log('Getting LaunchLab quote for:', {
      poolId: poolId.substring(0, 8) + '...',
      amount,
      direction,
      walletProvided: !!walletAddress
    });

    // Get quote from service
    const quoteResult = await launchLabService.getQuote(quoteRequest);

    const response: ApiResponse<LaunchLabQuoteResponse> = {
      success: true,
      data: quoteResult,
      timestamp: Date.now()
    };

    console.log('LaunchLab quote successful:', {
      inputAmount: quoteResult.inputAmountHuman,
      outputAmount: quoteResult.outputAmountHuman,
      priceImpact: quoteResult.priceImpactPercent
    });

    res.json(response);

  } catch (error: any) {
    console.error('LaunchLab quote error:', error);

    const errorResponse: ApiResponse<LaunchLabQuoteResponse> = {
      success: false,
      error: {
        code: LaunchLabErrorCode.QUOTE_FAILED,
        message: error.message || 'Failed to get LaunchLab quote'
      },
      timestamp: Date.now()
    };

    res.status(500).json(errorResponse);
  }
};

/**
 * Execute LaunchLab swap
 * @route POST /api/launchlab/swap
 */
export const executeSwap = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('LaunchLab swap request received:', {
      ...req.body,
      walletAddress: req.body.walletAddress?.substring(0, 8) + '...',
      walletId: req.body.walletId ? 'provided' : 'missing'
    });

    const { poolId, amount, walletAddress, walletId, direction, slippage, priorityFee, computeUnitLimit } = req.body;

    // Validate required fields
    if (!poolId || !amount || !walletAddress || !walletId || !direction || slippage === undefined) {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.INVALID_AMOUNT,
          message: 'Missing required fields: poolId, amount, walletAddress, walletId, direction, slippage'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabTradeResponse>);
      return;
    }

    // Validate direction
    if (direction !== 'buy' && direction !== 'sell') {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.INVALID_DIRECTION,
          message: 'Direction must be either "buy" or "sell"'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabTradeResponse>);
      return;
    }

    // Validate amount
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.INVALID_AMOUNT,
          message: 'Amount must be a positive number'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabTradeResponse>);
      return;
    }

    // Validate slippage
    const numSlippage = parseFloat(slippage);
    if (isNaN(numSlippage) || numSlippage < 0 || numSlippage > 1) {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.INVALID_AMOUNT,
          message: 'Slippage must be a number between 0 and 1'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabTradeResponse>);
      return;
    }

    // Validate wallet addresses
    try {
      new PublicKey(walletAddress);
      new PublicKey(poolId);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.WALLET_NOT_CONFIGURED,
          message: 'Invalid wallet address or pool ID format'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabTradeResponse>);
      return;
    }

    const swapRequest: LaunchLabSwapRequest = {
      poolId,
      amount: amount.toString(),
      walletAddress,
      walletId,
      direction,
      slippage: numSlippage,
      priorityFee: priorityFee ? parseInt(priorityFee) : undefined,
      computeUnitLimit: computeUnitLimit ? parseInt(computeUnitLimit) : undefined
    };

    console.log('Executing LaunchLab swap:', {
      poolId: poolId.substring(0, 8) + '...',
      amount,
      direction,
      slippage: numSlippage,
      walletAddress: walletAddress.substring(0, 8) + '...'
    });

    // Execute swap
    const swapResult = await launchLabService.executeSwap(swapRequest);

    const response: ApiResponse<LaunchLabTradeResponse> = {
      success: true,
      data: swapResult,
      timestamp: Date.now()
    };

    console.log('LaunchLab swap successful:', {
      txid: swapResult.txid,
      inputAmount: swapResult.inputAmountHuman,
      outputAmount: swapResult.outputAmountHuman
    });

    // Store trade history and notification for successful swaps
    try {
      // Clean user ID (remove 'did:privy:' prefix if present)
      const cleanedUserId = walletId?.startsWith('did:privy:') ? walletId : `did:privy:${walletId}`;
      
      // Get token details - use poolId as token address for LaunchLab
      const tokenAddress = poolId; // Pool ID serves as the token identifier
      const tokenName = 'LaunchLab Token'; // Default name for LaunchLab tokens
      const tokenSymbol = 'LLAB'; // Default symbol for LaunchLab tokens
      const tokenImage = null; // No image available from LaunchLab swap response
      
      // Calculate amounts based on direction
      const tokenAmount = direction === 'buy' ? parseFloat(swapResult.outputAmountHuman) : parseFloat(swapResult.inputAmountHuman);
      const solAmount = direction === 'buy' ? parseFloat(swapResult.inputAmountHuman) : parseFloat(swapResult.outputAmountHuman);
      const price = tokenAmount > 0 ? solAmount / tokenAmount : 0;

      // Store notification
      await notificationService.storeSwapNotification(
        cleanedUserId,
        direction,
        tokenAddress,
        tokenSymbol,
        tokenAmount,
        price,
        'LaunchLab',
        swapResult.txid,
        'completed'
      );

      // Store trade history
      await tradeHistoryService.storeSwapTradeHistory(
        cleanedUserId,
        walletAddress,
        tokenAddress,
        tokenName,
        tokenSymbol,
        tokenImage,
        poolId, // Using poolId as pool address
        direction,
        'launchlab',
        tokenAmount,
        solAmount,
        price,
        swapResult.txid,
        numSlippage,
        priorityFee ? parseInt(priorityFee) : undefined
      );

      console.log('LaunchLab trade history and notification stored successfully', {
        txid: swapResult.txid,
        tokenAddress
      });

    } catch (storageError: any) {
      console.warn('Failed to store LaunchLab trade history or notification', {
        error: storageError.message,
        txid: swapResult.txid
      });
    }

    res.json(response);

  } catch (error: any) {
    console.error('LaunchLab swap error:', error);

    const errorResponse: ApiResponse<LaunchLabTradeResponse> = {
      success: false,
      error: {
        code: LaunchLabErrorCode.SWAP_FAILED,
        message: error.message || 'Failed to execute LaunchLab swap'
      },
      timestamp: Date.now()
    };

    res.status(500).json(errorResponse);
  }
};

/**
 * Get LaunchLab pool information
 * @route GET /api/launchlab/pool/:poolId
 */
export const getPoolInfo = async (req: Request, res: Response): Promise<void> => {
  try {
    const { poolId } = req.params;

    if (!poolId) {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.POOL_NOT_FOUND,
          message: 'Pool ID is required'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabPoolInfo>);
      return;
    }

    // Validate poolId format
    try {
      new PublicKey(poolId);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: {
          code: LaunchLabErrorCode.POOL_NOT_FOUND,
          message: 'Invalid pool ID format'
        },
        timestamp: Date.now()
      } as ApiResponse<LaunchLabPoolInfo>);
      return;
    }

    console.log('Getting LaunchLab pool info for:', poolId.substring(0, 8) + '...');

    // Get pool info from service
    const poolInfo = await launchLabService.getPoolInfo(poolId);

    const response: ApiResponse<LaunchLabPoolInfo> = {
      success: true,
      data: poolInfo,
      timestamp: Date.now()
    };

    console.log('LaunchLab pool info retrieved:', {
      poolId: poolId.substring(0, 8) + '...',
      status: poolInfo.status,
      tokenSymbol: poolInfo.tokenSymbol
    });

    res.json(response);

  } catch (error: any) {
    console.error('LaunchLab pool info error:', error);

    const errorResponse: ApiResponse<LaunchLabPoolInfo> = {
      success: false,
      error: {
        code: LaunchLabErrorCode.POOL_NOT_FOUND,
        message: error.message || 'Failed to get LaunchLab pool information'
      },
      timestamp: Date.now()
    };

    res.status(500).json(errorResponse);
  }
};
