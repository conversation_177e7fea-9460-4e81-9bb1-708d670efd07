import { Request, Response } from 'express';
import { executeSwapWithPrivy } from '../../services/pumpFun/pump.service';
import { swapLogger } from '../../utils/logger';
import { config } from '../../config';
import { notificationService } from '../../services/notification/notification.service';
import {
  DexType,
  SwapDirection,
  SwapRequest
} from '../../types/pump.types';

// Helper function to remove 'did:privy:' prefix from wallet ID
function cleanWalletId(walletId: string): string {
  if (walletId.startsWith('did:privy:')) {
    return walletId.replace('did:privy:', '');
  }
  return walletId;
}

// Helper function to convert dexType to lowercase exchange_name
function mapDexTypeToExchangeName(dexType: string): string {
  return dexType.toLowerCase();
}

/**
 * Handle TP/SL order triggered webhook
 */
export async function executeTPSLWebhook(req: Request, res: Response): Promise<void> {
  try {
    const {
      order_id,
      trigger_price,
      current_price,
      order_data,
      timestamp
    } = req.body;

    swapLogger.info('TP/SL Webhook triggered', {
      orderId: order_id,
      triggerPrice: trigger_price,
      currentPrice: current_price,
      timestamp
    });

    // Validate required fields
    if (!order_id || !order_data) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: order_id, order_data'
      });
      return;
    }

    // Clean wallet ID and map exchange name
    const cleanedWalletId = cleanWalletId(order_data.wallet_id);
    const exchangeName = mapDexTypeToExchangeName(order_data.exchange_name || 'pumpfun');

    // Create swap request for the triggered TP/SL order
    const swapRequest: SwapRequest = {
      tokenAddress: order_data.token_address,
      poolAddress: order_data.pool_address,
      dexType: exchangeName as DexType, // Map to lowercase
      amount: parseFloat(order_data.amount),
      direction: 'sell' as SwapDirection, // TP/SL orders are always sell orders
      slippage: 0.15, // Default slippage for TP/SL
      walletAddress: order_data.wallet_address,
      walletId: cleanedWalletId, // Clean wallet ID
      mevProtection: true, // Enable MEV protection for TP/SL
      bribeAmount: 0.0001,
      priorityLevel: 'high', // High priority for TP/SL execution
      priorityFee: 0.0003
    };

    // Execute the swap
    const swapResult = await executeSwapWithPrivy(swapRequest);

    if (!swapResult.signature) {
      throw new Error(`Swap execution failed: No signature returned`);
    }

    // Prepare response with total_token and txn fields
    const responseData = {
      success: true,
      data: {
        orderId: order_id,
        outAmount: swapResult.quoteResult.outAmount,
        price: swapResult.quoteResult.price,
        signature: swapResult.signature,
        bundleId: null, // Will be null for direct execution
        solscanUrl: swapResult.signature ? `https://solscan.io/tx/${swapResult.signature}` : undefined,
        executionMethod: 'privy',
        mevProtected: true,
        tipAmount: 100000, // Default tip amount
        tipAmountSol: 0.0001,
        // Map response fields as required
        total_token: swapResult.quoteResult.outAmount, // Store outAmount as total_token
        txn: swapResult.signature // Store signature as txn
      }
    };

    // Store notification for TP/SL execution
    try {
      await notificationService.storeTPSLNotification(
        cleanedWalletId,
        order_data.action, // 'take_profit' or 'stop_loss'
        order_data.token_address,
        order_data.token_symbol || 'Unknown',
        parseFloat(order_data.amount),
        trigger_price,
        exchangeName,
        swapResult.signature,
        'completed'
      );
    } catch (notificationError: any) {
      swapLogger.warn('Failed to store TP/SL notification', {
        error: notificationError.message,
        orderId: order_id
      });
    }

    swapLogger.info('TP/SL order executed successfully', {
      orderId: order_id,
      signature: swapResult.signature,
      outAmount: swapResult.quoteResult.outAmount
    });

    res.json(responseData);

  } catch (error: any) {
    swapLogger.error('TP/SL webhook execution failed', {
      orderId: req.body.order_id,
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
}

/**
 * Handle limit order triggered webhook
 */
export async function executeLimitOrderWebhook(req: Request, res: Response): Promise<void> {
  try {
    const {
      order_id,
      trigger_price,
      current_price,
      order_data,
      timestamp
    } = req.body;

    swapLogger.info('Limit Order Webhook triggered', {
      orderId: order_id,
      triggerPrice: trigger_price,
      currentPrice: current_price,
      timestamp
    });

    // Validate required fields
    if (!order_id || !order_data) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: order_id, order_data'
      });
      return;
    }

    // Clean wallet ID and map exchange name
    const cleanedWalletId = cleanWalletId(order_data.wallet_id);
    const exchangeName = mapDexTypeToExchangeName(order_data.dex_type || 'pumpfun');

    // Create swap request for the triggered limit order
    const swapRequest: SwapRequest = {
      tokenAddress: order_data.token_address,
      poolAddress: order_data.pool_address,
      dexType: exchangeName as DexType, // Map to lowercase
      amount: parseFloat(order_data.amount),
      direction: order_data.direction as SwapDirection,
      slippage: parseFloat(order_data.slippage) || 0.15,
      walletAddress: order_data.wallet_address,
      walletId: cleanedWalletId, // Clean wallet ID
      mevProtection: true, // Enable MEV protection for limit orders
      bribeAmount: 0.0001,
      priorityLevel: 'high', // High priority for limit order execution
      priorityFee: 0.0003
    };

    // Execute the swap
    const swapResult = await executeSwapWithPrivy(swapRequest);

    if (!swapResult.signature) {
      throw new Error(`Swap execution failed: No signature returned`);
    }

    // Prepare response with total_token and txn fields
    const responseData = {
      success: true,
      data: {
        orderId: order_id,
        outAmount: swapResult.quoteResult.outAmount,
        price: swapResult.quoteResult.price,
        signature: swapResult.signature,
        bundleId: null, // Will be null for direct execution
        solscanUrl: swapResult.signature ? `https://solscan.io/tx/${swapResult.signature}` : undefined,
        executionMethod: 'privy',
        mevProtected: true,
        tipAmount: 100000, // Default tip amount
        tipAmountSol: 0.0001,
        // Map response fields as required
        total_token: swapResult.quoteResult.outAmount, // Store outAmount as total_token
        txn: swapResult.signature // Store signature as txn
      }
    };

    // Store notification for limit order execution
    try {
      await notificationService.storeLimitOrderNotification(
        cleanedWalletId,
        order_data.direction, // 'buy' or 'sell'
        order_data.token_address,
        order_data.token_symbol || 'Unknown',
        parseFloat(order_data.amount),
        trigger_price,
        exchangeName,
        swapResult.signature,
        'completed'
      );
    } catch (notificationError: any) {
      swapLogger.warn('Failed to store limit order notification', {
        error: notificationError.message,
        orderId: order_id
      });
    }

    swapLogger.info('Limit order executed successfully', {
      orderId: order_id,
      signature: swapResult.signature,
      outAmount: swapResult.quoteResult.outAmount
    });

    res.json(responseData);

  } catch (error: any) {
    swapLogger.error('Limit order webhook execution failed', {
      orderId: req.body.order_id,
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
}