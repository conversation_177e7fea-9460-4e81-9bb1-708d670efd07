import { Request, Response } from 'express';
import { Connection, PublicKey } from '@solana/web3.js';
import { logger } from '../../utils/logger.js';
import { SwapRequest, SwapDirection, DexType, SwapResponse } from '../../types/pump.types.js';
import { FastExecutionService } from '../../services/transaction/fast-execution.service.js';
import { ErrorRecoveryService } from '../../services/transaction/error-recovery.service.js';
import { executeSwapWithPrivy } from '../../services/privy/proper-privy.service.js';

// Import existing services
import { 
  convertSlippageToDecimal,
  convertPriorityFeeToMicroLamports,
  convertBribeAmountToLamports,
  formatTokenAmount,
  formatSolAmount,
  formatPrice
} from '../../utils/conversion.utils.js';

import {
  fetchPumpFunToken,
  fetchPumpFunQuote
} from '../../services/pumpFun/pumpFun.service.js';

import {
  fetchPumpSwapToken,
  fetchPumpSwapQuote
} from '../../services/pumpFun/pumpSwap.service.js';

interface EnhancedSwapContext {
  exchangeType: 'pumpfun' | 'pumpswap';
  tradeAmountSol: number;
  isUrgent: boolean;
  retryCount: number;
  startTime: number;
}

const swapLogger = logger.child({ service: 'enhanced-pump-controller' });

/**
 * Enhanced swap execution with production optimizations
 */
export const executeEnhancedSwap = async (req: Request, res: Response): Promise<void> => {
  const startTime = Date.now();
  let context: EnhancedSwapContext;

  try {
    const {
      tokenAddress,
      poolAddress,
      dexType,
      amount,
      direction,
      slippage,
      walletAddress,
      walletId,
      priorityFee,
      bribeAmount,
      mevProtection,
      priorityLevel = 'medium'
    } = req.body;

    // Input validation
    if (!tokenAddress || !dexType || !amount || !direction || !walletAddress || !walletId) {
      res.status(400).json({
        success: false,
        error: 'Missing required parameters'
      });
      return;
    }

    // Initialize context
    context = {
      exchangeType: dexType.toLowerCase() as 'pumpfun' | 'pumpswap',
      tradeAmountSol: parseFloat(amount),
      isUrgent: priorityLevel === 'veryHigh',
      retryCount: 0,
      startTime
    };

    swapLogger.info('Enhanced swap initiated', {
      tokenAddress,
      exchangeType: context.exchangeType,
      amount: context.tradeAmountSol,
      direction,
      priorityLevel,
      walletId: walletId.substring(0, 8)
    });

    // Convert and validate parameters
    const slippageValue = convertSlippageToDecimal(slippage);
    const finalPriorityFee = convertPriorityFeeToMicroLamports(priorityFee, context.tradeAmountSol);
    const finalBribeAmount = convertBribeAmountToLamports(bribeAmount, context.tradeAmountSol);

    // Clean wallet ID
    const cleanWalletId = walletId.replace('did:privy:', '');

    // Create swap request
    const swapRequest: SwapRequest = {
      tokenAddress,
      poolAddress,
      dexType: context.exchangeType as DexType,
      amount: context.tradeAmountSol,
      direction: direction as SwapDirection,
      slippage: slippageValue,
      walletAddress,
      walletId: cleanWalletId,
      priorityFee: finalPriorityFee,
      bribeAmount: finalBribeAmount,
      mevProtection: mevProtection !== false,
      priorityLevel
    };

    // Execute with fast execution service
    const result = await executeSwapWithFastService(swapRequest, context);

    // Format response
    const response = formatSuccessResponse(result, context);
    
    res.status(200).json(response);

  } catch (error) {
    const errorAnalysis = ErrorRecoveryService.analyzeError(error as Error, {
      exchangeType: context?.exchangeType,
      tradeAmount: context?.tradeAmountSol,
      retryCount: context?.retryCount
    });

    swapLogger.error('Enhanced swap failed', {
      error: (error as Error).message,
      category: errorAnalysis.category,
      action: errorAnalysis.action,
      executionTime: Date.now() - startTime
    });

    res.status(500).json({
      success: false,
      error: errorAnalysis.userMessage,
      technicalDetails: errorAnalysis.technicalDetails,
      category: errorAnalysis.category,
      retryable: errorAnalysis.retryable,
      suggestions: ErrorRecoveryService.getRecoverySuggestions(errorAnalysis)
    });
  }
};

/**
 * Execute swap with fast execution service and retry logic
 */
async function executeSwapWithFastService(
  swapRequest: SwapRequest,
  context: EnhancedSwapContext
): Promise<any> {
  const connection = new Connection(
    process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
    'confirmed'
  );

  const fastExecutionService = new FastExecutionService(connection);
  
  // Get recommended config for this trade
  const config = FastExecutionService.getRecommendedConfig(
    context.tradeAmountSol,
    context.exchangeType,
    context.isUrgent
  );

  swapLogger.info('Fast execution config', {
    exchangeType: context.exchangeType,
    computeUnitLimit: config.computeUnitLimit,
    computeUnitPrice: config.computeUnitPrice,
    priorityLevel: config.priorityLevel,
    maxRetries: config.maxRetries
  });

  let lastError: Error | null = null;

  // Retry loop with error recovery
  while (context.retryCount <= config.maxRetries) {
    try {
      // Fetch fresh quote for each attempt
      const quoteResult = await getQuoteForSwap(swapRequest, context);
      
      // Build transaction based on exchange type
      const transaction = await buildTransactionForExchange(
        swapRequest,
        quoteResult,
        context
      );

      // Execute with fast service
      const result = await fastExecutionService.executeTransactionFast(
        transaction,
        config,
        swapRequest.walletId,
        context.exchangeType
      );

      if (result.success) {
        swapLogger.info('Swap executed successfully', {
          signature: result.signature,
          executionTime: result.executionTime,
          retryCount: result.retryCount,
          exchangeType: context.exchangeType
        });

        return {
          success: true,
          signature: result.signature,
          quoteResult,
          executionTime: result.executionTime,
          retryCount: result.retryCount,
          executionMethod: 'fast-execution'
        };
      } else {
        throw new Error(result.error || 'Fast execution failed');
      }

    } catch (error) {
      lastError = error as Error;
      context.retryCount++;

      const errorAnalysis = ErrorRecoveryService.analyzeError(lastError, {
        exchangeType: context.exchangeType,
        tradeAmount: context.tradeAmountSol,
        retryCount: context.retryCount
      });

      swapLogger.warn('Swap attempt failed, analyzing recovery', {
        attempt: context.retryCount,
        category: errorAnalysis.category,
        action: errorAnalysis.action,
        retryable: errorAnalysis.retryable
      });

      // Check if we should retry
      if (!ErrorRecoveryService.shouldRetry(errorAnalysis, context.retryCount, config.maxRetries)) {
        break;
      }

      // Apply recovery strategy
      await applyRecoveryStrategy(errorAnalysis, swapRequest, context);

      // Wait before retry
      const retryDelay = ErrorRecoveryService.getRetryDelay(errorAnalysis, context.retryCount);
      await sleep(retryDelay);
    }
  }

  // All retries failed, throw the last error
  throw lastError || new Error('Swap execution failed after all retries');
}

/**
 * Get quote based on exchange type
 */
async function getQuoteForSwap(swapRequest: SwapRequest, context: EnhancedSwapContext): Promise<any> {
  if (context.exchangeType === 'pumpfun') {
    return await fetchPumpFunQuote(
      swapRequest.tokenAddress,
      swapRequest.amount,
      swapRequest.direction,
      swapRequest.slippage
    );
  } else {
    return await fetchPumpSwapQuote(
      swapRequest.tokenAddress,
      swapRequest.poolAddress,
      swapRequest.amount,
      swapRequest.direction,
      swapRequest.slippage
    );
  }
}

/**
 * Build transaction based on exchange type
 */
async function buildTransactionForExchange(
  swapRequest: SwapRequest,
  quoteResult: any,
  context: EnhancedSwapContext
): Promise<any> {
  const connection = new Connection(
    process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
    'confirmed'
  );

  if (context.exchangeType === 'pumpfun') {
    const { createPumpFunSwapTransaction } = await import('../../services/pumpFun/pumpFun.service.js');
    return await createPumpFunSwapTransaction(
      connection,
      new PublicKey(swapRequest.tokenAddress),
      new PublicKey(swapRequest.walletAddress),
      swapRequest.direction,
      swapRequest.slippage,
      quoteResult,
      undefined, // Pool data will be fetched inside
      9, // Token decimals
      true // Use user as fee payer
    );
  } else {
    const { createPumpSwapTransaction } = await import('../../services/pumpFun/pumpSwap.service.js');
    return await createPumpSwapTransaction(
      connection,
      new PublicKey(swapRequest.tokenAddress),
      new PublicKey(swapRequest.walletAddress),
      swapRequest.direction,
      swapRequest.amount,
      swapRequest.slippage,
      quoteResult,
      undefined, // Pool data
      9, // Token decimals
      true // Use user as fee payer
    );
  }
}

/**
 * Apply recovery strategy based on error analysis
 */
async function applyRecoveryStrategy(
  errorAnalysis: any,
  swapRequest: SwapRequest,
  context: EnhancedSwapContext
): Promise<void> {
  switch (errorAnalysis.action) {
    case 'INCREASE_SLIPPAGE':
      // Increase slippage by 0.5% for each retry
      swapRequest.slippage = Math.min(swapRequest.slippage + 0.005, 0.10); // Cap at 10%
      swapLogger.info('Increased slippage for retry', { 
        newSlippage: (swapRequest.slippage * 100).toFixed(1) + '%' 
      });
      break;

    case 'INCREASE_FEES':
      // Increase priority fee by 50% for each retry
      swapRequest.priorityFee = Math.floor(swapRequest.priorityFee * 1.5);
      swapLogger.info('Increased priority fee for retry', { 
        newPriorityFee: swapRequest.priorityFee 
      });
      break;

    case 'REFRESH_QUOTE':
      // Quote will be refreshed in the main retry loop
      swapLogger.info('Will refresh quote on next attempt');
      break;

    default:
      // No specific action needed
      break;
  }
}

/**
 * Format success response
 */
function formatSuccessResponse(result: any, context: EnhancedSwapContext): SwapResponse {
  const formattedOutAmount = result.quoteResult.direction === SwapDirection.Buy
    ? formatTokenAmount(result.quoteResult.outAmount)
    : formatSolAmount(result.quoteResult.outAmount);

  const formattedPrice = formatPrice(result.quoteResult.price);

  return {
    success: true,
    data: {
      signature: result.signature,
      outAmount: formattedOutAmount,
      price: formattedPrice,
      priceImpact: result.quoteResult.priceImpact,
      fee: result.quoteResult.fee,
      executionMethod: result.executionMethod,
      executionTime: result.executionTime,
      retryCount: result.retryCount,
      solscanUrl: `https://solscan.io/tx/${result.signature}`,
      exchangeType: context.exchangeType,
      optimizations: {
        fastExecution: true,
        circuitBreaker: true,
        errorRecovery: true,
        transactionSimulation: true
      }
    }
  };
}

/**
 * Utility function for delays
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Enhanced quote endpoint with price movement protection
 */
export const getEnhancedQuote = async (req: Request, res: Response): Promise<void> => {
  try {
    const { tokenAddress, poolAddress, dexType, amount, direction, slippage } = req.body;

    if (!tokenAddress || !dexType || !amount || !direction) {
      res.status(400).json({
        success: false,
        error: 'Missing required parameters'
      });
      return;
    }

    const exchangeType = dexType.toLowerCase() as 'pumpfun' | 'pumpswap';
    const slippageValue = convertSlippageToDecimal(slippage);

    // Get quote from appropriate service
    let quoteResult;
    if (exchangeType === 'pumpfun') {
      quoteResult = await fetchPumpFunQuote(tokenAddress, parseFloat(amount), direction, slippageValue);
    } else {
      quoteResult = await fetchPumpSwapQuote(tokenAddress, poolAddress, parseFloat(amount), direction, slippageValue);
    }

    // Add timestamp for price movement detection
    const response = {
      success: true,
      data: {
        ...quoteResult,
        timestamp: Date.now(),
        exchangeType,
        validityPeriod: 30000, // 30 seconds
        recommendations: {
          fastExecution: amount >= 10,
          mevProtection: amount >= 1,
          priorityLevel: amount >= 100 ? 'veryHigh' : amount >= 10 ? 'high' : 'medium'
        }
      }
    };

    res.status(200).json(response);

  } catch (error) {
    swapLogger.error('Enhanced quote failed', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to fetch quote'
    });
  }
};