import path from 'path';
import fs from 'fs';

// Simple logger that doesn't use winston
class SimpleLogger {
  private logFile: string;

  constructor() {
    // Ensure logs directory exists
    const logsDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    this.logFile = path.join(logsDir, 'combined.log');
  }

  private writeToFile(level: string, message: string, meta?: object) {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} ${level}: ${message} ${meta ? JSON.stringify(meta) : ''}\n`;
    
    try {
      fs.appendFileSync(this.logFile, logEntry);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  info(message: string, meta?: object) {
    console.log(`INFO: ${message}`, meta || '');
    this.writeToFile('info', message, meta);
  }

  warn(message: string, meta?: object) {
    console.warn(`WARN: ${message}`, meta || '');
    this.writeToFile('warn', message, meta);
  }

  error(message: string, meta?: object) {
    console.error(`ERROR: ${message}`, meta || '');
    this.writeToFile('error', message, meta);
  }

  debug(message: string, meta?: object) {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`DEBUG: ${message}`, meta || '');
      this.writeToFile('debug', message, meta);
    }
  }
}

export const logger = new SimpleLogger();

export const swapLogger = {
  info: (message: string, meta?: object) => {
    logger.info(message, { component: 'swap', ...meta });
  },
  warn: (message: string, meta?: object) => {
    logger.warn(message, { component: 'swap', ...meta });
  },
  error: (message: string, meta?: object) => {
    logger.error(message, { component: 'swap', ...meta });
  },
  debug: (message: string, meta?: object) => {
    logger.debug(message, { component: 'swap', ...meta });
  }
};

export default logger;