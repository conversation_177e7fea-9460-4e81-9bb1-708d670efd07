import { Connection, PublicKey, Transaction, SystemProgram, Keypair } from '@solana/web3.js';
import axios from 'axios';
import { config } from '../../config';

/**
 * Jito MEV Protection and Bribe Service
 * 
 * This service provides MEV protection and transaction prioritization
 * through <PERSON><PERSON>'s block engine and tip mechanism.
 */

// Jito tip accounts (these rotate, but these are common ones)
const JITO_TIP_ACCOUNTS = [
  'Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY',
  'DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL',
  'ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt',
  'DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh',
  '96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5',
  'HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe',
  'ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49',
  'Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY'
];

interface JitoBundle {
  transactions: string[]; // Base64 encoded transactions
}

interface JitoBundleResponse {
  jsonrpc: string;
  result?: string; // Bundle ID
  error?: {
    code: number;
    message: string;
    data?: any;
  };
  id: number;
}

export class JitoService {
  private jitoRpcUrls: string[];
  private currentUrlIndex: number;
  private connection: Connection;

  constructor() {
    // Set up multiple Jito endpoints for failover (including all available regions)
    this.jitoRpcUrls = [
      process.env.JITO_RPC_URL || 'https://mainnet.block-engine.jito.wtf/api/v1/bundles',
      process.env.JITO_RPC_URL_BACKUP || 'https://amsterdam.mainnet.block-engine.jito.wtf/api/v1/bundles',
      process.env.JITO_RPC_URL_BACKUP2 || 'https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles',
      'https://ny.mainnet.block-engine.jito.wtf/api/v1/bundles',
      'https://tokyo.mainnet.block-engine.jito.wtf/api/v1/bundles'
    ];
    this.currentUrlIndex = 0;
    this.connection = new Connection(process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com');
  }

  /**
   * Get the current Jito RPC URL
   */
  private getCurrentJitoUrl(): string {
    return this.jitoRpcUrls[this.currentUrlIndex];
  }

  /**
   * Rotate to the next Jito endpoint
   */
  private rotateJitoEndpoint(): void {
    this.currentUrlIndex = (this.currentUrlIndex + 1) % this.jitoRpcUrls.length;
    console.log(`Rotating to Jito endpoint: ${this.getCurrentJitoUrl()}`);
  }



  /**
   * Add Jito tip instruction to user's transaction
   * @param transaction The user's transaction to modify
   * @param tipAmount Amount to tip Jito validators (in lamports)
   * @param userPublicKey The user's public key (who will pay the tip)
   * @returns Modified transaction with tip instruction
   */
  addJitoTipToTransaction(
    transaction: Transaction,
    tipAmount: number,
    userPublicKey: PublicKey
  ): Transaction {
    console.log(`Adding Jito tip instruction: ${tipAmount} lamports from user ${userPublicKey.toString()}`);

    // Select a random tip account
    const randomTipAccount = JITO_TIP_ACCOUNTS[Math.floor(Math.random() * JITO_TIP_ACCOUNTS.length)];
    const tipAccount = new PublicKey(randomTipAccount);

    console.log(`Jito tip will be sent to: ${tipAccount.toString()}`);

    // Create tip instruction (user pays the tip)
    const tipInstruction = SystemProgram.transfer({
      fromPubkey: userPublicKey,
      toPubkey: tipAccount,
      lamports: tipAmount
    });

    // Add tip instruction to the beginning of the transaction
    transaction.instructions.unshift(tipInstruction);

    return transaction;
  }

  /**
   * Submit a bundle to Jito for MEV protection (user-paid tips)
   * @param userTransaction The user's transaction (should already include tip instruction)
   * @returns Bundle ID
   */
  async submitBundle(
    userTransaction: Transaction
  ): Promise<string> {
    try {
      console.log(`Submitting bundle to Jito (user-paid tips)`);
      console.log(`Transaction has ${userTransaction.instructions.length} instructions`);
      console.log(`Transaction fee payer: ${userTransaction.feePayer?.toString()}`);
      console.log(`Transaction blockhash: ${userTransaction.recentBlockhash}`);

      // Check if transaction is properly signed
      const hasSignatures = userTransaction.signatures.some(sig => sig.signature !== null);
      console.log(`Transaction has signatures: ${hasSignatures}`);

      // Serialize the user transaction with appropriate settings
      // For Jito bundles, we need the transaction to be fully signed but we can skip verification
      let userTxSerialized: string;

      if (hasSignatures) {
        // Transaction is signed, serialize with requireAllSignatures: true
        userTxSerialized = userTransaction.serialize({
          requireAllSignatures: true,
          verifySignatures: false
        }).toString('base64');
        console.log('✅ Serialized signed transaction for Jito');
      } else {
        // Transaction is not signed, serialize without requiring signatures
        userTxSerialized = userTransaction.serialize({
          requireAllSignatures: false,
          verifySignatures: false
        }).toString('base64');
        console.log('⚠️ Serialized unsigned transaction for Jito (this may fail)');
      }

      console.log(`Serialized transaction length: ${userTxSerialized.length} characters`);

      // Create bundle with single transaction (user's transaction + tip combined)
      const bundle: JitoBundle = {
        transactions: [userTxSerialized]
      };

      // Submit bundle to Jito with retry logic and exponential backoff
      let lastError: any;
      const maxRetries = this.jitoRpcUrls.length * 2; // Try each endpoint twice

      for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
          const currentUrl = this.getCurrentJitoUrl();
          console.log(`Attempting bundle submission to: ${currentUrl} (attempt ${attempt + 1}/${maxRetries})`);

          // Add exponential backoff for rate limiting
          if (attempt > 0) {
            const backoffDelay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Max 5 seconds
            console.log(`Waiting ${backoffDelay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, backoffDelay));
          }

          const response = await axios.post<JitoBundleResponse>(
            currentUrl,
            {
              jsonrpc: '2.0',
              id: 1,
              method: 'sendBundle',
              params: [bundle.transactions]
            },
            {
              headers: {
                'Content-Type': 'application/json'
              },
              timeout: 30000 // 30 second timeout
            }
          );

          if (response.data.result) {
            console.log(`✅ Bundle submitted successfully to ${currentUrl}. Bundle ID: ${response.data.result}`);
            return response.data.result;
          } else if (response.data.error) {
            const errorMsg = `Jito error: ${response.data.error.message} (code: ${response.data.error.code})`;
            console.error(errorMsg);
            throw new Error(errorMsg);
          } else {
            throw new Error('Failed to submit bundle to Jito - no result or error');
          }
        } catch (error: any) {
          lastError = error;
          const errorDetails = error.response?.data?.error || {};
          console.error(`❌ Bundle submission failed on ${this.getCurrentJitoUrl()}:`, {
            message: error.message,
            status: error.response?.status,
            errorCode: errorDetails.code,
            errorMessage: errorDetails.message
          });

          // Check if it's a rate limiting error (429) or network congestion
          const isRateLimited = error.response?.status === 429 ||
                               errorDetails.code === -32097 ||
                               error.message.includes('rate limited') ||
                               error.message.includes('congested');

          // Check if it's a transaction decoding error
          const isDecodingError = errorDetails.code === -32602 ||
                                 error.message.includes('could not be decoded');

          if (isRateLimited) {
            console.log('🚦 Rate limited or congested, trying next endpoint...');
            this.rotateJitoEndpoint();
            continue;
          } else if (isDecodingError) {
            console.log('🔧 Transaction decoding error, this may be a serialization issue');
            // Still try other endpoints in case it's endpoint-specific
            this.rotateJitoEndpoint();
            continue;
          } else {
            // For other errors, also try next endpoint
            console.log('🔄 Other error, trying next endpoint...');
            this.rotateJitoEndpoint();
          }
        }
      }

      // If all endpoints failed, throw the last error
      throw new Error(`All Jito endpoints failed. Last error: ${lastError?.message || 'Unknown error'}`);

    } catch (error: any) {
      console.error('Error submitting bundle to Jito:', error);

      // If Jito fails, we could fallback to regular transaction submission
      console.log('Jito submission failed, consider implementing fallback to regular RPC');
      throw new Error(`Jito bundle submission failed: ${error.message}`);
    }
  }

  /**
   * Calculate recommended tip amount based on trade size
   * @param tradeValueSol Trade value in SOL
   * @returns Recommended tip in lamports
   */
  calculateRecommendedTip(tradeValueSol: number): number {
    // Enhanced tip calculation logic with more tiers for larger trades:
    // - Small trades (< 0.1 SOL): 0.0005 SOL tip
    // - Medium trades (0.1 - 1 SOL): 0.001 SOL tip  
    // - Large trades (> 1 SOL): 0.002 SOL tip
    // - Very large trades (> 10 SOL): 0.005 SOL tip
    // - Huge trades (> 20 SOL): 0.01 SOL tip
    // - Massive trades (> 50 SOL): 0.02 SOL tip

    if (tradeValueSol < 0.1) {
      return 500000; // 0.0005 SOL
    } else if (tradeValueSol < 1) {
      return 1000000; // 0.001 SOL
    } else if (tradeValueSol < 10) {
      return 2000000; // 0.002 SOL
    } else if (tradeValueSol < 20) {
      return 5000000; // 0.005 SOL
    } else if (tradeValueSol < 50) {
      return 10000000; // 0.01 SOL
    } else {
      return 20000000; // 0.02 SOL
    }
  }

  /**
   * Check if MEV protection is recommended for a trade
   * @param tradeValueSol Trade value in SOL
   * @param slippage Slippage tolerance
   * @returns Whether MEV protection is recommended
   */
  isMEVProtectionRecommended(tradeValueSol: number, slippage: number): boolean {
    // Recommend MEV protection for:
    // - Trades > 0.1 SOL
    // - High slippage tolerance (> 1%)
    // - Any trade where tip cost < 1% of trade value

    const recommendedTip = this.calculateRecommendedTip(tradeValueSol);
    const tipCostPercentage = (recommendedTip / 1_000_000_000) / tradeValueSol;

    return tradeValueSol > 0.1 || slippage > 0.01 || tipCostPercentage < 0.01;
  }

  /**
   * Get Jito bundle status
   * @param bundleId Bundle ID returned from submitBundle
   * @returns Bundle status information
   */
  async getBundleStatus(bundleId: string): Promise<any> {
    try {
      const statusUrl = process.env.JITO_BUNDLE_STATUS_URL || this.getCurrentJitoUrl().replace('/bundles', '/bundle-status');

      const response = await axios.post(
        statusUrl,
        {
          jsonrpc: '2.0',
          id: 1,
          method: 'getBundleStatuses',
          params: [[bundleId]]
        },
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000 // 10 second timeout for status checks
        }
      );

      return response.data.result;
    } catch (error) {
      console.error('Error getting bundle status:', error);
      return null;
    }
  }
}

// Export singleton instance
export const jitoService = new JitoService();
