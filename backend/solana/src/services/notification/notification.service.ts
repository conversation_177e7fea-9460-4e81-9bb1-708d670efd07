import { supabase } from '../../config/supabase';
import { swapLogger } from '../../utils/logger';

export interface NotificationData {
  user_id: string;
  activity_type: 'tp_sl' | 'limit_order' | 'swap_buy' | 'swap_sell';
  token_address?: string;
  token_symbol?: string;
  amount?: number;
  price?: number;
  tx_hash?: string;
  exchange_name?: string;
  status: 'pending' | 'completed' | 'failed';
  message?: string;
  metadata?: Record<string, any>;
}

class NotificationService {
  /**
   * Store a notification for an activity
   */
  async storeNotification(data: NotificationData): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // Check if Supabase is configured
      if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
        swapLogger.warn('Supabase not configured, skipping notification storage');
        return { success: true, data: { id: 'mock-notification-id' } };
      }

      const { data: result, error } = await supabase
        .from('notification')
        .insert({
          user_id: data.user_id,
          activity_type: data.activity_type,
          token_address: data.token_address,
          token_symbol: data.token_symbol,
          amount: data.amount,
          price: data.price,
          tx_hash: data.tx_hash,
          exchange_name: data.exchange_name,
          status: data.status,
          message: data.message,
          metadata: data.metadata,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        swapLogger.error('Failed to store notification', {
          error: error.message,
          data: data
        });
        return { success: false, error: error.message };
      }

      swapLogger.info('Notification stored successfully', {
        notificationId: result.id,
        activityType: data.activity_type,
        userId: data.user_id
      });

      return { success: true, data: result };
    } catch (error: any) {
      swapLogger.error('Unexpected error storing notification', {
        error: error.message,
        data: data
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Update notification status
   */
  async updateNotificationStatus(
    notificationId: string, 
    status: 'pending' | 'completed' | 'failed',
    message?: string,
    txHash?: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // Check if Supabase is configured
      if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
        swapLogger.warn('Supabase not configured, skipping notification update');
        return { success: true, data: { id: notificationId } };
      }

      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      };

      if (message) updateData.message = message;
      if (txHash) updateData.tx_hash = txHash;

      const { data: result, error } = await supabase
        .from('notification')
        .update(updateData)
        .eq('id', notificationId)
        .select()
        .single();

      if (error) {
        swapLogger.error('Failed to update notification status', {
          error: error.message,
          notificationId,
          status
        });
        return { success: false, error: error.message };
      }

      return { success: true, data: result };
    } catch (error: any) {
      swapLogger.error('Unexpected error updating notification', {
        error: error.message,
        notificationId,
        status
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Get notifications for a user
   */
  async getUserNotifications(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      // Check if Supabase is configured
      if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
        swapLogger.warn('Supabase not configured, returning empty notifications');
        return { success: true, data: [] };
      }

      const { data: result, error } = await supabase
        .from('notification')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        swapLogger.error('Failed to get user notifications', {
          error: error.message,
          userId
        });
        return { success: false, error: error.message };
      }

      return { success: true, data: result };
    } catch (error: any) {
      swapLogger.error('Unexpected error getting user notifications', {
        error: error.message,
        userId
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Helper method to create notification for swap activities
   */
  async storeSwapNotification(
    userId: string,
    direction: 'buy' | 'sell',
    tokenAddress: string,
    tokenSymbol: string,
    amount: number,
    price: number,
    exchangeName: string,
    txHash?: string,
    status: 'pending' | 'completed' | 'failed' = 'completed'
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    const activityType = direction === 'buy' ? 'swap_buy' : 'swap_sell';
    const message = `${direction.toUpperCase()} ${amount} ${tokenSymbol} at $${price}`;

    return this.storeNotification({
      user_id: userId,
      activity_type: activityType,
      token_address: tokenAddress,
      token_symbol: tokenSymbol,
      amount,
      price,
      tx_hash: txHash,
      exchange_name: exchangeName,
      status,
      message,
      metadata: {
        direction,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Helper method to create notification for TP/SL activities
   */
  async storeTPSLNotification(
    userId: string,
    orderType: 'take_profit' | 'stop_loss',
    tokenAddress: string,
    tokenSymbol: string,
    amount: number,
    triggerPrice: number,
    exchangeName: string,
    txHash?: string,
    status: 'pending' | 'completed' | 'failed' = 'completed'
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    const message = `${orderType.replace('_', ' ').toUpperCase()} executed: ${amount} ${tokenSymbol} at $${triggerPrice}`;

    return this.storeNotification({
      user_id: userId,
      activity_type: 'tp_sl',
      token_address: tokenAddress,
      token_symbol: tokenSymbol,
      amount,
      price: triggerPrice,
      tx_hash: txHash,
      exchange_name: exchangeName,
      status,
      message,
      metadata: {
        order_type: orderType,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Helper method to create notification for limit order activities
   */
  async storeLimitOrderNotification(
    userId: string,
    direction: 'buy' | 'sell',
    tokenAddress: string,
    tokenSymbol: string,
    amount: number,
    targetPrice: number,
    exchangeName: string,
    txHash?: string,
    status: 'pending' | 'completed' | 'failed' = 'completed'
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    const message = `Limit ${direction.toUpperCase()} executed: ${amount} ${tokenSymbol} at $${targetPrice}`;

    return this.storeNotification({
      user_id: userId,
      activity_type: 'limit_order',
      token_address: tokenAddress,
      token_symbol: tokenSymbol,
      amount,
      price: targetPrice,
      tx_hash: txHash,
      exchange_name: exchangeName,
      status,
      message,
      metadata: {
        direction,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Get unread notification count for a user
   */
  async getUnreadNotificationCount(userId: string): Promise<{ success: boolean; count?: number; error?: string }> {
    try {
      // Check if Supabase is configured
      if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
        swapLogger.warn('Supabase not configured, returning 0 unread notifications');
        return { success: true, count: 0 };
      }

      const { count, error } = await supabase
        .from('notification')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_read', false);

      if (error) {
        swapLogger.error('Failed to get unread notification count', {
          error: error.message,
          userId
        });
        return { success: false, error: error.message };
      }

      return { success: true, count: count || 0 };
    } catch (error: any) {
      swapLogger.error('Unexpected error getting unread notification count', {
        error: error.message,
        userId
      });
      return { success: false, error: error.message };
    }
  }
}

export const notificationService = new NotificationService();