import { supabase } from '../../config/supabase';
import { swapLogger } from '../../utils/logger';

export interface TradeHistoryData {
  user_id: string;
  wallet_address: string;
  token_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string | null;
  pool_address?: string | null;
  trade_type: 'buy' | 'sell';
  dex_type: 'pumpfun' | 'pumpswap' | 'launchlab';
  token_amount: number;
  sol_amount: number;
  price: number;
  tx_hash: string;
  execution_status?: 'pending' | 'completed' | 'failed';
  slippage?: number;
  gas_fee?: number;
}

class TradeHistoryService {
  /**
   * Store a trade record in the trade_history table
   */
  async storeTradeHistory(data: TradeHistoryData): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // Check if Supabase is configured
      if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
        swapLogger.warn('Supabase not configured, skipping trade history storage', {
          hasUrl: !!process.env.SUPABASE_URL,
          hasKey: !!process.env.SUPABASE_ANON_KEY,
          urlValue: process.env.SUPABASE_URL ? 'Set' : 'Missing',
          keyValue: process.env.SUPABASE_ANON_KEY ? 'Set' : 'Missing'
        });
        return { success: true, data: { id: 'mock-trade-history-id' } };
      }

      swapLogger.info('Attempting to store trade history', {
        txHash: data.tx_hash,
        tokenAddress: data.token_address,
        tradeType: data.trade_type,
        dexType: data.dex_type,
        userId: data.user_id
      });

      const { data: result, error } = await supabase
        .from('trade_history')
        .insert({
          user_id: data.user_id,
          wallet_address: data.wallet_address,
          token_address: data.token_address,
          token_name: data.token_name,
          token_symbol: data.token_symbol,
          token_image: data.token_image,
          pool_address: data.pool_address,
          trade_type: data.trade_type,
          dex_type: data.dex_type,
          token_amount: data.token_amount,
          sol_amount: data.sol_amount,
          price: data.price,
          tx_hash: data.tx_hash,
          execution_status: data.execution_status || 'completed',
          slippage: data.slippage || 0.01,
          gas_fee: data.gas_fee,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        swapLogger.error('Failed to store trade history', {
          error: error.message,
          errorCode: error.code,
          errorDetails: error.details,
          errorHint: error.hint,
          txHash: data.tx_hash,
          tokenAddress: data.token_address,
          fullError: error
        });
        return { success: false, error: error.message };
      }

      swapLogger.info('Trade history stored successfully', {
        tradeId: result.id,
        txHash: data.tx_hash,
        tokenAddress: data.token_address,
        tradeType: data.trade_type,
        dexType: data.dex_type
      });

      return { success: true, data: result };
    } catch (error: any) {
      swapLogger.error('Unexpected error storing trade history', {
        error: error.message,
        txHash: data.tx_hash,
        tokenAddress: data.token_address
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Update trade execution status
   */
  async updateTradeStatus(
    txHash: string,
    status: 'pending' | 'completed' | 'failed',
    gasFee?: number
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // Check if Supabase is configured
      if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
        swapLogger.warn('Supabase not configured, skipping trade status update');
        return { success: true, data: { tx_hash: txHash } };
      }

      const updateData: any = {
        execution_status: status,
        updated_at: new Date().toISOString()
      };

      if (gasFee !== undefined) {
        updateData.gas_fee = gasFee;
      }

      const { data: result, error } = await supabase
        .from('trade_history')
        .update(updateData)
        .eq('tx_hash', txHash)
        .select()
        .single();

      if (error) {
        swapLogger.error('Failed to update trade status', {
          error: error.message,
          txHash,
          status
        });
        return { success: false, error: error.message };
      }

      return { success: true, data: result };
    } catch (error: any) {
      swapLogger.error('Unexpected error updating trade status', {
        error: error.message,
        txHash,
        status
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Get trade history for a user
   */
  async getUserTradeHistory(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      // Check if Supabase is configured
      if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
        swapLogger.warn('Supabase not configured, returning empty trade history');
        return { success: true, data: [] };
      }

      const { data: result, error } = await supabase
        .from('trade_history')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        swapLogger.error('Failed to get user trade history', {
          error: error.message,
          userId
        });
        return { success: false, error: error.message };
      }

      return { success: true, data: result };
    } catch (error: any) {
      swapLogger.error('Unexpected error getting user trade history', {
        error: error.message,
        userId
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Helper method to store swap trade history with formatted data
   */
  async storeSwapTradeHistory(
    userId: string,
    walletAddress: string,
    tokenAddress: string,
    tokenName: string,
    tokenSymbol: string,
    tokenImage: string | null,
    poolAddress: string,
    tradeType: 'buy' | 'sell',
    dexType: 'pumpfun' | 'pumpswap' | 'launchlab',
    tokenAmount: number,
    solAmount: number,
    price: number,
    txHash: string,
    slippage?: number,
    gasFee?: number
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.storeTradeHistory({
      user_id: userId,
      wallet_address: walletAddress,
      token_address: tokenAddress,
      token_name: tokenName,
      token_symbol: tokenSymbol,
      token_image: tokenImage,
      pool_address: poolAddress,
      trade_type: tradeType,
      dex_type: dexType,
      token_amount: tokenAmount,
      sol_amount: solAmount,
      price: price,
      tx_hash: txHash,
      execution_status: 'completed',
      slippage: slippage || 0.01,
      gas_fee: gasFee
    });
  }
}

export const tradeHistoryService = new TradeHistoryService();