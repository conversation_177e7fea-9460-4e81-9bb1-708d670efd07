import Queue from 'bull';
import { SwapRequest, QuoteRequest } from '../../types/pump.types';
import { swapLogger } from '../../utils/logger';
import { executeSwapTransaction } from '../pumpFun/pump.service';
import { getSwapQuote } from '../pumpFun/pump.service';

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
};

// Queue configurations
const queueOptions = {
  redis: redisConfig,
  defaultJobOptions: {
    removeOnComplete: 50, // Keep last 50 completed jobs
    removeOnFail: 100,    // Keep last 100 failed jobs
    attempts: 3,          // Retry failed jobs 3 times
    backoff: {
      type: 'exponential',
      delay: 2000,        // Start with 2 second delay
    },
  },
};

// Create separate queues for different operations
export const quoteQueue = new Queue('quote processing', queueOptions);
export const swapQueue = new Queue('swap processing', queueOptions);
export const prioritySwapQueue = new Queue('priority swap processing', queueOptions);

// Job types
export interface QuoteJob {
  id: string;
  request: QuoteRequest;
  timestamp: number;
  priority?: number;
}

export interface SwapJob {
  id: string;
  request: SwapRequest;
  timestamp: number;
  priority?: number;
  isUrgent?: boolean;
}

/**
 * Queue Service Class for managing transaction processing
 */
export class QueueService {
  private static instance: QueueService;
  
  private constructor() {
    this.setupQueueProcessors();
    this.setupQueueEvents();
  }
  
  public static getInstance(): QueueService {
    if (!QueueService.instance) {
      QueueService.instance = new QueueService();
    }
    return QueueService.instance;
  }
  
  /**
   * Set up queue processors
   */
  private setupQueueProcessors(): void {
    // Quote processing - High concurrency (20 concurrent jobs)
    quoteQueue.process(20, async (job) => {
      const { id, request } = job.data as QuoteJob;
      
      swapLogger.info('Processing quote job', { jobId: id, tokenAddress: request.tokenAddress });
      
      try {
        const startTime = Date.now();
        const result = await getSwapQuote(request);
        const processingTime = Date.now() - startTime;
        
        swapLogger.info('Quote job completed', { 
          jobId: id, 
          processingTime,
          success: true 
        });
        
        return {
          success: true,
          data: result,
          processingTime,
          timestamp: Date.now()
        };
      } catch (error) {
        swapLogger.error('Quote job failed', { 
          jobId: id, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
        throw error;
      }
    });
    
    // Regular swap processing - Medium concurrency (10 concurrent jobs)
    swapQueue.process(10, async (job) => {
      const { id, request } = job.data as SwapJob;
      
      swapLogger.info('Processing swap job', { jobId: id, tokenAddress: request.tokenAddress });
      
      try {
        const startTime = Date.now();
        const result = await executeSwapTransaction(request);
        const processingTime = Date.now() - startTime;
        
        swapLogger.info('Swap job completed', { 
          jobId: id, 
          processingTime,
          success: true,
          signature: result.signature 
        });
        
        return {
          success: true,
          data: result,
          processingTime,
          timestamp: Date.now()
        };
      } catch (error) {
        swapLogger.error('Swap job failed', { 
          jobId: id, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
        throw error;
      }
    });
    
    // Priority swap processing - Lower concurrency for better execution (5 concurrent jobs)
    prioritySwapQueue.process(5, async (job) => {
      const { id, request } = job.data as SwapJob;
      
      swapLogger.info('Processing priority swap job', { jobId: id, tokenAddress: request.tokenAddress });
      
      try {
        const startTime = Date.now();
        // Priority swaps get enhanced execution with better fees
        const result = await executeSwapTransaction({
          ...request,
          // Boost priority fee for faster execution
          priorityFee: Math.max(request.priorityFee || 0, 0.001),
          bribeAmount: Math.max(request.bribeAmount || 0, 0.0005)
        });
        const processingTime = Date.now() - startTime;
        
        swapLogger.info('Priority swap job completed', { 
          jobId: id, 
          processingTime,
          success: true,
          signature: result.signature 
        });
        
        return {
          success: true,
          data: result,
          processingTime,
          timestamp: Date.now()
        };
      } catch (error) {
        swapLogger.error('Priority swap job failed', { 
          jobId: id, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
        throw error;
      }
    });
  }
  
  /**
   * Set up queue event listeners
   */
  private setupQueueEvents(): void {
    // Quote queue events
    quoteQueue.on('completed', (job, result) => {
      swapLogger.info('Quote job completed successfully', { 
        jobId: job.id, 
        processingTime: result.processingTime 
      });
    });
    
    quoteQueue.on('failed', (job, err) => {
      swapLogger.error('Quote job failed', { 
        jobId: job.id, 
        error: err.message 
      });
    });
    
    // Swap queue events
    swapQueue.on('completed', (job, result) => {
      swapLogger.info('Swap job completed successfully', { 
        jobId: job.id, 
        transactionId: result.data?.transactionId,
        processingTime: result.processingTime 
      });
    });
    
    swapQueue.on('failed', (job, err) => {
      swapLogger.error('Swap job failed', { 
        jobId: job.id, 
        error: err.message 
      });
    });
    
    // Priority swap queue events
    prioritySwapQueue.on('completed', (job, result) => {
      swapLogger.info('Priority swap job completed successfully', { 
        jobId: job.id, 
        transactionId: result.data?.transactionId,
        processingTime: result.processingTime 
      });
    });
    
    prioritySwapQueue.on('failed', (job, err) => {
      swapLogger.error('Priority swap job failed', { 
        jobId: job.id, 
        error: err.message 
      });
    });
  }
  
  /**
   * Add quote request to queue
   */
  async addQuoteJob(request: QuoteRequest, priority: number = 0): Promise<string> {
    const jobId = `quote_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const job = await quoteQueue.add(
      'process-quote',
      {
        id: jobId,
        request,
        timestamp: Date.now(),
        priority
      } as QuoteJob,
      {
        priority: priority, // Higher priority = processed first
        delay: 0,
      }
    );
    
    swapLogger.info('Quote job added to queue', { jobId, tokenAddress: request.tokenAddress });
    return jobId;
  }
  
  /**
   * Add swap request to queue
   */
  async addSwapJob(request: SwapRequest, isUrgent: boolean = false): Promise<string> {
    const jobId = `swap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const targetQueue = isUrgent ? prioritySwapQueue : swapQueue;
    const queueName = isUrgent ? 'priority' : 'regular';
    
    const job = await targetQueue.add(
      'process-swap',
      {
        id: jobId,
        request,
        timestamp: Date.now(),
        isUrgent
      } as SwapJob,
      {
        priority: isUrgent ? 100 : 50, // Priority swaps get higher priority
        delay: 0,
      }
    );
    
    swapLogger.info('Swap job added to queue', { 
      jobId, 
      queueName,
      tokenAddress: request.tokenAddress,
      isUrgent 
    });
    return jobId;
  }
  
  /**
   * Get job status
   */
  async getJobStatus(jobId: string): Promise<any> {
    // Check all queues for the job
    const queues = [quoteQueue, swapQueue, prioritySwapQueue];
    
    for (const queue of queues) {
      try {
        const job = await queue.getJob(jobId);
        if (job) {
          const state = await job.getState();
          return {
            id: jobId,
            state,
            progress: job.progress(),
            data: job.data,
            result: job.returnvalue,
            failedReason: job.failedReason,
            processedOn: job.processedOn,
            finishedOn: job.finishedOn,
            timestamp: job.timestamp
          };
        }
      } catch (error) {
        // Continue checking other queues
      }
    }
    
    return null;
  }
  
  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<any> {
    const [quoteStats, swapStats, priorityStats] = await Promise.all([
      quoteQueue.getJobCounts(),
      swapQueue.getJobCounts(),
      prioritySwapQueue.getJobCounts()
    ]);
    
    return {
      quote: quoteStats,
      swap: swapStats,
      prioritySwap: priorityStats,
      timestamp: Date.now()
    };
  }
  
  /**
   * Clean up completed jobs (called periodically)
   */
  async cleanupJobs(): Promise<void> {
    const olderThan = 24 * 60 * 60 * 1000; // 24 hours
    
    await Promise.all([
      quoteQueue.clean(olderThan, 'completed'),
      quoteQueue.clean(olderThan, 'failed'),
      swapQueue.clean(olderThan, 'completed'),
      swapQueue.clean(olderThan, 'failed'),
      prioritySwapQueue.clean(olderThan, 'completed'),
      prioritySwapQueue.clean(olderThan, 'failed')
    ]);
    
    swapLogger.info('Queue cleanup completed');
  }
}

// Export singleton instance
export const queueService = QueueService.getInstance();

// Cleanup jobs every hour
setInterval(() => {
  queueService.cleanupJobs().catch(error => {
    swapLogger.error('Queue cleanup failed', { error: error.message });
  });
}, 60 * 60 * 1000);