import { Connection, PublicKey, Transaction } from '@solana/web3.js';
import { SwapRequest, QuoteResult, DexType } from '../../types/pump.types';
import { jitoService } from '../jito/jito.service';
import { createSwapTransaction } from '../pumpFun/pump.service';
import { executeCorrectedPumpFunSwap } from '../pumpFun/corrected-pump.service';
import { executeCorrectedPumpSwapSwap } from '../pumpFun/corrected-pumpswap.service';
import { config } from '../../config';

/**
 * Enhanced swap service with MEV protection and bribe capabilities
 */

export interface EnhancedSwapRequest extends SwapRequest {
  mevProtection?: boolean;
  bribeAmount?: number; // in lamports
  priorityLevel?: 'low' | 'medium' | 'high' | 'veryHigh';
  maxMevTip?: number; // maximum tip willing to pay for MEV protection
}

export interface EnhancedSwapResult {
  signature?: string;
  bundleId?: string; // If using Jito
  quoteResult: QuoteResult;
  mevProtected: boolean;
  tipAmount?: number;
  executionMethod: 'regular' | 'jito' | 'privy';
}

/**
 * Execute a swap with optional MEV protection and bribe
 */
export async function executeEnhancedSwap(
  swapRequest: EnhancedSwapRequest
): Promise<EnhancedSwapResult> {
  console.log('Executing enhanced swap with MEV protection options...');

  const {
    mevProtection = false,
    bribeAmount,
    priorityLevel = 'medium',
    maxMevTip = 5000000, // 0.005 SOL default max
    ...baseSwapRequest
  } = swapRequest;

  // Calculate trade value for MEV recommendations
  const tradeValueSol = baseSwapRequest.amount;

  // Determine if MEV protection should be used
  const shouldUseMEV = determineMEVUsage(
    mevProtection,
    tradeValueSol,
    baseSwapRequest.slippage || 0.01
  );

  // Calculate tip amount
  const tipAmount = calculateTipAmount(
    bribeAmount,
    tradeValueSol,
    priorityLevel,
    maxMevTip
  );

  console.log(`Trade analysis:`);
  console.log(`- Trade value: ${tradeValueSol} SOL`);
  console.log(`- MEV protection requested: ${mevProtection}`);
  console.log(`- Should use MEV: ${shouldUseMEV}`);
  console.log(`- Tip amount: ${tipAmount} lamports (${tipAmount / 1_000_000_000} SOL)`);

  try {
    // Force regular execution when mevProtection is explicitly false
    if (mevProtection === false) {
      console.log('🚫 MEV Protection explicitly disabled - using regular execution');
      return await executeRegular(baseSwapRequest);
    }

    if (shouldUseMEV && tipAmount > 0) {
      // Use Jito for MEV protection
      console.log('🚀 Attempting Jito execution with MEV protection...');
      console.log(`- Trade value: ${tradeValueSol} SOL`);
      console.log(`- Tip amount: ${tipAmount} lamports (${tipAmount / 1_000_000_000} SOL)`);
      console.log(`- Priority level: ${priorityLevel}`);

      try {
        const jitoResult = await executeWithJito(baseSwapRequest, tipAmount);
        console.log('✅ Jito execution successful!');
        return jitoResult;
      } catch (jitoError: any) {
        console.error('❌ Jito execution failed:', jitoError.message);
        console.error('Full Jito error:', jitoError);
        console.log('🔄 Falling back to regular Privy execution...');

        // Fallback to regular execution
        const regularResult = await executeRegular(baseSwapRequest);

        // Mark as MEV protected in response even though we fell back
        return {
          ...regularResult,
          mevProtected: true,
          tipAmount,
          executionMethod: 'privy' // Indicate fallback
        };
      }
    } else {
      // Use regular execution (existing implementation)
      console.log('Using regular execution (MEV not requested or tip amount is 0)');
      return await executeRegular(baseSwapRequest);
    }
  } catch (error: any) {
    console.error('Enhanced swap execution failed completely:', error);
    throw error;
  }
}

/**
 * Execute swap with Jito MEV protection
 */
async function executeWithJito(
  swapRequest: SwapRequest,
  tipAmount: number
): Promise<EnhancedSwapResult> {
  console.log('🔥 executeWithJito: Starting Jito MEV protection execution...');
  console.log(`🔥 executeWithJito: DEX Type: ${swapRequest.dexType}`);
  console.log(`🔥 executeWithJito: Tip Amount: ${tipAmount} lamports`);

  try {
    // For PumpFun, create transaction and submit to Jito
    if (swapRequest.dexType === DexType.PumpFun) {
      console.log('🔥 executeWithJito: Processing PumpFun swap...');

      // Use the corrected PumpFun implementation to create the transaction
      const { executeCorrectedPumpFunSwap } = await import('../pumpFun/corrected-pump.service');

      console.log('🔥 executeWithJito: Creating PumpFun transaction (executeTransaction=false)...');
      // Create the transaction without executing it
      const result = await executeCorrectedPumpFunSwap(swapRequest, false); // false = don't execute, just create

      if (!result.transaction) {
        throw new Error('Failed to create PumpFun transaction for Jito submission');
      }

      console.log('🔥 executeWithJito: Transaction created successfully');
      console.log(`🔥 executeWithJito: Transaction length: ${result.transaction.length} characters`);

      // Parse the transaction from base64
      console.log('🔥 executeWithJito: Parsing transaction from base64...');
      const txBuffer = Buffer.from(result.transaction, 'base64');
      const parsedTransaction = Transaction.from(txBuffer);
      console.log(`🔥 executeWithJito: Transaction parsed, instructions: ${parsedTransaction.instructions.length}`);

      // Add Jito tip instruction to the user's transaction
      const userPublicKey = new PublicKey(swapRequest.walletAddress);
      const transactionWithTip = jitoService.addJitoTipToTransaction(
        parsedTransaction,
        tipAmount,
        userPublicKey
      );
      console.log(`🔥 executeWithJito: Added Jito tip instruction, total instructions: ${transactionWithTip.instructions.length}`);

      // Submit to Jito (user will pay the tip)
      console.log('🔥 executeWithJito: Submitting bundle to Jito with user-paid tip...');

      const bundleId = await jitoService.submitBundle(transactionWithTip);

      console.log(`🔥 executeWithJito: ✅ SUCCESS! Bundle submitted to Jito. Bundle ID: ${bundleId}`);

      return {
        bundleId,
        quoteResult: result.quoteResult,
        mevProtected: true,
        tipAmount,
        executionMethod: 'jito'
      };
    }

    // For PumpSwap, create transaction and submit to Jito
    if (swapRequest.dexType === DexType.PumpSwap) {
      console.log('🔥 executeWithJito: Processing PumpSwap swap...');

      // Use the corrected PumpSwap implementation to create the transaction
      console.log('🔥 executeWithJito: Creating PumpSwap transaction (executeTransaction=false)...');
      // Create the transaction without executing it
      const result = await executeCorrectedPumpSwapSwap(swapRequest, false); // false = don't execute, just create

      if (!result.transaction) {
        throw new Error('Failed to create PumpSwap transaction for Jito submission');
      }

      console.log('🔥 executeWithJito: PumpSwap transaction created successfully');
      console.log(`🔥 executeWithJito: Transaction length: ${result.transaction.length} characters`);

      // Parse the transaction from base64
      console.log('🔥 executeWithJito: Parsing PumpSwap transaction from base64...');
      const txBuffer = Buffer.from(result.transaction, 'base64');
      const parsedTransaction = Transaction.from(txBuffer);
      console.log(`🔥 executeWithJito: PumpSwap transaction parsed, instructions: ${parsedTransaction.instructions.length}`);

      // Add Jito tip instruction to the user's transaction
      const userPublicKey = new PublicKey(swapRequest.walletAddress);
      const transactionWithTip = jitoService.addJitoTipToTransaction(
        parsedTransaction,
        tipAmount,
        userPublicKey
      );
      console.log(`🔥 executeWithJito: Added Jito tip instruction to PumpSwap, total instructions: ${transactionWithTip.instructions.length}`);

      // Submit to Jito (user will pay the tip)
      console.log('🔥 executeWithJito: Submitting PumpSwap bundle to Jito with user-paid tip...');

      const bundleId = await jitoService.submitBundle(transactionWithTip);

      console.log(`🔥 executeWithJito: ✅ SUCCESS! PumpSwap bundle submitted to Jito. Bundle ID: ${bundleId}`);

      return {
        bundleId,
        quoteResult: result.quoteResult,
        mevProtected: true,
        tipAmount,
        executionMethod: 'jito'
      };
    }

    // For other DEX types (fallback to generic implementation)
    console.log('🔥 executeWithJito: Processing other DEX type...');
    const { transaction, quoteResult } = await createSwapTransaction(
      swapRequest,
      false // Don't use SDK
    );

    // Parse the transaction from base64
    const txBuffer = Buffer.from(transaction, 'base64');
    const parsedTransaction = Transaction.from(txBuffer);

    // Add Jito tip instruction to the user's transaction
    const userPublicKey = new PublicKey(swapRequest.walletAddress);
    const transactionWithTip = jitoService.addJitoTipToTransaction(
      parsedTransaction,
      tipAmount,
      userPublicKey
    );
    console.log(`🔥 executeWithJito: Added Jito tip instruction to other DEX, total instructions: ${transactionWithTip.instructions.length}`);

    // Submit to Jito (user will pay the tip)
    const bundleId = await jitoService.submitBundle(transactionWithTip);

    console.log(`🔥 executeWithJito: Other DEX swap submitted to Jito. Bundle ID: ${bundleId}`);

    return {
      bundleId,
      quoteResult,
      mevProtected: true,
      tipAmount,
      executionMethod: 'jito'
    };

  } catch (error: any) {
    console.error('Jito execution failed:', error);
    throw new Error(`MEV-protected swap failed: ${error.message}`);
  }
}

/**
 * Execute swap using regular method (existing implementation)
 */
async function executeRegular(
  swapRequest: SwapRequest
): Promise<EnhancedSwapResult> {
  console.log('Executing swap with regular method...');

  try {
    if (swapRequest.dexType === DexType.PumpFun) {
      // Use corrected PumpFun implementation
      const result = await executeCorrectedPumpFunSwap(swapRequest);
      return {
        signature: result.signature,
        quoteResult: result.quoteResult,
        mevProtected: false,
        executionMethod: 'privy'
      };
    } else if (swapRequest.dexType === DexType.PumpSwap) {
      // Use corrected PumpSwap implementation
      const result = await executeCorrectedPumpSwapSwap(swapRequest);
      return {
        signature: result.signature,
        quoteResult: result.quoteResult,
        mevProtected: false,
        executionMethod: 'privy'
      };
    } else {
      // Use regular swap transaction for other DEX types
      const { transaction, quoteResult } = await createSwapTransaction(
        swapRequest,
        false // Don't use SDK
      );

      // This would need integration with your existing Privy signing
      // For now, return the transaction for client-side signing
      return {
        signature: 'transaction_created', // Placeholder
        quoteResult,
        mevProtected: false,
        executionMethod: 'regular'
      };
    }
  } catch (error: any) {
    console.error('Regular execution failed:', error);
    throw error;
  }
}

/**
 * Determine if MEV protection should be used
 */
function determineMEVUsage(
  mevRequested: boolean,
  tradeValueSol: number,
  slippage: number
): boolean {
  // Always respect user's explicit choice - if false, never use MEV
  if (mevRequested === false) {
    console.log('🚫 MEV Protection explicitly disabled by user');
    return false;
  }

  // If explicitly true, always use MEV
  if (mevRequested === true) {
    console.log('✅ MEV Protection explicitly enabled by user');
    return true;
  }

  // If undefined, auto-recommend MEV protection based on trade characteristics
  const autoRecommended = jitoService.isMEVProtectionRecommended(tradeValueSol, slippage);
  console.log(`🤖 Auto-recommendation for MEV protection: ${autoRecommended}`);
  return autoRecommended;
}

/**
 * Calculate appropriate tip amount
 */
function calculateTipAmount(
  requestedBribe: number | undefined,
  tradeValueSol: number,
  priorityLevel: string,
  maxTip: number
): number {
  // If user specified a bribe amount, use it (capped at maxTip)
  if (requestedBribe !== undefined) {
    return Math.min(requestedBribe, maxTip);
  }

  // Calculate based on priority level and trade size
  let baseTip = jitoService.calculateRecommendedTip(tradeValueSol);

  // Adjust based on priority level
  const priorityMultipliers = {
    low: 0.5,
    medium: 1.0,
    high: 1.5,
    veryHigh: 2.0
  };

  const multiplier = priorityMultipliers[priorityLevel as keyof typeof priorityMultipliers] || 1.0;
  const adjustedTip = Math.floor(baseTip * multiplier);

  // Set minimum tip amount for transactions (even when MEV is off)
  const minimumTip = 100000; // 0.0001 SOL minimum tip
  const finalTip = Math.max(adjustedTip, minimumTip);

  // Cap at maxTip
  return Math.min(finalTip, maxTip);
}

/**
 * Get MEV protection recommendation for a trade
 */
export function getMEVRecommendation(
  tradeValueSol: number,
  slippage: number
): {
  recommended: boolean;
  tipAmount: number;
  reason: string;
  costPercentage: number;
} {
  const recommended = jitoService.isMEVProtectionRecommended(tradeValueSol, slippage);
  const tipAmount = jitoService.calculateRecommendedTip(tradeValueSol);
  const costPercentage = (tipAmount / 1_000_000_000) / tradeValueSol * 100;

  let reason = '';
  if (tradeValueSol > 1) {
    reason = 'Large trade size - MEV protection recommended';
  } else if (slippage > 0.01) {
    reason = 'High slippage tolerance - MEV protection recommended';
  } else if (costPercentage < 1) {
    reason = 'Low cost relative to trade size - MEV protection recommended';
  } else {
    reason = 'Small trade size - MEV protection may not be cost effective';
  }

  return {
    recommended,
    tipAmount,
    reason,
    costPercentage
  };
}
