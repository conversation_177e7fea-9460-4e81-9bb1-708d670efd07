import { Connection, PublicKey, Transaction } from '@solana/web3.js';
import { SwapRequest, SwapDirection, QuoteResult } from '../../types/pump.types';
import { config } from '../../config';
import { 
  checkPumpSwapPool, 
  calculatePumpSwapQuote, 
  createPumpSwapSwapTransaction 
} from './pumpSwap.service';
import { getMint } from '@solana/spl-token';
import { swapLogger } from '../../utils/logger';

/**
 * Corrected PumpSwap service with Privy wallet signing and proper fee handling
 * This service mirrors the corrected PumpFun implementation for consistency
 */

/**
 * Execute a PumpSwap swap with Privy wallet signing (same approach as corrected PumpFun)
 * @param swapRequest Swap request
 * @param executeTransaction Whether to execute the transaction or just create it
 * @returns Transaction signature and quote result
 */
export async function executeCorrectedPumpSwapSwap(
  swapRequest: SwapRequest,
  executeTransaction: boolean = true
): Promise<{
  signature?: string;
  transaction?: string;
  quoteResult: QuoteResult;
}> {
  try {
    swapLogger.info('Starting corrected PumpSwap execution with Privy signing', { 
      executeTransaction, 
      direction: swapRequest.direction,
      amount: swapRequest.amount,
      tokenAddress: swapRequest.tokenAddress,
      poolAddress: swapRequest.poolAddress,
      walletAddress: swapRequest.walletAddress.substring(0, 8) + '...' // Log only first few chars for privacy
    });

    if (!config.rpcUrl) {
      const error = new Error('SOLANA_RPC_URL environment variable is not set');
      swapLogger.error('Missing RPC URL configuration', { error: error.message });
      throw error;
    }

    const connection = new Connection(config.rpcUrl, 'confirmed');
    let transaction: Transaction;
    let quoteResult: QuoteResult;
    let tokenDecimals = 0;
    const tokenMint = new PublicKey(swapRequest.tokenAddress);
    
    // Get token decimals
    try {
      const mintInfo = await getMint(connection, tokenMint);
      tokenDecimals = mintInfo.decimals;

      swapLogger.debug('Token details retrieved', { tokenDecimals, tokenMint: tokenMint.toString() });
    } catch (error: any) {
      swapLogger.error('Failed to retrieve token mint information', { 
        error: error.message, 
        tokenAddress: swapRequest.tokenAddress,
        stack: error.stack 
      });
      throw new Error(`Failed to retrieve token details: ${error.message}`);
    }

    // Get user wallet public key
    const userWallet = new PublicKey(swapRequest.walletAddress);
    let userBalance = 0;

    // Check user's SOL balance before proceeding
    try {
      userBalance = await connection.getBalance(userWallet);
      swapLogger.debug('User SOL balance retrieved', { 
        userBalance, 
        balanceSOL: userBalance / 1e9 
      });
    } catch (error: any) {
      swapLogger.error('Failed to retrieve user SOL balance', { 
        error: error.message, 
        walletAddress: swapRequest.walletAddress.substring(0, 8) + '...',
        stack: error.stack 
      });
      throw new Error(`Failed to check wallet balance: ${error.message}`);
    }

    if (swapRequest.direction === SwapDirection.Buy) {
      // For buy: SOL -> Token
      swapLogger.info('Processing BUY transaction');

      // Check pool and get quote
      const poolAddress = new PublicKey(swapRequest.poolAddress);
      const poolData = await checkPumpSwapPool(connection, poolAddress);

      if (!poolData.exists) {
        throw new Error(`PumpSwap pool not found: ${poolAddress.toString()}`);
      }

      // Calculate quote using PumpSwap algorithm
      quoteResult = await calculatePumpSwapQuote(
        connection,
        poolData,
        swapRequest.amount,
        true, // isBuy
        tokenDecimals,
        (swapRequest.slippage || 0.05) * 100 // Convert to percentage
      );

      swapLogger.info('Buy quote retrieved', { 
        input: `${swapRequest.amount} SOL`, 
        output: `${quoteResult.outAmount} tokens`
      });

      // Calculate required balance for buy transaction
      const swapAmountLamports = BigInt(Math.floor(swapRequest.amount * 1e9)); // Convert SOL to lamports
      const platformFeeLamports = (swapAmountLamports * BigInt(100)) / BigInt(10000); // 1% platform fee
      const ataCreationCost = BigInt(2039280); // Cost to create Associated Token Account
      const transactionFees = BigInt(10000); // Estimated transaction fees
      const bufferAmount = BigInt(5000000); // Buffer for wrapped SOL operations
      const totalRequiredLamports = swapAmountLamports + platformFeeLamports + ataCreationCost + transactionFees + bufferAmount;

      swapLogger.info('Balance check for buy transaction', {
        swapAmount: `${swapAmountLamports} lamports (${swapRequest.amount} SOL)`,
        platformFee: `${platformFeeLamports} lamports`,
        ataCreation: `${ataCreationCost} lamports`,
        transactionFees: `${transactionFees} lamports`,
        buffer: `${bufferAmount} lamports`,
        totalRequired: `${totalRequiredLamports} lamports (${Number(totalRequiredLamports) / 1e9} SOL)`
      });
      swapLogger.info('User balance', { 
        userBalance, 
        balanceSOL: userBalance / 1e9 
      });

      if (BigInt(userBalance) < totalRequiredLamports) {
        const shortfall = totalRequiredLamports - BigInt(userBalance);
        const shortfallSOL = Number(shortfall) / 1e9;
        const requiredSOL = Number(totalRequiredLamports) / 1e9;

        throw new Error(
          `Insufficient SOL balance for PumpSwap transaction. ` +
          `Required: ${requiredSOL.toFixed(6)} SOL, Available: ${(userBalance / 1e9).toFixed(6)} SOL. ` +
          `Please add ${shortfallSOL.toFixed(6)} SOL to your wallet. ` +
          `Note: Small swaps require significant SOL for account creation (~0.002 SOL) and fees.`
        );
      }

      // Create transaction with user as fee payer (for Privy signing)
      transaction = await createPumpSwapSwapTransaction(
        connection,
        poolAddress,
        tokenMint,
        userWallet, // User pays fees (will be signed via Privy)
        config.platformFeeWallet, // Platform fee wallet
        swapRequest.amount,
        true, // isBuy
        swapRequest.slippage || 0.05,
        quoteResult,
        poolData,
        tokenDecimals
      );

    } else {
      // For sell: Token -> SOL
      swapLogger.info('Processing SELL transaction');

      // Check pool and get quote
      const poolAddress = new PublicKey(swapRequest.poolAddress);
      const poolData = await checkPumpSwapPool(connection, poolAddress);

      if (!poolData.exists) {
        throw new Error(`PumpSwap pool not found: ${poolAddress.toString()}`);
      }

      // Calculate quote using PumpSwap algorithm
      quoteResult = await calculatePumpSwapQuote(
        connection,
        poolData,
        swapRequest.amount,
        false, // isBuy = false for sell
        tokenDecimals,
        (swapRequest.slippage || 0.05) * 100 // Convert to percentage
      );

      swapLogger.info('Sell quote retrieved', { 
        input: `${swapRequest.amount} tokens`, 
        output: `${quoteResult.outAmount} SOL`
      });

      // STEP 1: Validate user's token balance before proceeding
      swapLogger.info('TOKEN BALANCE VALIDATION');
      swapLogger.info('Checking token balance for user', { walletAddress: userWallet.toString() });
      swapLogger.info('Token mint', { tokenMint: tokenMint.toString() });
      swapLogger.info('Requested sell amount', { amount: swapRequest.amount });

      try {
        // Get user's token account
        const { getAssociatedTokenAddress } = await import('@solana/spl-token');
        const userTokenAccount = await getAssociatedTokenAddress(tokenMint, userWallet);
        swapLogger.info('User token account', { userTokenAccount: userTokenAccount.toString() });

        // Check if token account exists
        const tokenAccountInfo = await connection.getAccountInfo(userTokenAccount);
        if (!tokenAccountInfo) {
          throw new Error(`Token account does not exist for user ${userWallet.toString()}. User has no tokens to sell.`);
        }

        // Get the user's current token balance
        const tokenBalance = await connection.getTokenAccountBalance(userTokenAccount);
        const currentBalance = parseFloat(tokenBalance.value.uiAmountString || '0');

        swapLogger.info('Current token balance', { currentBalance: currentBalance });
        swapLogger.info('Token balance (raw)', { amount: tokenBalance.value.amount });
        swapLogger.info('Token decimals', { decimals: tokenBalance.value.decimals });

        // Validate that user has sufficient tokens with tolerance for floating-point precision
        const tolerance = 0.000001; // 1 microtoken tolerance
        if (currentBalance < (swapRequest.amount - tolerance)) {
          const shortfall = swapRequest.amount - currentBalance;
          const errorMessage = `Insufficient token balance: User has ${currentBalance} tokens, requested to sell ${swapRequest.amount} tokens. Shortfall: ${shortfall.toFixed(6)} tokens.`;
          swapLogger.error(errorMessage);
          throw new Error(errorMessage);
        }

        // If the amount is very close to the balance (within tolerance), use the exact balance
        if (Math.abs(currentBalance - swapRequest.amount) <= tolerance && currentBalance < swapRequest.amount) {
          swapLogger.info('Adjusting sell amount from', { originalAmount: swapRequest.amount, adjustedAmount: currentBalance });
          swapRequest.amount = currentBalance;
        }

        swapLogger.info('Token balance validation passed', { 
          currentBalance, 
          sellingAmount: swapRequest.amount, 
          remainingBalance: `${(currentBalance - swapRequest.amount).toFixed(6)} tokens`
        });

      } catch (error: any) {
        swapLogger.error('Token balance validation failed', { error: error.message });
        throw error;
      }

      // Calculate required balance for sell transaction
      const tokenValueInSol = swapRequest.amount * quoteResult.price; // SOL value of tokens being sold
      const platformFeeLamports = BigInt(Math.floor(tokenValueInSol * 1e9 * 0.01)); // 1% platform fee
      const ataCreationCost = BigInt(2039280); // Cost for wrapped SOL account if needed
      const transactionFees = BigInt(10000); // Estimated transaction fees
      const totalRequiredLamports = platformFeeLamports + ataCreationCost + transactionFees;

      swapLogger.info('Balance check for sell transaction', {
        tokenValue: `${tokenValueInSol} SOL`,
        platformFee: `${platformFeeLamports} lamports`,
        ataCreation: `${ataCreationCost} lamports`,
        transactionFees: `${transactionFees} lamports`,
        totalRequired: `${totalRequiredLamports} lamports (${Number(totalRequiredLamports) / 1e9} SOL)`
      });
      swapLogger.info('User balance', { 
        userBalance, 
        balanceSOL: userBalance / 1e9 
      });

      if (BigInt(userBalance) < totalRequiredLamports) {
        const shortfall = totalRequiredLamports - BigInt(userBalance);
        const shortfallSOL = Number(shortfall) / 1e9;
        const requiredSOL = Number(totalRequiredLamports) / 1e9;

        throw new Error(
          `Insufficient SOL balance for PumpSwap sell transaction. ` +
          `Required: ${requiredSOL.toFixed(6)} SOL, Available: ${(userBalance / 1e9).toFixed(6)} SOL. ` +
          `Please add ${shortfallSOL.toFixed(6)} SOL to your wallet for transaction fees and account creation.`
        );
      }

      // Create transaction with user as fee payer (for Privy signing)
      transaction = await createPumpSwapSwapTransaction(
        connection,
        poolAddress,
        tokenMint,
        userWallet, // User pays fees (will be signed via Privy)
        config.platformFeeWallet, // Platform fee wallet
        swapRequest.amount,
        false, // isBuy = false for sell
        swapRequest.slippage || 0.05,
        quoteResult,
        poolData,
        tokenDecimals
      );
    }

    // Get fresh blockhash
    const { blockhash } = await connection.getLatestBlockhash('finalized');
    transaction.recentBlockhash = blockhash;

    const operationType = swapRequest.direction === SwapDirection.Buy ? 'buy' : 'sell';
    swapLogger.info('Transaction created successfully', {
      instructionCount: transaction.instructions.length,
      operationType,
      feePayer: transaction.feePayer?.toString(),
      recentBlockhash: transaction.recentBlockhash
    });

    // If executeTransaction is false, just return the transaction for external execution (e.g., Jito)
    if (!executeTransaction) {
      const serializedTransaction = transaction.serialize({ requireAllSignatures: false }).toString('base64');
      swapLogger.info('Transaction created for external execution');
      return { transaction: serializedTransaction, quoteResult };
    }

    // Since PumpSwap requires user to be a signer, we need to use Privy session signing
    if (!swapRequest.walletId) {
      throw new Error('Wallet ID is required for PumpSwap transactions with Privy session signing');
    }

    // User will sign via Privy, platform covers gas costs through reimbursement
    swapLogger.info('User will sign via Privy, platform covers gas costs through reimbursement');
    // Import the Privy signing function
    const { signAndSendTransactionWithPrivy } = await import('../privy/proper-privy.service');

    // Serialize transaction for Privy (user will sign, user pays gas but platform reimburses)
    const serializedTransaction = transaction.serialize({ requireAllSignatures: false }).toString('base64');
    swapLogger.info('Transaction serialized for Privy session signing');

    // Sign and send with Privy (user signs and pays gas, platform reimburses off-chain)
    // IMPORTANT: Don't pass connection to prevent blockhash modification in Privy service
    swapLogger.info(`Signing transaction with Privy for wallet: ${swapRequest.walletAddress}`);
    const signature = await signAndSendTransactionWithPrivy(swapRequest.walletId, serializedTransaction);

    swapLogger.info('Transaction signed and sent successfully with Privy:', { signature });
    return { signature, quoteResult };
  } catch (error: any) {
    swapLogger.error('Error in executeCorrectedPumpSwapSwap', { error: error.message, stack: error.stack });
    throw error;
  }
}

/**
 * Get a quote and pool status for a PumpSwap swap (external helper function)
 */
export async function getPumpSwapQuoteAndStatus(
  swapRequest: SwapRequest
): Promise<{
  quoteResult: QuoteResult;
  poolStatus: any;
}> {
  try {
    swapLogger.info('Getting PumpSwap quote and pool status');

    if (!process.env.SOLANA_RPC_URL) {
      const error = new Error('SOLANA_RPC_URL environment variable is not set');
      swapLogger.error('Missing RPC URL configuration', { error: error.message });
      throw error;
    }

    const connection = new Connection(process.env.SOLANA_RPC_URL, 'confirmed');
    const tokenMint = new PublicKey(swapRequest.tokenAddress);
    const poolAddress = new PublicKey(swapRequest.poolAddress);

    // Get token decimals
    let tokenDecimals = 0;
    try {
      const mintInfo = await getMint(connection, tokenMint);
      tokenDecimals = mintInfo.decimals;
    } catch (error: any) {
      swapLogger.error('Failed to retrieve token mint information', { 
        error: error.message, 
        tokenAddress: swapRequest.tokenAddress,
        stack: error.stack 
      });
      throw new Error(`Failed to retrieve token details: ${error.message}`);
    }

    // Check pool and get status
    const poolData = await checkPumpSwapPool(connection, poolAddress);
    
    if (!poolData.exists) {
      const error = new Error(`PumpSwap pool not found: ${poolAddress.toString()}`);
      swapLogger.error('PumpSwap pool not found', {
        poolAddress: poolAddress.toString(),
        tokenAddress: swapRequest.tokenAddress
      });
      throw error;
    }

    // Calculate quote using PumpSwap algorithm
    const quoteResult = await calculatePumpSwapQuote(
      connection,
      poolData,
      swapRequest.amount,
      swapRequest.direction === SwapDirection.Buy, // isBuy
      tokenDecimals,
      (swapRequest.slippage || 0.05) * 100 // Convert to percentage
    );

    swapLogger.info('Quote and status retrieved successfully', {
      direction: swapRequest.direction,
      inputAmount: swapRequest.amount,
      outputAmount: quoteResult.outAmount,
      price: quoteResult.price
    });

    return { quoteResult, poolStatus: poolData };
  } catch (error: any) {
    swapLogger.error('Error getting PumpSwap quote and status', {
      error: error.message,
      tokenAddress: swapRequest.tokenAddress,
      poolAddress: swapRequest.poolAddress,
      stack: error.stack
    });
    throw error;
  }
}
