import { Connection, PublicKey, Transaction, TransactionInstruction, SystemProgram, ComputeBudgetProgram } from '@solana/web3.js';
import {
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction,
  createSyncNativeInstruction,
  NATIVE_MINT,
  getAccount
} from '@solana/spl-token';
import { BN } from '@coral-xyz/anchor';
import { PumpSwapPoolData, QuoteResult, PoolStatus } from '../../types/pump.types';
import {
  derivePumpSwapCreatorVaultPDA,
  derivePumpSwapEventAuthorityPDA,
  derivePumpSwapGlobalConfigPDA,
  derivePumpSwapCoinCreatorVaultAuthorityPDA,
  fetchPumpSwapFeeRecipient
} from './pda.utils';

import {
  SOL_DECIMALS,
  solToLamports,
  tokenToRaw,
  rawToToken,
  lamportsToSol,
  calculatePlatformFee
} from './utils.service';
import {
  PUMP_SWAP_PROGRAM_ID,
  PUMP_SWAP_BUY_DISCRIMINATOR,
  PUMP_SWAP_SELL_DISCRIMINATOR
} from '../../utils/idl.utils';
import {
  GlobalConfig,
  buyQuoteInputInternal,
  sellBaseInputInternal
} from './sdk-utils';

// Discriminator constants for PumpSwap instructions
const BUY_DISCRIMINATOR = PUMP_SWAP_BUY_DISCRIMINATOR;
const SELL_DISCRIMINATOR = PUMP_SWAP_SELL_DISCRIMINATOR;

// SPL Token Program ID constant
const SPL_TOKEN_PROGRAM_ID = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');

/**
 * Fetch global config for dynamic fee rates
 */
async function fetchGlobalConfig(connection: Connection): Promise<GlobalConfig> {
  try {
    const globalConfigPDA = derivePumpSwapGlobalConfigPDA();
    console.log(`🔍 [CONFIG] Fetching global config from: ${globalConfigPDA.toString()}`);

    const accountInfo = await connection.getAccountInfo(globalConfigPDA);
    if (!accountInfo) {
      throw new Error('Global config account not found');
    }

    console.log(`✅ [CONFIG] Global config account found, data length: ${accountInfo.data.length} bytes`);

    // Parse the global config data manually
    // Based on the IDL structure:
    // - discriminator: 8 bytes
    // - lpFeeBasisPoints: 8 bytes (u64)
    // - protocolFeeBasisPoints: 8 bytes (u64)
    // - coinCreatorFeeBasisPoints: 8 bytes (u64)
    // - protocolFeeRecipients: Vec<Pubkey> (4 bytes length + 32 bytes per pubkey)
    // - admin: 32 bytes (Pubkey)

    const data = accountInfo.data;
    let offset = 8; // Skip discriminator

    // Parse fee basis points (convert bigint to string first, then to BN)
    const lpFeeBasisPoints = new BN(data.readBigUInt64LE(offset).toString());
    offset += 8;

    const protocolFeeBasisPoints = new BN(data.readBigUInt64LE(offset).toString());
    offset += 8;

    const coinCreatorFeeBasisPoints = new BN(data.readBigUInt64LE(offset).toString());
    offset += 8;

    // Parse protocol fee recipients vector
    const recipientsLength = data.readUInt32LE(offset);
    offset += 4;

    const protocolFeeRecipients: PublicKey[] = [];
    for (let i = 0; i < recipientsLength; i++) {
      const recipientBytes = data.subarray(offset, offset + 32);
      protocolFeeRecipients.push(new PublicKey(recipientBytes));
      offset += 32;
    }

    // Parse admin
    const adminBytes = data.subarray(offset, offset + 32);
    const admin = new PublicKey(adminBytes);

    console.log(`✅ [CONFIG] Parsed global config:`);
    console.log(`  - LP Fee: ${lpFeeBasisPoints.toString()} basis points`);
    console.log(`  - Protocol Fee: ${protocolFeeBasisPoints.toString()} basis points`);
    console.log(`  - Coin Creator Fee: ${coinCreatorFeeBasisPoints.toString()} basis points`);
    console.log(`  - Protocol Fee Recipients: ${protocolFeeRecipients.map(r => r.toString()).join(', ')}`);
    console.log(`  - Admin: ${admin.toString()}`);

    return {
      lpFeeBasisPoints,
      protocolFeeBasisPoints,
      coinCreatorFeeBasisPoints,
      protocolFeeRecipients,
      admin
    };
  } catch (error) {
    console.warn('⚠️ [CONFIG] Failed to parse global config, using defaults:', error);
    // Fallback to default values (25 basis points = 0.25%)
    return {
      lpFeeBasisPoints: new BN(25),
      protocolFeeBasisPoints: new BN(0),
      coinCreatorFeeBasisPoints: new BN(0),
      protocolFeeRecipients: [],
      admin: PublicKey.default
    };
  }
}

/**
 * Derives the coin creator vault authority PDA for a PumpSwap pool
 * Uses dynamic PDA derivation instead of hardcoded values
 *
 * Note: This implementation no longer requires the poolMint, coinCreator, or programId parameters
 * as it uses a simplified approach with our utility function.
 *
 * @returns The derived vault authority PDA and its bump seed
 */
export async function deriveCoinCreatorVaultAuthority(): Promise<{ vaultAuthority: PublicKey; bump: number }> {
  // Use the creator vault PDA from our utility function
  const vaultAuthority = derivePumpSwapCreatorVaultPDA();

  // For compatibility with existing code, we still need to return a bump
  // Since we're not using findProgramAddress directly, we'll use 255 as a placeholder
  const bump = 255;

  return { vaultAuthority, bump };
}

/**
 * Fetches the protocol fee recipient address from the on-chain global config
 * Uses dynamic PDA derivation instead of hardcoded values
 * @param connection - Solana connection
 * @returns The protocol fee recipient public key and the global config PDA
 */
export async function fetchProtocolFeeRecipient(
  connection: Connection
): Promise<{ protocolFeeRecipient: PublicKey; globalConfigPda: PublicKey }> {
  try {
    // Use the global config PDA from our utility function
    const globalConfigPda = derivePumpSwapGlobalConfigPDA();
    console.log(`Derived global config PDA: ${globalConfigPda.toString()}`);

    // Use our utility function to fetch the fee recipient
    const protocolFeeRecipient = await fetchPumpSwapFeeRecipient(connection);
    console.log(`Using protocol fee recipient: ${protocolFeeRecipient.toString()}`);
    console.log(`Using global config PDA: ${globalConfigPda.toString()}`);

    return { protocolFeeRecipient, globalConfigPda };
  } catch (error) {
    console.error('Error deriving protocol fee recipient:', error);
    // FIXED: Use the correct PumpSwap protocol fee recipient from successful transaction analysis
    const fallbackRecipient = new PublicKey('62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV');
    const fallbackConfigPda = derivePumpSwapGlobalConfigPDA();

    console.log(`Using fallback protocol fee recipient (verified): ${fallbackRecipient.toString()}`);
    console.log(`Using fallback global config PDA: ${fallbackConfigPda.toString()}`);

    return {
      protocolFeeRecipient: fallbackRecipient,
      globalConfigPda: fallbackConfigPda
    };
  }
}



/**
 * Check if a PumpSwap pool exists and get its data using SDK-based Anchor decoding
 * @param connection Solana connection
 * @param poolAddress Pool address
 * @returns Pool data
 */
export async function checkPumpSwapPool(
  connection: Connection,
  poolAddress: PublicKey
): Promise<PumpSwapPoolData> {
  console.log(`🔍 [POOL] Checking PumpSwap pool at address: ${poolAddress.toString()}`);

  const accountInfo = await connection.getAccountInfo(poolAddress);

  if (!accountInfo) {
    console.error(`❌ [POOL] No PumpSwap pool found at address: ${poolAddress.toString()}`);
    console.error(`❌ [POOL] RPC returned null account info`);
    throw new Error(`PumpSwap pool not found at address: ${poolAddress.toString()}`);
  }

  console.log(`✅ [POOL] Pool account found:`);
  console.log(`  - Data Length: ${accountInfo.data.length} bytes`);
  console.log(`  - Owner: ${accountInfo.owner.toString()}`);
  console.log(`  - Lamports: ${accountInfo.lamports}`);

  // Validate that this is a PumpSwap pool
  if (!accountInfo.owner.equals(PUMP_SWAP_PROGRAM_ID)) {
    console.error(`❌ [POOL] Invalid pool owner. Expected: ${PUMP_SWAP_PROGRAM_ID.toString()}, Got: ${accountInfo.owner.toString()}`);
    throw new Error(`Pool is not owned by PumpSwap program. Owner: ${accountInfo.owner.toString()}`);
  }

  console.log(`✅ [SUCCESS] PumpSwap pool FOUND!`);

  // PRIORITY 1 FIX: Use manual buffer parsing for reliable pool data extraction
  try {
    console.log(`🔍 [MANUAL] Using manual buffer parsing for pool data...`);

    // Manual buffer parsing based on the Pool struct layout
    // Pool struct layout (from IDL analysis):
    // - discriminator: 8 bytes
    // - poolBump: 1 byte (u8)
    // - index: 2 bytes (u16)
    // - creator: 32 bytes (pubkey)
    // - baseMint: 32 bytes (pubkey)
    // - quoteMint: 32 bytes (pubkey)
    // - lpMint: 32 bytes (pubkey)
    // - poolBaseTokenAccount: 32 bytes (pubkey)
    // - poolQuoteTokenAccount: 32 bytes (pubkey)
    // - lpSupply: 8 bytes (u64)
    // - coinCreator: 32 bytes (pubkey)

    const data = accountInfo.data;
    let offset = 8; // Skip discriminator

    // Parse pool data manually
    const poolBump = data.readUInt8(offset);
    offset += 1;

    const index = data.readUInt16LE(offset);
    offset += 2;

    const creator = new PublicKey(data.subarray(offset, offset + 32));
    offset += 32;

    const baseMint = new PublicKey(data.subarray(offset, offset + 32));
    offset += 32;

    const quoteMint = new PublicKey(data.subarray(offset, offset + 32));
    offset += 32;

    const lpMint = new PublicKey(data.subarray(offset, offset + 32));
    offset += 32;

    const poolBaseTokenAccount = new PublicKey(data.subarray(offset, offset + 32));
    offset += 32;

    const poolQuoteTokenAccount = new PublicKey(data.subarray(offset, offset + 32));
    offset += 32;

    const lpSupplyBuffer = data.subarray(offset, offset + 8);
    const lpSupply = lpSupplyBuffer.readBigUInt64LE(0);
    offset += 8;

    const coinCreator = new PublicKey(data.subarray(offset, offset + 32));

    console.log(`✅ [MANUAL] Successfully parsed pool data:`);
    console.log(`  - Pool Bump: ${poolBump}`);
    console.log(`  - Index: ${index}`);
    console.log(`  - Creator: ${creator.toString()}`);
    console.log(`  - Base Mint: ${baseMint.toString()}`);
    console.log(`  - Quote Mint: ${quoteMint.toString()}`);
    console.log(`  - LP Mint: ${lpMint.toString()}`);
    console.log(`  - Pool Base Token Account: ${poolBaseTokenAccount.toString()}`);
    console.log(`  - Pool Quote Token Account: ${poolQuoteTokenAccount.toString()}`);
    console.log(`  - LP Supply: ${lpSupply.toString()}`);
    console.log(`  - Coin Creator: ${coinCreator.toString()}`);

    // Fetch actual token account balances using the parsed pool data
    console.log(`🔍 [MANUAL] Fetching token account balances...`);

    const baseBalance = await connection.getTokenAccountBalance(poolBaseTokenAccount);
    const quoteBalance = await connection.getTokenAccountBalance(poolQuoteTokenAccount);

    console.log(`✅ [MANUAL] Token balances fetched:`);
    console.log(`  - Token Balance: ${baseBalance.value.amount} (${baseBalance.value.uiAmount})`);
    console.log(`  - WSOL Balance: ${quoteBalance.value.amount} (${quoteBalance.value.uiAmount})`);

    const tokenReserve = BigInt(baseBalance.value.amount);
    const solReserve = BigInt(quoteBalance.value.amount);

    // Validate reserves are not zero
    if (tokenReserve === 0n || solReserve === 0n) {
      throw new Error("Invalid input: 'baseReserve' or 'quoteReserve' cannot be zero.");
    }

    console.log(`✅ [MANUAL] Final reserves:`);
    console.log(`  - Token Reserve: ${tokenReserve.toString()}`);
    console.log(`  - SOL Reserve: ${solReserve.toString()} (${Number(solReserve) / 1e9} SOL)`);

    return {
      exists: true,
      reserves: {
        token: tokenReserve,
        sol: solReserve
      },
      baseVault: poolBaseTokenAccount,
      quoteVault: poolQuoteTokenAccount,
      baseMint: baseMint,
      quoteMint: quoteMint,
      coinCreator: coinCreator,
      owner: accountInfo.owner
    };

  } catch (error) {
    console.error('❌ [MANUAL] Failed to parse pool data or fetch balances:', error);
    throw new Error(`Failed to parse PumpSwap pool data: ${(error as Error).message}`);
  }
}

/**
 * Calculate a quote for a PumpSwap pool using SDK-based algorithms
 * PRIORITY 2 FIX: Replace with mathematically correct formulas from SDK
 * @param connection Solana connection for fetching dynamic fees
 * @param poolData Pool data
 * @param amount Amount to swap
 * @param isBuy Whether this is a buy or sell
 * @param tokenDecimals Token decimals
 * @param slippage Slippage percentage (e.g., 1 for 1%)
 * @returns Quote result
 */
export async function calculatePumpSwapQuote(
  connection: Connection,
  poolData: PumpSwapPoolData,
  amount: number,
  isBuy: boolean,
  tokenDecimals: number,
  slippage: number = 1
): Promise<QuoteResult> {
  console.log('🧮 [QUOTE] Starting SDK-based PumpSwap quote calculation...');
  console.log(`🧮 [QUOTE] Input parameters:`);
  console.log(`  - Amount: ${amount}`);
  console.log(`  - Direction: ${isBuy ? 'Buy (SOL -> Token)' : 'Sell (Token -> SOL)'}`);
  console.log(`  - Token Decimals: ${tokenDecimals}`);
  console.log(`  - Slippage: ${slippage}%`);

  if (!poolData) {
    console.error('❌ [QUOTE] Pool data is null/undefined');
    throw new Error('Pool data is required for quote calculation');
  }

  if (!poolData.reserves) {
    console.error('❌ [QUOTE] Pool reserves are null/undefined');
    throw new Error('Invalid pool data for quote calculation - missing reserves');
  }

  const tokenReserve = poolData.reserves.token;
  const solReserve = poolData.reserves.sol;

  console.log(`🧮 [QUOTE] Pool reserves:`);
  console.log(`  - Token Reserve: ${tokenReserve.toString()}`);
  console.log(`  - SOL Reserve: ${solReserve.toString()} (${Number(solReserve) / 1e9} SOL)`);

  // FIXED: Use default fee rates (PumpFun approach) - avoid complex global config parsing
  console.log(`🔍 [FEES] Using default fee rates (PumpFun approach)...`);
  const globalConfig = {
    lpFeeBasisPoints: new BN(25), // 0.25%
    protocolFeeBasisPoints: new BN(0),
    coinCreatorFeeBasisPoints: new BN(0),
    protocolFeeRecipients: [],
    admin: PublicKey.default
  };

  console.log(`✅ [FEES] Default fee rates applied:`);
  console.log(`  - LP Fee: ${globalConfig.lpFeeBasisPoints.toString()} basis points`);
  console.log(`  - Protocol Fee: ${globalConfig.protocolFeeBasisPoints.toString()} basis points`);
  console.log(`  - Coin Creator Fee: ${globalConfig.coinCreatorFeeBasisPoints.toString()} basis points`);

  // Convert reserves to BN for SDK calculations
  const baseReserve = new BN(tokenReserve.toString());
  const quoteReserve = new BN(solReserve.toString());

  // Validate reserves using SDK error handling
  if (baseReserve.isZero() || quoteReserve.isZero()) {
    throw new Error("Invalid input: 'baseReserve' or 'quoteReserve' cannot be zero.");
  }

  let result: any;
  let outAmount: number;
  let price: number;
  let outputAmountRaw: bigint;

  if (isBuy) {
    console.log('🧮 [QUOTE] Calculating BUY quote using SDK algorithm...');

    // FIXED: For buy operations, amount represents SOL to spend, not tokens to buy
    // Convert SOL amount to lamports (raw SOL units)
    const quoteAmountToSpend = new BN(tokenToRaw(amount, SOL_DECIMALS).toString());

    console.log(`🧮 [QUOTE] Buy calculation inputs:`);
    console.log(`  - SOL amount to spend: ${quoteAmountToSpend.toString()} lamports (${amount} SOL)`);
    console.log(`  - Slippage: ${slippage}%`);

    // PRIORITY 2 FIX: Use SDK's buyQuoteInputInternal function (SOL input -> tokens output)
    result = buyQuoteInputInternal(
      quoteAmountToSpend,
      slippage,
      baseReserve,
      quoteReserve,
      globalConfig.lpFeeBasisPoints,
      globalConfig.protocolFeeBasisPoints,
      globalConfig.coinCreatorFeeBasisPoints,
      poolData.coinCreator || PublicKey.default
    );

    // Convert results back to UI format
    outputAmountRaw = BigInt(result.baseAmountOut.toString()); // Tokens received
    outAmount = rawToToken(outputAmountRaw, tokenDecimals); // Output is token amount received
    price = amount / outAmount; // SOL per token

    console.log(`🧮 [QUOTE] SDK BUY Results:`);
    console.log(`  - Base Amount Out (tokens received): ${result.baseAmountOut.toString()}`);
    console.log(`  - Min Base Amount Out (with slippage): ${result.minBaseAmountOut.toString()}`);
    console.log(`  - Internal Base Amount Out: ${result.internalBaseAmountOut.toString()}`);
    console.log(`  - Output Amount: ${outAmount} tokens received`);
    console.log(`  - Price: ${price} SOL per token`);

  } else {
    console.log('🧮 [QUOTE] Calculating SELL quote using SDK algorithm...');

    // Convert token amount to raw and then to BN
    const baseAmountToSell = new BN(tokenToRaw(amount, tokenDecimals).toString());

    console.log(`🧮 [QUOTE] Sell calculation inputs:`);
    console.log(`  - Base amount to sell: ${baseAmountToSell.toString()}`);
    console.log(`  - Slippage: ${slippage}%`);

    // PRIORITY 2 FIX: Use SDK's sellBaseInputInternal function
    result = sellBaseInputInternal(
      baseAmountToSell,
      slippage,
      baseReserve,
      quoteReserve,
      globalConfig.lpFeeBasisPoints,
      globalConfig.protocolFeeBasisPoints,
      globalConfig.coinCreatorFeeBasisPoints,
      poolData.coinCreator || PublicKey.default
    );

    // Convert results back to UI format
    outputAmountRaw = BigInt(result.uiQuote.toString()); // Final SOL received after fees
    outAmount = rawToToken(outputAmountRaw, SOL_DECIMALS); // Output is SOL received
    price = outAmount / amount; // SOL per token

    console.log(`🧮 [QUOTE] SDK SELL Results:`);
    console.log(`  - UI Quote (SOL received): ${result.uiQuote.toString()}`);
    console.log(`  - Min Quote (with slippage): ${result.minQuote.toString()}`);
    console.log(`  - Internal Quote Amount Out: ${result.internalQuoteAmountOut.toString()}`);
    console.log(`  - Output Amount: ${outAmount} SOL received`);
    console.log(`  - Price: ${price} SOL per token`);
  }

  return { outAmount, price, outputAmountRaw };
}

/**
 * Get the status of a PumpSwap pool
 * @param poolData Pool data
 * @returns Pool status
 */
export function getPumpSwapPoolStatus(poolData: PumpSwapPoolData): PoolStatus {
  if (!poolData.exists) {
    return PoolStatus.Unknown;
  }

  return PoolStatus.Active;
}

/**
 * Encode a swap instruction for the PumpSwap program using Anchor format
 *
 * CORRECTED: Parameter order and values now match IDL specification exactly
 *
 * @param isBuy Whether this is a buy or sell operation
 * @param firstParam For buy: base_amount_out (expected token output), For sell: base_amount_in (tokens to sell)
 * @param secondParam For buy: max_quote_amount_in (max SOL input with slippage), For sell: min_quote_amount_out (min SOL output)
 * @returns Encoded instruction data
 */
function encodeSwapInstruction(isBuy: boolean, firstParam: bigint, secondParam: bigint): Buffer {
  // Use the appropriate discriminator based on the operation
  const discriminator = isBuy
    ? BUY_DISCRIMINATOR  // "buy" method: base_amount_out, max_quote_amount_in
    : SELL_DISCRIMINATOR; // "sell" method: base_amount_in, min_quote_amount_out

  // Create a buffer for the full instruction data
  const data = Buffer.alloc(8 + 16); // 8 bytes for discriminator + 16 bytes for two u64 values

  // Copy discriminator to the beginning of the buffer
  discriminator.copy(data, 0);

  // Write parameters as little-endian u64 values
  const firstParamBuffer = Buffer.alloc(8);
  const secondParamBuffer = Buffer.alloc(8);
  firstParamBuffer.writeBigUInt64LE(firstParam, 0);
  secondParamBuffer.writeBigUInt64LE(secondParam, 0);

  // Copy the values after the discriminator in the correct order
  // For buy: first param is base_amount_out (tokens), second is max_quote_amount_in (SOL)
  // For sell: first param is base_amount_in (tokens), second is min_quote_amount_out (SOL)
  firstParamBuffer.copy(data, 8);
  secondParamBuffer.copy(data, 16);

  return data;
}

/**
 * Create a swap transaction for a PumpSwap pool
 * @param connection Solana connection
 * @param poolAddress Pool address
 * @param tokenMint Token mint address
 * @param userPublicKey User's public key
 * @param platformFeeWallet Platform fee wallet address
 * @param amount Amount to swap
 * @param isBuy Whether this is a buy or sell
 * @param slippage Slippage tolerance (e.g., 0.01 for 1%)
 * @param quoteResult Quote result
 * @param poolData Pool data
 * @param useUserAsFeePayer Whether to use the user as the fee payer
 * @returns Transaction
 */
export async function createPumpSwapSwapTransaction(
  connection: Connection,
  poolAddress: PublicKey,
  tokenMint: PublicKey,
  userPublicKey: PublicKey,
  platformFeeWallet: PublicKey,
  amount: number,
  isBuy: boolean,
  slippage: number,
  quoteResult: QuoteResult,
  poolData?: PumpSwapPoolData,
  tokenDecimals: number = 9 // Default to 9 decimals if not provided
): Promise<Transaction> {
  console.log(`🚀 [TRANSACTION] Starting PumpSwap transaction creation`);
  console.log(`🔍 [PARAMS] Pool: ${poolAddress.toString()}`);
  console.log(`🔍 [PARAMS] Token: ${tokenMint.toString()}`);
  console.log(`🔍 [PARAMS] User: ${userPublicKey.toString()}`);
  console.log(`🔍 [PARAMS] Platform Fee Wallet: ${platformFeeWallet.toString()}`);
  console.log(`🔍 [PARAMS] Amount: ${amount}`);
  console.log(`🔍 [PARAMS] Direction: ${isBuy ? 'Buy' : 'Sell'}`);
  console.log(`🔍 [PARAMS] Slippage: ${slippage * 100}%`);
  console.log(`🔍 [PARAMS] Token Decimals: ${tokenDecimals}`);
  console.log(`🔍 [PARAMS] Quote Result:`);
  console.log(`  - outAmount: ${quoteResult.outAmount}`);
  console.log(`  - price: ${quoteResult.price}`);
  console.log(`  - outputAmountRaw: ${quoteResult.outputAmountRaw?.toString() || 'undefined'}`);

  // FIXED: Skip global config fetching - use PumpFun's approach with verified addresses
  console.log(`🔍 [CONFIG] Using PumpFun approach - verified protocol fee recipient...`);

  // Validate input parameters with detailed error messages
  if (!poolData?.owner) {
    console.error(`❌ [ERROR] Pool data validation failed - no owner found`);
    console.error(`❌ [ERROR] Pool data exists: ${!!poolData}`);
    console.error(`❌ [ERROR] Pool data owner: ${poolData?.owner?.toString() || 'undefined'}`);
    throw new Error(`Pool owner not found in pool data`);
  }

  if (!poolAddress || !tokenMint || !userPublicKey || !platformFeeWallet) {
    console.error(`❌ [ERROR] Missing required parameters:`);
    console.error(`❌ [ERROR] poolAddress: ${poolAddress?.toString() || 'MISSING'}`);
    console.error(`❌ [ERROR] tokenMint: ${tokenMint?.toString() || 'MISSING'}`);
    console.error(`❌ [ERROR] userPublicKey: ${userPublicKey?.toString() || 'MISSING'}`);
    console.error(`❌ [ERROR] platformFeeWallet: ${platformFeeWallet?.toString() || 'MISSING'}`);
    throw new Error(`Missing required parameters for transaction creation`);
  }

  if (amount <= 0) {
    console.error(`❌ [ERROR] Invalid amount: ${amount} (must be > 0)`);
    throw new Error(`Amount must be greater than 0, got: ${amount}`);
  }

  if (slippage < 0 || slippage > 1) {
    console.error(`❌ [ERROR] Invalid slippage: ${slippage} (must be between 0 and 1)`);
    throw new Error(`Slippage must be between 0 and 1, got: ${slippage}`);
  }

  if (!quoteResult || !quoteResult.outputAmountRaw) {
    console.error(`❌ [ERROR] Invalid quote result:`);
    console.error(`❌ [ERROR] - outAmount: ${quoteResult?.outAmount || 'undefined'}`);
    console.error(`❌ [ERROR] - price: ${quoteResult?.price || 'undefined'}`);
    console.error(`❌ [ERROR] - outputAmountRaw: ${quoteResult?.outputAmountRaw?.toString() || 'undefined'}`);
    throw new Error(`Invalid quote result - missing outputAmountRaw`);
  }

  // Validate that the pool owner is the PumpSwap program
  console.log(`🔍 [VALIDATION] Pool owner: ${poolData.owner.toString()}`);
  console.log(`🔍 [VALIDATION] Expected PumpSwap program ID: ${PUMP_SWAP_PROGRAM_ID.toString()}`);

  if (!poolData.owner.equals(PUMP_SWAP_PROGRAM_ID)) {
    console.error(`❌ [ERROR] Pool owner validation failed`);
    console.error(`❌ [ERROR] Pool owner: ${poolData.owner.toString()}`);
    console.error(`❌ [ERROR] Expected: ${PUMP_SWAP_PROGRAM_ID.toString()}`);
    throw new Error(`Pool is not owned by the PumpSwap program. Owner: ${poolData.owner.toString()}, Expected: ${PUMP_SWAP_PROGRAM_ID.toString()}`);
  }

  console.log(`✅ [VALIDATION] All parameter validations passed`);

  // PLATFORM FEE IMPLEMENTATION (following PumpFun pattern)
  console.log(`💰 [PLATFORM FEE] Calculating platform fee for PumpSwap transaction...`);

  let platformFeeLamports: bigint;
  if (isBuy) {
    // For buy transactions: calculate fee based on SOL input amount
    const buyAmountLamports = solToLamports(amount);
    platformFeeLamports = calculatePlatformFee(buyAmountLamports, 'pumpswap');
    console.log(`💰 [PLATFORM FEE] Buy transaction - SOL input: ${amount} SOL (${buyAmountLamports.toString()} lamports)`);
  } else {
    // For sell transactions: calculate fee based on equivalent SOL value of tokens
    const tokenValueInSol = amount * quoteResult.price; // price = SOL per token
    platformFeeLamports = calculatePlatformFee(solToLamports(tokenValueInSol), 'pumpswap');
    console.log(`💰 [PLATFORM FEE] Sell transaction - Token value: ${tokenValueInSol} SOL equivalent`);
  }

  console.log(`💰 [PLATFORM FEE] Platform fee: ${platformFeeLamports.toString()} lamports (${lamportsToSol(platformFeeLamports)} SOL)`);
  console.log(`💰 [PLATFORM FEE] Platform fee wallet: ${platformFeeWallet.toString()}`);

  // Create platform fee transfer instruction (following PumpFun pattern)
  console.log(`💰 [PLATFORM FEE] Creating platform fee transfer instruction...`);
  const platformFeeInstruction = SystemProgram.transfer({
    fromPubkey: userPublicKey,
    toPubkey: platformFeeWallet,
    lamports: platformFeeLamports,
  });
  console.log(`💰 [PLATFORM FEE] Platform fee instruction created successfully`);

  // Get the user's token account
  console.log(`🔍 [ACCOUNTS] Deriving user token account...`);
  const userTokenAccount = await getAssociatedTokenAddress(tokenMint, userPublicKey);
  console.log(`🔍 [ACCOUNTS] User token account: ${userTokenAccount.toString()}`);

  // For PumpSwap, we need to use wrapped SOL instead of native SOL
  // Get the user's wrapped SOL account
  console.log(`🔍 [ACCOUNTS] Deriving user wrapped SOL account...`);
  const userWrappedSolAccount = await getAssociatedTokenAddress(
    NATIVE_MINT, // Wrapped SOL mint
    userPublicKey
  );
  console.log(`🔍 [ACCOUNTS] User wrapped SOL account: ${userWrappedSolAccount.toString()}`);

  console.log(`🚀 [TRANSACTION] Creating new transaction...`);
  const transaction = new Transaction();

  // Add compute budget instructions to match successful transaction pattern
  console.log(`🔧 [INSTRUCTION] Adding SetComputeUnitLimit (200,000 units)...`);
  const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
    units: 200000 // Higher limit for PumpSwap transactions
  });
  transaction.add(computeUnitLimitIx);

  console.log(`🔧 [INSTRUCTION] Adding SetComputeUnitPrice (100,000 microLamports)...`);
  const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
    microLamports: 100000 // Priority fee for faster processing
  });
  transaction.add(computeUnitPriceIx);

  // Add platform fee transfer instruction (following PumpFun pattern - after compute budget, before other instructions)
  console.log(`💰 [PLATFORM FEE] Adding platform fee transfer instruction to transaction...`);
  transaction.add(platformFeeInstruction);
  console.log(`✅ [PLATFORM FEE] Platform fee instruction added to transaction`);

  // Check if token account already exists to avoid "owner not allowed" error
  console.log(`🔍 [ACCOUNTS] Checking if token account already exists: ${userTokenAccount.toString()}`);
  try {
    const tokenAccountInfo = await connection.getAccountInfo(userTokenAccount);
    if (!tokenAccountInfo) {
      // Account doesn't exist, create it
      console.log(`🔧 [INSTRUCTION] Adding createAssociatedTokenAccount for: ${userTokenAccount.toString()}`);
      const createTokenAccountInstruction = createAssociatedTokenAccountInstruction(
        userPublicKey, // payer
        userTokenAccount, // associated token account address
        userPublicKey, // owner
        tokenMint // mint
      );
      transaction.add(createTokenAccountInstruction);
      console.log(`✅ [INSTRUCTION] Added token account creation instruction`);
    } else {
      console.log(`✅ [ACCOUNTS] Token account already exists, skipping creation`);
    }
  } catch (error) {
    console.log(`⚠️ [ACCOUNTS] Error checking token account, will attempt creation: ${error}`);
    // If we can't check, try to create anyway (idempotent)
    const createTokenAccountInstruction = createAssociatedTokenAccountInstruction(
      userPublicKey, // payer
      userTokenAccount, // associated token account address
      userPublicKey, // owner
      tokenMint // mint
    );
    transaction.add(createTokenAccountInstruction);
    console.log(`✅ [INSTRUCTION] Added token account creation instruction (fallback)`);
  }

  // FIXED: Use standard CreateAssociatedTokenAccount for wrapped SOL (revert CreateAccountWithSeed)
  // The CreateAccountWithSeed approach was causing address mismatch errors
  console.log(`� [ACCOUNTS] Checking if wrapped SOL account already exists: ${userWrappedSolAccount.toString()}`);
  try {
    const wrappedSolAccountInfo = await connection.getAccountInfo(userWrappedSolAccount);
    if (!wrappedSolAccountInfo) {
      // MATCH SUCCESSFUL PATTERN: Use CreateAccountWithSeed instead of CreateAssociatedTokenAccount
      console.log(`�🔧 [INSTRUCTION] Adding CreateAccountWithSeed for wrapped SOL (matching successful pattern)...`);

      // Use standard CreateAssociatedTokenAccount for wrapped SOL
      console.log(`🔧 [INSTRUCTION] Adding CreateAssociatedTokenAccount for wrapped SOL: ${userWrappedSolAccount.toString()}`);
      const createWrappedSolAccountInstruction = createAssociatedTokenAccountInstruction(
        userPublicKey, // payer
        userWrappedSolAccount, // associated token account address
        userPublicKey, // owner
        NATIVE_MINT // wrapped SOL mint
      );
      transaction.add(createWrappedSolAccountInstruction);
      console.log(`✅ [INSTRUCTION] Added wrapped SOL account creation instruction`);
    } else {
      console.log(`✅ [ACCOUNTS] Wrapped SOL account already exists, skipping creation`);
    }
  } catch (error) {
    console.log(`⚠️ [ACCOUNTS] Error checking wrapped SOL account, will attempt creation: ${error}`);
    // If we can't check, try to create anyway (idempotent)
    const createWrappedSolAccountInstruction = createAssociatedTokenAccountInstruction(
      userPublicKey, // payer
      userWrappedSolAccount, // associated token account address
      userPublicKey, // owner
      NATIVE_MINT // wrapped SOL mint
    );
    transaction.add(createWrappedSolAccountInstruction);
    console.log(`✅ [INSTRUCTION] Added wrapped SOL account creation instruction (fallback)`);
  }

  // For buy operations, transfer SOL to the wrapped SOL account and sync
  if (isBuy) {
    // CRITICAL FIX: Add buffer for fees and multiple transfers in PumpSwap
    // PumpSwap performs multiple transfers: LP fees, protocol fees, coin creator fees
    // We need to fund the wrapped SOL account with extra SOL to cover all transfers
    const baseAmount = solToLamports(amount);
    const feeBuffer = BigInt(Math.floor(Number(baseAmount) * 0.1)); // 10% buffer for fees
    const minBuffer = BigInt(5000000); // Minimum 0.005 SOL buffer
    const actualBuffer = feeBuffer > minBuffer ? feeBuffer : minBuffer;
    const totalSolAmount = baseAmount + actualBuffer;

    console.log(`🔧 [INSTRUCTION] SOL funding calculation:`);
    console.log(`  - Base amount: ${baseAmount.toString()} lamports (${amount} SOL)`);
    console.log(`  - Fee buffer: ${actualBuffer.toString()} lamports`);
    console.log(`  - Total transfer: ${totalSolAmount.toString()} lamports`);

    const transferSolInstruction = SystemProgram.transfer({
      fromPubkey: userPublicKey,
      toPubkey: userWrappedSolAccount,
      lamports: totalSolAmount
    });
    transaction.add(transferSolInstruction);
    console.log(`✅ [INSTRUCTION] Added SOL transfer instruction with fee buffer`);

    // Sync native instruction to update wrapped SOL account balance
    console.log(`🔧 [INSTRUCTION] Adding syncNative instruction for wrapped SOL account`);
    const syncNativeInstruction = createSyncNativeInstruction(userWrappedSolAccount);
    transaction.add(syncNativeInstruction);
    console.log(`✅ [INSTRUCTION] Added syncNative instruction`);
  } else {
    // For sell operations, we need to ensure the wrapped SOL account can receive SOL
    // Add a small amount of SOL to make the account rent-exempt if needed
    console.log(`🔧 [SELL] Ensuring wrapped SOL account is properly funded for receiving SOL...`);
    const minRentExempt = BigInt(2039280); // Minimum rent-exempt amount for token account

    const transferMinSolInstruction = SystemProgram.transfer({
      fromPubkey: userPublicKey,
      toPubkey: userWrappedSolAccount,
      lamports: minRentExempt
    });
    transaction.add(transferMinSolInstruction);
    console.log(`✅ [INSTRUCTION] Added minimum SOL transfer for sell operation`);

    // Sync native instruction to update wrapped SOL account balance
    console.log(`🔧 [INSTRUCTION] Adding syncNative instruction for wrapped SOL account`);
    const syncNativeInstruction = createSyncNativeInstruction(userWrappedSolAccount);
    transaction.add(syncNativeInstruction);
    console.log(`✅ [INSTRUCTION] Added syncNative instruction for sell`);
  }

  // Note: Platform fees are handled separately in the main swap service
  // The PumpSwap instruction itself handles protocol fees automatically

  // Create the accounts array for the swap instruction based on the PumpSwap IDL
  // This account structure matches the requirements in the IDL for buy/sell instructions

  // We don't need to derive the global config address here anymore
  // It's already provided by the fetchProtocolFeeRecipient function

  // Get the base and quote mints from the pool data
  // For PumpSwap, the token is the base mint and SOL (native) is the quote mint
  console.log(`🔍 [MINTS] Setting up base and quote mints...`);
  const baseMint = tokenMint;
  const quoteMint = new PublicKey('So11111111111111111111111111111111111111112'); // Native SOL mint
  console.log(`🔍 [MINTS] Base mint (token): ${baseMint.toString()}`);
  console.log(`🔍 [MINTS] Quote mint (SOL): ${quoteMint.toString()}`);

  // Get the pool's token accounts
  console.log(`🔍 [ACCOUNTS] Deriving pool token accounts...`);
  const poolBaseTokenAccount = await getAssociatedTokenAddress(
    baseMint,
    poolAddress,
    true
  );
  console.log(`🔍 [ACCOUNTS] Pool base token account: ${poolBaseTokenAccount.toString()}`);

  const poolQuoteTokenAccount = await getAssociatedTokenAddress(
    quoteMint,
    poolAddress,
    true
  );
  console.log(`🔍 [ACCOUNTS] Pool quote token account: ${poolQuoteTokenAccount.toString()}`);

  // FIXED: Use PumpFun's approach - hardcoded protocol fee recipient (like PumpFun does)
  // This avoids the complex global config parsing that's causing issues
  console.log(`🔍 [PDA] Using verified PumpSwap protocol fee recipient (PumpFun approach)...`);
  const globalConfigPda = derivePumpSwapGlobalConfigPDA();
  console.log(`🔍 [PDA] Global config PDA: ${globalConfigPda.toString()}`);

  // Use the verified protocol fee recipient from successful transaction analysis
  // This matches how PumpFun handles fee recipients - simple and reliable
  const protocolFeeRecipient = new PublicKey('62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV');
  console.log(`🔍 [PDA] Using verified protocol fee recipient: ${protocolFeeRecipient.toString()}`);

  // Get the protocol fee recipient's token account
  console.log(`🔍 [ACCOUNTS] Deriving protocol fee recipient token account...`);
  const protocolFeeRecipientTokenAccount = await getAssociatedTokenAddress(
    quoteMint,
    protocolFeeRecipient,
    true
  );
  console.log(`🔍 [ACCOUNTS] Protocol fee recipient token account: ${protocolFeeRecipientTokenAccount.toString()}`);

  // FIXED: Derive coin creator vault authority dynamically from pool data
  if (!poolData.coinCreator) {
    throw new Error('Pool data missing coin creator - cannot derive vault authority');
  }

  const coinCreatorVaultAuthority = derivePumpSwapCoinCreatorVaultAuthorityPDA(poolData.coinCreator);
  console.log(`🔍 [PDA] Dynamically derived coin_creator_vault_authority: ${coinCreatorVaultAuthority.toString()}`);
  console.log(`🔍 [PDA] Using coin creator from pool: ${poolData.coinCreator.toString()}`);

  // Derive the creator vault PDA for comparison
  const derivedCreatorVault = derivePumpSwapCreatorVaultPDA();
  console.log(`🔍 [PDA] Derived creator vault PDA: ${derivedCreatorVault.toString()}`);

  // Get the coin creator vault ATA
  console.log(`🔍 [ACCOUNTS] Deriving coin creator vault ATA...`);
  const coinCreatorVaultAta = await getAssociatedTokenAddress(
    quoteMint,
    coinCreatorVaultAuthority,
    true
  );
  console.log(`🔍 [ACCOUNTS] Coin creator vault ATA: ${coinCreatorVaultAta.toString()}`);

  // Dynamically derive the event authority PDA using our utility function
  const eventAuthority = derivePumpSwapEventAuthorityPDA();
  console.log(`🔍 [PDA] Derived event_authority PDA: ${eventAuthority.toString()}`);

  // Create the complete account structure according to the successful transaction (19 accounts)
  // This matches the exact structure from the working Solscan transaction
  const accounts = [
    // 0. Pool account
    { pubkey: poolAddress, isSigner: false, isWritable: true },
    // 1. User account (signer and fee payer)
    { pubkey: userPublicKey, isSigner: true, isWritable: true },
    // 2. Global config PDA
    { pubkey: globalConfigPda, isSigner: false, isWritable: false },
    // 3. Base mint (token)
    { pubkey: baseMint, isSigner: false, isWritable: false },
    // 4. Quote mint (wrapped SOL)
    { pubkey: quoteMint, isSigner: false, isWritable: false },
    // 5. User base token account
    { pubkey: userTokenAccount, isSigner: false, isWritable: true },
    // 6. User quote token account (wrapped SOL)
    { pubkey: userWrappedSolAccount, isSigner: false, isWritable: true },
    // 7. Pool base token account
    { pubkey: poolBaseTokenAccount, isSigner: false, isWritable: true },
    // 8. Pool quote token account
    { pubkey: poolQuoteTokenAccount, isSigner: false, isWritable: true },
    // 9. Protocol fee recipient
    { pubkey: protocolFeeRecipient, isSigner: false, isWritable: false },
    // 10. Protocol fee recipient token account
    { pubkey: protocolFeeRecipientTokenAccount, isSigner: false, isWritable: true },
    // 11. Base token program
    { pubkey: SPL_TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    // 12. Quote token program
    { pubkey: SPL_TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    // 13. System program
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
    // 14. Associated token program
    { pubkey: new PublicKey('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL'), isSigner: false, isWritable: false },
    // 15. Event authority PDA
    { pubkey: eventAuthority, isSigner: false, isWritable: false },
    // 16. Program ID (PumpSwap program)
    { pubkey: poolData.owner, isSigner: false, isWritable: false },
    // 17. Coin creator vault ATA
    { pubkey: coinCreatorVaultAta, isSigner: false, isWritable: true },
    // 18. Coin creator vault authority
    { pubkey: coinCreatorVaultAuthority, isSigner: false, isWritable: false },
  ];

  // Prepare the instruction data using proper Anchor discriminator
  console.log(`🔧 [INSTRUCTION] Preparing PumpSwap instruction data...`);
  let instructionData: Buffer;

  // Use the correct method names from the PumpSwap IDL
  const methodName = isBuy
    ? "buy"  // For SOL → token (buy)
    : "sell"; // For token → SOL (sell)

  console.log(`🔧 [INSTRUCTION] Using method "${methodName}" for PumpSwap transaction`);
  console.log(`🔧 [INSTRUCTION] Pool address: ${poolAddress.toString()}`);
  console.log(`🔧 [INSTRUCTION] Pool owner (program ID): ${poolData.owner.toString()}`);

  if (isBuy) {
    console.log(`🔧 [BUY] Creating buy instruction...`);

    // CORRECTED: For buy instruction, IDL expects:
    // - base_amount_out: expected token output (what we want to receive)
    // - max_quote_amount_in: maximum SOL input (what we're willing to pay)

    // The base_amount_out should be the expected token output from the quote
    const baseAmountOut = quoteResult.outputAmountRaw; // Expected tokens to receive

    // The max_quote_amount_in should be the SOL input amount (with slippage tolerance)
    // We add slippage to the input amount to allow for price movement
    const solInputLamports = solToLamports(amount);
    const maxQuoteAmountIn = BigInt(Math.floor(Number(solInputLamports) * (1 + slippage))); // Max SOL willing to pay

    console.log(`🔧 [BUY] Expected token output (base_amount_out): ${baseAmountOut.toString()} tokens`);
    console.log(`🔧 [BUY] SOL input: ${amount} SOL = ${solInputLamports.toString()} lamports`);
    console.log(`🔧 [BUY] Max SOL input with ${slippage * 100}% slippage (max_quote_amount_in): ${maxQuoteAmountIn.toString()} lamports`);
    console.log(`🔧 [BUY] Example comparison - Your example: base_amount_out="7490952699731", max_quote_amount_in="232312602"`);

    // Encode the instruction data using the CORRECT Anchor format for buy
    // First param: base_amount_out (expected tokens), Second param: max_quote_amount_in (max SOL)
    console.log(`🔧 [BUY] Encoding instruction with BUY_DISCRIMINATOR...`);
    instructionData = encodeSwapInstruction(true, baseAmountOut, maxQuoteAmountIn);
    console.log(`🔧 [BUY] Instruction data encoded successfully`);
  } else {
    console.log(`🔧 [SELL] Creating sell instruction...`);

    // For sell instruction, IDL expects:
    // - base_amount_in: token amount to sell
    // - min_quote_amount_out: minimum SOL output expected
    const sellAmountTokens = tokenToRaw(amount, tokenDecimals);
    const minSolOutput = BigInt(Math.floor(Number(quoteResult.outputAmountRaw) * (1 - slippage)));

    console.log(`🔧 [SELL] Input amount: ${amount} tokens = ${sellAmountTokens.toString()} raw tokens`);
    console.log(`🔧 [SELL] Expected output: ${quoteResult.outputAmountRaw.toString()} lamports`);
    console.log(`🔧 [SELL] Min output (with ${slippage * 100}% slippage): ${minSolOutput.toString()} lamports`);

    // Encode the instruction data using the proper Anchor format for sell
    // First param: base_amount_in (tokens to sell), Second param: min_quote_amount_out (min SOL)
    console.log(`🔧 [SELL] Encoding instruction with SELL_DISCRIMINATOR...`);
    instructionData = encodeSwapInstruction(false, sellAmountTokens, minSolOutput);
    console.log(`🔧 [SELL] Instruction data encoded successfully`);
  }

  // Log the instruction data for debugging
  console.log(`🔍 [DEBUG] PumpSwap Instruction Data (hex): ${instructionData.toString('hex')}`);
  console.log(`🔍 [DEBUG] Instruction data length: ${instructionData.length} bytes`);
  console.log(`🔍 [DEBUG] PumpSwap Accounts (${accounts.length} total):`);
  accounts.forEach((account, index) => {
    console.log(`  [${index.toString().padStart(2, '0')}] ${account.pubkey.toString()} (Signer: ${account.isSigner}, Writable: ${account.isWritable})`);
  });

  // Create the swap instruction with the primary method name
  let swapInstruction = new TransactionInstruction({
    keys: accounts,
    programId: poolData.owner,
    data: instructionData,
  });

  // No fallback needed since we're using the correct Anchor discriminators

  // Using the correct Anchor discriminators should resolve the "InstructionFallbackNotFound" error

  // Add the PumpSwap instruction (matches successful transaction pattern)
  transaction.add(swapInstruction);

  // CRITICAL FIX: Add cleanup instruction to close wrapped SOL account and return SOL
  // This prevents SOL from being locked in the wrapped SOL account
  // For both buy and sell operations, we want to return SOL to the user
  console.log(`🔧 [INSTRUCTION] Adding closeAccount instruction to return SOL from wrapped SOL account...`);
  const { createCloseAccountInstruction } = await import('@solana/spl-token');
  const closeAccountInstruction = createCloseAccountInstruction(
    userWrappedSolAccount, // Account to close
    userPublicKey, // Destination for remaining SOL
    userPublicKey, // Owner of the account
    [], // No multisig signers
    SPL_TOKEN_PROGRAM_ID // Token program
  );
  transaction.add(closeAccountInstruction);
  console.log(`✅ [INSTRUCTION] Added closeAccount instruction for ${isBuy ? 'buy' : 'sell'} operation`);

  console.log(`✅ [TRANSACTION] Transaction created with ${transaction.instructions.length} instructions:`);
  console.log('1. SetComputeUnitLimit');
  console.log('2. SetComputeUnitPrice');
  console.log('3. Platform Fee Transfer (SOL to platform wallet)');
  console.log('4. CreateAssociatedTokenAccount (for base token)');
  console.log('5. CreateAssociatedTokenAccount (for wrapped SOL)');
  if (isBuy) {
    console.log('6. Transfer SOL to wrapped SOL account (with fee buffer)');
    console.log('7. SyncNative (update wrapped SOL balance)');
    console.log('8. PumpSwap Buy instruction');
    console.log('9. CloseAccount (return unused wrapped SOL)');
  } else {
    console.log('6. Transfer minimum SOL to wrapped SOL account (for receiving)');
    console.log('7. SyncNative (update wrapped SOL balance)');
    console.log('8. PumpSwap Sell instruction');
    console.log('9. CloseAccount (return SOL to user)');
  }

  // Set the fee payer (user is always fee payer for PumpSwap to match successful transaction)
  transaction.feePayer = userPublicKey;
  console.log(`Using user wallet as fee payer for transaction: ${userPublicKey.toString()}`);

  // Don't set blockhash or sign here - this will be done in signAndSendTransactionWithPrivy
  // This way we avoid signature verification issues when the blockhash is updated

  return transaction;
}
