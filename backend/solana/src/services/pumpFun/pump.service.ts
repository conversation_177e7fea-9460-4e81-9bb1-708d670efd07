import { Connection, PublicKey, Transaction } from '@solana/web3.js';
import { getMint } from '@solana/spl-token';
import {
  QuoteRequest,
  SwapRequest,
  DexType,
  SwapDirection,
  QuoteResult,
  PoolStatus
} from '../../types/pump.types';
import {
  checkPumpFunPool,
  calculateDirectQuote,
  getPumpFunPoolStatus
} from './pumpFun.service';
import {
  checkPumpSwapPool,
  calculatePumpSwapQuote,
  getPumpSwapPoolStatus,
  createPumpSwapSwapTransaction
} from './pumpSwap.service';

import { config } from '../../config';
import { signAndSendTransactionWithPrivy } from '../privy/proper-privy.service';
import { executeCorrectedPumpFunSwap } from './corrected-pump.service';
import { executeCorrectedPumpSwapSwap } from './corrected-pumpswap.service';

// Constants
const RPC_URL = config.rpcUrl || '';
if (!RPC_URL) {
  console.error('SOLANA_RPC_URL environment variable is not set');
}
const PLATFORM_FEE_WALLET = config.platformFeeWallet;

/**
 * Get a quote for a swap
 * @param quoteRequest Quote request
 * @returns Quote result and additional data
 */
export async function getSwapQuote(
  quoteRequest: QuoteRequest
): Promise<{
  quoteResult: QuoteResult;
  tokenDecimals: number;
  poolStatus: PoolStatus;
}> {
  const { tokenAddress, poolAddress, dexType, amount, direction } = quoteRequest;
  const isBuy = direction === SwapDirection.Buy;
  console.log('Getting swap quote');

  const connection = new Connection(RPC_URL, 'confirmed');
  const tokenMint = new PublicKey(tokenAddress);
  const poolPublicKey = new PublicKey(poolAddress);

  // Fetch token metadata
  let tokenDecimals: number;
  try {
    console.log('Fetching token metadata...');
    const mintInfo = await getMint(connection, tokenMint);
    tokenDecimals = mintInfo.decimals;
    console.log(`Token exists with ${tokenDecimals} decimals`);
  } catch (error) {
    console.log('Error fetching token metadata. Using default decimals of 6');
    tokenDecimals = 6;
  }

  let quoteResult: QuoteResult;
  let poolStatus: PoolStatus;

  // Get quote based on DEX type
  if (dexType === DexType.PumpFun) {
    const poolData = await checkPumpFunPool(connection, poolPublicKey);
    poolStatus = getPumpFunPoolStatus(poolData);

    if (poolStatus !== PoolStatus.Active) {
      throw new Error(`PumpFun pool is not active. Status: ${poolStatus}`);
    }

    quoteResult = calculateDirectQuote(amount, isBuy, poolData, tokenDecimals);
  } else if (dexType === DexType.PumpSwap) {
    const poolData = await checkPumpSwapPool(connection, poolPublicKey);
    poolStatus = getPumpSwapPoolStatus(poolData);

    if (poolStatus !== PoolStatus.Active) {
      throw new Error(`PumpSwap pool is not active. Status: ${poolStatus}`);
    }

    // Use SDK-based implementation with dynamic fees
    console.log('Using SDK-based implementation for PumpSwap quote');
    quoteResult = await calculatePumpSwapQuote(connection, poolData, amount, isBuy, tokenDecimals);
  } else {
    throw new Error(`Unsupported DEX type: ${dexType}`);
  }

  return { quoteResult, tokenDecimals, poolStatus };
}

/**
 * Create a swap transaction
 * @param swapRequest Swap request
 * @param usePrivyForSigning Whether to use Privy for signing
 * @returns Base64 encoded transaction and quote result
 */
export async function createSwapTransaction(
  swapRequest: SwapRequest,
  _useSDK: boolean = false // Kept for backward compatibility but not used (prefixed with _ to indicate unused)
): Promise<{
  transaction: string;
  quoteResult: QuoteResult;
}> {
  console.log('Creating swap transaction with request:', JSON.stringify(swapRequest));

  const { tokenAddress, poolAddress, dexType, amount, direction, slippage, walletAddress } = swapRequest;
  const isBuy = direction === SwapDirection.Buy;

  console.log('Connecting to RPC URL:', RPC_URL);
  const connection = new Connection(RPC_URL, 'confirmed');

  console.log('Creating PublicKeys...');
  const tokenMint = new PublicKey(tokenAddress);
  const poolPublicKey = new PublicKey(poolAddress);

  // Create user public key with walletId attached for Anchor provider
  const userPublicKey = new PublicKey(walletAddress);
  // Attach walletId to the userPublicKey object for use with Anchor provider
  (userPublicKey as any).walletId = swapRequest.walletId;

  console.log('Platform fee wallet:', PLATFORM_FEE_WALLET.toString());

  // Fetch token metadata
  let tokenDecimals: number;
  try {
    console.log('Fetching token metadata...');
    const mintInfo = await getMint(connection, tokenMint);
    tokenDecimals = mintInfo.decimals;
    console.log('Token decimals:', tokenDecimals);
  } catch (error) {
    console.log('Error fetching token metadata:', error);
    tokenDecimals = 6; // Default if we can't fetch
    console.log('Using default token decimals:', tokenDecimals);
  }

  let transaction;
  let quoteResult: QuoteResult;

  // Create transaction based on DEX type
  if (dexType === DexType.PumpFun) {
    console.log('Creating PumpFun swap transaction...');

    console.log('Checking PumpFun pool...');
    const poolData = await checkPumpFunPool(connection, poolPublicKey);

    // Log pool data without using JSON.stringify
    console.log('Pool data:', {
      exists: poolData.exists,
      isGraduated: poolData.isGraduated,
      virtualTokenReserves: poolData.virtualTokenReserves?.toString(),
      virtualSolReserves: poolData.virtualSolReserves?.toString(),
      realTokenReserves: poolData.realTokenReserves?.toString(),
      realSolReserves: poolData.realSolReserves?.toString(),
      tokenTotalSupply: poolData.tokenTotalSupply?.toString(),
      owner: poolData.owner?.toString()
    });

    const poolStatus = getPumpFunPoolStatus(poolData);
    console.log('Pool status:', poolStatus);

    if (poolStatus !== PoolStatus.Active) {
      throw new Error(`PumpFun pool is not active. Status: ${poolStatus}`);
    }

    console.log('Calculating quote...');
    quoteResult = calculateDirectQuote(amount, isBuy, poolData, tokenDecimals);

    // Log quote result without using JSON.stringify
    console.log('Quote result:', {
      outAmount: quoteResult.outAmount,
      price: quoteResult.price,
      outputAmountRaw: quoteResult.outputAmountRaw?.toString()
    });

    console.log('Creating PumpFun swap transaction...');
    try {
      // This path should not be reached since PumpFun is handled in executeSwapWithPrivy
      throw new Error('PumpFun transactions should be handled by executeSwapWithPrivy with platform signing');
    } catch (error) {
      console.error('Error creating PumpFun swap transaction:', error);
      throw error;
    }
  } else if (dexType === DexType.PumpSwap) {
    console.log('Creating PumpSwap swap transaction...');

    console.log('Checking PumpSwap pool...');
    const poolData = await checkPumpSwapPool(connection, poolPublicKey);

    // Log pool data without using JSON.stringify
    console.log('Pool data:', {
      exists: poolData.exists,
      reserves: poolData.reserves ? {
        token: poolData.reserves.token?.toString(),
        sol: poolData.reserves.sol?.toString()
      } : undefined,
      baseVault: poolData.baseVault?.toString(),
      quoteVault: poolData.quoteVault?.toString(),
      owner: poolData.owner?.toString()
    });

    const poolStatus = getPumpSwapPoolStatus(poolData);
    console.log('Pool status:', poolStatus);

    if (poolStatus !== PoolStatus.Active) {
      throw new Error(`PumpSwap pool is not active. Status: ${poolStatus}`);
    }

    // Calculate quote
    console.log('Calculating quote...');

    // FIXED: For PumpSwap, force token decimals to 6 if the fetched value seems wrong
    let adjustedTokenDecimals = tokenDecimals;
    if (tokenDecimals !== 6 && tokenDecimals !== 9) {
      console.log(`⚠️ [PUMPSWAP] Unusual token decimals: ${tokenDecimals}, forcing to 6 for PumpSwap`);
      adjustedTokenDecimals = 6;
    }
    console.log(`Using token decimals: ${adjustedTokenDecimals} for PumpSwap quote calculation`);

    quoteResult = await calculatePumpSwapQuote(connection, poolData, amount, isBuy, adjustedTokenDecimals);

    // Log quote result without using JSON.stringify
    console.log('Quote result:', {
      outAmount: quoteResult.outAmount,
      price: quoteResult.price,
      outputAmountRaw: quoteResult.outputAmountRaw?.toString()
    });

    console.log('Creating PumpSwap swap transaction...');
    try {
      // Use the original implementation
      console.log('Using original implementation for PumpSwap transaction');
      // Pass flag to indicate whether user wallet should be used as fee payer
      transaction = await createPumpSwapSwapTransaction(
        connection,
        poolPublicKey,
        tokenMint,
        userPublicKey,
        PLATFORM_FEE_WALLET,
        amount,
        isBuy,
        slippage,
        quoteResult,
        poolData,
        tokenDecimals
      );
      console.log('PumpSwap swap transaction created successfully');
    } catch (error) {
      console.error('Error creating PumpSwap swap transaction:', error);
      throw error;
    }
  } else {
    throw new Error(`Unsupported DEX type: ${dexType}`);
  }

  // Set a temporary blockhash for serialization
  // This is required for serialization, but will be replaced in signAndSendTransactionWithPrivy
  console.log('Setting temporary blockhash for serialization...');
  if (!transaction.recentBlockhash) {
    transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
    console.log(`Set temporary blockhash: ${transaction.recentBlockhash}`);
  }

  // Serialize transaction
  console.log('Serializing transaction...');
  const serializedTx = transaction.serialize({ requireAllSignatures: false, verifySignatures: false });
  const base64Tx = serializedTx.toString('base64');
  console.log('Transaction serialized successfully');

  return { transaction: base64Tx, quoteResult };
}

/**
 * Execute a swap with Privy session signing
 * @param swapRequest Swap request
 * @param usePrivy Whether to use Privy session signing
 * @param walletId Optional wallet ID for Privy session signing
 * @returns Transaction signature and quote result
 *
 * UPDATED: Fixed PDA derivation to use correct seed formats for Privy compatibility
 */
/**
 * Alias for executeSwapWithPrivy for backwards compatibility
 */
export const executeSwapTransaction = executeSwapWithPrivy;

/**
 * Execute a swap with Privy session signing
 */
export async function executeSwapWithPrivy(
  swapRequest: SwapRequest,
  usePrivy: boolean = true,
  walletId?: string
): Promise<{
  signature?: string;
  transaction?: string;
  quoteResult: QuoteResult;
}> {
  console.log('Executing swap with Privy:', usePrivy);

  // For PumpFun, use Privy wallet signing - user signs via frontend
  if (swapRequest.dexType === DexType.PumpFun) {
    console.log('Using Privy wallet signing for PumpFun');
    const result = await executeCorrectedPumpFunSwap(swapRequest);
    return {
      signature: result.signature,
      quoteResult: result.quoteResult
    };
  }

  // For PumpSwap, use Privy wallet signing - user signs via frontend
  if (swapRequest.dexType === DexType.PumpSwap) {
    console.log('Using Privy wallet signing for PumpSwap');
    const result = await executeCorrectedPumpSwapSwap(swapRequest);
    return {
      signature: result.signature,
      quoteResult: result.quoteResult
    };
  }

  // For other DEX types, continue with the original flow
  // Create the swap transaction
  // Pass usePrivy flag to ensure transaction is created with the correct fee payer
  const { transaction: serializedTx, quoteResult } = await createSwapTransaction(
    swapRequest,
    false
  );

  // If explicitly not using Privy, just return the transaction for client-side signing
  if (usePrivy === false) {
    console.log('Privy session signing explicitly disabled, returning transaction for client-side signing');
    return { transaction: serializedTx, quoteResult };
  }

  // If no wallet ID is provided, return the transaction for client-side signing
  if (!walletId) {
    console.log('No wallet ID provided, returning transaction for client-side signing');
    return { transaction: serializedTx, quoteResult };
  }

  try {
    // Deserialize the transaction
    if (!config.rpcUrl) {
      throw new Error('SOLANA_RPC_URL environment variable is not set');
    }
    const connection = new Connection(config.rpcUrl, 'confirmed');
    let decodedTransaction = Transaction.from(Buffer.from(serializedTx, 'base64'));

    // IMPORTANT: Only set fee payer if it wasn't already set correctly during transaction creation
    // This prevents double fee payer assignment which can cause signature verification failures
    if (usePrivy && walletId) {
      const userPublicKey = new PublicKey(swapRequest.walletAddress);

      // Check if fee payer is already correctly set
      if (!decodedTransaction.feePayer || !decodedTransaction.feePayer.equals(userPublicKey)) {
        console.log(`Current fee payer: ${decodedTransaction.feePayer?.toString() || 'None'}`);
        console.log(`Setting fee payer to user wallet for Privy signing: ${userPublicKey.toString()}`);
        decodedTransaction.feePayer = userPublicKey;
      } else {
        console.log(`Fee payer already correctly set to user wallet: ${userPublicKey.toString()}`);
      }

      // Log all signers to verify
      console.log('Transaction signers:');
      decodedTransaction.signatures.forEach((signatureObj, pubkeyStr) => {
        console.log(`- ${pubkeyStr}: ${signatureObj.signature ? 'Signed' : 'Unsigned'}`);
      });

      // PumpFun validation is now handled in the corrected implementation
    }

    console.log('Getting fresh blockhash from the network with finalized commitment...');
    // Get a fresh blockhash with finalized commitment
    // This is more reliable for transaction submission
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash({
      commitment: 'finalized'
    });

    // Update the transaction
    decodedTransaction.recentBlockhash = blockhash;
    console.log(`Updated transaction with fresh blockhash: ${blockhash}, last valid block height: ${lastValidBlockHeight}`);

    // Validate the blockhash with the correct parameter format
    if (decodedTransaction.recentBlockhash) {
      const isValid = await connection.isBlockhashValid(decodedTransaction.recentBlockhash, { commitment: 'finalized' });
      console.log(`Blockhash validation: ${isValid.value ? 'VALID' : 'INVALID'} - this might cause issues`);

      if (!isValid.value) {
        console.log('Blockhash is invalid, getting a new one...');
        const { blockhash: newBlockhash } = await connection.getLatestBlockhash({
          commitment: 'finalized'
        });
        decodedTransaction.recentBlockhash = newBlockhash;
        console.log(`Updated transaction with new blockhash: ${newBlockhash}`);
      }
    } else {
      console.warn('No blockhash set on transaction - this will cause issues');
    }

    // IMPORTANT: Do not modify the transaction structure after this point
    // Only update the blockhash to ensure signature verification

    // Log detailed transaction info for troubleshooting
    console.log('Transaction details before sending:');
    console.log(`- Instructions count: ${decodedTransaction.instructions.length}`);
    console.log(`- Fee payer: ${decodedTransaction.feePayer?.toString()}`);
    console.log(`- Recent blockhash: ${decodedTransaction.recentBlockhash}`);
    decodedTransaction.instructions.forEach((instr, idx) => {
      console.log(`- Instruction ${idx}: Program ${instr.programId.toString()}`);
      console.log(`  - Accounts: ${instr.keys.length}`);
      console.log(`  - Data length: ${instr.data.length} bytes`);
      console.log(`  - Data (hex): ${Buffer.from(instr.data).toString('hex').substring(0, 20)}...`);
    });

    // Sign and send with Privy
    console.log(`Signing transaction for wallet: ${swapRequest.walletAddress}`);

    // Log the final structure of the transaction before serialization
    console.log('FINAL TRANSACTION STRUCTURE:');
    for (const ix of decodedTransaction.instructions) {
      if (ix.programId.toString() === '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P') {
        console.log('PumpFun instruction - Account list:');
        ix.keys.forEach((acc, idx) => {
          console.log(`  Account ${idx}: ${acc.pubkey.toString()} (isSigner: ${acc.isSigner}, isWritable: ${acc.isWritable})`);
        });
      }
    }

    // IMPORTANT: Serialize the transaction with our updated blockhash and fee payer
    // Use serialize instead of serializeMessage to include the fee payer information
    console.log('Final transaction structure before serialization:');
    console.log(`- Fee payer: ${decodedTransaction.feePayer?.toString()}`);
    console.log(`- Recent blockhash: ${decodedTransaction.recentBlockhash}`);
    console.log(`- Number of instructions: ${decodedTransaction.instructions.length}`);

    // Log each instruction's program ID and accounts
    decodedTransaction.instructions.forEach((ix, index) => {
      console.log(`Instruction ${index}:`);
      console.log(`- Program ID: ${ix.programId.toString()}`);
      console.log(`- Number of accounts: ${ix.keys.length}`);

      // For PumpFun instructions, log more details
      if (ix.programId.toString() === '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P') {
        console.log('PumpFun instruction accounts:');
        ix.keys.forEach((acc, idx) => {
          console.log(`  Account ${idx}: ${acc.pubkey.toString()} (isSigner: ${acc.isSigner}, isWritable: ${acc.isWritable})`);
        });
      }
    });

    const serializedTransaction = decodedTransaction.serialize({verifySignatures: false}).toString('base64');
    console.log(`Transaction serialized for Privy with fresh blockhash, length: ${serializedTransaction.length}`);

    // Send transaction to Privy for signing and broadcasting
    // Pass the connection and priority level for proper confirmation handling
    const priorityLevel = swapRequest.priorityLevel || 'medium';
    const signature = await signAndSendTransactionWithPrivy(
      walletId,
      serializedTransaction,
      connection,
      priorityLevel,
      true // Wait for confirmation
    );
    console.log('Transaction signed and sent with Privy, signature:', signature);
    return { signature, quoteResult };
  } catch (error) {
    console.error('Error executing swap:', error);

    // Handle specific error types to provide better user feedback
    const errorMessage = (error as Error).message || 'Unknown error';

    // Check if this is a program error related to instruction format
    if (errorMessage.includes('InstructionFallbackNotFound') || errorMessage.includes('custom program error: 0x65')) {
      console.log('Detected PumpFun program instruction format error - this is a known issue');

      // Provide a more user-friendly error message
      throw new Error('The swap transaction failed due to a program instruction format error. ' +
        'This is a known issue with the PumpFun program integration. ' +
        'Please try again later or contact support.');
    }

    // Check if this is a blockhash error
    if (errorMessage.includes('Blockhash not found')) {
      console.log('Detected blockhash not found error - likely due to network congestion');

      // Provide a more user-friendly error message
      throw new Error('The swap transaction failed due to network congestion. ' +
        'Please try again in a few moments.');
    }

    // Check if this is a signature verification error
    if (errorMessage.includes('signature verification failed')) {
      console.log('Detected signature verification error - retry disabled as requested');

      // Provide a more user-friendly error message
      throw new Error('The swap transaction failed due to signature verification issues. ' +
        'This is likely due to incorrect PDA derivation or transaction structure. ' +
        'Please check the transaction structure and try again.');
    }

    // For other errors, just pass them through
    throw error;
  }
}
