import { logger } from '../../utils/logger.js';

export interface ErrorAnalysis {
  category: 'RECOVERABLE' | 'USER_ERROR' | 'SYSTEM_ERROR' | 'NETWORK_ERROR';
  action: 'RETRY' | 'INCREASE_SLIPPAGE' | 'INCREASE_FEES' | 'REFRESH_QUOTE' | 'CHECK_BALANCE' | 'FAIL';
  userMessage: string;
  technicalDetails: string;
  retryable: boolean;
  suggestedWaitTime?: number;
}

export class ErrorRecoveryService {
  private static readonly SOLANA_ERROR_CODES = {
    // Program errors
    '0x1': 'InsufficientFunds',
    '0x1771': 'SlippageToleranceExceeded', 
    '0x1772': 'PriceImpactTooHigh',
    '0x0': 'Custom', // Custom program error
    
    // Transaction errors
    'AccountInUse': 'AccountInUse',
    'AccountLoadedTwice': 'AccountLoadedTwice',
    'AccountNotFound': 'AccountNotFound',
    'ProgramAccountNotFound': 'ProgramAccountNotFound',
    'InsufficientFundsForFee': 'InsufficientFundsForFee',
    'InvalidAccountForFee': 'InvalidAccountForFee',
    'AlreadyProcessed': 'AlreadyProcessed',
    'BlockhashNotFound': 'BlockhashNotFound',
    'InstructionError': 'InstructionError',
    'CallChainTooDeep': 'CallChainTooDeep',
    'MissingSignature': 'MissingSignature',
    'InvalidAccountIndex': 'InvalidAccountIndex',
    'SignatureFailure': 'SignatureFailure',
    'InvalidProgramForExecution': 'InvalidProgramForExecution',
    'SanitizeFailure': 'SanitizeFailure',
    'ClusterMaintenance': 'ClusterMaintenance',
    'AccountBorrowOutstanding': 'AccountBorrowOutstanding',
    'WouldExceedMaxBlockCostLimit': 'WouldExceedMaxBlockCostLimit',
    'UnsupportedVersion': 'UnsupportedVersion',
    'InvalidWritableAccount': 'InvalidWritableAccount',
    'WouldExceedMaxAccountCostLimit': 'WouldExceedMaxAccountCostLimit',
    'WouldExceedAccountDataBlockLimit': 'WouldExceedAccountDataBlockLimit',
    'TooManyAccountLocks': 'TooManyAccountLocks',
    'AddressLookupTableNotFound': 'AddressLookupTableNotFound',
    'InvalidAddressLookupTableOwner': 'InvalidAddressLookupTableOwner',
    'InvalidAddressLookupTableData': 'InvalidAddressLookupTableData',
    'InvalidAddressLookupTableIndex': 'InvalidAddressLookupTableIndex',
    'InvalidLookupTableIndex': 'InvalidLookupTableIndex',
    'ResanitizationNeeded': 'ResanitizationNeeded',
    'ProgramExecutionTemporarilyRestricted': 'ProgramExecutionTemporarilyRestricted',
    'UnbalancedTransaction': 'UnbalancedTransaction',
    'ProgramCacheHitMaxLimit': 'ProgramCacheHitMaxLimit'
  };

  /**
   * Analyze error and provide recovery strategy
   */
  static analyzeError(error: Error | string, context?: {
    exchangeType?: 'pumpfun' | 'pumpswap';
    tradeAmount?: number;
    slippage?: number;
    retryCount?: number;
  }): ErrorAnalysis {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const lowerError = errorMessage.toLowerCase();

    // Log error for monitoring
    logger.error('Transaction error analysis', {
      error: errorMessage,
      context,
      timestamp: new Date().toISOString()
    });

    // Slippage and price movement errors
    if (this.isSlippageError(errorMessage)) {
      return {
        category: 'RECOVERABLE',
        action: 'INCREASE_SLIPPAGE',
        userMessage: 'Price moved during transaction. Try increasing slippage tolerance or refreshing quote.',
        technicalDetails: `Slippage exceeded: ${errorMessage}`,
        retryable: true,
        suggestedWaitTime: 2000
      };
    }

    // Insufficient funds errors
    if (this.isInsufficientFundsError(errorMessage)) {
      return {
        category: 'USER_ERROR',
        action: 'CHECK_BALANCE',
        userMessage: 'Insufficient balance for this transaction. Please check your wallet balance.',
        technicalDetails: `Insufficient funds: ${errorMessage}`,
        retryable: false
      };
    }

    // Network congestion and fee errors
    if (this.isNetworkCongestionError(errorMessage)) {
      return {
        category: 'RECOVERABLE',
        action: 'INCREASE_FEES',
        userMessage: 'Network is congested. Increasing priority fees for faster execution.',
        technicalDetails: `Network congestion: ${errorMessage}`,
        retryable: true,
        suggestedWaitTime: 3000
      };
    }

    // Blockhash and timing errors
    if (this.isBlockhashError(errorMessage)) {
      return {
        category: 'RECOVERABLE',
        action: 'RETRY',
        userMessage: 'Transaction timing issue. Retrying with fresh blockhash.',
        technicalDetails: `Blockhash error: ${errorMessage}`,
        retryable: true,
        suggestedWaitTime: 1000
      };
    }

    // Account state errors
    if (this.isAccountStateError(errorMessage)) {
      return {
        category: 'RECOVERABLE',
        action: 'RETRY',
        userMessage: 'Account temporarily unavailable. Retrying transaction.',
        technicalDetails: `Account state error: ${errorMessage}`,
        retryable: true,
        suggestedWaitTime: 2000
      };
    }

    // RPC and connection errors
    if (this.isRpcError(errorMessage)) {
      return {
        category: 'NETWORK_ERROR',
        action: 'RETRY',
        userMessage: 'Network connection issue. Retrying transaction.',
        technicalDetails: `RPC error: ${errorMessage}`,
        retryable: true,
        suggestedWaitTime: 5000
      };
    }

    // Program-specific errors (PumpFun/PumpSwap)
    if (this.isProgramError(errorMessage, context?.exchangeType)) {
      return this.analyzeProgramError(errorMessage, context?.exchangeType);
    }

    // Privy wallet errors
    if (this.isPrivyError(errorMessage)) {
      return {
        category: 'SYSTEM_ERROR',
        action: 'RETRY',
        userMessage: 'Wallet service temporarily unavailable. Please try again.',
        technicalDetails: `Privy error: ${errorMessage}`,
        retryable: true,
        suggestedWaitTime: 3000
      };
    }

    // Timeout errors
    if (this.isTimeoutError(errorMessage)) {
      return {
        category: 'RECOVERABLE',
        action: 'INCREASE_FEES',
        userMessage: 'Transaction took too long. Increasing fees for faster processing.',
        technicalDetails: `Timeout error: ${errorMessage}`,
        retryable: true,
        suggestedWaitTime: 2000
      };
    }

    // Unknown error - default fallback
    return {
      category: 'SYSTEM_ERROR',
      action: 'FAIL',
      userMessage: 'An unexpected error occurred. Please try again or contact support.',
      technicalDetails: `Unknown error: ${errorMessage}`,
      retryable: false
    };
  }

  private static isSlippageError(error: string): boolean {
    const slippageKeywords = [
      'slippage',
      'price impact',
      'too little',
      'too much',
      'tooLittleSolReceived',
      'tooLittleTokensReceived',
      'priceImpactTooHigh',
      '0x1771',
      '0x1772'
    ];
    return slippageKeywords.some(keyword => error.toLowerCase().includes(keyword.toLowerCase()));
  }

  private static isInsufficientFundsError(error: string): boolean {
    const insufficientFundsKeywords = [
      'insufficient funds',
      'insufficient balance',
      'not enough',
      'balance too low',
      'insufficientFundsForFee',
      '0x1'
    ];
    return insufficientFundsKeywords.some(keyword => error.toLowerCase().includes(keyword.toLowerCase()));
  }

  private static isNetworkCongestionError(error: string): boolean {
    const congestionKeywords = [
      'network congestion',
      'high traffic',
      'wouldExceedMaxBlockCostLimit',
      'tooManyAccountLocks',
      'cluster maintenance',
      'programExecutionTemporarilyRestricted'
    ];
    return congestionKeywords.some(keyword => error.toLowerCase().includes(keyword.toLowerCase()));
  }

  private static isBlockhashError(error: string): boolean {
    const blockhashKeywords = [
      'blockhash not found',
      'blockhashNotFound',
      'invalid blockhash',
      'blockhash expired',
      'block height exceeded'
    ];
    return blockhashKeywords.some(keyword => error.toLowerCase().includes(keyword.toLowerCase()));
  }

  private static isAccountStateError(error: string): boolean {
    const accountStateKeywords = [
      'account in use',
      'accountInUse',
      'account loaded twice',
      'accountLoadedTwice',
      'account borrow outstanding',
      'accountBorrowOutstanding'
    ];
    return accountStateKeywords.some(keyword => error.toLowerCase().includes(keyword.toLowerCase()));
  }

  private static isRpcError(error: string): boolean {
    const rpcKeywords = [
      'rpc',
      'connection',
      'network error',
      'timeout',
      'fetch failed',
      'econnreset',
      'enotfound',
      'getaddrinfo',
      'socket'
    ];
    return rpcKeywords.some(keyword => error.toLowerCase().includes(keyword.toLowerCase()));
  }

  private static isProgramError(error: string, exchangeType?: string): boolean {
    const programKeywords = [
      'program error',
      'custom program error',
      'anchor error',
      'instruction error'
    ];
    return programKeywords.some(keyword => error.toLowerCase().includes(keyword.toLowerCase()));
  }

  private static isPrivyError(error: string): boolean {
    const privyKeywords = [
      'privy',
      'wallet service',
      'authentication failed',
      'signature failed',
      'wallet not found'
    ];
    return privyKeywords.some(keyword => error.toLowerCase().includes(keyword.toLowerCase()));
  }

  private static isTimeoutError(error: string): boolean {
    const timeoutKeywords = [
      'timeout',
      'timed out',
      'request timeout',
      'execution timeout',
      'deadline exceeded'
    ];
    return timeoutKeywords.some(keyword => error.toLowerCase().includes(keyword.toLowerCase()));
  }

  private static analyzeProgramError(error: string, exchangeType?: string): ErrorAnalysis {
    const lowerError = error.toLowerCase();

    // PumpFun specific errors
    if (exchangeType === 'pumpfun') {
      if (lowerError.includes('bonding curve')) {
        return {
          category: 'USER_ERROR',
          action: 'REFRESH_QUOTE',
          userMessage: 'Bonding curve state changed. Please refresh and try again.',
          technicalDetails: `PumpFun bonding curve error: ${error}`,
          retryable: true,
          suggestedWaitTime: 2000
        };
      }
    }

    // PumpSwap specific errors
    if (exchangeType === 'pumpswap') {
      if (lowerError.includes('pool') || lowerError.includes('liquidity')) {
        return {
          category: 'RECOVERABLE',
          action: 'REFRESH_QUOTE',
          userMessage: 'Pool liquidity changed. Refreshing quote and retrying.',
          technicalDetails: `PumpSwap pool error: ${error}`,
          retryable: true,
          suggestedWaitTime: 3000
        };
      }
    }

    // Generic program error
    return {
      category: 'SYSTEM_ERROR',
      action: 'RETRY',
      userMessage: 'Smart contract error. Retrying transaction.',
      technicalDetails: `Program error: ${error}`,
      retryable: true,
      suggestedWaitTime: 2000
    };
  }

  /**
   * Get user-friendly error message based on error analysis
   */
  static getUserMessage(analysis: ErrorAnalysis): string {
    return analysis.userMessage;
  }

  /**
   * Determine if error should trigger a retry
   */
  static shouldRetry(analysis: ErrorAnalysis, currentRetryCount: number, maxRetries: number): boolean {
    return analysis.retryable && currentRetryCount < maxRetries && analysis.action !== 'FAIL';
  }

  /**
   * Get suggested retry delay based on error type
   */
  static getRetryDelay(analysis: ErrorAnalysis, retryCount: number): number {
    const baseDelay = analysis.suggestedWaitTime || 1000;
    const exponentialBackoff = Math.min(baseDelay * Math.pow(1.5, retryCount), 10000);
    return exponentialBackoff;
  }

  /**
   * Generate recovery suggestions for the user
   */
  static getRecoverySuggestions(analysis: ErrorAnalysis): string[] {
    const suggestions: string[] = [];

    switch (analysis.action) {
      case 'INCREASE_SLIPPAGE':
        suggestions.push('Try increasing slippage tolerance to 2-5%');
        suggestions.push('Wait for better market conditions');
        suggestions.push('Use a smaller trade amount');
        break;
        
      case 'INCREASE_FEES':
        suggestions.push('Enable higher priority fees');
        suggestions.push('Try trading during less busy hours');
        suggestions.push('Use MEV protection if available');
        break;
        
      case 'CHECK_BALANCE':
        suggestions.push('Check your wallet balance');
        suggestions.push('Ensure you have enough SOL for fees');
        suggestions.push('Try a smaller trade amount');
        break;
        
      case 'REFRESH_QUOTE':
        suggestions.push('Refresh the quote and try again');
        suggestions.push('Check if the token is still tradeable');
        suggestions.push('Verify pool liquidity');
        break;
        
      case 'RETRY':
        suggestions.push('Try the transaction again');
        suggestions.push('Check network status');
        suggestions.push('Ensure stable internet connection');
        break;
        
      default:
        suggestions.push('Contact support if the issue persists');
        suggestions.push('Try again later');
        break;
    }

    return suggestions;
  }
}