import { Connection, Transaction, TransactionInstruction, ComputeBudgetProgram, PublicKey } from '@solana/web3.js';
import { logger } from '../../utils/logger.js';

interface FastExecutionConfig {
  computeUnitLimit: number;
  computeUnitPrice: number;
  maxRetries: number;
  timeoutMs: number;
  priorityLevel: 'low' | 'medium' | 'high' | 'veryHigh';
}

interface SimulationResult {
  success: boolean;
  error?: string;
  computeUnitsConsumed?: number;
  logs?: string[];
  accounts?: any[];
}

interface ExecutionResult {
  success: boolean;
  signature?: string;
  error?: string;
  executionTime: number;
  retryCount: number;
}

export class FastExecutionService {
  private connection: Connection;
  private circuitBreakerFailures = 0;
  private lastFailureTime = 0;
  private readonly CIRCUIT_BREAKER_THRESHOLD = 5;
  private readonly CIRCUIT_BREAKER_TIMEOUT = 60000; // 1 minute

  constructor(connection: Connection) {
    this.connection = connection;
  }

  /**
   * Production-ready fast transaction execution with comprehensive optimizations
   */
  async executeTransactionFast(
    transaction: Transaction,
    config: FastExecutionConfig,
    walletId: string,
    exchangeType: 'pumpfun' | 'pumpswap' = 'pumpfun'
  ): Promise<ExecutionResult> {
    const startTime = Date.now();
    let retryCount = 0;

    try {
      // Circuit breaker check
      if (this.isCircuitBreakerOpen()) {
        throw new Error('Circuit breaker open - service temporarily unavailable');
      }

      // 1. Pre-execution optimizations
      await this.optimizeTransaction(transaction, config, exchangeType);

      // 2. Transaction simulation
      const simulationResult = await this.simulateTransactionSafe(transaction);
      if (!simulationResult.success) {
        throw new Error(`Simulation failed: ${simulationResult.error}`);
      }

      logger.info('Transaction simulation successful', {
        computeUnitsConsumed: simulationResult.computeUnitsConsumed,
        exchangeType,
        walletId: walletId.substring(0, 8)
      });

      // 3. Execute with retries and optimizations
      let lastError: Error | null = null;
      
      while (retryCount <= config.maxRetries) {
        try {
          // Dynamic priority fee adjustment based on retry count
          if (retryCount > 0) {
            await this.adjustPriorityFee(transaction, retryCount, config);
          }

          // Execute transaction with timeout
          const signature = await this.executeWithTimeout(
            transaction,
            walletId,
            config.timeoutMs
          );

          // Success - reset circuit breaker
          this.onSuccess();

          return {
            success: true,
            signature,
            executionTime: Date.now() - startTime,
            retryCount
          };

        } catch (error) {
          lastError = error as Error;
          retryCount++;
          
          logger.warn('Transaction execution attempt failed', {
            attempt: retryCount,
            maxRetries: config.maxRetries,
            error: lastError.message,
            exchangeType
          });

          // Wait before retry with exponential backoff
          if (retryCount <= config.maxRetries) {
            const backoffMs = Math.min(1000 * Math.pow(2, retryCount - 1), 5000);
            await this.sleep(backoffMs);
          }
        }
      }

      // All retries failed
      this.onFailure();
      throw lastError || new Error('Transaction execution failed after all retries');

    } catch (error) {
      this.onFailure();
      return {
        success: false,
        error: (error as Error).message,
        executionTime: Date.now() - startTime,
        retryCount
      };
    }
  }

  /**
   * Optimize transaction for fast execution
   */
  private async optimizeTransaction(
    transaction: Transaction,
    config: FastExecutionConfig,
    exchangeType: 'pumpfun' | 'pumpswap'
  ): Promise<void> {
    // Get fresh blockhash
    const { blockhash, lastValidBlockHeight } = await this.connection.getLatestBlockhash('confirmed');
    transaction.recentBlockhash = blockhash;
    transaction.lastValidBlockHeight = lastValidBlockHeight;

    // Remove existing compute budget instructions
    transaction.instructions = transaction.instructions.filter(ix => 
      !ix.programId.equals(ComputeBudgetProgram.programId)
    );

    // Add optimized compute budget instructions at the beginning
    const computeUnitLimit = this.getOptimalComputeLimit(exchangeType);
    const computeUnitPrice = this.getOptimalComputePrice(config.priorityLevel, exchangeType);

    const computeBudgetInstructions: TransactionInstruction[] = [
      ComputeBudgetProgram.setComputeUnitLimit({ units: computeUnitLimit }),
      ComputeBudgetProgram.setComputeUnitPrice({ microLamports: computeUnitPrice })
    ];

    // Insert at the beginning
    transaction.instructions.unshift(...computeBudgetInstructions);

    logger.info('Transaction optimized', {
      computeUnitLimit,
      computeUnitPrice,
      exchangeType,
      instructionCount: transaction.instructions.length
    });
  }

  /**
   * Get optimal compute unit limit based on exchange type
   */
  private getOptimalComputeLimit(exchangeType: 'pumpfun' | 'pumpswap'): number {
    switch (exchangeType) {
      case 'pumpfun':
        return 150000; // PumpFun bonding curve operations are simpler
      case 'pumpswap':
        return 300000; // PumpSwap DEX operations need more compute
      default:
        return 200000;
    }
  }

  /**
   * Get optimal compute unit price based on priority level and exchange
   */
  private getOptimalComputePrice(priorityLevel: string, exchangeType: 'pumpfun' | 'pumpswap'): number {
    const basePrice = exchangeType === 'pumpfun' ? 50000 : 100000; // microLamports
    
    const multipliers = {
      low: 1.0,
      medium: 2.0,
      high: 4.0,
      veryHigh: 8.0
    };

    return Math.floor(basePrice * (multipliers[priorityLevel as keyof typeof multipliers] || 1.0));
  }

  /**
   * Safe transaction simulation with error handling
   */
  private async simulateTransactionSafe(transaction: Transaction): Promise<SimulationResult> {
    try {
      const simulation = await this.connection.simulateTransaction(transaction);

      if (simulation.value.err) {
        return {
          success: false,
          error: JSON.stringify(simulation.value.err)
        };
      }

      return {
        success: true,
        computeUnitsConsumed: simulation.value.unitsConsumed || 0,
        logs: simulation.value.logs || [],
        accounts: simulation.value.accounts || []
      };

    } catch (error) {
      logger.error('Transaction simulation error', error as Error);
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Execute transaction with timeout using Privy
   */
  private async executeWithTimeout(
    transaction: Transaction,
    walletId: string,
    timeoutMs: number
  ): Promise<string> {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Transaction execution timeout after ${timeoutMs}ms`));
      }, timeoutMs);

      try {
        // Import the Privy service
        const { signAndSendTransactionWithPrivy } = await import('../privy/proper-privy.service.js');
        
        // Serialize transaction for Privy
        const serializedTx = transaction.serialize({ requireAllSignatures: false }).toString('base64');
        const signature = await signAndSendTransactionWithPrivy(walletId, serializedTx);

        clearTimeout(timeoutId);
        resolve(signature);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Adjust priority fee for retries
   */
  private async adjustPriorityFee(
    transaction: Transaction,
    retryCount: number,
    config: FastExecutionConfig
  ): Promise<void> {
    // Find and update compute unit price instruction
    const computePriceIx = transaction.instructions.find(ix => 
      ix.programId.equals(ComputeBudgetProgram.programId) && 
      ix.data.length === 9 // setComputeUnitPrice instruction length
    );

    if (computePriceIx) {
      // Increase priority fee by 50% for each retry
      const multiplier = 1 + (retryCount * 0.5);
      const newPrice = Math.floor(config.computeUnitPrice * multiplier);
      
      // Replace the instruction
      const index = transaction.instructions.indexOf(computePriceIx);
      transaction.instructions[index] = ComputeBudgetProgram.setComputeUnitPrice({ 
        microLamports: newPrice 
      });

      logger.info('Priority fee adjusted for retry', {
        retryCount,
        newPrice,
        multiplier
      });
    }
  }

  /**
   * Circuit breaker implementation
   */
  private isCircuitBreakerOpen(): boolean {
    const now = Date.now();
    
    if (this.circuitBreakerFailures >= this.CIRCUIT_BREAKER_THRESHOLD) {
      if (now - this.lastFailureTime < this.CIRCUIT_BREAKER_TIMEOUT) {
        return true;
      } else {
        // Reset circuit breaker after timeout
        this.circuitBreakerFailures = 0;
      }
    }
    
    return false;
  }

  private onSuccess(): void {
    this.circuitBreakerFailures = 0;
  }

  private onFailure(): void {
    this.circuitBreakerFailures++;
    this.lastFailureTime = Date.now();
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get recommended config based on trade parameters
   */
  static getRecommendedConfig(
    tradeAmountSol: number,
    exchangeType: 'pumpfun' | 'pumpswap',
    isUrgent: boolean = false
  ): FastExecutionConfig {
    let priorityLevel: 'low' | 'medium' | 'high' | 'veryHigh' = 'medium';
    
    // Determine priority level based on trade size and urgency
    if (isUrgent || tradeAmountSol >= 100) {
      priorityLevel = 'veryHigh';
    } else if (tradeAmountSol >= 10) {
      priorityLevel = 'high';
    } else if (tradeAmountSol >= 1) {
      priorityLevel = 'medium';
    } else {
      priorityLevel = 'low';
    }

    return {
      computeUnitLimit: exchangeType === 'pumpfun' ? 150000 : 300000,
      computeUnitPrice: exchangeType === 'pumpfun' ? 50000 : 100000,
      maxRetries: 3,
      timeoutMs: 15000, // 15 seconds
      priorityLevel
    };
  }
}