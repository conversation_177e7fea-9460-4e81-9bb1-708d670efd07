import { logger } from '../../utils/logger.js';

interface PriceSnapshot {
  price: number;
  timestamp: number;
  tokenAddress: string;
  exchangeType: 'pumpfun' | 'pumpswap';
}

interface PriceMovementResult {
  isValid: boolean;
  priceChange: number;
  priceChangePercent: number;
  recommendation: 'PROCEED' | 'REFRESH_QUOTE' | 'INCREASE_SLIPPAGE' | 'ABORT';
  warningMessage?: string;
}

export class PriceProtectionService {
  private static readonly MAX_PRICE_MOVEMENT_PERCENT = 5; // 5% max movement
  private static readonly QUOTE_VALIDITY_MS = 30000; // 30 seconds
  private static readonly PRICE_CACHE = new Map<string, PriceSnapshot>();

  /**
   * Check if price movement is within acceptable limits
   */
  static async validatePriceMovement(
    tokenAddress: string,
    currentPrice: number,
    exchangeType: 'pumpfun' | 'pumpswap',
    cachedQuoteTimestamp?: number
  ): Promise<PriceMovementResult> {
    try {
      const cacheKey = `${tokenAddress}_${exchangeType}`;
      const now = Date.now();

      // Get cached price snapshot
      const cachedSnapshot = this.PRICE_CACHE.get(cacheKey);

      // If no cached data, store current price and proceed
      if (!cachedSnapshot) {
        this.PRICE_CACHE.set(cacheKey, {
          price: currentPrice,
          timestamp: now,
          tokenAddress,
          exchangeType
        });

        return {
          isValid: true,
          priceChange: 0,
          priceChangePercent: 0,
          recommendation: 'PROCEED'
        };
      }

      // Check if cached data is too old
      const timeDiff = now - cachedSnapshot.timestamp;
      if (timeDiff > this.QUOTE_VALIDITY_MS) {
        // Update cache with current price
        this.PRICE_CACHE.set(cacheKey, {
          price: currentPrice,
          timestamp: now,
          tokenAddress,
          exchangeType
        });

        return {
          isValid: true,
          priceChange: 0,
          priceChangePercent: 0,
          recommendation: 'REFRESH_QUOTE',
          warningMessage: 'Quote is stale, please refresh'
        };
      }

      // Calculate price movement
      const priceChange = currentPrice - cachedSnapshot.price;
      const priceChangePercent = Math.abs(priceChange / cachedSnapshot.price) * 100;

      logger.info('Price movement analysis', {
        tokenAddress,
        exchangeType,
        cachedPrice: cachedSnapshot.price,
        currentPrice,
        priceChange,
        priceChangePercent: priceChangePercent.toFixed(2) + '%',
        timeDiff: timeDiff + 'ms'
      });

      // Update cache with current price
      this.PRICE_CACHE.set(cacheKey, {
        price: currentPrice,
        timestamp: now,
        tokenAddress,
        exchangeType
      });

      // Determine recommendation based on price movement
      if (priceChangePercent <= 1) {
        return {
          isValid: true,
          priceChange,
          priceChangePercent,
          recommendation: 'PROCEED'
        };
      } else if (priceChangePercent <= 3) {
        return {
          isValid: true,
          priceChange,
          priceChangePercent,
          recommendation: 'PROCEED',
          warningMessage: `Price moved ${priceChangePercent.toFixed(1)}% since quote`
        };
      } else if (priceChangePercent <= this.MAX_PRICE_MOVEMENT_PERCENT) {
        return {
          isValid: true,
          priceChange,
          priceChangePercent,
          recommendation: 'INCREASE_SLIPPAGE',
          warningMessage: `Significant price movement (${priceChangePercent.toFixed(1)}%). Consider increasing slippage.`
        };
      } else {
        return {
          isValid: false,
          priceChange,
          priceChangePercent,
          recommendation: 'ABORT',
          warningMessage: `Excessive price movement (${priceChangePercent.toFixed(1)}%). Please refresh quote.`
        };
      }

    } catch (error) {
      logger.error('Price protection validation error', error as Error);
      
      // On error, proceed but warn user
      return {
        isValid: true,
        priceChange: 0,
        priceChangePercent: 0,
        recommendation: 'PROCEED',
        warningMessage: 'Unable to verify price movement. Proceeding with caution.'
      };
    }
  }

  /**
   * Get current market price from exchange
   */
  static async getCurrentMarketPrice(
    tokenAddress: string,
    exchangeType: 'pumpfun' | 'pumpswap'
  ): Promise<number | null> {
    try {
      if (exchangeType === 'pumpfun') {
        const { calculateDirectQuote } = await import('../pumpFun/pumpFun.service.js');
        // Simplified price fetch - would need full pool data for accurate quote
        return 0.000001; // Placeholder price
      } else {
        const { calculatePumpSwapQuote } = await import('../pumpFun/pumpSwap.service.js');
        // Simplified price fetch - would need full pool data for accurate quote
        return 0.000001; // Placeholder price
      }
    } catch (error) {
      logger.error('Error fetching current market price', error as Error);
      return null;
    }
  }

  /**
   * Pre-execution price check
   */
  static async preExecutionPriceCheck(
    tokenAddress: string,
    expectedPrice: number,
    exchangeType: 'pumpfun' | 'pumpswap',
    quoteTimestamp?: number
  ): Promise<PriceMovementResult> {
    try {
      // Get current market price
      const currentPrice = await this.getCurrentMarketPrice(tokenAddress, exchangeType);
      
      if (!currentPrice) {
        return {
          isValid: true,
          priceChange: 0,
          priceChangePercent: 0,
          recommendation: 'PROCEED',
          warningMessage: 'Unable to verify current price. Proceeding with original quote.'
        };
      }

      // Validate against expected price
      return await this.validatePriceMovement(
        tokenAddress,
        currentPrice,
        exchangeType,
        quoteTimestamp
      );

    } catch (error) {
      logger.error('Pre-execution price check failed', error as Error);
      
      return {
        isValid: true,
        priceChange: 0,
        priceChangePercent: 0,
        recommendation: 'PROCEED',
        warningMessage: 'Price check failed. Proceeding with caution.'
      };
    }
  }

  /**
   * Suggest optimal slippage based on price volatility
   */
  static suggestOptimalSlippage(
    priceChangePercent: number,
    currentSlippage: number,
    exchangeType: 'pumpfun' | 'pumpswap'
  ): number {
    // Base slippage recommendations by exchange type
    const baseSlippage = exchangeType === 'pumpfun' ? 0.01 : 0.02; // 1% for PumpFun, 2% for PumpSwap

    // Adjust based on recent price movement
    let recommendedSlippage = baseSlippage;

    if (priceChangePercent > 5) {
      recommendedSlippage = Math.max(baseSlippage * 3, 0.05); // Minimum 5% for high volatility
    } else if (priceChangePercent > 3) {
      recommendedSlippage = Math.max(baseSlippage * 2, 0.03); // Minimum 3% for medium volatility
    } else if (priceChangePercent > 1) {
      recommendedSlippage = Math.max(baseSlippage * 1.5, 0.02); // Minimum 2% for low volatility
    }

    // Don't suggest lower than current slippage
    recommendedSlippage = Math.max(recommendedSlippage, currentSlippage);

    // Cap at 10% maximum
    recommendedSlippage = Math.min(recommendedSlippage, 0.10);

    logger.info('Slippage recommendation', {
      priceChangePercent,
      currentSlippage: (currentSlippage * 100).toFixed(1) + '%',
      recommendedSlippage: (recommendedSlippage * 100).toFixed(1) + '%',
      exchangeType
    });

    return recommendedSlippage;
  }

  /**
   * Clean up old price cache entries
   */
  static cleanupPriceCache(): void {
    const now = Date.now();
    const maxAge = this.QUOTE_VALIDITY_MS * 2; // 1 minute

    for (const [key, snapshot] of this.PRICE_CACHE.entries()) {
      if (now - snapshot.timestamp > maxAge) {
        this.PRICE_CACHE.delete(key);
      }
    }

    logger.debug('Price cache cleanup completed', {
      remainingEntries: this.PRICE_CACHE.size
    });
  }

  /**
   * Get price volatility metrics for a token
   */
  static async getPriceVolatilityMetrics(
    tokenAddress: string,
    exchangeType: 'pumpfun' | 'pumpswap'
  ): Promise<{
    isHighVolatility: boolean;
    recommendedSlippage: number;
    priceStability: 'STABLE' | 'MODERATE' | 'VOLATILE' | 'HIGHLY_VOLATILE';
  }> {
    try {
      const cacheKey = `${tokenAddress}_${exchangeType}`;
      const snapshot = this.PRICE_CACHE.get(cacheKey);
      
      if (!snapshot) {
        return {
          isHighVolatility: false,
          recommendedSlippage: exchangeType === 'pumpfun' ? 0.01 : 0.02,
          priceStability: 'STABLE'
        };
      }

      const currentPrice = await this.getCurrentMarketPrice(tokenAddress, exchangeType);
      if (!currentPrice) {
        return {
          isHighVolatility: false,
          recommendedSlippage: exchangeType === 'pumpfun' ? 0.01 : 0.02,
          priceStability: 'STABLE'
        };
      }

      const priceChangePercent = Math.abs(currentPrice - snapshot.price) / snapshot.price * 100;
      
      let priceStability: 'STABLE' | 'MODERATE' | 'VOLATILE' | 'HIGHLY_VOLATILE';
      let recommendedSlippage: number;

      if (priceChangePercent < 1) {
        priceStability = 'STABLE';
        recommendedSlippage = exchangeType === 'pumpfun' ? 0.01 : 0.02;
      } else if (priceChangePercent < 3) {
        priceStability = 'MODERATE';
        recommendedSlippage = exchangeType === 'pumpfun' ? 0.02 : 0.03;
      } else if (priceChangePercent < 5) {
        priceStability = 'VOLATILE';
        recommendedSlippage = exchangeType === 'pumpfun' ? 0.03 : 0.05;
      } else {
        priceStability = 'HIGHLY_VOLATILE';
        recommendedSlippage = 0.05;
      }

      return {
        isHighVolatility: priceChangePercent > 3,
        recommendedSlippage,
        priceStability
      };

    } catch (error) {
      logger.error('Error calculating price volatility metrics', error as Error);
      
      return {
        isHighVolatility: false,
        recommendedSlippage: exchangeType === 'pumpfun' ? 0.01 : 0.02,
        priceStability: 'STABLE'
      };
    }
  }
}

// Schedule periodic cache cleanup
setInterval(() => {
  PriceProtectionService.cleanupPriceCache();
}, 60000); // Every minute