import { Connection, Transaction } from '@solana/web3.js';
import { logger } from '../../utils/logger.js';

interface MemoryUsage {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
}

interface TransactionContext {
  id: string;
  startTime: number;
  connection?: Connection;
  transaction?: Transaction;
  cleanupCallbacks: (() => void)[];
}

export class MemoryOptimizerService {
  private static readonly activeContexts = new Map<string, TransactionContext>();
  private static readonly MEMORY_CHECK_INTERVAL = 30000; // 30 seconds
  private static readonly MAX_HEAP_USAGE_MB = 512; // 512MB threshold
  private static readonly CONTEXT_TIMEOUT_MS = 300000; // 5 minutes

  static {
    // Start memory monitoring
    this.startMemoryMonitoring();
    
    // Schedule periodic cleanup
    this.schedulePeriodicCleanup();
  }

  /**
   * Create a new transaction context with automatic cleanup
   */
  static createContext(id: string): TransactionContext {
    const context: TransactionContext = {
      id,
      startTime: Date.now(),
      cleanupCallbacks: []
    };

    this.activeContexts.set(id, context);

    logger.debug('Transaction context created', {
      id,
      totalContexts: this.activeContexts.size
    });

    return context;
  }

  /**
   * Register cleanup callback for a context
   */
  static registerCleanup(contextId: string, callback: () => void): void {
    const context = this.activeContexts.get(contextId);
    if (context) {
      context.cleanupCallbacks.push(callback);
    }
  }

  /**
   * Clean up transaction context and free memory
   */
  static async cleanupContext(contextId: string): Promise<void> {
    const context = this.activeContexts.get(contextId);
    if (!context) {
      return;
    }

    try {
      // Execute cleanup callbacks
      for (const callback of context.cleanupCallbacks) {
        try {
          callback();
        } catch (error) {
          logger.warn('Cleanup callback failed', { contextId, error });
        }
      }

      // Clean up Connection
      if (context.connection) {
        try {
          // Close connection if it has a close method
          if (typeof (context.connection as any).close === 'function') {
            await (context.connection as any).close();
          }
          context.connection = undefined;
        } catch (error) {
          logger.warn('Connection cleanup failed', { contextId, error });
        }
      }

      // Clean up Transaction
      if (context.transaction) {
        // Clear instructions array
        context.transaction.instructions.length = 0;
        
        // Clear other properties
        context.transaction.recentBlockhash = undefined as any;
        context.transaction.feePayer = undefined as any;
        context.transaction.signatures.length = 0;
        
        context.transaction = undefined;
      }

      // Remove from active contexts
      this.activeContexts.delete(contextId);

      logger.debug('Transaction context cleaned up', {
        contextId,
        executionTime: Date.now() - context.startTime,
        remainingContexts: this.activeContexts.size
      });

    } catch (error) {
      logger.error('Context cleanup error', { contextId, error });
    }
  }

  /**
   * Optimize Connection instance for memory efficiency
   */
  static optimizeConnection(connection: Connection): Connection {
    // Set conservative commitment for memory efficiency
    if ((connection as any)._commitment !== 'confirmed') {
      (connection as any)._commitment = 'confirmed';
    }

    // Disable WebSocket if not needed (saves memory)
    if ((connection as any)._rpcWebSocket) {
      try {
        (connection as any)._rpcWebSocket.close();
        (connection as any)._rpcWebSocket = null;
      } catch (error) {
        // Ignore close errors
      }
    }

    return connection;
  }

  /**
   * Optimize Transaction for memory efficiency
   */
  static optimizeTransaction(transaction: Transaction): Transaction {
    // Remove any large data buffers from instructions
    transaction.instructions.forEach(ix => {
      // Clear any large data buffers that aren't needed
      if (ix.data && ix.data.length > 1024) {
        logger.debug('Large instruction data detected', {
          programId: ix.programId.toString(),
          dataSize: ix.data.length
        });
      }
    });

    return transaction;
  }

  /**
   * Force garbage collection if available and needed
   */
  static async forceGarbageCollection(): Promise<void> {
    try {
      const memUsage = process.memoryUsage();
      const heapUsedMB = memUsage.heapUsed / 1024 / 1024;

      if (heapUsedMB > this.MAX_HEAP_USAGE_MB) {
        // Force garbage collection if available
        if (global.gc) {
          logger.info('Forcing garbage collection', {
            heapUsedMB: heapUsedMB.toFixed(2),
            threshold: this.MAX_HEAP_USAGE_MB
          });
          
          global.gc();
          
          // Log memory usage after GC
          const afterGC = process.memoryUsage();
          logger.info('Garbage collection completed', {
            beforeMB: heapUsedMB.toFixed(2),
            afterMB: (afterGC.heapUsed / 1024 / 1024).toFixed(2),
            freedMB: ((memUsage.heapUsed - afterGC.heapUsed) / 1024 / 1024).toFixed(2)
          });
        } else {
          logger.warn('Garbage collection not available', {
            heapUsedMB: heapUsedMB.toFixed(2)
          });
        }
      }
    } catch (error) {
      logger.error('Garbage collection error', error as Error);
    }
  }

  /**
   * Get current memory usage statistics
   */
  static getMemoryUsage(): MemoryUsage & {
    activeContexts: number;
    heapUsedMB: number;
    heapTotalMB: number;
    externalMB: number;
    rssMB: number;
  } {
    const usage = process.memoryUsage();
    
    return {
      ...usage,
      activeContexts: this.activeContexts.size,
      heapUsedMB: usage.heapUsed / 1024 / 1024,
      heapTotalMB: usage.heapTotal / 1024 / 1024,
      externalMB: usage.external / 1024 / 1024,
      rssMB: usage.rss / 1024 / 1024
    };
  }

  /**
   * Start memory monitoring
   */
  private static startMemoryMonitoring(): void {
    setInterval(() => {
      const usage = this.getMemoryUsage();
      
      // Log memory usage
      logger.debug('Memory usage check', {
        heapUsedMB: usage.heapUsedMB.toFixed(2),
        heapTotalMB: usage.heapTotalMB.toFixed(2),
        activeContexts: usage.activeContexts
      });

      // Check for memory pressure
      if (usage.heapUsedMB > this.MAX_HEAP_USAGE_MB) {
        logger.warn('High memory usage detected', {
          heapUsedMB: usage.heapUsedMB.toFixed(2),
          threshold: this.MAX_HEAP_USAGE_MB,
          activeContexts: usage.activeContexts
        });

        // Force garbage collection
        this.forceGarbageCollection();
        
        // Clean up old contexts
        this.cleanupExpiredContexts();
      }

    }, this.MEMORY_CHECK_INTERVAL);
  }

  /**
   * Schedule periodic cleanup of expired contexts
   */
  private static schedulePeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredContexts();
    }, 60000); // Every minute
  }

  /**
   * Clean up expired transaction contexts
   */
  private static cleanupExpiredContexts(): void {
    const now = Date.now();
    const expiredContexts: string[] = [];

    for (const [id, context] of this.activeContexts.entries()) {
      if (now - context.startTime > this.CONTEXT_TIMEOUT_MS) {
        expiredContexts.push(id);
      }
    }

    if (expiredContexts.length > 0) {
      logger.info('Cleaning up expired contexts', {
        expiredCount: expiredContexts.length,
        totalContexts: this.activeContexts.size
      });

      for (const contextId of expiredContexts) {
        this.cleanupContext(contextId);
      }
    }
  }

  /**
   * Create memory-optimized connection
   */
  static createOptimizedConnection(rpcUrl: string): Connection {
    const connection = new Connection(rpcUrl, {
      commitment: 'confirmed',
      wsEndpoint: undefined, // Disable WebSocket to save memory
      httpHeaders: {
        'Connection': 'close' // Don't keep connections alive
      }
    });

    return this.optimizeConnection(connection);
  }

  /**
   * Safe execution wrapper with automatic cleanup
   */
  static async executeWithCleanup<T>(
    contextId: string,
    executeFn: (context: TransactionContext) => Promise<T>
  ): Promise<T> {
    const context = this.createContext(contextId);
    
    try {
      const result = await executeFn(context);
      return result;
    } catch (error) {
      logger.error('Execution failed', { contextId, error });
      throw error;
    } finally {
      // Always cleanup, even on error
      await this.cleanupContext(contextId);
    }
  }

  /**
   * Monitor and log memory leaks
   */
  static detectMemoryLeaks(): void {
    const usage = this.getMemoryUsage();
    
    // Detect potential memory leaks
    if (usage.activeContexts > 50) {
      logger.warn('Potential memory leak - too many active contexts', {
        activeContexts: usage.activeContexts,
        heapUsedMB: usage.heapUsedMB.toFixed(2)
      });
    }

    if (usage.heapUsedMB > this.MAX_HEAP_USAGE_MB * 1.5) {
      logger.error('Critical memory usage detected', {
        heapUsedMB: usage.heapUsedMB.toFixed(2),
        threshold: this.MAX_HEAP_USAGE_MB,
        activeContexts: usage.activeContexts
      });
    }
  }
}