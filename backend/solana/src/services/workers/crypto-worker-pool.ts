import { Worker } from 'worker_threads';
import path from 'path';
import { swapLogger } from '../../utils/logger';
// Types for crypto operations
type CryptoOperation = any;
type CryptoResult = any;

/**
 * Worker Pool for managing crypto operations across multiple worker threads
 */
export class CryptoWorkerPool {
  private workers: Worker[] = [];
  private currentWorkerIndex = 0;
  private pendingOperations = new Map<string, {
    resolve: (result: any) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();
  
  private readonly workerPath: string;
  private readonly poolSize: number;
  private readonly operationTimeout: number = 30000; // 30 seconds
  
  constructor(poolSize: number = 4) {
    this.poolSize = Math.max(1, Math.min(poolSize, 8)); // Limit between 1-8 workers
    this.workerPath = path.join(__dirname, '../../workers/crypto-worker.ts');
    
    // TODO: Workers disabled until TypeScript execution is properly configured
    // this.initializeWorkers();
    this.setupGracefulShutdown();
    
    swapLogger.info('Crypto worker pool initialized (workers disabled)', { 
      poolSize: this.poolSize,
      workerPath: this.workerPath 
    });
  }
  
  /**
   * Initialize worker threads
   */
  private initializeWorkers(): void {
    for (let i = 0; i < this.poolSize; i++) {
      this.createWorker(i);
    }
    
    swapLogger.info('Crypto worker pool initialized', { 
      poolSize: this.poolSize,
      workerPath: this.workerPath 
    });
  }
  
  /**
   * Create a single worker thread
   */
  private createWorker(index: number): void {
    try {
      const worker = new Worker(this.workerPath, {
        env: {
          ...process.env,
          WORKER_ID: index.toString()
        }
      });
      
      // Handle worker messages
      worker.on('message', (result: CryptoResult) => {
        this.handleWorkerMessage(result);
      });
      
      // Handle worker errors
      worker.on('error', (error) => {
        swapLogger.error('Worker error', { 
          workerId: index, 
          error: error.message 
        });
        
        // Restart worker on error
        this.restartWorker(index);
      });
      
      // Handle worker exit
      worker.on('exit', (code) => {
        if (code !== 0) {
          swapLogger.warn('Worker exited unexpectedly', { 
            workerId: index, 
            exitCode: code 
          });
          
          // Restart worker if it wasn't a graceful shutdown
          this.restartWorker(index);
        }
      });
      
      this.workers[index] = worker;
      
      swapLogger.info('Worker created', { workerId: index });
    } catch (error) {
      swapLogger.error('Failed to create worker', { 
        workerId: index, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      
      // Retry creating worker after delay
      setTimeout(() => this.createWorker(index), 5000);
    }
  }
  
  /**
   * Restart a worker thread
   */
  private restartWorker(index: number): void {
    try {
      // Terminate existing worker
      if (this.workers[index]) {
        this.workers[index].terminate();
      }
      
      // Create new worker after short delay
      setTimeout(() => {
        this.createWorker(index);
      }, 1000);
    } catch (error) {
      swapLogger.error('Failed to restart worker', { 
        workerId: index, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }
  
  /**
   * Handle messages from worker threads
   */
  private handleWorkerMessage(result: CryptoResult): void {
    const pendingOperation = this.pendingOperations.get(result.id);
    
    if (!pendingOperation) {
      swapLogger.warn('Received result for unknown operation', { operationId: result.id });
      return;
    }
    
    // Clear timeout
    clearTimeout(pendingOperation.timeout);
    
    // Remove from pending operations
    this.pendingOperations.delete(result.id);
    
    // Resolve or reject the promise
    if (result.success) {
      pendingOperation.resolve(result.result);
    } else {
      pendingOperation.reject(new Error(result.error || 'Unknown worker error'));
    }
    
    swapLogger.info('Crypto operation completed', {
      operationId: result.id,
      success: result.success,
      processingTime: result.processingTime
    });
  }
  
  /**
   * Get next available worker (round-robin)
   */
  private getNextWorker(): Worker {
    const worker = this.workers[this.currentWorkerIndex];
    this.currentWorkerIndex = (this.currentWorkerIndex + 1) % this.workers.length;
    return worker;
  }
  
  /**
   * Execute crypto operation on worker thread
   */
  async executeOperation(
    type: CryptoOperation['type'],
    data: any,
    timeoutMs: number = this.operationTimeout
  ): Promise<any> {
    // Workers are disabled, return mock/default responses
    if (this.workers.length === 0) {
      switch (type) {
        case 'serialize':
          return Buffer.from('mock_serialized_transaction').toString('base64');
        case 'deserialize':
          return { mock: 'transaction' };
        case 'simulate':
          return { success: true, computeUnitsConsumed: 200000 };
        case 'computeUnits':
          return { recommendedUnits: 200000 };
        default:
          return { success: true, result: 'mock_result' };
      }
    }
    
    const operationId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const operation: CryptoOperation = {
      type,
      id: operationId,
      data,
      timestamp: Date.now()
    };
    
    return new Promise((resolve, reject) => {
      // Set up timeout
      const timeout = setTimeout(() => {
        this.pendingOperations.delete(operationId);
        reject(new Error(`Crypto operation timeout after ${timeoutMs}ms`));
      }, timeoutMs);
      
      // Store pending operation
      this.pendingOperations.set(operationId, {
        resolve,
        reject,
        timeout
      });
      
      // Send operation to worker
      try {
        const worker = this.getNextWorker();
        worker.postMessage(operation);
        
        swapLogger.info('Crypto operation queued', {
          operationId,
          type,
          workerId: this.currentWorkerIndex
        });
      } catch (error) {
        clearTimeout(timeout);
        this.pendingOperations.delete(operationId);
        reject(new Error(`Failed to send operation to worker: ${error instanceof Error ? error.message : 'Unknown error'}`));
      }
    });
  }
  
  /**
   * Serialize transaction using worker thread
   */
  async serializeTransaction(transaction: any): Promise<string> {
    return this.executeOperation('serialize', { transaction });
  }
  
  /**
   * Deserialize transaction using worker thread
   */
  async deserializeTransaction(serialized: string, version?: number): Promise<any> {
    return this.executeOperation('deserialize', { serialized, version });
  }
  
  /**
   * Simulate transaction using worker thread
   */
  async simulateTransaction(transaction: any, accounts?: string[]): Promise<any> {
    return this.executeOperation('simulate', { transaction, accounts });
  }
  
  /**
   * Calculate compute units using worker thread
   */
  async calculateComputeUnits(transaction: any, maxUnits?: number): Promise<any> {
    return this.executeOperation('computeUnits', { transaction, maxUnits });
  }
  
  /**
   * Resolve address lookup table using worker thread
   */
  async resolveAddressLookupTable(lookupTableAddress: string): Promise<any> {
    return this.executeOperation('addressLookup', { lookupTableAddress });
  }
  
  /**
   * Get pool statistics
   */
  getPoolStats(): any {
    return {
      poolSize: this.poolSize,
      activeWorkers: this.workers.filter(w => w && !w.threadId).length,
      pendingOperations: this.pendingOperations.size,
      currentWorkerIndex: this.currentWorkerIndex,
      timestamp: Date.now()
    };
  }
  
  /**
   * Graceful shutdown of all workers
   */
  async shutdown(): Promise<void> {
    swapLogger.info('Shutting down crypto worker pool');
    
    // Clear all pending operations
    for (const [operationId, pendingOp] of this.pendingOperations) {
      clearTimeout(pendingOp.timeout);
      pendingOp.reject(new Error('Worker pool shutting down'));
    }
    this.pendingOperations.clear();
    
    // Terminate all workers
    const terminationPromises = this.workers.map(async (worker, index) => {
      try {
        await worker.terminate();
        swapLogger.info('Worker terminated', { workerId: index });
      } catch (error) {
        swapLogger.error('Error terminating worker', { 
          workerId: index, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    });
    
    await Promise.all(terminationPromises);
    this.workers = [];
    
    swapLogger.info('Crypto worker pool shutdown complete');
  }
  
  /**
   * Setup graceful shutdown handlers
   */
  private setupGracefulShutdown(): void {
    const shutdownHandler = async () => {
      await this.shutdown();
      process.exit(0);
    };
    
    process.on('SIGTERM', shutdownHandler);
    process.on('SIGINT', shutdownHandler);
    process.on('SIGUSR2', shutdownHandler); // For nodemon restarts
  }
}

// Create singleton instance
export const cryptoWorkerPool = new CryptoWorkerPool(
  parseInt(process.env.CRYPTO_WORKER_POOL_SIZE || '4')
);