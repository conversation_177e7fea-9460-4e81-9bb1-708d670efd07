{"name": "solana", "version": "1.0.0", "main": "dist/index.js", "scripts": {"start": "node -r dotenv/config dist/index.js", "dev": "nodemon -r dotenv/config --exec ts-node src/index.ts", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "description": "Solana liquidity pool project", "devDependencies": {"@types/bn.js": "^5.1.6", "@types/bull": "^4.10.0", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.15.18", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@privy-io/server-auth": "^1.22.1", "@raydium-io/raydium-sdk-v2": "^0.1.138-alpha", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.2", "@supabase/supabase-js": "^2.50.2", "axios": "^1.9.0", "bn.js": "^5.2.2", "bs58": "^6.0.0", "bull": "^4.16.0", "canonicalize": "^2.1.0", "cors": "^2.8.5", "decimal.js": "^10.5.0", "dotenv": "^16.5.0", "express": "^5.1.0", "winston": "^3.17.0"}}