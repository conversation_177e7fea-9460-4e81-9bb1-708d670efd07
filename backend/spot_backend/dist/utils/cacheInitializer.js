import { logger } from './logger.js';
// import { refreshConnectCoinsCache, refreshNetworkHighlightsCache, refreshTokenRadarCache } from '../controllers/homeController.js'; // REMOVED
/**
 * Initialize all caches on server startup (non-blocking)
 * This handles the initial loading of data for all cache endpoints
 */
export async function initializeCaches() {
    // REMOVED - Cache initialization for connect coins, network highlights, and token radar
    logger.info('Cache initialization disabled - connect coins, network highlights, and token radar removed');
}
// REMOVED - All cache initialization functions for connect coins, network highlights, and token radar
//# sourceMappingURL=cacheInitializer.js.map