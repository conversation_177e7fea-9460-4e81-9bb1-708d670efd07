import cron from 'node-cron';
import { logger } from './logger.js';
import { performanceMonitorService } from '../services/performanceMonitorService.js';
/**
 * Schedule all cache refresh jobs with worker thread optimization
 * @param schedule Cron schedule expression (default: every 5 minutes)
 */
export function scheduleCacheRefresh(schedule = '*/5 * * * *') {
    logger.info(`Setting up scheduled cache refresh with schedule: ${schedule}`);
    // Schedule the refresh job
    cron.schedule(schedule, async () => {
        const startTime = Date.now();
        logger.info('Running scheduled refresh of home page data');
        try {
            // Execute cache refreshes in parallel (worker threads temporarily disabled)
            const refreshPromises = [
            // Cache refresh disabled - connect coins, network highlights, and token radar removed
            ];
            // Wait for all cache refreshes to complete
            const results = await Promise.allSettled(refreshPromises);
            // Log results
            results.forEach((result, index) => {
                const cacheTypes = ['connect-coins', 'network-highlights', 'token-radar'];
                if (result.status === 'fulfilled') {
                    logger.info(`✅ ${cacheTypes[index]} cache refresh completed`);
                }
                else {
                    logger.error(`❌ ${cacheTypes[index]} cache refresh failed:`, result.reason);
                }
            });
            const totalTime = Date.now() - startTime;
            performanceMonitorService.recordMetric('cache_refresh_duration', totalTime, { type: 'scheduled' });
            logger.info(`Scheduled cache refresh completed in ${totalTime}ms`);
        }
        catch (error) {
            logger.error('Failed to execute scheduled cache refresh:', error);
            performanceMonitorService.recordMetric('cache_refresh_errors', 1, { type: 'scheduled' });
        }
    });
    logger.info('Cache refresh scheduler initialized with worker thread support');
}
/**
 * Execute cache refresh with fallback to main thread if worker fails
 * (Currently disabled - worker threads temporarily unavailable)
 */
// async function executeWithFallback(cacheType: string, fallbackFn: () => Promise<any>): Promise<any> {
//   try {
//     // Try to execute in worker thread first
//     const result = await workerThreadService.executeCacheRefresh(cacheType, {});
//     if (result.success) {
//       return result.data;
//     } else {
//       logger.warn(`Worker thread failed for ${cacheType}, falling back to main thread`);
//       return await fallbackFn();
//     }
//   } catch (error) {
//     logger.warn(`Worker thread error for ${cacheType}, falling back to main thread:`, error);
//     return await fallbackFn();
//   }
// }
// Token radar refresh function removed - no longer needed
//# sourceMappingURL=cacheScheduler.js.map