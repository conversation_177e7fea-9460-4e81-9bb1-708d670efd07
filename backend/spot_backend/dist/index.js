import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from 'http';
import config from './config/index.js';
// Load environment variables
dotenv.config();
import homeRoutes from './routes/homeRoutes.js';
import walletRoutes from './routes/walletRoutes.js';
import activityRoutes from './routes/activityRoutes.js';
import notificationRoutes from './routes/notificationRoutes.js';
import { errorHandler, notFound } from './middleware/errorHandler.js';
import { initRedis } from './utils/redis.js';
import { initializeCaches } from './utils/cacheInitializer.js';
import { logger } from './utils/logger.js';
import { frontendWebSocketService } from './services/frontendWebSocketService.js';
import { workerThreadService } from './services/workerThreadService.js';
import { performanceMonitorService } from './services/performanceMonitorService.js';
// Initialize express app
const app = express();
// Middleware
app.use(cors({
    origin: '*', // Allow all origins
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());
// Add performance monitoring middleware
app.use(performanceMonitorService.createMiddleware());
// Routes
app.get('/', (req, res) => {
    res.json({ message: 'Welcome to the Spot Trading API' });
});
// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        service: 'spot-backend',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});
// Enhanced performance monitoring endpoint
app.get('/api/performance', (req, res) => {
    try {
        const performanceStats = performanceMonitorService.getStats();
        const realTimeMetrics = performanceMonitorService.getRealTimeMetrics();
        const workerStats = workerThreadService.getStats();
        res.json({
            status: 'success',
            timestamp: Date.now(),
            data: {
                performance: performanceStats,
                workers: workerStats,
                realTime: realTimeMetrics,
                cache: {},
                deduplication: {},
                memory: {
                    heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                    heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
                    external: Math.round(process.memoryUsage().external / 1024 / 1024),
                    rss: Math.round(process.memoryUsage().rss / 1024 / 1024)
                },
                uptime: Math.round(process.uptime())
            }
        });
    }
    catch (error) {
        logger.error('Performance endpoint error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to retrieve performance metrics'
        });
    }
});
// WebSocket health check endpoint
app.get('/api/websocket/health', (req, res) => {
    try {
        const isInitialized = frontendWebSocketService.isInitialized();
        const stats = frontendWebSocketService.getStats();
        if (isInitialized) {
            res.json({
                ready: true,
                status: 'active',
                connectedClients: stats.totalClients,
                pulseRoomClients: stats.pulseRoomClients,
                message: 'WebSocket service is running',
                timestamp: Date.now()
            });
        }
        else {
            res.status(503).json({
                ready: false,
                status: 'initializing',
                connectedClients: 0,
                pulseRoomClients: 0,
                message: 'WebSocket service is initializing',
                timestamp: Date.now()
            });
        }
    }
    catch (error) {
        res.status(500).json({
            ready: false,
            status: 'error',
            connectedClients: 0,
            pulseRoomClients: 0,
            message: `WebSocket service error: ${error.message}`,
            timestamp: Date.now()
        });
    }
});
// API Routes
app.use('/api/home', homeRoutes);
app.use('/api/wallet', walletRoutes);
// Also register wallet routes without /api prefix for backward compatibility
app.use('/wallet', walletRoutes);
app.use('/api/activity', activityRoutes);
app.use('/api/notifications', notificationRoutes);
// Error handling middleware
app.use(notFound);
app.use(errorHandler);
// Create HTTP server
const httpServer = createServer(app);
console.log('HTTP server created');
// Start the server
const PORT = config.port;
console.log(`Attempting to start server on port ${PORT}...`);
const server = httpServer.listen(Number(PORT), '0.0.0.0', async () => {
    console.log(`Server running on port ${PORT}`);
    try {
        console.log('Starting server initialization...');
        // Initialize Redis first
        console.log('Initializing Redis...');
        await initRedis();
        console.log('Redis initialized');
        // Test database connection
        console.log('🗄️ Database connection test skipped (will test via API)');
        // const dbConnected = await testDatabaseConnection();
        // if (dbConnected) {
        //   console.log('✅ Database connection successful');
        // } else {
        //   console.warn('⚠️ Database connection failed - limit orders may not work');
        // }
        // Initialize Frontend WebSocket service EARLY - before heavy operations
        console.log('🔧 Initializing Frontend WebSocket service...');
        try {
            frontendWebSocketService.initialize(httpServer);
            console.log('✅ Frontend WebSocket service initialization completed');
        }
        catch (error) {
            console.error('❌ Failed to initialize Frontend WebSocket service:', error.message);
            logger.error('Frontend WebSocket service initialization failed:', error);
        }
        // Initialize Worker Thread Service
        console.log('🧵 Initializing Worker Thread Service...');
        try {
            await workerThreadService.initialize();
            console.log('✅ Worker Thread Service initialization completed');
        }
        catch (error) {
            console.error('❌ Failed to initialize Worker Thread Service:', error.message);
            logger.error('Worker Thread Service initialization failed:', error);
            // Continue without worker threads - will fall back to main thread processing
        }
        // Initialize all data caches (this can take time)
        console.log('📦 Initializing data caches...');
        await initializeCaches();
        console.log('✅ Data caches initialization completed');
        // Set up scheduled tasks with worker thread support
        // scheduleCacheRefresh('*/5 * * * *'); // Disabled - cache refresh removed
        logger.info('Server initialization completed');
        // Return realistic values - this is what we'll show to users
        // In production, this would be replaced with real blockchain data
        const balances = {
            BTC: 0.128, // ~$5,500 at current prices
            ETH: 0.25, // ~$500 at current prices
            SOL: 1.5 // ~$150 at current prices
        };
    }
    catch (error) {
        logger.error('Error during server initialization:', error);
    }
});
// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
// Graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('SIGTERM received, shutting down gracefully');
    // Shutdown services in order
    try {
        await workerThreadService.shutdown();
        frontendWebSocketService.shutdown();
        performanceMonitorService.stop();
    }
    catch (error) {
        logger.error('Error during service shutdown:', error);
    }
    server.close(() => {
        logger.info('Process terminated');
        process.exit(0);
    });
});
process.on('SIGINT', async () => {
    logger.info('SIGINT received, shutting down gracefully');
    // Shutdown services in order
    // await workerThreadService.shutdown();
    frontendWebSocketService.shutdown();
    // performanceMonitorService.stop();
    server.close(() => {
        logger.info('Process terminated');
        process.exit(0);
    });
});
//# sourceMappingURL=index.js.map