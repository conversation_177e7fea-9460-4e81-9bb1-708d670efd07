import express from 'express';
import homeRoutes from './homeRoutes';
import limitOrderRoutes from './limitOrderRoutes';
import activityRoutes from './activityRoutes';
import tradeHistoryRoutes from './tradeHistoryRoutes';
const router = express.Router();
// Register all API routes
router.use('/home', homeRoutes);
router.use('/limit-orders', limitOrderRoutes);
router.use('/activity', activityRoutes);
router.use('/trade-history', tradeHistoryRoutes);
export default router;
//# sourceMappingURL=index.js.map