import express from 'express';
import { logger } from '../utils/logger.js';
import { notificationService } from '../services/notificationService.js';
const router = express.Router();
router.get('/', async (req, res) => {
    try {
        const userId = req.query.user_id;
        const limit = parseInt(req.query.limit) || 50;
        const offset = parseInt(req.query.offset) || 0;
        if (!userId) {
            return res.status(400).json({
                success: false,
                message: 'user_id is required',
                timestamp: new Date().toISOString()
            });
        }
        const result = await notificationService.getUserNotifications(userId, limit, offset);
        if (!result.success) {
            return res.status(500).json({
                success: false,
                message: result.error || 'Failed to get notifications',
                timestamp: new Date().toISOString()
            });
        }
        res.json({
            success: true,
            data: result.data,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger.error('Error getting notifications', { error: error.message });
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
router.get('/unread-count', async (req, res) => {
    try {
        const userId = req.query.user_id;
        if (!userId) {
            return res.status(400).json({
                success: false,
                message: 'user_id is required',
                timestamp: new Date().toISOString()
            });
        }
        const result = await notificationService.getUnreadNotificationCount(userId);
        if (!result.success) {
            return res.status(500).json({
                success: false,
                message: result.error || 'Failed to get unread count',
                timestamp: new Date().toISOString()
            });
        }
        res.json({
            success: true,
            data: {
                unread_count: result.count
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger.error('Error getting unread count', { error: error.message });
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
router.post('/', async (req, res) => {
    try {
        const result = await notificationService.storeNotification(req.body);
        if (!result.success) {
            return res.status(500).json({
                success: false,
                message: result.error || 'Failed to create notification',
                timestamp: new Date().toISOString()
            });
        }
        res.status(201).json({
            success: true,
            data: result.data,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger.error('Error creating notification', { error: error.message });
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
router.patch('/:id/read', async (req, res) => {
    try {
        const notificationId = req.params.id;
        const result = await notificationService.updateNotificationStatus(notificationId, 'completed');
        if (!result.success) {
            return res.status(500).json({
                success: false,
                message: result.error || 'Failed to mark notification as read',
                timestamp: new Date().toISOString()
            });
        }
        res.json({
            success: true,
            data: result.data,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger.error('Error marking notification as read', { error: error.message });
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
router.delete('/:id', async (_req, res) => {
    res.status(501).json({
        success: false,
        message: 'Delete notification not implemented yet',
        timestamp: new Date().toISOString()
    });
});
export default router;
//# sourceMappingURL=notificationRoutes.js.map