import { logger } from '../utils/logger.js';
import { 
// fetchConnectCoinsData, // REMOVED
fetchMarketPairs, fetchHolders, fetchChartData, fetchSearchResults, getApiKey, fetchMarketData, fetchMarketPair, fetchActivity, fetchNetworkIcons } from '../services/coinService.js';
import { fetchPairListFromDexScreener } from '../services/dexService.js';
import { fetchPulseData } from '../services/pulseService.js';
// Cache keys for different sections (base keys)
const NETWORK_TRENDS_CACHE_KEY = 'home:network:trends';
const CONNECT_COINS_CACHE_KEY = 'home:connect:coins';
const TOKEN_RADAR_CACHE_KEY = 'home:token:radar';
const CACHE_EXPIRY = 3000; // 5 minutes in seconds
// Supported networks
// Only support BSC and Solana networks, plus universal which combines both
const SUPPORTED_NETWORKS = ['universal', 'solana', 'binance-smart-chain'];
/**
 * Helper function to generate network-specific cache key
 */
function getNetworkCacheKey(baseKey, network) {
    if (network === 'universal' || !SUPPORTED_NETWORKS.includes(network)) {
        return baseKey;
    }
    return `${baseKey}:${network}`;
}
/**
 * Process the combined BSC and Solana data for universal network response
 * This combines data from these two networks into a single unified structure
 */
function processUniversalHighlights(allNetworkData) {
    const trending = [];
    const gainers = [];
    const losers = [];
    const newCoins = [];
    for (const network in allNetworkData) {
        if (network === 'universal')
            continue;
        const networkData = allNetworkData[network];
        if (!networkData)
            continue;
        if (Array.isArray(networkData.trending)) {
            trending.push(...networkData.trending.map((coin) => ({ ...coin, network })));
        }
        if (Array.isArray(networkData.gainers)) {
            gainers.push(...networkData.gainers.map((coin) => ({ ...coin, network })));
        }
        if (Array.isArray(networkData.losers)) {
            losers.push(...networkData.losers.map((coin) => ({ ...coin, network })));
        }
        if (Array.isArray(networkData.new)) {
            newCoins.push(...networkData.new.map((coin) => ({ ...coin, network })));
        }
    }
    logger.info(`[Universal] Counts after merge before sort/dedupe - Trending: ${trending.length}, Gainers: ${gainers.length}, Losers: ${losers.length}, New: ${newCoins.length}`);
    // --- Re-sort the combined lists --- 
    const sortedTrending = [...trending].sort((a, b) => (b.total_volume || 0) - (a.total_volume || 0));
    const sortedGainers = [...gainers].sort((a, b) => (b.price_change_percentage_24h || 0) - (a.price_change_percentage_24h || 0));
    const sortedLosers = [...losers].sort((a, b) => (a.price_change_percentage_24h || 0) - (b.price_change_percentage_24h || 0));
    const sortedNewCoins = [...newCoins].sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime());
    logger.info(`[Universal] Counts after re-sort - Trending: ${sortedTrending.length}, Gainers: ${sortedGainers.length}, Losers: ${sortedLosers.length}, New: ${sortedNewCoins.length}`);
    // --- Deduplicate the sorted lists --- (Using existing helper)
    const uniqueTrending = getUniqueCoins(sortedTrending);
    const uniqueGainers = getUniqueCoins(sortedGainers);
    const uniqueLosers = getUniqueCoins(sortedLosers);
    const uniqueNewCoins = getUniqueCoins(sortedNewCoins);
    logger.info(`[Universal] Counts after dedupe - Trending: ${uniqueTrending.length}, Gainers: ${uniqueGainers.length}, Losers: ${uniqueLosers.length}, New: ${uniqueNewCoins.length}`);
    // --- Slice the final lists --- 
    const finalTrending = uniqueTrending.slice(0, 20);
    const finalGainers = uniqueGainers.slice(0, 20);
    const finalLosers = uniqueLosers.slice(0, 20);
    const finalNew = uniqueNewCoins.slice(0, 20);
    logger.info(`[Universal] Counts after slice - Trending: ${finalTrending.length}, Gainers: ${finalGainers.length}, Losers: ${finalLosers.length}, New: ${finalNew.length}`);
    return {
        trending: finalTrending,
        gainers: finalGainers,
        losers: finalLosers,
        new: finalNew
    };
}
/**
 * Helper to remove duplicate coins by id
 */
function getUniqueCoins(coins) {
    const uniqueMap = new Map();
    for (const coin of coins) {
        if (coin && coin.id && !uniqueMap.has(coin.id)) {
            uniqueMap.set(coin.id, coin);
        }
    }
    return Array.from(uniqueMap.values());
}
/**
 * Process the result from fetchUniversalTokenRadar for API response
 * This combines data from different networks into a single unified structure
 */
function processUniversalTokenRadar(tokenData) {
    if (!Array.isArray(tokenData) || tokenData.length === 0) {
        return [];
    }
    try {
        // Remove duplicates by token id
        const uniqueTokens = getUniqueTokens(tokenData);
        return uniqueTokens;
    }
    catch (error) {
        logger.error(`Error processing universal token radar data: ${error.message}`);
        return tokenData;
    }
}
/**
 * Helper to remove duplicate tokens by id
 */
function getUniqueTokens(tokens) {
    const uniqueMap = new Map();
    for (const token of tokens) {
        if (token && token.id && !uniqueMap.has(token.id)) {
            uniqueMap.set(token.id, token);
        }
    }
    return Array.from(uniqueMap.values());
}
/**
 * Validate if a given network is supported
 * @param network Network string to validate
 * @returns Object with validation result and optional message
 */
function validateNetwork(network) {
    // Only support the 3 specified networks
    if (!SUPPORTED_NETWORKS.includes(network)) {
        return {
            valid: false,
            message: `Invalid network parameter. Supported networks are: ${SUPPORTED_NETWORKS.join(', ')}`
        };
    }
    return { valid: true };
}
/**
 * Get network-specific highlights (Trending, Gainers, Losers, New tokens)
 * Supports filtering by network (universal, solana, binance-smart-chain)
 */
// // REMOVED - Network highlights endpoint
// // export const getNetworkHighlights = async (req: Request, res: Response) => {
//   try {
//     const { network, forceRefresh } = req.query;
//     
//     // Validate network parameter, default to universal if not provided
//     const selectedNetwork = String(network || 'universal').toLowerCase();
//     const validation = validateNetwork(selectedNetwork);
//     
//     if (!validation.valid) {
//       return res.status(400).json({
//         error: validation.message,
//         status: 'error'
//       });
//     }
//     
//     // Generate cache key
//     const cacheKey = getNetworkCacheKey(NETWORK_TRENDS_CACHE_KEY, selectedNetwork);
//     
//     // Skip cache if forceRefresh is true
//     const skipCache = forceRefresh === 'true';
// 
//     if (skipCache) {
//       // Clear the cache for this network
//       await removeFromCache(cacheKey);
//     } else {
//       const cachedData = await getFromCache(cacheKey);
//       if (cachedData) {
//         if (isDataEmpty(cachedData)) {
//           return res.json({
//             data: cachedData,
//             status: 'success',
//             source: 'cache'
//           });
//         } else {
//           await removeFromCache(cacheKey);
//         }
//       }
//     }
//     
//     // Get API key from env
//     const apiKey = await getApiKey();
//     
//     if (!apiKey) {
//       logger.error('API key not found for Mobula API');
//       return res.status(500).json({
//         error: 'Mobula API key is not configured',
//         status: 'error'
//       });
//     }
//     
//     // Fetch from API if no valid cache hit or forceRefresh
//     let responseData;
//     let source = 'api';
// 
//     if (selectedNetwork === 'universal') {
//       const allNetworkData: Record<string, any> = {};
// 
//       for (const net of ['binance-smart-chain', 'solana']) {
//         try {
//           const data = await fetchNetworkHighlights(net, apiKey);
//           allNetworkData[net] = data;
//         } catch (error: any) {
//           logger.error(`Error fetching ${net} highlights for universal: ${error.message}`);
//         }
//       }
//       responseData = processUniversalHighlights(allNetworkData);
//     } else {
//       // For specific networks (BSC or Solana)
//       responseData = await fetchNetworkHighlights(selectedNetwork, apiKey);
//     }
// 
//     // Cache the API response if it has content
//     if (isDataEmpty(responseData)) {
//       await setToCache(cacheKey, responseData, CACHE_EXPIRY);
//     }
// 
//     return res.json({
//       data: responseData,
//       status: 'success',
//       source: source // Keep source field as 'api'
//     });
// 
//   } catch (error: any) {
//     logger.error(`Error fetching network highlights: ${error.message}`);
//     return res.status(500).json({
//       error: 'Server error processing network highlights',
//       message: error.message,
//       status: 'error'
//     });
//   }
// };
// Helper function to check if network data is empty
function isDataEmpty(data) {
    if (!data)
        return false;
    // Check if any of the four categories has content
    const categories = ['trending', 'gainers', 'losers', 'new'];
    for (const category of categories) {
        if (Array.isArray(data[category]) && data[category].length > 0) {
            return true; // Data has content
        }
    }
    return false; // All categories are empty
}
export const getNetworkIcons = async (req, res) => {
    try {
        // Fetch network icons from CoinGecko
        const icons = await fetchNetworkIcons();
        if (!icons || Object.keys(icons).length === 0) {
            return res.status(404).json({
                error: 'No network icons available',
                status: 'error'
            });
        }
        return res.json({
            data: icons,
            status: 'success',
            source: 'api'
        });
    }
    catch (error) {
        logger.error(`Error in getNetworkIcons: ${error.message}`);
        return res.status(500).json({
            error: 'Server error fetching network icons',
            message: error.message,
            status: 'error'
        });
    }
};
export const getPulseData = async (req, res) => {
    try {
        const apiKey = await getApiKey();
        if (!apiKey) {
            logger.error('API key not found for Mobula API');
            return res.status(500).json({
                error: 'Mobula API key is not configured',
                status: 'error'
            });
        }
        // Optional: Register user activity if userId is provided in query params
        const userId = req.query.userId;
        if (userId) {
            const { userActivityService } = await import('../services/userActivityService.js');
            userActivityService.registerPulseActivity(userId);
            logger.debug(`Registered pulse activity for user ${userId} via API call`);
        }
        logger.info('Fetching Pulse data');
        const coinsData = await fetchPulseData(apiKey, 'solana');
        if (!coinsData || (Array.isArray(coinsData) && coinsData.length === 0)) {
            logger.warn('No Pulse data found');
            return res.status(200).json({
                data: {
                    new: [],
                    bonding: [],
                    bonded: []
                },
                status: 'success',
                source: 'empty'
            });
        }
        // Determine data source
        const { mobulaWebSocketService } = await import('../services/mobulaWebSocketService.js');
        const isFromWebSocket = mobulaWebSocketService.hasFreshData();
        const source = isFromWebSocket ? 'websocket' : 'api';
        // Log data source before returning
        logger.info(`[${source.toUpperCase()}] Returning Pulse data from ${source}.`);
        return res.json({
            data: coinsData,
            status: 'success',
            source: source
        });
    }
    catch (error) {
        logger.error(`Error in Pulse Data: ${error.message}`);
        return res.status(500).json({
            error: 'Server error processing Pulse data',
            message: error.message,
            status: 'error'
        });
    }
};
/**
 * Get connect coins (BTC, ETH, SOL) basic data for home page
 */
// export const getConnectCoins = async (req: Request, res: Response) => {
//   try {
//     const cachedData = await getFromCache(CONNECT_COINS_CACHE_KEY);
//     if (cachedData) {
//       return res.json({
//         data: cachedData,
//         status: 'success',
//         source: 'cache'
//       });
//     }
// 
//     // Get API key from env
//     const apiKey = await getApiKey();
//     
//     if (!apiKey) {
//       logger.error('API key not found for Mobula API');
//       return res.status(500).json({
//         error: 'Mobula API key is not configured',
//         status: 'error'
//       });
//     }
//     
//     const coinsData = await fetchConnectCoinsData(apiKey);
// 
//     if (!coinsData || coinsData.length === 0) {
//       // Decide if you want to return 404 or empty data
//       return res.status(404).json({
//         error: 'No connect coins data found',
//         status: 'error'
//       });
//     }
// 
//     await setToCache(CONNECT_COINS_CACHE_KEY, coinsData, CACHE_EXPIRY);
// 
//     return res.json({
//       data: coinsData,
//       status: 'success',
//       source: 'api'
//     });
//   } catch (error: any) {
//     logger.error(`Error in getConnectCoins: ${error.message}`);
//     return res.status(500).json({
//       error: 'Server error processing connect coins data',
//       message: error.message,
//       status: 'error'
//     });
//   }
// };
// 
// /**
//  * Get token radar data with comprehensive information for home page
//  * @param req Express request
//  * @param res Express response
//  */
// export const getTokenRadar = async (req: Request, res: Response) => {
//   try {
//     // Get limit parameter, default to 30 if not provided
//     const limit = req.query.limit ? parseInt(String(req.query.limit), 10) : 30;
//     
//     // Get optional filter parameters
//     let network = req.query.network ? String(req.query.network).toLowerCase() : 'universal';
//     const timeframe = req.query.timeframe ? String(req.query.timeframe).toLowerCase() : '24h';
//     const category = req.query.category ? String(req.query.category).toLowerCase() : undefined;
//     
//     // Check if we should bypass cache (for fresh data)
//     const noCache = req.query.nocache === 'true' || req.query.refresh === 'true';
//     
//     // Validate limit parameter
//     if (isNaN(limit) || limit < 1 || limit > 250) {
//       return res.status(400).json({
//         error: 'Invalid limit parameter. Must be a number between 1 and 250.',
//         status: 'error'
//       });
//     }
//     
//     // Validate timeframe parameter
//     const validTimeframes = ['1h', '4h', '24h', '7d', '14d', '30d'];
//     if (!validTimeframes.includes(timeframe)) {
//       return res.status(400).json({
//         error: `Invalid timeframe parameter. Must be one of: ${validTimeframes.join(', ')}`,
//         status: 'error'
//       });
//     }
//     
//     // Validate network parameter - only support BSC, Solana and universal
//     if (!SUPPORTED_NETWORKS.includes(network)) {
//       return res.status(400).json({
//         error: `Invalid network parameter. Must be one of: ${SUPPORTED_NETWORKS.join(', ')}`,
//         status: 'error'
//       });
//     }
//     
//     // Generate cache key with all parameters
//     const cacheKey = `${TOKEN_RADAR_CACHE_KEY}:${limit}:${network}:${timeframe}${category ? `:${category}` : ''}`;
//     
//     // Check cache first if not explicitly bypassing
//     let cachedData = null;
//     if (!noCache) {
//       cachedData = await getFromCache(cacheKey);
//     }
//     
//     if (cachedData && !noCache) {
//       const filters = { network, timeframe, category: category || 'all' };
//       return res.json({
//         data: cachedData,
//         count: cachedData.length,
//         source: 'cache',
//         status: 'success',
//         filters
//       });
//     }
//     
//     // Get API key from env
//     const apiKey = await getApiKey();
//     
//     if (!apiKey) {
//       logger.error('API key not found for Mobula API');
//       return res.status(500).json({
//         error: 'Mobula API key is not configured',
//         status: 'error'
//       });
//     }
//     
//     const filters = { network, timeframe, category: category || 'all' };
//     const tokenData = await fetchTokenRadar(limit, network, timeframe, category);
// 
//     if (!tokenData || tokenData.length === 0) {
//       return res.status(200).json({
//         data: [],
//         count: 0,
//         source: 'api',
//         status: 'success',
//         message: 'No token radar data found with the given filters',
//         filters
//       });
//     }
// 
//     await setToCache(cacheKey, tokenData, CACHE_EXPIRY);
// 
//     return res.json({
//       data: tokenData,
//       count: tokenData.length,
//       source: 'api',
//       status: 'success',
//       filters,
//       refreshed: noCache // Indicate if this was a forced refresh
//     });
//   } catch (error: any) {
//     logger.error(`Error in getTokenRadar: ${error.message}`);
//     return res.status(500).json({
//       error: 'Server error processing token radar data',
//       message: error.message,
//       status: 'error'
//     });
//   }
// };
// 
// /**
//  * Refresh token radar cache
//  * Used for manual and scheduled refresh
//  */
// export const refreshTokenRadarCache = async (
//   limit: number = 30,
//   network?: string,
//   timeframe?: string,
//   category?: string
// ): Promise<boolean> => {
//   try {
//     // Default parameters if not provided
//     const actualNetwork = network || 'universal';
//     const actualTimeframe = timeframe || '24h';
//     
//     const filterDescription = [
//       `limit: ${limit}`,
//       `network: ${actualNetwork}`,
//       `timeframe: ${actualTimeframe}`,
//       category ? `category: ${category}` : 'category: all'
//     ].join(', ');
//     
//     // Get API key from env
//     const apiKey = await getApiKey();
// 
//     if (!apiKey) {
//       return false;
//     }
// 
//     // Generate cache key with all parameters
//     const cacheKey = `${TOKEN_RADAR_CACHE_KEY}:${limit}:${actualNetwork}:${actualTimeframe}${category ? `:${category}` : ''}`;
// 
//     // Use the Mobula API service to fetch token radar data
//     const tokenData = await fetchTokenRadar(limit, actualNetwork, actualTimeframe, category);
// 
//     if (!tokenData || tokenData.length === 0) {
//       return false;
//     }
// 
//     // Cache the results
//     await setToCache(cacheKey, tokenData, CACHE_EXPIRY);
// 
//     return true;
//   } catch (error: any) {
//     logger.error(`Error refreshing token radar cache: ${error.message}`);
//     return false;
//   }
// };
// 
// /**
//  * Refresh connect coins cache
//  * Used for manual and scheduled refresh
//  */
// export const refreshConnectCoinsCache = async (): Promise<boolean> => {
//   try {
//     // Get API key from env
//     const apiKey = await getApiKey();
// 
//     if (!apiKey) {
//       return false;
//     }
// 
//     // Use the service function to fetch data
//     const filteredData = await fetchConnectCoinsData(apiKey);
// 
//     if (!filteredData) {
//       return false;
//     }
// 
//     // Cache the filtered results
//     await setToCache(CONNECT_COINS_CACHE_KEY, filteredData, CACHE_EXPIRY);
// 
//     return true;
//   } catch (error: any) {
//     logger.error(`Error refreshing connect coins cache: ${error.message}`);
//     return false;
//   }
// };
// 
// /**
//  * Refresh network highlights cache for all supported networks
//  * Used for manual and scheduled refresh
//  */
// export const refreshNetworkHighlightsCache = async (): Promise<Record<string, boolean>> => {
//   const results: Record<string, boolean> = {};
//   
//   try {
//     // Get API key from env
//     const apiKey = await getApiKey();
// 
//     if (!apiKey) {
//       for (const network of SUPPORTED_NETWORKS) {
//         results[network] = false;
//       }
//       return results;
//     }
// 
//     // Fetch and cache individual network data first (BSC and Solana)
//     for (const network of ['binance-smart-chain', 'solana']) {
//       try {
//         // Generate network-specific cache key
//         const cacheKey = getNetworkCacheKey(NETWORK_TRENDS_CACHE_KEY, network);
// 
//         // Fetch fresh data from Mobula API
//         const networkData = await fetchNetworkHighlights(network, apiKey);
// 
//         if (!networkData ||
//             !(Array.isArray(networkData.trending) ||
//               Array.isArray(networkData.gainers) ||
//               Array.isArray(networkData.losers) ||
//               Array.isArray(networkData.new))) {
//           results[network] = false;
//           continue;
//         }
// 
//         // Cache the result
//         await setToCache(cacheKey, networkData, CACHE_EXPIRY);
// 
//         results[network] = true;
//       } catch (error: any) {
//         logger.error(`Error refreshing ${network} highlights cache: ${error.message}`);
//         results[network] = false;
//       }
//     }
//     
//     // Now handle the universal combined data
//     try {
//       // Use the cached individual network data that we just updated
//       const allNetworkData: Record<string, any> = {};
// 
//       for (const network of ['binance-smart-chain', 'solana']) {
//         const cacheKey = getNetworkCacheKey(NETWORK_TRENDS_CACHE_KEY, network);
//         const networkData = await getFromCache(cacheKey);
// 
//         if (networkData) {
//           allNetworkData[network] = networkData;
//         }
//       }
// 
//       // Process universal data from the networks we have data for
//       const universalData = processUniversalHighlights(allNetworkData);
// 
//       // Cache the universal result
//       const universalCacheKey = getNetworkCacheKey(NETWORK_TRENDS_CACHE_KEY, 'universal');
//       await setToCache(universalCacheKey, universalData, CACHE_EXPIRY);
// 
//       results['universal'] = true;
//     } catch (error: any) {
//       logger.error(`Error refreshing universal highlights cache: ${error.message}`);
//       results['universal'] = false;
//     }
//     
//     return results;
//   } catch (error: any) {
//     logger.error(`Error in refreshNetworkHighlightsCache: ${error.message}`);
//     
//     // Set all networks to failed
//     for (const network of SUPPORTED_NETWORKS) {
//       results[network] = false;
//     }
//     
//     return results;
//   }
// };
/**
 * Controller function to get trading pair list by calling the DexService.
 */
export const getPairList = async (req, res) => {
    const { activeTokenAddress, selectedTokenAddress } = req.query;
    // 1. Validate Input
    if (!activeTokenAddress || typeof activeTokenAddress !== 'string' ||
        !selectedTokenAddress || typeof selectedTokenAddress !== 'string') {
        res.status(400).json({
            success: false,
            message: 'Missing or invalid token addresses in query parameters',
        });
        return;
    }
    try {
        // 2. Call the Service
        const serviceResponse = await fetchPairListFromDexScreener(activeTokenAddress, selectedTokenAddress);
        // 3. Send Response based on Service Result
        // If serviceResponse indicates success or no pair found (success: false but no error thrown)
        res.status(200).json(serviceResponse);
    }
    catch (error) {
        // 4. Handle Errors thrown by the Service
        logger.error("[Controller:getPairList] Error calling fetchPairListFromDexScreener service:", error);
        res.status(500).json({
            success: false,
            message: error.message || 'Internal server error fetching pair list', // Use error message from service
        });
    }
};
/**
 * Handle search requests
 */
export const getSearchResults = async (req, res) => {
    try {
        const { input } = req.query;
        const apiKey = await getApiKey();
        const data = await fetchSearchResults(apiKey, input);
        if (!data) {
            res.status(500).json({ error: 'Failed to fetch Search Results' });
        }
        else {
            res.status(200).json(data);
        }
    }
    catch (error) {
        logger.error('Controller error fetching Search results:', error.message || error);
        res.status(500).json({ error: 'Internal server error.' });
    }
};
/**
 * Handle comprehensive market data requests
 * This is a multi-purpose endpoint that can fetch different types of market data
 * based on the type parameter (chart-data, market-pair, market-pairs, holders, etc.)
 */
export const handleMarketDataRequests = async (req, res) => {
    const type = req.query.type;
    try {
        switch (type) {
            case 'market-data':
                return await getMarketData(req, res);
            case 'market-pair':
                return await getMarketPair(req, res);
            case 'market-pairs':
                return await getMarketPairs(req, res);
            case 'holders':
                return await getHolders(req, res);
            case 'activities':
                return await getActivites(req, res);
            case 'chart-data':
                return await getChart(req, res);
            default:
                return res.status(400).json({ error: 'Invalid request type.' });
        }
    }
    catch (error) {
        console.error('Error in market-data-handler:', error);
        return res.status(500).json({ error: 'Internal server error.' });
    }
};
export const getMarketData = async (req, res) => {
    try {
        const { network, address } = req.query;
        if (!network || !address) {
            res.status(400).json({ error: 'Missing required query parameters:  network, or address.' });
            return;
        }
        const apiKey = await getApiKey();
        const data = await fetchMarketData(apiKey, network, address);
        if (!data) {
            res.status(500).json({ error: 'Failed to fetch market data.' });
        }
        else {
            res.status(200).json(data);
        }
    }
    catch (error) {
        logger.error('Controller error fetching market data:', error.message || error);
        res.status(500).json({ error: 'Internal server error.' });
    }
};
export const getMarketPair = async (req, res) => {
    try {
        const { network, address } = req.query;
        if (!network || !address) {
            res.status(400).json({ error: 'Missing required query parameters:  network, or address.' });
            return;
        }
        const apiKey = await getApiKey();
        const data = await fetchMarketPair(apiKey, network, address);
        if (!data) {
            res.status(500).json({ error: 'Failed to fetch market paie data.' });
        }
        else {
            res.status(200).json(data);
        }
    }
    catch (error) {
        logger.error('Controller error fetching market pair data:', error.message || error);
        res.status(500).json({ error: 'Internal server error.' });
    }
};
export const getMarketPairs = async (req, res) => {
    try {
        const { network, address } = req.query;
        if (!network || !address) {
            res.status(400).json({ error: 'Missing required query parameters:  network, or address.' });
            return;
        }
        const apiKey = await getApiKey();
        const data = await fetchMarketPairs(apiKey, network, address);
        if (!data) {
            res.status(500).json({ error: 'Failed to fetch market data.' });
        }
        else {
            res.status(200).json(data);
        }
    }
    catch (error) {
        logger.error('Controller error fetching market pair data:', error.message || error);
        res.status(500).json({ error: 'Internal server error.' });
    }
};
export const getHolders = async (req, res) => {
    try {
        const { network, address } = req.query;
        if (!network || !address) {
            res.status(400).json({ error: 'Missing required query parameters:  network, or address.' });
            return;
        }
        const apiKey = await getApiKey();
        const data = await fetchHolders(apiKey, network, address);
        if (!data) {
            res.status(500).json({ error: 'Failed to fetch Holders' });
        }
        else {
            res.status(200).json(data);
        }
    }
    catch (error) {
        logger.error('Controller error fetching Holders:', error.message || error);
        res.status(500).json({ error: 'Internal server error.' });
    }
};
export const getActivites = async (req, res) => {
    try {
        const { network, address } = req.query;
        if (!network || !address) {
            res.status(400).json({ error: 'Missing required query parameters:  network, or address.' });
            return;
        }
        const apiKey = await getApiKey();
        const data = await fetchActivity(apiKey, network, address);
        if (!data) {
            res.status(500).json({ error: 'Failed to fetch Activites' });
        }
        else {
            res.status(200).json(data);
        }
    }
    catch (error) {
        logger.error('Controller error fetching Activities:', error.message || error);
        res.status(500).json({ error: 'Internal server error.' });
    }
};
export const getChart = async (req, res) => {
    try {
        const { network, address, timeframe } = req.query;
        if (!network || !address) {
            res.status(400).json({ error: 'Missing required query parameters:  network, or address.' });
            return;
        }
        const apiKey = await getApiKey();
        const data = await fetchChartData(apiKey, network, address, timeframe);
        if (!data) {
            res.status(500).json({ error: 'Failed to fetch Chart data' });
        }
        else {
            res.status(200).json(data);
        }
    }
    catch (error) {
        logger.error('Controller error fetching Chart Data:', error.message || error);
        res.status(500).json({ error: 'Internal server error.' });
    }
};
//# sourceMappingURL=homeController.js.map