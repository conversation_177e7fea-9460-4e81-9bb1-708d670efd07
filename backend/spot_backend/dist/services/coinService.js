import axios from 'axios';
import { logger } from '../utils/logger.js';
import dotenv from 'dotenv';
// import { stringify } from 'querystring'; // Removed as it's unused
// import { deduplicateBySymbol } from '@utils/formatter'; // Removed as it's moved
// Load environment variables
dotenv.config();
// Functions moved to networkHighlightsService.ts:
// - processNetworkPairs
// - fetchSolanaCoinsFromCoinGecko
// - fetchCoinGeckoNetworkData
// - fetchNetworkHighlights
// Functions moved to tokenRadarService.ts:
// - fetchTokenRadar
// - fetchUniversalTokenRadarMobula
/**
 * Fetch connect coins data (Bitcoin, Ethereum, Solana)
 * Uses CoinGecko API to get real market data
 * @param apiKey Unused, kept for compatibility (might be needed for future API choices)
 * @returns Array of BTC, ETH, SOL data with real-time pricing
 */
export async function fetchNetworkIcons() {
    try {
        const response = await axios.get('https://api.coingecko.com/api/v3/coins/markets', {
            params: {
                vs_currency: 'usd',
                ids: 'bitcoin,ethereum,solana,binancecoin',
                per_page: 4,
                page: 1,
                sparkline: false
            },
            timeout: 10000
        });
        if (!Array.isArray(response?.data) || response.data.length === 0) {
            throw new Error('No data from CoinGecko');
        }
        const networkIcons = {};
        for (const coin of response.data) {
            networkIcons[coin.id] = coin.image;
        }
        return networkIcons;
    }
    catch (error) {
        console.error('Failed to fetch network icons:', error);
        return {
            bitcoin: '/default-icons/bitcoin.png',
            ethereum: '/default-icons/ethereum.png',
            solana: '/default-icons/solana.png'
        }; // fallback local images
    }
}
// export async function fetchConnectCoinsData(apiKey: string): Promise<any[]> {
//   try {
//     logger.info('Fetching BTC, ETH, and SOL data from CoinGecko API');
// 
//     // Get data directly from CoinGecko API - more reliable for major coins
//     const response = await axios.get('https://api.coingecko.com/api/v3/coins/markets', {
//       params: {
//         vs_currency: 'usd',
//         ids: 'bitcoin,ethereum,solana',  // Specifically only these 3 coins
//         order: 'market_cap_desc',
//         per_page: 3,
//         page: 1,
//         sparkline: false,
//         price_change_percentage: '24h'
//       },
//       timeout: 10000
//     });
// 
//     // Check if we have valid data
//     if (!Array.isArray(response?.data) || response.data.length === 0) {
//       logger.warn('No valid data returned from CoinGecko API');
//       return getDefaultTopCoins(); // Return default data if API fails
//     }
// 
//     // Map to our standardized format
//     const coins = response.data.map((coin: any) => {
//       return {
//         id: coin.id,
//         name: coin.name,
//         symbol: coin.symbol.toUpperCase(),
//         image: coin.image,
//         network: coin.id,  // Use coin ID as network identifier
//         current_price: coin.current_price,
//         price_change_percentage_24h: coin.price_change_percentage_24h,
//         market_cap: coin.market_cap,
//         total_volume: coin.total_volume
//       };
//     });
// 
//     logger.info(`Successfully fetched ${coins.length} connect coins with real-time data:`,coins);
//     return coins;
//   } catch (error: any) {
//     logger.error(`Error fetching connect coins data: ${error.message}`);
// 
//     if (axios.isAxiosError(error)) {
//       if (error.response) {
//         logger.error(`CoinGecko API error status: ${error.response.status}`);
//         logger.error(`CoinGecko API error data: ${JSON.stringify(error.response.data)}`);
//       } else if (error.request) {
//         logger.error('CoinGecko API request was made but no response received');
//       }
//     }
// 
//     // If CoinGecko rate limited or down, try backup API
//     try {
//       logger.info('Attempting to fetch from backup API (CoinCap)');
//       const backupResponse = await axios.get('https://api.coincap.io/v2/assets', {
//         params: {
//           ids: 'bitcoin,ethereum,solana',
//           limit: 3
//         },
//         timeout: 8000
//       });
// 
//       if (backupResponse?.data?.data && Array.isArray(backupResponse.data.data)) {
//         const backupCoins = backupResponse.data.data.map((coin: any) => ({
//           id: coin.id,
//           name: coin.name,
//           symbol: coin.symbol,
//           image: `https://assets.coingecko.com/coins/images/${coin.id === 'bitcoin' ? '1' : coin.id === 'ethereum' ? '279' : '4128'}/large/${coin.id}.png`,
//           network: coin.id,
//           current_price: parseFloat(coin.priceUsd),
//           price_change_percentage_24h: parseFloat(coin.changePercent24Hr),
//           market_cap: parseFloat(coin.marketCapUsd),
//           total_volume: parseFloat(coin.volumeUsd24Hr)
//         }));
// 
//         logger.info('Successfully fetched coins from backup API');
//         return backupCoins;
//       }
//     } catch (backupError) {
//       logger.error(`Backup API also failed: ${backupError}`);
//     }
// 
//     // Return default data if all APIs fail
//     return getDefaultTopCoins();
//   }
// }
/**
 * Returns default data for BTC, ETH, and SOL if the API fails
 * @returns Array with default data for top 3 cryptocurrencies
 */
function getDefaultTopCoins() {
    return [
        {
            id: 'bitcoin',
            name: 'Bitcoin',
            symbol: 'BTC',
            image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
            network: 'bitcoin',
            current_price: 64000,
            price_change_percentage_24h: 0,
            market_cap: 1251849067766,
            total_volume: 27379806690
        },
        {
            id: 'ethereum',
            name: 'Ethereum',
            symbol: 'ETH',
            image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
            network: 'ethereum',
            current_price: 3000,
            price_change_percentage_24h: 0,
            market_cap: 359767036400,
            total_volume: 14470762221
        },
        {
            id: 'solana',
            name: 'Solana',
            symbol: 'SOL',
            image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
            network: 'solana',
            current_price: 141,
            price_change_percentage_24h: 0,
            market_cap: 61976318336,
            total_volume: **********
        }
    ];
}
/**
 * Check CoinGecko API health
 * @param apiKey CoinGecko API key
 * @returns Object with health status
 */
export async function checkCoinGeckoApiHealth(apiKey) {
    try {
        const pingResponse = await axios.get('https://pro-api.coingecko.com/api/v3/ping', {
            headers: { 'x-cg-pro-api-key': apiKey },
            timeout: 5000
        });
        if (pingResponse.status === 200) {
            return { isHealthy: true, message: 'CoinGecko API is healthy' };
        }
        else {
            return { isHealthy: false, message: `Unexpected status code: ${pingResponse.status}` };
        }
    }
    catch (error) {
        if (axios.isAxiosError(error)) {
            if (error.response) {
                return {
                    isHealthy: false,
                    message: `API error: ${error.response.status} - ${JSON.stringify(error.response.data)}`
                };
            }
            else if (error.request) {
                return { isHealthy: false, message: 'Request timeout or no response' };
            }
        }
        return { isHealthy: false, message: `Network error: ${error.message}` };
    }
}
/**
 * Get API key from environment
 * @returns API key string or null
 */
export async function getApiKey() {
    const apiKey = process.env.MOBULA_API_KEY;
    if (!apiKey) {
        logger.error('MOBULA_API_KEY is not set in environment variables');
        return null;
    }
    return apiKey;
}
// --- Remaining Market Data Functions ---
export async function fetchMarketData(apiKey, network, address) {
    try {
        logger.info("Fetching market data for address and network:", address, network);
        let formattedNetwork = network;
        if (network === 'binance-smart-chain') {
            formattedNetwork = 'BNB Smart Chain (BEP20)';
        }
        const response = await axios.get('https://api.mobula.io/api/1/market/data', {
            params: {
                asset: address,
                blockchain: formattedNetwork
            },
            headers: {
                Authorization: `Bearer ${apiKey}`,
                accept: 'application/json'
            },
            timeout: 10000
        });
        logger.info("Market data fetched successfully");
        return response.data;
    }
    catch (error) {
        logger.error("Error fetching market data:", error.message || error);
        return null;
    }
}
export async function fetchMarketPair(apiKey, network, address) {
    try {
        logger.info("Fetching market pair data for address and network:", address, network);
        let formattedNetwork = network;
        if (network === 'binance-smart-chain') {
            formattedNetwork = 'BNB Smart Chain (BEP20)';
        }
        const response = await axios.get('https://api.mobula.io/api/1/market/pair', {
            params: {
                asset: address,
                blockchain: formattedNetwork,
                stats: true
            },
            headers: {
                Authorization: `Bearer ${apiKey}`,
                accept: 'application/json'
            },
            timeout: 20000
        });
        logger.info("Market Pair fetched successfully");
        return response.data;
    }
    catch (error) {
        logger.error("Error fetching market pair data:", error.message || error);
        return null;
    }
}
export async function fetchMarketPairs(apiKey, network, address) {
    try {
        logger.info("Fetching market pairs data for address and network:", address, network);
        let formattedNetwork = network;
        if (network === 'binance-smart-chain') {
            formattedNetwork = 'BNB Smart Chain (BEP20)';
        }
        const response = await axios.get('https://api.mobula.io/api/1/market/pairs', {
            params: {
                limit: '10',
                asset: address,
                blockchain: formattedNetwork,
            },
            headers: {
                Authorization: `Bearer ${apiKey}`,
                accept: 'application/json'
            },
            timeout: 10000
        });
        logger.info("Market Pairs fetched successfully");
        return response.data;
    }
    catch (error) {
        logger.error("Error fetching market pairs data:", error.message || error);
        return null;
    }
}
export async function fetchChartData(apiKey, network, address, timeframe) {
    try {
        logger.info("Fetching Chart data for address and network:", address, network);
        let formattedNetwork = network;
        if (network === 'binance-smart-chain') {
            formattedNetwork = 'BNB Smart Chain (BEP20)';
        }
        const response = await axios.get('https://api.mobula.io/api/1/market/history/pair', {
            params: {
                asset: address,
                blockchain: network, // Note: Mobula docs suggest network might be correct here
                period: timeframe,
            },
            headers: {
                Authorization: `Bearer ${apiKey}`,
                accept: 'application/json'
            },
            timeout: 10000
        });
        logger.info("Chart data fetched successfully");
        return response.data;
    }
    catch (error) {
        logger.error("Error fetching Chart data:", error.message || error);
        return null;
    }
}
export async function fetchHolders(apiKey, network, address) {
    try {
        logger.info("Fetching Holders for address and network:", address, network);
        // Note: Mobula docs for holders use network name directly, not formatted
        // let formattedNetwork = network;
        // if (network === 'binance-smart-chain') {
        //   formattedNetwork = 'BNB Smart Chain (BEP20)';
        // }
        const response = await axios.get('https://api.mobula.io/api/1/market/token/holders', {
            params: {
                asset: address,
                blockchain: network, // Using direct network name
                limit: 10
            },
            headers: {
                Authorization: `Bearer ${apiKey}`,
                accept: "application/json"
            },
            timeout: 25000
        });
        logger.info("Holders fetched successfully");
        return response.data;
    }
    catch (error) {
        logger.error("Error fetching holders:", error.message || error);
        return null;
    }
}
export async function fetchSearchResults(apiKey, query) {
    try {
        logger.info("Fetching search results for query:", query);
        const response = await axios.get('https://api-prod.mobula.io/api/1/search', {
            params: {
                input: query,
            },
            headers: {
                Authorization: `Bearer ${apiKey}`,
            },
        });
        logger.info("Search results fetched successfully");
        return response.data;
    }
    catch (error) {
        logger.error("Error fetching search results:", error.message || error);
        return null;
    }
}
export async function fetchActivity(apiKey, network, address) {
    try {
        logger.info("Fetching Activities for address and network:", address, network);
        // Note: Mobula docs for trades use network name directly, not formatted
        // let formattedNetwork = network;
        // if (network === 'binance-smart-chain') {
        //   formattedNetwork = 'BNB Smart Chain (BEP20)';
        // }
        const response = await axios.get('https://api.mobula.io/api/1/market/trades/pair', {
            params: {
                asset: address,
                blockchain: network, // Using direct network name
                limit: 20
            },
            headers: {
                Authorization: `Bearer ${apiKey}`,
                accept: "application/json"
            },
            timeout: 25000
        });
        logger.info("Activities fetched successfully");
        return response.data;
    }
    catch (error) {
        logger.error("Error fetching activities:", error.message || error);
        return null;
    }
}
//# sourceMappingURL=coinService.js.map