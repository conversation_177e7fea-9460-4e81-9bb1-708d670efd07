import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
import { limitOrderService } from './limitOrderService.js';
/**
 * Background service for monitoring limit orders using Mobula WebSocket API
 * Efficiently groups orders by token/pool to minimize API calls
 */
class LimitOrderMonitoringService extends EventEmitter {
    ws = null;
    subscriptions = new Map();
    reconnectAttempts = 0;
    maxReconnectAttempts = 10;
    reconnectDelay = 5000; // Start with 5 seconds
    maxReconnectDelay = 300000; // Max 5 minutes
    isConnecting = false;
    monitoringInterval = null;
    heartbeatInterval = null;
    stats;
    apiKey;
    primaryWsUrl;
    fallbackWsUrl;
    currentWsUrl;
    constructor() {
        super();
        this.apiKey = process.env.MOBULA_API_KEY || '';
        if (!this.apiKey) {
            logger.warn('⚠️ MOBULA_API_KEY not found in environment variables');
        }
        // Initialize Mobula WebSocket URLs (Enterprise)
        this.primaryWsUrl = process.env.MOBULA_PRICE_WSS_PRIMARY || 'wss://production-feed.mobula.io';
        this.fallbackWsUrl = process.env.MOBULA_PRICE_WSS_FALLBACK || 'wss://production-feed.mobula.io';
        this.currentWsUrl = this.primaryWsUrl;
        logger.info(`💎 Using Mobula Price Feed (Enterprise): ${this.currentWsUrl}`);
        logger.info('🎯 Features: Volume & market depth weighted pricing, real-time updates, unlimited assets');
        this.stats = {
            activeSubscriptions: 0,
            totalOrders: 0,
            executedOrders: 0,
            failedExecutions: 0,
            lastUpdate: 0,
            connectionState: 'disconnected',
            reconnectAttempts: 0
        };
        // Start monitoring every 30 seconds
        this.startMonitoring();
    }
    /**
     * Initialize the Price Feed monitoring service
     */
    async initialize() {
        try {
            logger.info('🔍 Initializing Price Feed Limit Order Monitoring Service (Enterprise)...');
            // Load initial orders and establish Price Feed WebSocket connection
            await this.refreshOrderSubscriptions();
            await this.connect();
            logger.info('✅ Price Feed Limit Order Monitoring Service initialized successfully');
            logger.info('💎 Features: Unlimited assets, real-time updates, superior pricing accuracy');
        }
        catch (error) {
            logger.error('❌ Failed to initialize Price Feed Limit Order Monitoring Service:', error);
            throw error;
        }
    }
    /**
     * Start the monitoring loop (optimized for real-time WebSocket)
     */
    startMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
        // Optimized: Only check for new orders, WebSocket handles real-time price monitoring
        this.monitoringInterval = setInterval(async () => {
            try {
                await this.refreshOrderSubscriptions(); // Only check for new orders
                // Removed redundant checkOrderExecution() - WebSocket handles price updates
            }
            catch (error) {
                logger.error('❌ Error in monitoring loop:', error);
            }
        }, 60000); // Reduced to 60 seconds - only for new order discovery
        logger.info('🔄 Optimized monitoring loop started (60s interval for new orders only)');
    }
    /**
     * Connect to Mobula Price Feed WebSocket
     */
    async connect() {
        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
            return;
        }
        this.isConnecting = true;
        this.stats.connectionState = 'connecting';
        try {
            logger.info(`🔌 Connecting to Mobula Price Feed: ${this.currentWsUrl}`);
            this.ws = new WebSocket(this.currentWsUrl);
            this.ws.on('open', () => {
                logger.info(`✅ Connected to Mobula Price Feed: ${this.currentWsUrl}`);
                this.stats.connectionState = 'connected';
                this.reconnectAttempts = 0;
                this.reconnectDelay = 5000; // Reset delay
                this.isConnecting = false;
                // Subscribe to current tokens
                this.subscribeToTokens();
                // Start heartbeat
                this.startHeartbeat();
                this.emit('connected');
            });
            this.ws.on('message', (data) => {
                try {
                    const rawDataString = data.toString();
                    // Check if data is empty or invalid
                    if (!rawDataString || rawDataString.trim() === '') {
                        logger.warn('⚠️ Received empty WebSocket message');
                        return;
                    }
                    const rawMessage = JSON.parse(rawDataString);
                    // Log the raw message for debugging (only first 500 chars to avoid spam)
                    logger.debug('📨 Raw WebSocket message:', {
                        type: typeof rawMessage,
                        preview: JSON.stringify(rawMessage).substring(0, 500) + (JSON.stringify(rawMessage).length > 500 ? '...' : '')
                    });
                    // Handle different message formats from Mobula
                    if (this.isValidMarketDataMessage(rawMessage)) {
                        this.handleMarketData(rawMessage);
                    }
                    else {
                        // Handle other message types (heartbeat, subscription confirmations, etc.)
                        this.handleNonMarketDataMessage(rawMessage);
                    }
                }
                catch (error) {
                    logger.error('❌ Error parsing WebSocket message:', {
                        error: error instanceof Error ? error.message : error,
                        rawDataLength: data.toString().length,
                        rawDataPreview: data.toString().substring(0, 200) + (data.toString().length > 200 ? '...' : ''),
                        dataType: typeof data
                    });
                }
            });
            this.ws.on('error', (error) => {
                logger.error(`❌ Price Feed WebSocket error on ${this.currentWsUrl}:`, error);
                this.stats.connectionState = 'error';
                this.handleConnectionError();
            });
            this.ws.on('close', (code, reason) => {
                logger.warn(`⚠️ Price Feed WebSocket closed on ${this.currentWsUrl}: ${code} - ${reason.toString()}`);
                this.stats.connectionState = 'disconnected';
                this.isConnecting = false;
                this.stopHeartbeat();
                this.scheduleReconnect();
            });
        }
        catch (error) {
            logger.error(`❌ Failed to create Price Feed WebSocket connection to ${this.currentWsUrl}:`, error);
            this.stats.connectionState = 'error';
            this.isConnecting = false;
            this.scheduleReconnect();
        }
    }
    /**
     * Subscribe to tokens using official Price Feed format (Enterprise)
     */
    subscribeToTokens() {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            logger.warn('⚠️ Cannot subscribe - WebSocket not connected');
            return;
        }
        if (this.subscriptions.size === 0) {
            logger.info('📭 No active subscriptions to send');
            return;
        }
        const assets = Array.from(this.subscriptions.values()).map(sub => sub.asset);
        // Try different subscription formats based on the error
        // Format 1: Try with event field (based on error message)
        const subscriptionMessage = {
            event: "subscribe",
            type: "feed",
            authorization: this.apiKey,
            kind: "address",
            tokens: assets.map(asset => ({
                blockchain: asset.blockchain || 'solana',
                address: asset.address
            }))
        };
        try {
            this.ws.send(JSON.stringify(subscriptionMessage));
            this.stats.activeSubscriptions = assets.length;
            logger.info(`💎 Subscribed to ${assets.length} tokens via Price Feed (Enterprise)`);
            logger.info(`🎯 Format: Official Price Feed V2 with volume & market depth weighting`);
            logger.info(`✅ Subscription sent to ${this.currentWsUrl}`);
        }
        catch (error) {
            logger.error('❌ Failed to send Price Feed subscription message:', error);
        }
    }
    /**
     * Try alternative subscription formats if the first one fails
     */
    subscribeToTokensAlternative() {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            logger.warn('⚠️ Cannot subscribe - WebSocket not connected');
            return;
        }
        if (this.subscriptions.size === 0) {
            logger.info('📭 No active subscriptions to send');
            return;
        }
        const assets = Array.from(this.subscriptions.values()).map(sub => sub.asset);
        // Try Format 2: Original documentation format without event field
        const subscriptionMessage = {
            type: "feed",
            authorization: this.apiKey,
            kind: "address",
            tokens: assets.map(asset => ({
                blockchain: asset.blockchain || 'solana',
                address: asset.address
            }))
        };
        try {
            this.ws.send(JSON.stringify(subscriptionMessage));
            logger.info(`🔄 Sent alternative subscription format (without event field)`);
        }
        catch (error) {
            logger.error('❌ Failed to send alternative subscription:', error);
            // Try Format 3: Market Feed format as fallback
            setTimeout(() => this.subscribeToTokensMarketFeed(), 2000);
        }
    }
    /**
     * Fallback to Market Feed format if Price Feed fails
     */
    subscribeToTokensMarketFeed() {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            logger.warn('⚠️ Cannot subscribe - WebSocket not connected');
            return;
        }
        const assets = Array.from(this.subscriptions.values()).map(sub => sub.asset);
        // Market Feed format as final fallback
        const subscriptionMessage = {
            type: 'market',
            authorization: this.apiKey,
            payload: {
                assets: assets,
                interval: 5
            }
        };
        try {
            this.ws.send(JSON.stringify(subscriptionMessage));
            logger.info(`🔄 Sent Market Feed fallback subscription`);
        }
        catch (error) {
            logger.error('❌ Failed to send Market Feed subscription:', error);
        }
    }
    /**
     * Validate if the message contains market data (supports both Price Feed and Market Feed)
     */
    isValidMarketDataMessage(message) {
        // Log message structure for debugging
        logger.debug('🔍 Validating message structure:', {
            type: typeof message,
            isArray: Array.isArray(message),
            hasPrice: message?.price !== undefined,
            hasSymbol: message?.baseSymbol !== undefined,
            hasTimestamp: message?.timestamp !== undefined,
            hasExtra: message?.extra !== undefined,
            keys: typeof message === 'object' && message !== null ? Object.keys(message) : []
        });
        // Check if message is an array (Market Feed format)
        if (Array.isArray(message)) {
            const isValid = message.every(item => typeof item === 'object' &&
                item !== null &&
                typeof item.price === 'number' &&
                typeof item.baseSymbol === 'string' &&
                typeof item.timestamp === 'number');
            if (isValid) {
                logger.debug('✅ Valid Market Feed array data');
            }
            else if (message.length > 0) {
                logger.warn('⚠️ Invalid Market Feed array data:', {
                    firstItem: message[0],
                    itemKeys: typeof message[0] === 'object' ? Object.keys(message[0]) : []
                });
            }
            return isValid;
        }
        // Check if message is a single object (Price Feed format)
        if (typeof message === 'object' && message !== null) {
            const isValid = typeof message.price === 'number' &&
                typeof message.baseSymbol === 'string' &&
                typeof message.timestamp === 'number';
            if (isValid) {
                logger.debug('✅ Valid Price Feed single object data');
            }
            else {
                logger.warn('⚠️ Invalid market data object:', {
                    hasPrice: typeof message.price,
                    hasBaseSymbol: typeof message.baseSymbol,
                    hasTimestamp: typeof message.timestamp,
                    hasQuoteSymbol: typeof message.quoteSymbol,
                    actualKeys: Object.keys(message)
                });
            }
            return isValid;
        }
        return false;
    }
    /**
     * Handle non-price feed messages (heartbeat, confirmations, etc.)
     */
    handleNonMarketDataMessage(message) {
        // Handle error messages with event field
        if (message.event === 'error') {
            logger.error('❌ Price Feed subscription error:', {
                message: message.data?.message,
                details: message.data?.details
            });
            // Try alternative subscription format
            if (message.data?.message?.includes('Invalid message format')) {
                logger.info('🔄 Trying alternative subscription format...');
                setTimeout(() => this.subscribeToTokensAlternative(), 2000);
            }
            return;
        }
        // Handle subscription confirmations
        if (message.event === 'subscribed' || message.event === 'subscribe' || message.success) {
            logger.info('✅ Price Feed subscription confirmed:', message);
            return;
        }
        // Handle heartbeat/ping messages
        if (message.type === 'ping' || message.type === 'heartbeat') {
            logger.debug('💓 Received heartbeat from Price Feed');
            return;
        }
        // Handle legacy error format
        if (message.type === 'error' || message.error) {
            logger.error('❌ Error message from Price Feed:', message);
            return;
        }
        // Handle feed status messages
        if (message.type === 'feed' || message.kind) {
            logger.info('📡 Price Feed status message:', message);
            return;
        }
        // Log unknown message types for debugging
        logger.warn('⚠️ Unknown Price Feed message type:', {
            event: message.event,
            type: message.type,
            keys: Object.keys(message),
            sample: JSON.stringify(message).substring(0, 200) + '...'
        });
    }
    /**
     * Handle incoming market data (supports both Price Feed and Market Feed formats)
     */
    handleMarketData(marketDataInput) {
        const now = Date.now();
        this.stats.lastUpdate = now;
        // Normalize input to array for consistent processing
        const marketDataArray = Array.isArray(marketDataInput) ? marketDataInput : [marketDataInput];
        logger.debug(`💰 Processing ${marketDataArray.length} market data update(s)`);
        for (const marketData of marketDataArray) {
            const quoteSymbol = marketData.quoteSymbol || 'USD';
            logger.debug(`💎 Price update: ${marketData.baseSymbol}/${quoteSymbol} = $${marketData.price}`);
            if (marketData.marketDepthUSDUp && marketData.marketDepthUSDDown) {
                logger.debug(`📊 Market depth: Up $${marketData.marketDepthUSDUp?.toLocaleString()}, Down $${marketData.marketDepthUSDDown?.toLocaleString()}`);
            }
            // Find matching subscription by symbol
            const subscription = this.findSubscriptionBySymbol(marketData.baseSymbol);
            if (subscription) {
                subscription.lastPrice = marketData.price;
                subscription.lastUpdate = now;
                // Check if any orders should be executed
                this.checkOrdersForExecution(subscription, marketData);
            }
            else {
                logger.debug(`⚠️ No subscription found for ${marketData.baseSymbol}`);
            }
        }
        // Record performance metric (temporarily disabled)
        // performanceMonitorService.recordApiMetric({
        //   endpoint: 'mobula-websocket',
        //   method: 'WS',
        //   responseTime: 0, // WebSocket doesn't have response time
        //   statusCode: 200,
        //   timestamp: now,
        //   success: true
        // });
    }
    /**
     * Find subscription by token symbol
     */
    findSubscriptionBySymbol(symbol) {
        for (const [, subscription] of this.subscriptions) {
            if (subscription.orders.length > 0 &&
                subscription.orders[0].token_symbol.toLowerCase() === symbol.toLowerCase()) {
                return subscription;
            }
        }
        return undefined;
    }
    /**
     * Check if orders should be executed based on current price
     */
    checkOrdersForExecution(subscription, marketData) {
        const currentPrice = marketData.price;
        for (const order of subscription.orders) {
            let shouldExecute = false;
            // Check execution conditions based on order direction
            if (order.direction === 'buy' && currentPrice <= order.target_price) {
                shouldExecute = true;
            }
            else if (order.direction === 'sell' && currentPrice >= order.target_price) {
                shouldExecute = true;
            }
            if (shouldExecute) {
                this.executeOrder(order, currentPrice, marketData);
            }
        }
    }
    /**
     * Execute a limit order with real swap transaction
     */
    async executeOrder(order, currentPrice, marketData) {
        try {
            logger.info(`🎯 Executing limit order ${order.id}: ${order.direction} ${order.amount} ${order.token_symbol} at $${currentPrice}`);
            // Step 1: Execute the actual swap transaction
            const swapResult = await this.executeSwapTransaction(order, currentPrice);
            if (swapResult.success && swapResult.signature) {
                // Step 2: Mark order as executed with real transaction hash
                const result = await limitOrderService.markOrderExecuted(order.id, swapResult.signature, // Real transaction signature
                {
                    execution_price: currentPrice,
                    swap_result: swapResult,
                    market_data: {
                        volume24h: marketData.volume24h,
                        marketDepth: {
                            up: marketData.marketDepthUSDUp,
                            down: marketData.marketDepthUSDDown
                        }
                    }
                });
                if (result.success) {
                    this.stats.executedOrders++;
                    logger.info(`✅ Order ${order.id} executed successfully with tx: ${swapResult.signature}`);
                    // Remove from subscription
                    this.removeOrderFromSubscription(order);
                    // Emit execution event
                    this.emit('orderExecuted', {
                        order,
                        executionPrice: currentPrice,
                        transactionSignature: swapResult.signature,
                        marketData
                    });
                }
                else {
                    logger.error(`❌ Failed to mark order ${order.id} as executed:`, result.error);
                    this.stats.failedExecutions++;
                }
            }
            else {
                // Swap failed
                logger.error(`❌ Swap execution failed for order ${order.id}:`, swapResult.error);
                this.stats.failedExecutions++;
                await limitOrderService.markOrderFailed(order.id, `Swap failed: ${swapResult.error}`);
            }
        }
        catch (error) {
            logger.error(`❌ Error executing order ${order.id}:`, error);
            this.stats.failedExecutions++;
            // Mark order as failed
            await limitOrderService.markOrderFailed(order.id, `Execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Execute the actual swap transaction for a limit order
     */
    async executeSwapTransaction(order, currentPrice) {
        try {
            // Prepare swap request with HIGH MEV protection by default
            const swapRequest = {
                tokenAddress: order.token_address,
                poolAddress: order.pool_address,
                dexType: order.dex_type,
                amount: order.amount,
                direction: order.direction,
                slippage: order.slippage,
                walletAddress: order.wallet_address,
                walletId: order.wallet_id,
                // 🚀 HIGH MEV PROTECTION FOR LIMIT ORDERS
                mevProtection: true,
                priorityLevel: 'high', // Always use high priority for limit orders
                bribeAmount: this.calculateLimitOrderBribe(order.amount),
                maxMevTip: 10000000 // 0.01 SOL max tip for limit orders
            };
            logger.info(`🔥 Executing swap for limit order with HIGH MEV protection:`, {
                orderId: order.id,
                direction: order.direction,
                amount: order.amount,
                targetPrice: order.target_price,
                currentPrice: currentPrice,
                mevProtection: true,
                priorityLevel: 'high'
            });
            // Call the appropriate trading service based on DEX type
            let swapResult;
            if (order.dex_type === 'pumpfun') {
                // Use PumpFun trading service
                swapResult = await this.executePumpFunSwap(swapRequest);
            }
            else if (order.dex_type === 'pumpswap') {
                // Use PumpSwap trading service
                swapResult = await this.executePumpSwapSwap(swapRequest);
            }
            else if (order.dex_type === 'launchlab') {
                // Use LaunchLab trading service
                swapResult = await this.executeLaunchLabSwap(swapRequest);
            }
            else {
                throw new Error(`Unsupported DEX type: ${order.dex_type}`);
            }
            return {
                success: true,
                signature: swapResult.signature,
                executionMethod: swapResult.executionMethod || 'enhanced'
            };
        }
        catch (error) {
            logger.error(`❌ Swap transaction failed for order ${order.id}:`, error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown swap error'
            };
        }
    }
    /**
     * Calculate MEV bribe amount for limit orders based on trade size
     */
    calculateLimitOrderBribe(amount) {
        // Higher bribe for limit orders to ensure execution
        if (amount >= 10)
            return 5000000; // 0.005 SOL for large trades (≥10 SOL)
        if (amount >= 1)
            return 2000000; // 0.002 SOL for medium trades (≥1 SOL)
        return 1000000; // 0.001 SOL for small trades (<1 SOL)
    }
    /**
     * Execute PumpFun swap via API call
     */
    async executePumpFunSwap(swapRequest) {
        try {
            const response = await fetch(`${process.env.SOLANA_SERVICE_URL || 'http://localhost:3001'}/api/pumpfun/swap`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(swapRequest)
            });
            if (!response.ok) {
                throw new Error(`PumpFun API error: ${response.status} ${response.statusText}`);
            }
            const result = await response.json();
            if (!result.success) {
                throw new Error(`PumpFun swap failed: ${result.error || 'Unknown error'}`);
            }
            return {
                signature: result.data.signature,
                executionMethod: result.data.executionMethod || 'enhanced'
            };
        }
        catch (error) {
            logger.error('PumpFun swap execution failed:', error);
            throw error;
        }
    }
    /**
     * Execute PumpSwap swap via API call
     */
    async executePumpSwapSwap(swapRequest) {
        try {
            const response = await fetch(`${process.env.SOLANA_SERVICE_URL || 'http://localhost:3001'}/api/pumpswap/swap`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(swapRequest)
            });
            if (!response.ok) {
                throw new Error(`PumpSwap API error: ${response.status} ${response.statusText}`);
            }
            const result = await response.json();
            if (!result.success) {
                throw new Error(`PumpSwap swap failed: ${result.error || 'Unknown error'}`);
            }
            return {
                signature: result.data.signature,
                executionMethod: result.data.executionMethod || 'enhanced'
            };
        }
        catch (error) {
            logger.error('PumpSwap swap execution failed:', error);
            throw error;
        }
    }
    /**
     * Execute LaunchLab swap via API call
     */
    async executeLaunchLabSwap(swapRequest) {
        try {
            const response = await fetch(`${process.env.SOLANA_SERVICE_URL || 'http://localhost:3001'}/api/launchlab/swap`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(swapRequest)
            });
            if (!response.ok) {
                throw new Error(`LaunchLab API error: ${response.status} ${response.statusText}`);
            }
            const result = await response.json();
            if (!result.success) {
                throw new Error(`LaunchLab swap failed: ${result.error || 'Unknown error'}`);
            }
            return {
                signature: result.data.signature,
                executionMethod: result.data.executionMethod || 'enhanced'
            };
        }
        catch (error) {
            logger.error('LaunchLab swap execution failed:', error);
            throw error;
        }
    }
    /**
     * Remove order from subscription and clean up if no orders left
     */
    removeOrderFromSubscription(order) {
        const key = this.getSubscriptionKey(order.token_address, order.pool_address);
        const subscription = this.subscriptions.get(key);
        if (subscription) {
            subscription.orders = subscription.orders.filter(o => o.id !== order.id);
            // Remove subscription if no orders left
            if (subscription.orders.length === 0) {
                this.subscriptions.delete(key);
                logger.info(`🗑️ Removed subscription for ${order.token_symbol} (no orders left)`);
                // Resubscribe to update WebSocket
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.subscribeToTokens();
                }
            }
        }
    }
    /**
     * Refresh order subscriptions from database
     */
    async refreshOrderSubscriptions() {
        try {
            // Get all pending orders
            const result = await limitOrderService.getPendingOrders();
            if (!result.success || !result.data) {
                logger.warn('⚠️ Failed to fetch pending orders:', result.error);
                return;
            }
            const orders = result.data;
            this.stats.totalOrders = orders.length;
            // Group orders by token/pool combination
            const newSubscriptions = new Map();
            for (const order of orders) {
                const key = this.getSubscriptionKey(order.token_address, order.pool_address);
                if (!newSubscriptions.has(key)) {
                    newSubscriptions.set(key, {
                        asset: this.createMobulaAsset(order),
                        orders: [],
                        lastPrice: undefined,
                        lastUpdate: undefined
                    });
                }
                newSubscriptions.get(key).orders.push(order);
            }
            // Update subscriptions
            const oldCount = this.subscriptions.size;
            this.subscriptions = newSubscriptions;
            const newCount = this.subscriptions.size;
            if (oldCount !== newCount) {
                logger.info(`📊 Updated subscriptions: ${oldCount} → ${newCount} (${orders.length} total orders)`);
                // Resubscribe if connected
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.subscribeToTokens();
                }
            }
        }
        catch (error) {
            logger.error('❌ Error refreshing order subscriptions:', error);
        }
    }
    /**
     * Create subscription key for grouping orders
     */
    getSubscriptionKey(tokenAddress, poolAddress) {
        return `${tokenAddress}_${poolAddress}`;
    }
    /**
     * Create Mobula asset object from limit order
     */
    createMobulaAsset(order) {
        // For most tokens, use the token address and blockchain
        // Mobula supports multiple blockchains, map dex_type to blockchain ID
        const blockchainMap = {
            'pumpfun': '1399811149', // Solana
            'pumpswap': '1399811149', // Solana
            'launchlab': '1399811149', // Solana
            'uniswap': '1', // Ethereum
            'pancakeswap': '56', // BSC
            'sushiswap': '1' // Ethereum (default)
        };
        const blockchain = blockchainMap[order.dex_type.toLowerCase()] || '1';
        return {
            address: order.token_address,
            blockchain: blockchain
        };
    }
    /**
     * Handle connection errors and implement exponential backoff
     */
    handleConnectionError() {
        this.stopHeartbeat();
        this.scheduleReconnect();
    }
    /**
     * Schedule reconnection with exponential backoff and fallback URL switching
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            // Try switching to fallback URL if we haven't already
            if (this.currentWsUrl === this.primaryWsUrl) {
                logger.info(`🔄 Switching to fallback WebSocket URL: ${this.fallbackWsUrl}`);
                this.currentWsUrl = this.fallbackWsUrl;
                this.reconnectAttempts = 0; // Reset attempts for fallback URL
                this.stats.reconnectAttempts = 0;
            }
            else {
                logger.error(`❌ Max reconnection attempts (${this.maxReconnectAttempts}) reached on both URLs. Stopping reconnection.`);
                this.stats.connectionState = 'error';
                return;
            }
        }
        this.reconnectAttempts++;
        this.stats.reconnectAttempts = this.reconnectAttempts;
        const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
        logger.info(`🔄 Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} to ${this.currentWsUrl} in ${delay}ms`);
        setTimeout(() => {
            this.connect();
        }, delay);
    }
    /**
     * Start heartbeat to keep connection alive
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatInterval = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                // Send ping to keep connection alive
                this.ws.ping();
            }
        }, 30000); // Ping every 30 seconds
    }
    /**
     * Stop heartbeat
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    /**
     * Get current monitoring statistics
     */
    getStats() {
        return {
            ...this.stats,
            activeSubscriptions: this.subscriptions.size,
            totalOrders: Array.from(this.subscriptions.values()).reduce((sum, sub) => sum + sub.orders.length, 0)
        };
    }
    /**
     * Get detailed status information
     */
    getStatus() {
        return {
            connected: this.ws?.readyState === WebSocket.OPEN,
            activeSubscriptions: this.subscriptions.size,
            totalOrders: Array.from(this.subscriptions.values()).reduce((sum, sub) => sum + sub.orders.length, 0),
            lastUpdate: this.stats.lastUpdate,
            connectionState: this.stats.connectionState,
            config: {
                currentWsUrl: this.currentWsUrl,
                primaryWsUrl: this.primaryWsUrl,
                fallbackWsUrl: this.fallbackWsUrl,
                hasApiKey: !!this.apiKey,
                maxReconnectAttempts: this.maxReconnectAttempts
            }
        };
    }
    /**
     * Manually trigger order refresh (for testing/debugging)
     */
    async refreshOrders() {
        logger.info('🔄 Manually refreshing orders...');
        await this.refreshOrderSubscriptions();
    }
    /**
     * Disconnect and cleanup
     */
    async shutdown() {
        logger.info('🛑 Shutting down Limit Order Monitoring Service...');
        // Stop monitoring
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        // Stop heartbeat
        this.stopHeartbeat();
        // Close WebSocket connection
        if (this.ws) {
            if (this.ws.readyState === WebSocket.OPEN) {
                // Send unsubscribe message
                try {
                    this.ws.send(JSON.stringify({
                        type: 'unsubscribe',
                        payload: {}
                    }));
                }
                catch (error) {
                    logger.warn('⚠️ Failed to send unsubscribe message:', error);
                }
            }
            this.ws.close();
            this.ws = null;
        }
        // Clear subscriptions
        this.subscriptions.clear();
        // Reset stats
        this.stats.connectionState = 'disconnected';
        this.stats.activeSubscriptions = 0;
        this.stats.totalOrders = 0;
        logger.info('✅ Limit Order Monitoring Service shutdown complete');
    }
    /**
     * Force reconnection (for testing/debugging)
     */
    async forceReconnect() {
        logger.info('🔄 Forcing reconnection...');
        if (this.ws) {
            this.ws.close();
        }
        this.reconnectAttempts = 0;
        await this.connect();
    }
    /**
     * Get subscription details (for debugging)
     */
    getSubscriptions() {
        return Array.from(this.subscriptions.entries()).map(([key, subscription]) => ({
            key,
            asset: subscription.asset,
            orderCount: subscription.orders.length,
            lastPrice: subscription.lastPrice,
            lastUpdate: subscription.lastUpdate,
            orders: subscription.orders.map(order => ({
                id: order.id,
                symbol: order.token_symbol,
                direction: order.direction,
                amount: order.amount,
                targetPrice: order.target_price
            }))
        }));
    }
}
// Export singleton instance
export const limitOrderMonitoringService = new LimitOrderMonitoringService();
//# sourceMappingURL=limitOrderMonitoringService.js.map