import { Server as SocketIOServer } from 'socket.io';
import { logger } from '../utils/logger.js';
import { mobulaWebSocketService } from './mobulaWebSocketService.js';
import { userActivityService } from './userActivityService.js';
class FrontendWebSocketService {
    io = null;
    connectedClients = new Map();
    pulseRoomClients = new Set();
    heartbeatInterval = null;
    connectionPool = new Map();
    maxPoolSize = 100;
    poolCleanupInterval = 300000; // 5 minutes
    pulseDataInterval = null; // Track pulse data polling timer
    timers = new Set(); // Track all timers for cleanup
    initialized = false;
    // Enhanced connection lifecycle management
    clientActivityTimeout = null;
    CLIENT_INACTIVITY_TIMEOUT = 30000; // 30 seconds
    lastClientActivity = Date.now();
    /**
     * Initialize the WebSocket server
     */
    initialize(httpServer) {
        if (this.initialized) {
            logger.warn('Frontend WebSocket service already initialized');
            return;
        }
        this.io = new SocketIOServer(httpServer, {
            cors: {
                origin: [
                    "http://localhost:4001",
                    "http://127.0.0.1:4001",
                    "http://localhost:5173",
                    "http://127.0.0.1:5173",
                    "https://redfyn.crypfi.io",
                    "https://redfyn.lrbinfotech.com"
                ],
                methods: ["GET", "POST"],
                credentials: false
            },
            transports: ['websocket', 'polling'],
            pingTimeout: 60000,
            pingInterval: 25000,
            allowEIO3: true,
            // Enable compression for WebSocket messages
            perMessageDeflate: {
                threshold: 1024, // Only compress messages larger than 1KB
                zlibDeflateOptions: {
                    level: 6 // Compression level (1-9, higher = more compression)
                }
            },
            httpCompression: {
                threshold: 1024
            }
        });
        this.setupEventHandlers();
        this.startHeartbeat();
        this.setupMobulaDataListener();
        this.initialized = true;
        logger.info('✅ Frontend WebSocket server initialized successfully');
    }
    /**
     * Setup Socket.IO event handlers
     */
    setupEventHandlers() {
        if (!this.io)
            return;
        this.io.on('connection', (socket) => {
            logger.info(`🔌 Frontend client connected: ${socket.id}`);
            // Handle client registration
            socket.on('register', (data) => {
                this.handleClientRegistration(socket, data);
            });
            // Handle pulse page join
            socket.on('join-pulse', (data) => {
                this.handlePulsePageJoin(socket, data);
            });
            // Handle pulse page leave
            socket.on('leave-pulse', (data) => {
                this.handlePulsePageLeave(socket, data);
            });
            // Handle heartbeat
            socket.on('heartbeat', (data) => {
                this.handleHeartbeat(socket, data);
            });
            // Handle disconnection
            socket.on('disconnect', (reason) => {
                this.handleClientDisconnection(socket, reason);
            });
            // Handle errors
            socket.on('error', (error) => {
                logger.error(`❌ Socket error for client ${socket.id}:`, error);
            });
        });
    }
    /**
     * Handle client registration
     */
    handleClientRegistration(socket, data) {
        const { userId, sessionId } = data;
        if (!userId || !sessionId) {
            socket.emit('error', { message: 'userId and sessionId are required' });
            return;
        }
        const client = {
            id: socket.id,
            userId,
            sessionId,
            connectedAt: Date.now(),
            lastActivity: Date.now(),
            isOnPulsePage: false
        };
        this.connectedClients.set(socket.id, client);
        // Send current connection status
        socket.emit('connection-status', {
            connected: true,
            clientId: socket.id,
            timestamp: Date.now()
        });
        logger.info(`📝 Client registered: ${socket.id} (User: ${userId})`);
    }
    /**
     * Handle pulse page join
     */
    handlePulsePageJoin(socket, data) {
        const { userId, sessionId } = data;
        const client = this.connectedClients.get(socket.id);
        if (!client) {
            socket.emit('error', { message: 'Client not registered' });
            return;
        }
        // Join pulse room
        socket.join('pulse-room');
        this.pulseRoomClients.add(socket.id);
        client.isOnPulsePage = true;
        client.lastActivity = Date.now();
        // Update global client activity tracking
        this.updateClientActivity();
        // Register with user activity service
        userActivityService.registerPulseActivity(userId, sessionId);
        // Send current pulse data with improved fallback handling
        const cachedData = mobulaWebSocketService.getCachedData();
        const hasFreshData = mobulaWebSocketService.hasFreshData();
        const hasUsableData = mobulaWebSocketService.hasUsableData();
        if (hasUsableData) {
            // Determine data source and freshness
            let dataSource = 'api';
            let dataAge = 0;
            if (hasFreshData) {
                dataSource = 'websocket';
            }
            else if (cachedData) {
                dataSource = 'fallback';
                // Calculate approximate age for logging
                const status = mobulaWebSocketService.getStatus();
                if (status.lastUpdate) {
                    dataAge = Date.now() - status.lastUpdate;
                }
            }
            const pulseUpdate = {
                data: cachedData,
                timestamp: Date.now(),
                source: dataSource
            };
            socket.emit('pulse-data', pulseUpdate);
            if (dataSource === 'fallback') {
                logger.info(`📤 Sent fallback pulse data to client ${socket.id} (age: ${Math.round(dataAge / 1000)}s) - fresh data will follow`);
            }
            else {
                logger.info(`📤 Sent initial pulse data to client ${socket.id} (source: ${dataSource}, fresh: ${hasFreshData})`);
            }
        }
        else {
            // No data available at all - send empty structure
            const emptyPulseUpdate = {
                data: { new: [], bonding: [], bonded: [] },
                timestamp: Date.now(),
                source: 'api'
            };
            socket.emit('pulse-data', emptyPulseUpdate);
            logger.info(`📤 Sent empty pulse data to client ${socket.id} - no cached data available`);
        }
        // Immediately ensure we have fresh data coming by checking connection status
        logger.info(`🎯 Client joined pulse room: ${socket.id} (User: ${userId}) - ensuring fresh data flow`);
        this.ensureFreshDataFlow();
        // Also trigger an immediate check for new data
        setTimeout(() => {
            this.checkForNewPulseData();
        }, 1000); // Check after 1 second to allow connection to establish
        // Emit room stats to all pulse clients
        this.broadcastPulseRoomStats();
    }
    /**
     * Handle pulse page leave
     */
    handlePulsePageLeave(socket, data) {
        const { userId, sessionId } = data;
        const client = this.connectedClients.get(socket.id);
        if (!client)
            return;
        // Leave pulse room
        socket.leave('pulse-room');
        this.pulseRoomClients.delete(socket.id);
        client.isOnPulsePage = false;
        client.lastActivity = Date.now();
        // Update global client activity tracking
        this.updateClientActivity();
        // Unregister from user activity service
        userActivityService.unregisterPulseActivity(userId, sessionId);
        logger.info(`🚪 Client left pulse room: ${socket.id} (User: ${userId})`);
        // Check if we should trigger backend cleanup
        this.checkBackendConnectionCleanup();
        // Restore normal polling frequency if no clients remain
        if (this.pulseRoomClients.size === 0 && this.pulseDataInterval) {
            clearInterval(this.pulseDataInterval);
            this.timers.delete(this.pulseDataInterval);
            this.pulseDataInterval = setInterval(() => {
                this.checkForNewPulseData();
            }, 15000); // Back to 15 seconds when no clients
            this.timers.add(this.pulseDataInterval);
            logger.debug('🔄 Restored normal polling frequency - no active clients');
        }
        // Emit room stats to remaining pulse clients
        this.broadcastPulseRoomStats();
    }
    /**
     * Handle client heartbeat
     */
    handleHeartbeat(socket, data) {
        const client = this.connectedClients.get(socket.id);
        if (client) {
            client.lastActivity = Date.now();
            userActivityService.updateActivity(data.userId, data.sessionId);
            socket.emit('heartbeat-ack', { timestamp: Date.now() });
            logger.debug(`💓 Heartbeat received from client: ${socket.id}`);
        }
    }
    /**
     * Handle client disconnection
     */
    handleClientDisconnection(socket, reason) {
        const client = this.connectedClients.get(socket.id);
        if (client) {
            // Clean up pulse room if client was in it
            if (client.isOnPulsePage) {
                this.pulseRoomClients.delete(socket.id);
                userActivityService.unregisterPulseActivity(client.userId, client.sessionId);
            }
            this.connectedClients.delete(socket.id);
            // Update global client activity tracking
            this.updateClientActivity();
            logger.info(`🔌 Client disconnected: ${socket.id} (User: ${client.userId}, Reason: ${reason})`);
            // Check if we should trigger backend cleanup
            this.checkBackendConnectionCleanup();
            // Emit room stats to remaining pulse clients
            this.broadcastPulseRoomStats();
        }
    }
    /**
     * Update client activity tracking for backend connection management
     */
    updateClientActivity() {
        this.lastClientActivity = Date.now();
        // Clear existing timeout
        if (this.clientActivityTimeout) {
            clearTimeout(this.clientActivityTimeout);
            this.clientActivityTimeout = null;
        }
        // If we have active clients, don't schedule cleanup
        if (this.pulseRoomClients.size > 0) {
            logger.debug(`📊 Client activity updated: ${this.pulseRoomClients.size} active pulse clients`);
            return;
        }
        // Schedule backend cleanup check if no active clients
        this.clientActivityTimeout = setTimeout(() => {
            this.checkBackendConnectionCleanup();
        }, this.CLIENT_INACTIVITY_TIMEOUT);
        logger.debug(`⏰ Scheduled backend cleanup check in ${this.CLIENT_INACTIVITY_TIMEOUT / 1000}s`);
    }
    /**
     * Check if backend connections should be cleaned up due to client inactivity
     */
    checkBackendConnectionCleanup() {
        const hasActivePulseClients = this.pulseRoomClients.size > 0;
        const timeSinceLastActivity = Date.now() - this.lastClientActivity;
        logger.info(`🔍 Backend cleanup check:`, {
            activePulseClients: this.pulseRoomClients.size,
            totalClients: this.connectedClients.size,
            timeSinceLastActivity: Math.round(timeSinceLastActivity / 1000),
            threshold: Math.round(this.CLIENT_INACTIVITY_TIMEOUT / 1000)
        });
        // If no active pulse clients and enough time has passed, trigger backend cleanup
        if (!hasActivePulseClients && timeSinceLastActivity >= this.CLIENT_INACTIVITY_TIMEOUT) {
            logger.info('🧹 Triggering backend WebSocket cleanup - no active pulse clients');
            this.triggerBackendCleanup();
        }
    }
    /**
     * Trigger cleanup of backend WebSocket connections
     */
    triggerBackendCleanup() {
        try {
            // Unregister all users from Mobula WebSocket service
            const mobulaStatus = mobulaWebSocketService.getStatus();
            if (mobulaStatus.activeUsers > 0) {
                logger.info(`🧹 Cleaning up ${mobulaStatus.activeUsers} active users from Mobula WebSocket service`);
                // Force cleanup by resetting the service state
                mobulaWebSocketService.resetServiceState();
                logger.info('✅ Backend WebSocket cleanup completed');
            }
            else {
                logger.debug('🧹 No active users in Mobula WebSocket service - cleanup not needed');
            }
        }
        catch (error) {
            logger.error('❌ Error during backend cleanup:', error.message);
        }
    }
    /**
     * Setup listener for Mobula WebSocket data updates
     */
    setupMobulaDataListener() {
        // We'll extend the MobulaWebSocketService to emit events when new data arrives
        // For now, we'll poll for new data periodically with reduced frequency
        this.pulseDataInterval = setInterval(() => {
            this.checkForNewPulseData();
        }, 15000); // Check every 15 seconds (reduced from 5 seconds)
        // Track timer for cleanup
        this.timers.add(this.pulseDataInterval);
    }
    /**
     * Check for new pulse data and broadcast to clients
     */
    checkForNewPulseData() {
        if (this.pulseRoomClients.size === 0) {
            return; // No clients in pulse room
        }
        const cachedData = mobulaWebSocketService.getCachedData();
        if (cachedData && mobulaWebSocketService.hasFreshData()) {
            // Use the existing broadcastPulseData method which handles priority correctly
            this.broadcastPulseData(cachedData, 'websocket');
            logger.debug(`📡 Checked and broadcasted pulse data to ${this.pulseRoomClients.size} clients`);
        }
        else if (this.pulseRoomClients.size > 0) {
            // We have clients but no fresh data - ensure connection is active
            this.ensureFreshDataFlow();
        }
    }
    /**
     * Ensure fresh data flow is active when clients are connected
     */
    ensureFreshDataFlow() {
        const status = mobulaWebSocketService.getStatus();
        const clientCount = this.pulseRoomClients.size;
        logger.info(`🔄 Ensuring fresh data flow: ${clientCount} clients, Mobula connected: ${status.connected}`);
        if (clientCount > 0) {
            if (!status.connected) {
                logger.info('🔄 Clients waiting for data but Mobula WebSocket not connected - triggering immediate reconnection');
                // Force reconnection by registering a temporary user
                const tempUserId = `frontend-service-${Date.now()}`;
                mobulaWebSocketService.registerUser(tempUserId);
                // Remove the temporary user after a short delay to allow connection
                setTimeout(() => {
                    mobulaWebSocketService.unregisterUser(tempUserId);
                    logger.info('🔄 Removed temporary user after connection attempt');
                }, 5000);
            }
            else {
                logger.info('🔄 Mobula WebSocket already connected, checking data freshness');
                // Even if connected, check if we have recent data
                const lastUpdate = status.lastUpdate;
                const dataAge = lastUpdate ? Date.now() - lastUpdate : Infinity;
                if (dataAge > 60000) { // If data is older than 1 minute
                    logger.info(`🔄 Data is stale (${Math.round(dataAge / 1000)}s old), requesting fresh data`);
                    // Request fresh data by briefly registering a user
                    const refreshUserId = `refresh-${Date.now()}`;
                    mobulaWebSocketService.registerUser(refreshUserId);
                    setTimeout(() => {
                        mobulaWebSocketService.unregisterUser(refreshUserId);
                    }, 2000);
                }
            }
            // Increase polling frequency when clients are active
            if (this.pulseDataInterval) {
                clearInterval(this.pulseDataInterval);
                this.timers.delete(this.pulseDataInterval);
            }
            this.pulseDataInterval = setInterval(() => {
                this.checkForNewPulseData();
            }, 1000); // SPEED OPTIMIZATION: Check every 1 second when clients are active
            this.timers.add(this.pulseDataInterval);
            logger.debug('🔄 Set active polling frequency (1s) for pulse data');
        }
    }
    /**
     * Broadcast pulse room statistics
     */
    broadcastPulseRoomStats() {
        const stats = {
            totalClients: this.connectedClients.size,
            pulseRoomClients: this.pulseRoomClients.size,
            timestamp: Date.now()
        };
        this.broadcastToPulseRoom('room-stats', stats);
    }
    /**
     * Broadcast message to all clients in pulse room
     */
    broadcastToPulseRoom(event, data) {
        if (!this.io)
            return;
        this.io.to('pulse-room').emit(event, data);
    }
    /**
     * Start heartbeat monitoring for connected clients
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            this.cleanupInactiveClients();
        }, 30000); // Check every 30 seconds
        // Track timer for cleanup
        this.timers.add(this.heartbeatInterval);
        logger.info('💓 Started frontend client heartbeat monitoring');
    }
    /**
     * Clean up inactive clients
     */
    cleanupInactiveClients() {
        const now = Date.now();
        const inactiveThreshold = 5 * 60 * 1000; // 5 minutes
        const clientsToRemove = [];
        for (const [socketId, client] of this.connectedClients.entries()) {
            if (now - client.lastActivity > inactiveThreshold) {
                clientsToRemove.push(socketId);
            }
        }
        clientsToRemove.forEach(socketId => {
            const client = this.connectedClients.get(socketId);
            if (client) {
                logger.info(`🧹 Cleaning up inactive client: ${socketId} (User: ${client.userId})`);
                if (client.isOnPulsePage) {
                    this.pulseRoomClients.delete(socketId);
                    userActivityService.unregisterPulseActivity(client.userId, client.sessionId);
                }
                this.connectedClients.delete(socketId);
                // Disconnect the socket if still connected
                const socket = this.io?.sockets.sockets.get(socketId);
                if (socket) {
                    socket.disconnect(true);
                }
            }
        });
        if (clientsToRemove.length > 0) {
            this.broadcastPulseRoomStats();
        }
    }
    /**
     * Manually broadcast new pulse data (called by MobulaWebSocketService) with priority handling
     */
    broadcastPulseData(data, source = 'websocket') {
        if (this.pulseRoomClients.size === 0) {
            return; // No clients to broadcast to
        }
        // SPEED OPTIMIZATION: Priority broadcast for any token category updates
        const hasNewTokens = data?.new && data.new.length > 0;
        const hasBondingTokens = data?.bonding && data.bonding.length > 0;
        const hasBondedTokens = data?.bonded && data.bonded.length > 0;
        if (hasNewTokens || hasBondingTokens || hasBondedTokens) {
            // Immediate priority broadcast for any category with tokens
            const priorityData = {};
            let priorityTypes = [];
            if (hasNewTokens) {
                priorityData.new = data.new;
                priorityTypes.push(`${data.new.length} new`);
            }
            if (hasBondingTokens) {
                priorityData.bonding = data.bonding;
                priorityTypes.push(`${data.bonding.length} bonding`);
            }
            if (hasBondedTokens) {
                priorityData.bonded = data.bonded;
                priorityTypes.push(`${data.bonded.length} bonded`);
            }
            const priorityUpdate = {
                data: priorityData,
                timestamp: Date.now(),
                source
            };
            this.broadcastToPulseRoom('pulse-data-priority', priorityUpdate);
            logger.info(`🚀 PRIORITY: Broadcasted ${priorityTypes.join(', ')} tokens to ${this.pulseRoomClients.size} clients instantly`);
            // ALWAYS send full data update after priority
            setImmediate(() => {
                const fullUpdate = {
                    data,
                    timestamp: Date.now(),
                    source
                };
                this.broadcastToPulseRoom('pulse-data', fullUpdate);
                logger.info(`📡 Broadcasted complete pulse data to ${this.pulseRoomClients.size} clients (source: ${source})`);
            });
        }
        else {
            // Regular broadcast for empty updates or other data
            const pulseUpdate = {
                data,
                timestamp: Date.now(),
                source
            };
            this.broadcastToPulseRoom('pulse-data', pulseUpdate);
            logger.info(`📡 Broadcasted pulse data to ${this.pulseRoomClients.size} clients (source: ${source})`);
        }
    }
    /**
     * Check if the service is initialized
     */
    isInitialized() {
        return this.initialized;
    }
    /**
     * Get service status for health checks
     */
    getStatus() {
        return {
            initialized: this.initialized,
            totalClients: this.connectedClients.size,
            pulseRoomClients: this.pulseRoomClients.size,
            hasActiveConnections: this.connectedClients.size > 0,
            timestamp: Date.now()
        };
    }
    /**
     * Get service statistics
     */
    getStats() {
        return {
            totalClients: this.connectedClients.size,
            pulseRoomClients: this.pulseRoomClients.size,
            isInitialized: this.initialized,
            connectedClients: Array.from(this.connectedClients.values()).map(client => ({
                id: client.id,
                userId: client.userId,
                connectedAt: client.connectedAt,
                lastActivity: client.lastActivity,
                isOnPulsePage: client.isOnPulsePage
            }))
        };
    }
    /**
     * Shutdown the service gracefully with comprehensive timer cleanup
     */
    shutdown() {
        logger.info('🛑 Shutting down Frontend WebSocket service...');
        // Clear all tracked timers
        for (const timer of this.timers) {
            clearInterval(timer);
        }
        this.timers.clear();
        // Clear specific timers
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        if (this.pulseDataInterval) {
            clearInterval(this.pulseDataInterval);
            this.pulseDataInterval = null;
        }
        if (this.io) {
            this.io.close();
            this.io = null;
        }
        this.connectedClients.clear();
        this.pulseRoomClients.clear();
        this.initialized = false;
        logger.info('✅ Frontend WebSocket service shutdown complete with timer cleanup');
    }
}
// Export singleton instance
export const frontendWebSocketService = new FrontendWebSocketService();
//# sourceMappingURL=frontendWebSocketService.js.map