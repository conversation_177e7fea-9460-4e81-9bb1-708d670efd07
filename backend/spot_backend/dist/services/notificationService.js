import { logger } from '../utils/logger.js';
import { supabase } from '../config/supabase.js';
class NotificationService {
    /**
     * Store a notification for an activity
     */
    async storeNotification(data) {
        try {
            // Check if Supabase is configured
            if (!process.env.SUPABASE_URL || (!process.env.SUPABASE_SERVICE_ROLE_KEY && !process.env.SUPABASE_ANON_KEY)) {
                logger.warn('Supabase not configured, skipping notification storage');
                return { success: true, data: { id: 'mock-notification-id' } };
            }
            const { data: result, error } = await supabase
                .from('notification')
                .insert({
                user_id: data.user_id,
                activity_type: data.activity_type,
                token_address: data.token_address,
                token_symbol: data.token_symbol,
                amount: data.amount,
                price: data.price,
                tx_hash: data.tx_hash,
                exchange_name: data.exchange_name,
                status: data.status,
                message: data.message,
                metadata: data.metadata,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            })
                .select()
                .single();
            if (error) {
                logger.error('Failed to store notification', {
                    error: error.message,
                    data: data
                });
                return { success: false, error: error.message };
            }
            logger.info('Notification stored successfully', {
                notificationId: result.id,
                activityType: data.activity_type,
                userId: data.user_id
            });
            return { success: true, data: result };
        }
        catch (error) {
            logger.error('Unexpected error storing notification', {
                error: error.message,
                data: data
            });
            return { success: false, error: error.message };
        }
    }
    /**
     * Update notification status
     */
    async updateNotificationStatus(notificationId, status, message, txHash) {
        try {
            // Check if Supabase is configured
            if (!process.env.SUPABASE_URL || (!process.env.SUPABASE_SERVICE_ROLE_KEY && !process.env.SUPABASE_ANON_KEY)) {
                logger.warn('Supabase not configured, skipping notification update');
                return { success: true, data: { id: notificationId } };
            }
            const updateData = {
                status,
                updated_at: new Date().toISOString()
            };
            if (message)
                updateData.message = message;
            if (txHash)
                updateData.tx_hash = txHash;
            const { data: result, error } = await supabase
                .from('notification')
                .update(updateData)
                .eq('id', notificationId)
                .select()
                .single();
            if (error) {
                logger.error('Failed to update notification status', {
                    error: error.message,
                    notificationId,
                    status
                });
                return { success: false, error: error.message };
            }
            return { success: true, data: result };
        }
        catch (error) {
            logger.error('Unexpected error updating notification', {
                error: error.message,
                notificationId,
                status
            });
            return { success: false, error: error.message };
        }
    }
    /**
     * Get notifications for a user
     */
    async getUserNotifications(userId, limit = 50, offset = 0) {
        try {
            // Check if Supabase is configured
            if (!process.env.SUPABASE_URL || (!process.env.SUPABASE_SERVICE_ROLE_KEY && !process.env.SUPABASE_ANON_KEY)) {
                logger.warn('Supabase not configured, returning empty notifications');
                return { success: true, data: [] };
            }
            const { data: result, error } = await supabase
                .from('notification')
                .select('*')
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);
            if (error) {
                logger.error('Failed to get user notifications', {
                    error: error.message,
                    userId
                });
                return { success: false, error: error.message };
            }
            return { success: true, data: result };
        }
        catch (error) {
            logger.error('Unexpected error getting user notifications', {
                error: error.message,
                userId
            });
            return { success: false, error: error.message };
        }
    }
    /**
     * Get unread notification count for a user
     */
    async getUnreadNotificationCount(userId) {
        try {
            // Check if Supabase is configured
            if (!process.env.SUPABASE_URL || (!process.env.SUPABASE_SERVICE_ROLE_KEY && !process.env.SUPABASE_ANON_KEY)) {
                logger.warn('Supabase not configured, returning 0 unread notifications');
                return { success: true, count: 0 };
            }
            // Since the is_read column doesn't exist in the current table,
            // we'll return the total count of notifications as "unread"
            const { count, error } = await supabase
                .from('notification')
                .select('*', { count: 'exact', head: true })
                .eq('user_id', userId);
            if (error) {
                logger.error('Failed to get notification count', {
                    error: error.message,
                    userId
                });
                return { success: false, error: error.message };
            }
            logger.info('Got notification count for user', {
                userId,
                count: count || 0
            });
            return { success: true, count: count || 0 };
        }
        catch (error) {
            logger.error('Unexpected error getting notification count', {
                error: error.message,
                userId
            });
            return { success: false, error: error.message };
        }
    }
    /**
     * Helper method to create notification for swap activities
     */
    async storeSwapNotification(userId, direction, tokenAddress, tokenSymbol, amount, price, exchangeName, txHash, status = 'completed') {
        const activityType = direction === 'buy' ? 'swap_buy' : 'swap_sell';
        const message = `${direction.toUpperCase()} ${amount} ${tokenSymbol} at $${price}`;
        return this.storeNotification({
            user_id: userId,
            activity_type: activityType,
            token_address: tokenAddress,
            token_symbol: tokenSymbol,
            amount,
            price,
            tx_hash: txHash,
            exchange_name: exchangeName,
            status,
            message,
            metadata: {
                direction,
                timestamp: new Date().toISOString()
            }
        });
    }
    /**
     * Helper method to create notification for TP/SL activities
     */
    async storeTPSLNotification(userId, orderType, tokenAddress, tokenSymbol, amount, triggerPrice, exchangeName, txHash, status = 'completed') {
        const message = `${orderType.replace('_', ' ').toUpperCase()} executed: ${amount} ${tokenSymbol} at $${triggerPrice}`;
        return this.storeNotification({
            user_id: userId,
            activity_type: 'tp_sl',
            token_address: tokenAddress,
            token_symbol: tokenSymbol,
            amount,
            price: triggerPrice,
            tx_hash: txHash,
            exchange_name: exchangeName,
            status,
            message,
            metadata: {
                order_type: orderType,
                timestamp: new Date().toISOString()
            }
        });
    }
    /**
     * Helper method to create notification for limit order activities
     */
    async storeLimitOrderNotification(userId, direction, tokenAddress, tokenSymbol, amount, targetPrice, exchangeName, txHash, status = 'completed') {
        const message = `Limit ${direction.toUpperCase()} executed: ${amount} ${tokenSymbol} at $${targetPrice}`;
        return this.storeNotification({
            user_id: userId,
            activity_type: 'limit_order',
            token_address: tokenAddress,
            token_symbol: tokenSymbol,
            amount,
            price: targetPrice,
            tx_hash: txHash,
            exchange_name: exchangeName,
            status,
            message,
            metadata: {
                direction,
                timestamp: new Date().toISOString()
            }
        });
    }
}
export const notificationService = new NotificationService();
//# sourceMappingURL=notificationService.js.map