import { createClient } from '@supabase/supabase-js';
// Initialize client lazily
let supabaseClient = null;
let isInitialized = false;
function initializeSupabase() {
    if (isInitialized)
        return supabaseClient;
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    // Log configuration status
    console.log('[Supabase Config] Initializing Supabase client...');
    console.log('[Supabase Config] SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
    console.log('[Supabase Config] SUPABASE_KEY:', supabaseKey ? 'Set (length: ' + supabaseKey.length + ')' : 'Missing');
    if (supabaseUrl && supabaseKey) {
        supabaseClient = createClient(supabaseUrl, supabaseKey);
        console.log('[Supabase Config] Supabase client created successfully');
    }
    else {
        console.log('[Supabase Config] Supabase client NOT created - missing configuration');
        supabaseClient = null;
    }
    isInitialized = true;
    return supabaseClient;
}
export const supabase = {
    from: (table) => {
        const client = initializeSupabase();
        if (!client) {
            throw new Error('Supabase not configured. Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.');
        }
        return client.from(table);
    }
};
//# sourceMappingURL=supabase.js.map