/**
 * Solana Token Balance Service
 * 
 * This service provides functions to retrieve token balances from Solana wallets
 * using Metaplex and Solana Web3.js libraries.
 */

import { Connection, PublicKey, clusterApiUrl, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { unpackMint, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults';
import { fetchAllDigitalAsset, mplTokenMetadata } from '@metaplex-foundation/mpl-token-metadata';
import { publicKey } from '@metaplex-foundation/umi';
import { logger } from '../utils/logger.js';

// Define interfaces
interface TokenMetadata {
  name?: string;
  symbol?: string;
  uri?: string;
  sellerFeeBasisPoints?: number;
  primarySaleHappened?: boolean;
  isMutable?: boolean;
  tokenStandard?: any;
  creators?: {
    __option: string;
    value?: Array<{
      address: string;
      verified: boolean;
      share: number;
    }>;
  };
  updateAuthority?: string;
}

interface OffChainMetadata {
  image?: string;
  logo?: string;
  description?: string;
  external_url?: string;
  extensions?: {
    twitter?: string;
    website?: string;
    discord?: string;
  };
  social_links?: {
    twitter?: string;
    website?: string;
    discord?: string;
  };
}

interface TokenInfo {
  symbol?: string;
  name?: string;
  mintAddress: string;
  balance: string;
  decimals: number;
  tokenStandard?: string;
  logo?: string;
  uri?: string;
  description?: string;
  website?: string;
  twitter?: string;
  discord?: string;
}

/**
 * Get Solana SOL balance
 * @param connection - Solana Connection
 * @param walletAddress - Solana wallet address
 * @returns SOL balance in lamports and SOL
 */
async function getSolBalance(connection: Connection, walletAddress: string): Promise<{ lamports: number; sol: number }> {
  try {
    const publicKey = new PublicKey(walletAddress);
    const balance = await connection.getBalance(publicKey);
    logger.info(`SOL Balance for ${walletAddress}: ${balance} lamports (${balance / LAMPORTS_PER_SOL} SOL)`);
    return {
      lamports: balance,
      sol: balance / LAMPORTS_PER_SOL
    };
  } catch (error: any) {
    logger.error(`Could not get SOL balance for ${walletAddress}: ${error.message}`);
    throw new Error(`Failed to get SOL balance: ${error.message}`);
  }
}

/**
 * Get all token balances for a Solana wallet
 * @param connection - Solana Connection
 * @param umi - Metaplex Umi instance
 * @param walletAddress - Solana wallet address
 * @returns Array of token information
 */
async function getAllTokenBalances(connection: Connection, umi: any, walletAddress: string): Promise<TokenInfo[]> {
  const ownerPublicKey = new PublicKey(walletAddress);
  const TOKEN_PROGRAM_ID_FOR_ACCOUNTS = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');

  logger.info(`Fetching SPL Token balances for ${walletAddress} using Umi for metadata...`);
  try {
    const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
      ownerPublicKey,
      { programId: TOKEN_PROGRAM_ID_FOR_ACCOUNTS }
    );

    if (!tokenAccounts.value || tokenAccounts.value.length === 0) {
      logger.info("No SPL token accounts found for this address.");
      return [];
    }

    const mintPubkeysToFetch: any[] = [];
    const tokenInfosForDisplay: { mintString: string; tokenBalance: string }[] = [];

    for (const tokenAccountInfo of tokenAccounts.value) {
      const accountData = tokenAccountInfo.account.data.parsed.info;
      const mintString = accountData.mint;
      const tokenBalance = accountData.tokenAmount.uiAmountString;
      
      mintPubkeysToFetch.push(publicKey(mintString));
      tokenInfosForDisplay.push({ mintString, tokenBalance });
    }

    if (mintPubkeysToFetch.length === 0) {
      logger.info("No mints to fetch metadata for.");
      return [];
    }

    logger.info(`Fetching metadata for ${mintPubkeysToFetch.length} mints in a batch...`);
    const digitalAssets = await fetchAllDigitalAsset(umi, mintPubkeysToFetch);

    // Batch fetch mint information
    logger.info(`Batch fetching mint info for ${tokenInfosForDisplay.length} tokens...`);
    const allMintPubkeys = tokenInfosForDisplay.map(ti => new PublicKey(ti.mintString));
    const mintAccountInfos = await connection.getMultipleAccountsInfo(allMintPubkeys);
    
    const mintDataMap = new Map();
    for (let i = 0; i < mintAccountInfos.length; i++) {
      const accountInfo = mintAccountInfos[i];
      const mintPubkey = allMintPubkeys[i];
      if (accountInfo) {
        try {
          const parsedMint = unpackMint(mintPubkey, accountInfo, TOKEN_PROGRAM_ID);
          mintDataMap.set(mintPubkey.toBase58(), parsedMint);
        } catch (e: any) {
          logger.error(`Could not unpack mint info for ${mintPubkey.toBase58()}: ${e.message}`);
        }
      } else {
        logger.warn(`Mint account not found for ${mintPubkey.toBase58()}`);
      }
    }

    const result: TokenInfo[] = [];
    const offChainFetchPromises: Promise<any>[] = [];

    // Process all digital assets and gather token information
    for (let i = 0; i < digitalAssets.length; i++) {
      const digitalAsset = digitalAssets[i];
      const tokenInfo = tokenInfosForDisplay[i];
      
      const resultToken: TokenInfo = {
        mintAddress: tokenInfo.mintString,
        balance: tokenInfo.tokenBalance,
        decimals: 0 // Default value, will be updated if mint info is available
      };

      // Extract metadata from digital asset
      if (digitalAsset && digitalAsset.metadata) {
        const metadata = digitalAsset.metadata;
        resultToken.name = metadata.name || '';
        resultToken.symbol = metadata.symbol || '';
        resultToken.uri = metadata.uri || '';

        // Handle token standard
        if (metadata.tokenStandard) {
          let tsString = 'Unknown';
          if (typeof metadata.tokenStandard === 'object' && 
              metadata.tokenStandard !== null && 
              metadata.tokenStandard.__option === 'Some') {
            const tsValue = metadata.tokenStandard.value;
            if (typeof tsValue === 'number') { // New enum style
              const standards = ['NonFungible', 'FungibleAsset', 'Fungible', 'NonFungibleEdition', 'ProgrammableNonFungible', 'ProgrammableNonFungibleEdition'];
              tsString = standards[tsValue] || `Unknown (${tsValue})`;
            } else if (typeof tsValue === 'object' && tsValue !== null) { // Old enum style
              // Safe access without assuming specific properties
              tsString = 'Custom Token Standard';
            }
          }
          resultToken.tokenStandard = tsString;
        }

        // Get mint info for decimals
        const mintInfo = mintDataMap.get(tokenInfo.mintString);
        if (mintInfo) {
          resultToken.decimals = mintInfo.decimals;
        }

        // Prepare to fetch off-chain metadata if URI exists
        if (metadata.uri && metadata.uri.trim() !== '') {
          let offChainUri = metadata.uri;
          if (offChainUri.startsWith('ipfs://')) {
            offChainUri = offChainUri.replace('ipfs://', 'https://ipfs.io/ipfs/');
          }
          offChainFetchPromises.push(
            fetch(offChainUri, { signal: AbortSignal.timeout(2000) })
              .then(res => res.ok ? res.json() : Promise.reject(`Failed with status: ${res.status}`))
              .catch(err => {
                logger.warn(`Off-chain fetch failed for ${offChainUri}: ${err.message}`);
                return null; // Return null on failure to not break Promise.allSettled
              })
          );
        } else {
          offChainFetchPromises.push(Promise.resolve(null)); // Push null for tokens without a URI to keep indices aligned
        }
      } else {
        offChainFetchPromises.push(Promise.resolve(null));
      }

      result.push(resultToken);
    }

    // Concurrently fetch all off-chain metadata
    logger.info(`Fetching ${offChainFetchPromises.length} off-chain metadata files concurrently...`);
    const offChainResults = await Promise.allSettled(offChainFetchPromises);

    // Process the results of the off-chain fetches
    for (let i = 0; i < offChainResults.length; i++) {
      const offChainResult = offChainResults[i];
      if (offChainResult.status === 'fulfilled' && offChainResult.value) {
        const offChainData: OffChainMetadata = offChainResult.value;
        const resultToken = result[i];

        resultToken.logo = offChainData.image || offChainData.logo;
        resultToken.description = offChainData.description ? 
          (offChainData.description.length > 100 ? 
            `${offChainData.description.substring(0, 100)}...` : 
            offChainData.description) : 
          undefined;

        resultToken.website = offChainData.external_url || 
          (offChainData.extensions?.website) || 
          (offChainData.social_links?.website);
        
        resultToken.twitter = (offChainData.extensions?.twitter) || 
          (offChainData.social_links?.twitter);
        
        resultToken.discord = (offChainData.extensions?.discord) || 
          (offChainData.social_links?.discord);
      }
    }

    return result;
  } catch (error: any) {
    logger.error(`Could not get token balances for ${walletAddress}: ${error.message}`);
    throw new Error(`Failed to get Solana token balances: ${error.message}`);
  }
}

/**
 * Main function to get Solana wallet token balances
 * @param walletAddress - Solana wallet address
 * @returns Object containing SOL balance and token balances
 */
export async function getSolanaTokenBalances(walletAddress: string) {
  const rpcUrl = clusterApiUrl('mainnet-beta');
  const connection = new Connection(rpcUrl, 'confirmed');
  logger.info(`Connected to Solana ${connection.rpcEndpoint.includes('mainnet') ? 'mainnet-beta' : connection.rpcEndpoint.includes('devnet') ? 'devnet' : 'testnet'}`);

  // Create Umi instance
  const umi = createUmi(rpcUrl).use(mplTokenMetadata());
  
  try {
    // Get SOL balance
    const solBalance = await getSolBalance(connection, walletAddress);
    
    // Get token balances
    const tokenBalances = await getAllTokenBalances(connection, umi, walletAddress);
    
    return {
      success: true,
      sol: solBalance,
      tokens: tokenBalances,
      network: 'mainnet-beta'
    };
  } catch (error: any) {
    logger.error(`Error in getSolanaTokenBalances: ${error.message}`);
    return {
      success: false,
      message: error.message,
      sol: null,
      tokens: [],
      network: 'mainnet-beta'
    };
  }
}
