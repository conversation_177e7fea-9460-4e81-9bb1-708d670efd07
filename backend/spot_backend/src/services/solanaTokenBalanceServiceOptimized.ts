/**
 * Optimized Solana Token Balance Service
 * 
 * Performance improvements:
 * - <PERSON>is caching with TTL
 * - Request deduplication
 * - Batch RPC calls
 * - Parallel processing
 * - Custom RPC endpoint support
 * - Response streaming for large wallets
 */

import { Connection, PublicKey, clusterApiUrl, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { unpackMint, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults';
import { fetchAllDigitalAsset, mplTokenMetadata } from '@metaplex-foundation/mpl-token-metadata';
import { publicKey } from '@metaplex-foundation/umi';
import { logger } from '../utils/logger.js';
import { getFromCache, setToCache } from '../utils/redis.js';

// Cache configuration
const CACHE_TTL = 300; // 5 minutes cache for token balances
const CACHE_PREFIX = 'solana:balance:';

// Request deduplication
const pendingRequests = new Map<string, Promise<any>>();

// Custom RPC endpoints for better performance
const RPC_ENDPOINTS = [
  process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
  'https://solana-api.projectserum.com',
  'https://rpc.ankr.com/solana'
];

// Connection pool
let connectionPool: Connection[] = [];
let currentConnectionIndex = 0;

// Initialize connection pool
function initializeConnectionPool() {
  connectionPool = RPC_ENDPOINTS.map(endpoint => 
    new Connection(endpoint, {
      commitment: 'confirmed',
      confirmTransactionInitialTimeout: 60000,
      disableRetryOnRateLimit: false
    })
  );
}

// Get next available connection (round-robin)
function getConnection(): Connection {
  if (connectionPool.length === 0) {
    initializeConnectionPool();
  }
  const connection = connectionPool[currentConnectionIndex];
  currentConnectionIndex = (currentConnectionIndex + 1) % connectionPool.length;
  return connection;
}

// Initialize pool on module load
initializeConnectionPool();

interface TokenInfo {
  symbol?: string;
  name?: string;
  mintAddress: string;
  balance: string;
  decimals: number;
  tokenStandard?: string;
  logo?: string;
  uri?: string;
  description?: string;
  website?: string;
  twitter?: string;
  discord?: string;
}

interface CachedBalance {
  sol: { lamports: number; sol: number };
  tokens: TokenInfo[];
  cachedAt: number;
  network: string;
}

/**
 * Get cached balance if available and not expired
 */
async function getCachedBalance(walletAddress: string): Promise<CachedBalance | null> {
  try {
    const cacheKey = `${CACHE_PREFIX}${walletAddress}`;
    const cached = await getFromCache(cacheKey);
    
    if (cached && cached.cachedAt) {
      const age = Date.now() - cached.cachedAt;
      if (age < CACHE_TTL * 1000) {
        logger.info(`Cache hit for wallet ${walletAddress}, age: ${age}ms`);
        return cached;
      }
    }
    return null;
  } catch (error) {
    logger.error('Cache read error:', error);
    return null;
  }
}

/**
 * Save balance to cache
 */
async function setCachedBalance(walletAddress: string, data: any): Promise<void> {
  try {
    const cacheKey = `${CACHE_PREFIX}${walletAddress}`;
    const cacheData: CachedBalance = {
      ...data,
      cachedAt: Date.now()
    };
    await setToCache(cacheKey, cacheData, CACHE_TTL);
    logger.info(`Cached balance for wallet ${walletAddress}`);
  } catch (error) {
    logger.error('Cache write error:', error);
  }
}

/**
 * Optimized SOL balance fetch with retry
 */
async function getSolBalanceOptimized(walletAddress: string): Promise<{ lamports: number; sol: number }> {
  const publicKey = new PublicKey(walletAddress);
  let lastError: any;
  
  // Try multiple connections
  for (let i = 0; i < connectionPool.length; i++) {
    try {
      const connection = getConnection();
      const balance = await connection.getBalance(publicKey);
      return {
        lamports: balance,
        sol: balance / LAMPORTS_PER_SOL
      };
    } catch (error: any) {
      lastError = error;
      logger.warn(`SOL balance fetch failed on connection ${i + 1}: ${error.message}`);
    }
  }
  
  throw new Error(`Failed to get SOL balance after ${connectionPool.length} attempts: ${lastError?.message}`);
}

/**
 * Batch fetch off-chain metadata with timeout and concurrency limit
 */
async function batchFetchOffChainMetadata(uris: (string | undefined)[], concurrencyLimit = 10): Promise<(any | null)[]> {
  const results: (any | null)[] = new Array(uris.length).fill(null);
  const fetchPromises: Promise<any>[] = [];
  
  // Process in batches
  for (let i = 0; i < uris.length; i += concurrencyLimit) {
    const batch = uris.slice(i, i + concurrencyLimit);
    const batchPromise = Promise.all(
      batch.map(async (uri, batchIndex) => {
        const globalIndex = i + batchIndex;
        if (!uri || uri.trim() === '') {
          return;
        }
        
        try {
          let fetchUri = uri;
          if (fetchUri.startsWith('ipfs://')) {
            fetchUri = fetchUri.replace('ipfs://', 'https://ipfs.io/ipfs/');
          }
          
          const response = await fetch(fetchUri, { 
            signal: AbortSignal.timeout(3000) // 3 second timeout per request
          });
          
          if (response.ok) {
            results[globalIndex] = await response.json();
          }
        } catch (error) {
          // Silently fail for individual metadata fetches
          logger.debug(`Off-chain metadata fetch failed for ${uri}: ${error}`);
        }
      })
    );
    
    fetchPromises.push(batchPromise);
  }
  
  await Promise.all(fetchPromises);
  return results;
}

/**
 * Optimized token balance fetching
 */
async function getAllTokenBalancesOptimized(connection: Connection, umi: any, walletAddress: string): Promise<TokenInfo[]> {
  const ownerPublicKey = new PublicKey(walletAddress);
  const TOKEN_PROGRAM_ID_FOR_ACCOUNTS = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');

  try {
    // Get token accounts
    const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
      ownerPublicKey,
      { programId: TOKEN_PROGRAM_ID_FOR_ACCOUNTS }
    );

    if (!tokenAccounts.value || tokenAccounts.value.length === 0) {
      return [];
    }

    // Prepare data for batch processing
    const mintPubkeysToFetch: any[] = [];
    const tokenInfosForDisplay: { mintString: string; tokenBalance: string; decimals: number }[] = [];

    for (const tokenAccountInfo of tokenAccounts.value) {
      const accountData = tokenAccountInfo.account.data.parsed.info;
      const mintString = accountData.mint;
      const tokenBalance = accountData.tokenAmount.uiAmountString;
      const decimals = accountData.tokenAmount.decimals;
      
      mintPubkeysToFetch.push(publicKey(mintString));
      tokenInfosForDisplay.push({ mintString, tokenBalance, decimals });
    }

    // Batch fetch metadata and mint info in parallel
    const [digitalAssets, mintAccountInfos] = await Promise.all([
      fetchAllDigitalAsset(umi, mintPubkeysToFetch),
      connection.getMultipleAccountsInfo(tokenInfosForDisplay.map(ti => new PublicKey(ti.mintString)))
    ]);

    // Process mint data
    const mintDataMap = new Map();
    for (let i = 0; i < mintAccountInfos.length; i++) {
      const accountInfo = mintAccountInfos[i];
      const mintPubkey = new PublicKey(tokenInfosForDisplay[i].mintString);
      if (accountInfo) {
        try {
          const parsedMint = unpackMint(mintPubkey, accountInfo, TOKEN_PROGRAM_ID);
          mintDataMap.set(mintPubkey.toBase58(), parsedMint);
        } catch (e) {
          logger.debug(`Could not unpack mint: ${e}`);
        }
      }
    }

    // Prepare tokens and collect URIs
    const result: TokenInfo[] = [];
    const urisToFetch: (string | undefined)[] = [];

    for (let i = 0; i < digitalAssets.length; i++) {
      const digitalAsset = digitalAssets[i];
      const tokenInfo = tokenInfosForDisplay[i];
      
      const resultToken: TokenInfo = {
        mintAddress: tokenInfo.mintString,
        balance: tokenInfo.tokenBalance,
        decimals: tokenInfo.decimals
      };

      if (digitalAsset?.metadata) {
        const metadata = digitalAsset.metadata;
        resultToken.name = metadata.name || '';
        resultToken.symbol = metadata.symbol || '';
        resultToken.uri = metadata.uri || '';
        
        // Handle token standard
        if (metadata.tokenStandard?.__option === 'Some') {
          const tsValue = metadata.tokenStandard.value;
          if (typeof tsValue === 'number') {
            const standards = ['NonFungible', 'FungibleAsset', 'Fungible', 'NonFungibleEdition', 'ProgrammableNonFungible', 'ProgrammableNonFungibleEdition'];
            resultToken.tokenStandard = standards[tsValue] || `Unknown (${tsValue})`;
          }
        }
        
        urisToFetch.push(metadata.uri);
      } else {
        urisToFetch.push(undefined);
      }

      result.push(resultToken);
    }

    // Batch fetch off-chain metadata
    const offChainData = await batchFetchOffChainMetadata(urisToFetch);

    // Merge off-chain data
    for (let i = 0; i < result.length; i++) {
      const offChain = offChainData[i];
      if (offChain) {
        const token = result[i];
        token.logo = offChain.image || offChain.logo;
        token.description = offChain.description?.substring(0, 100);
        token.website = offChain.external_url || offChain.extensions?.website || offChain.social_links?.website;
        token.twitter = offChain.extensions?.twitter || offChain.social_links?.twitter;
        token.discord = offChain.extensions?.discord || offChain.social_links?.discord;
      }
    }

    return result;
  } catch (error: any) {
    logger.error(`Token balance fetch error: ${error.message}`);
    throw error;
  }
}

/**
 * Main optimized function with caching and deduplication
 */
export async function getSolanaTokenBalancesOptimized(walletAddress: string) {
  // Check if request is already in progress (deduplication)
  const existingRequest = pendingRequests.get(walletAddress);
  if (existingRequest) {
    logger.info(`Request deduplication: reusing pending request for ${walletAddress}`);
    return existingRequest;
  }

  // Create new request promise
  const requestPromise = (async () => {
    try {
      // Check cache first
      const cached = await getCachedBalance(walletAddress);
      if (cached) {
        return {
          success: true,
          sol: cached.sol,
          tokens: cached.tokens,
          network: cached.network,
          cached: true,
          cachedAt: cached.cachedAt
        };
      }

      // Get connection and create Umi instance
      const connection = getConnection();
      const umi = createUmi(connection.rpcEndpoint).use(mplTokenMetadata());

      // Fetch SOL balance and token balances in parallel
      const [solBalance, tokenBalances] = await Promise.all([
        getSolBalanceOptimized(walletAddress),
        getAllTokenBalancesOptimized(connection, umi, walletAddress)
      ]);

      const result = {
        success: true,
        sol: solBalance,
        tokens: tokenBalances,
        network: 'mainnet-beta'
      };

      // Cache the result
      await setCachedBalance(walletAddress, result);

      return result;
    } catch (error: any) {
      logger.error(`Error in getSolanaTokenBalancesOptimized: ${error.message}`);
      return {
        success: false,
        message: error.message,
        sol: null,
        tokens: [],
        network: 'mainnet-beta'
      };
    } finally {
      // Clean up pending request
      pendingRequests.delete(walletAddress);
    }
  })();

  // Store pending request
  pendingRequests.set(walletAddress, requestPromise);

  return requestPromise;
}

// Export the optimized version as the main function
export const getSolanaTokenBalances = getSolanaTokenBalancesOptimized;