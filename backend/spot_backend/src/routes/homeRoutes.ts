import express from 'express';
import {
  // getNetworkHighlights, // REMOVED
  // getConnectCoins, // REMOVED
  // getTokenRadar, // REMOVED
  getSearchResults,
  getPairList,
  handleMarketDataRequests,
  // refreshConnectCoinsCache, // REMOVED
  // refreshTokenRadarCache, // REMOVED
  // refreshNetworkHighlightsCache, // REMOVED
  getNetworkIcons,
  getPulseData,
  getMarketData
} from '../controllers/homeController.js';
import { fetchPulseData } from '../services/pulseService.js';
import { checkCoinGeckoApiHealth, getApiKey } from '../services/coinService.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Get connect coins (BTC, ETH, SOL) for the connect section - REMOVED
// router.get('/connect-coins', getConnectCoins);
router.get('/network-icons',getNetworkIcons);
// router.get('/market-pair', getMarketPair);
// router.get('/market-pairs', getMarketPairs);
// router.get('/holders', getHolders);
// router.get('/chart-data', getChart);
router.get('/search-results', getSearchResults);

router.get('/pulse-data',getPulseData)

// Direct market data endpoint for pulse tokens
router.get('/market-data', getMarketData);

// Unified market data handler endpoint
router.get('/market-data-handler', handleMarketDataRequests);

// Get network-specific highlights (trending, gainers, losers, new) - REMOVED
// router.get('/network-highlights', getNetworkHighlights);

// Get token radar data with comprehensive info for home page - REMOVED
// router.get('/token-radar', getTokenRadar);

// Direct access to highlights API (same as universal network highlights) - REMOVED
// router.get('/highlights', (req, res) => {
//   // Set network to universal and forward to getNetworkHighlights
//   req.query.network = 'universal';
//   return getNetworkHighlights(req, res);
// });

// Health check endpoint for CoinGecko Pro API
router.get('/coingecko-health', async (req, res) => {
  try {
    const apiKey = await getApiKey();
    
    if (!apiKey) {
      return res.status(500).json({
        status: 'error',
        isHealthy: false,
        message: 'CoinGecko API key is not configured'
      });
    }
    
    const healthResult = await checkCoinGeckoApiHealth(apiKey);
    
    if (healthResult.isHealthy) {
      return res.json({
        status: 'success',
        ...healthResult
      });
    } else {
      return res.status(503).json({
        status: 'error',
        ...healthResult
      });
    }
  } catch (error: any) {
    logger.error(`Error checking CoinGecko API health: ${error.message}`);
    return res.status(500).json({
      status: 'error',
      isHealthy: false,
      message: `Error checking CoinGecko API health: ${error.message}`
    });
  }
});

// Manually refresh connect coins cache - REMOVED
// router.post('/refresh-connect-coins', async (req, res) => {
//   try {
//     const result = await refreshConnectCoinsCache();
//     if (result) {
//       return res.json({ 
//         message: 'Connect coins cache refreshed successfully',
//         status: 'success'
//       });
//     } else {
//       return res.status(500).json({ 
//         message: 'Failed to refresh connect coins cache',
//         status: 'error'
//       });
//     }
//   } catch (error: any) {
//     return res.status(500).json({
//       message: `Error refreshing connect coins cache: ${error.message}`,
//       status: 'error'
//     });
//   }
// });

// Manually refresh token radar cache - REMOVED

// Manually refresh network highlights cache - REMOVED

// Manually refresh home page data
// router.post('/refresh', refreshHomePageData);

router.get('/get-pair-list', getPairList);

export default router; 