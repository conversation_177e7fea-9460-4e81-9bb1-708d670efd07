import axios from 'axios';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get the directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from the backend directory
dotenv.config({ path: join(__dirname, '../../.env') });

async function testPrivyWallets() {
  const userId = 'cmabr4ky3001ujs0m61g7cshy';
  const appId = process.env.PRIVY_APP_ID;
  const appSecret = process.env.PRIVY_APP_SECRET;

  if (!appId || !appSecret) {
    console.error('Missing PRIVY_APP_ID or PRIVY_APP_SECRET in environment variables');
    process.exit(1);
  }

  console.log('Testing Privy API for user:', userId);
  console.log('Using App ID:', appId);
  console.log('-----------------------------------\n');

  try {
    const credentials = Buffer.from(`${appId}:${appSecret}`).toString('base64');
    
    const response = await axios.get(
      `https://auth.privy.io/api/v1/users/${userId}`,
      {
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/json',
          'privy-app-id': appId
        },
        timeout: 10000
      }
    );

    console.log('✅ Successfully fetched user data\n');
    
    // Log the complete response
    console.log('Full Privy Response:');
    console.log('===================');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('\n');

    // Analyze linked accounts
    console.log('Linked Accounts Analysis:');
    console.log('========================');
    console.log(`Total linked accounts: ${response.data.linked_accounts.length}`);
    
    const accountTypes = response.data.linked_accounts.reduce((acc: any, account: any) => {
      acc[account.type] = (acc[account.type] || 0) + 1;
      return acc;
    }, {});
    
    console.log('Account types:', accountTypes);
    console.log('\n');

    // Focus on Solana wallets
    console.log('Solana Wallets Detailed Analysis:');
    console.log('=================================');
    
    const solanaWallets = response.data.linked_accounts.filter((account: any) => 
      account.type === 'wallet' && account.chain_type === 'solana'
    );
    
    console.log(`Found ${solanaWallets.length} Solana wallet(s)\n`);

    solanaWallets.forEach((wallet: any, index: number) => {
      console.log(`Solana Wallet ${index + 1}:`);
      console.log('----------------');
      console.log('Address:', wallet.address);
      console.log('ID:', wallet.id);
      console.log('Chain Type:', wallet.chain_type);
      console.log('Connector Type:', wallet.connector_type);
      console.log('Wallet Client Type:', wallet.wallet_client_type);
      console.log('Imported At:', wallet.imported_at);
      console.log('Verified At:', wallet.verified_at);
      
      // Check for delegation-related fields
      console.log('\nDelegation-related fields:');
      console.log('Delegated:', wallet.delegated);
      console.log('Session Signer Enabled:', wallet.session_signer_enabled);
      console.log('Delegated At:', wallet.delegated_at);
      
      // Log all other fields
      console.log('\nAll fields:');
      Object.keys(wallet).forEach(key => {
        if (!['address', 'id', 'chain_type', 'connector_type', 'wallet_client_type', 
            'imported_at', 'verified_at', 'delegated', 'session_signer_enabled', 'delegated_at'].includes(key)) {
          console.log(`${key}:`, wallet[key]);
        }
      });
      
      console.log('\n---\n');
    });

    // Check for specific wallets
    console.log('Checking for specific wallets:');
    console.log('=============================');
    
    const wallet1 = 'g935C6veQ53oxSjc5LTHMhsZMNNzJH8rodioR9JnBzJ';
    const wallet2 = '594bYtjAQtFQx2dBSmLZCFMeneYXzRca9EbHeRDKC4qX';
    
    const findWallet = (address: string) => {
      return response.data.linked_accounts.find((acc: any) => acc.address === address);
    };
    
    const foundWallet1 = findWallet(wallet1);
    const foundWallet2 = findWallet(wallet2);
    
    console.log(`\nWallet 1 (${wallet1}):`);
    if (foundWallet1) {
      console.log('✅ Found in linked accounts');
      console.log('Details:', JSON.stringify(foundWallet1, null, 2));
    } else {
      console.log('❌ NOT found in linked accounts');
    }
    
    console.log(`\nWallet 2 (${wallet2}):`);
    if (foundWallet2) {
      console.log('✅ Found in linked accounts');
      console.log('Details:', JSON.stringify(foundWallet2, null, 2));
    } else {
      console.log('❌ NOT found in linked accounts');
    }

  } catch (error) {
    console.error('❌ Error fetching Privy data:');
    if (axios.isAxiosError(error)) {
      console.error('Status:', error.response?.status);
      console.error('Message:', error.response?.data?.message || error.message);
      console.error('Data:', error.response?.data);
    } else {
      console.error(error);
    }
  }
}

// Run the test
testPrivyWallets().then(() => {
  console.log('\n✅ Test completed');
}).catch(error => {
  console.error('\n❌ Test failed:', error);
  process.exit(1);
});