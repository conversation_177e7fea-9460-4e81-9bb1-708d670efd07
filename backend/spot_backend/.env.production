PORT=5001
PRIVY_APP_ID=cm8iaeayj00odvpx8lr8uao1q
JWT_SECRET=your_secure_jwt_secret
NODE_ENV=production
COINGECKO_API_KEY=CG-uaLyiV5AXb39XDwzS3o6WLEd
MOBULA_API_KEY=fffa68cd-6bde-4ac5-909d-eb627d8baca0
# Redis connection (default: localhost:6379)
REDIS_URL=redis://localhost:6379
#REDIS_URL=redis://redis:6379
ETHERSCAN_API_KEY=**********************************
BSCSCAN_API_KEY=**********************************
SOLSCAN_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************.Z68HdUB6rpnlv4xcmaak3rHvgoLFqqJz4Itw1mURKn4
PRIVY_APP_SECRET=4uUMe6w4efz2SUZTwJWMwzQQhrEqXTKXMYPkfcLHxVUGoQyX4JxBUXUXGWV1aJZu9gFoaemPbkkDnVCCxSri54k4
MOBULA_WSS_URL=wss://pulse-api.mobula.io
# Mobula WebSocket URLs for limit order monitoring (Enterprise)
MOBULA_PRICE_WSS_PRIMARY=wss://api.mobula.io
MOBULA_PRICE_WSS_FALLBACK=wss://api-prod.mobula.io
# Wallet API Keys
ALCHEMY_API_KEY_ETH=https://eth-mainnet.g.alchemy.com/v2/sQLdRXYdXOU8D27i-pXh2SgQGBFJsNX6
ALCHEMY_API_KEY_POLY=https://polygon-mainnet.g.alchemy.com/v2/sQLdRXYdXOU8D27i-pXh2SgQGBFJsNX6
SOLANA_RPC_URL=https://solana-mainnet.g.alchemy.com/v2/demo
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# Supabase Configuration (for Limit Orders)
SUPABASE_URL=https://mfzyylxnuzadmsdxivib.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1menl5bHhudXphZG1zZHhpdmliIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTQ4OTM4NiwiZXhwIjoyMDYxMDY1Mzg2fQ.Y3yRBHmKO1woifofik0R4tEheFacAY8o0UiaYzpjSGE
