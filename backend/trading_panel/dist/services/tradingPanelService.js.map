{"version": 3, "file": "tradingPanelService.js", "sourceRoot": "", "sources": ["../../src/services/tradingPanelService.ts"], "names": [], "mappings": ";;;;;;AACA,kDAA0B;AAC1B,uDAAqD;AAErD,MAAa,mBAAmB;IAI9B;QAHiB,mBAAc,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,sCAAsC,CAAC;QAIrG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;QAC7C,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;QAE1D,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBAC/C,MAAM,EAAE,CAAC,CAAC,WAAW;gBACrB,MAAM,EAAE,CAAC,CAAC,WAAW;aACtB,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAA,0BAAY,EAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC5D,CAAC;IAED,KAAK,CAAC,SAAS;QACb,OAAO;YACL,EAAE,EAAE,mBAAmB;YACvB,IAAI,EAAE,uBAAuB;SAC9B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,YAAoB;QACrC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;QAE/E,MAAM,OAAO,GAAG;YACd,eAAe,EAAE,IAAI,CAAC,cAAc;YACpC,QAAQ,EAAE,kBAAkB;SAC7B,CAAC;QAEF,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,EAAE,CAAC,CAAC;YAExD,MAAM,CAAC,eAAe,EAAE,cAAc,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC1D,eAAK,CAAC,GAAG,CAAC,qFAAqF,YAAY,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;gBAC3H,eAAK,CAAC,GAAG,CAAC,mGAAmG,YAAY,YAAY,EAAE,EAAE,OAAO,EAAE,CAAC;aACpJ,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YAChD,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YAE9C,kDAAkD;YAClD,MAAM,SAAS,GAAG,MAAM;iBACrB,IAAI,CAAC,CAAC,CAAM,EAAE,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,CAAC;iBACjE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAEhB,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;QACxC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAE/D,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACrD,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,2CAA2C,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,aAAqB,EAAE,QAAgB,EAAE,EAAE,SAAiB,CAAC,EAAE,OAAe,CAAC,EAAE,YAAqB;QACxH,OAAO,CAAC,GAAG,CAAC,+BAA+B,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,uBAAuB,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAExH,IAAI,CAAC;YACH,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ;iBACtB,IAAI,CAAC,eAAe,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC;iBACX,EAAE,CAAC,gBAAgB,EAAE,aAAa,CAAC;iBACnC,KAAK,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;YAE7C,oCAAoC;YACpC,IAAI,YAAY,EAAE,CAAC;gBACjB,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAClD,CAAC;YAED,mBAAmB;YACnB,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,MAAM,EAAE,CAAC;gBACX,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;YAClD,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,KAAK,CAAC;YAEpC,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;gBAClE,MAAM,IAAI,KAAK,CAAC,8CAA8C,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjF,CAAC;YAED,uDAAuD;YACvD,OAAO;gBACL,IAAI,EAAE,IAAI,IAAI,EAAE;gBAChB,KAAK,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;gBACxB,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,MAAM;aACf,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;CACF;AA/GD,kDA+GC"}