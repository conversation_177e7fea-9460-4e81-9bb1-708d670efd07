"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingPanelService = void 0;
const axios_1 = __importDefault(require("axios"));
const supabase_js_1 = require("@supabase/supabase-js");
class TradingPanelService {
    constructor() {
        this.MOBULA_API_KEY = process.env.MOBULA_API_KEY || 'fffa68cd-6bde-4ac5-909d-eb627d8baca0';
        this.initializeSupabase();
    }
    initializeSupabase() {
        const supabaseUrl = process.env.SUPABASE_URL;
        const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
        if (!supabaseUrl || !supabaseKey) {
            console.error('Missing Supabase configuration:', {
                hasUrl: !!supabaseUrl,
                hasKey: !!supabaseKey
            });
            throw new Error('Supabase URL and Service Role Key are required');
        }
        this.supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseKey);
        console.log('✅ Supabase client initialized successfully');
    }
    async getConfig() {
        return {
            id: 'trading_panel_001',
            name: 'Trading Panel Service'
        };
    }
    async getTokenInfo(tokenAddress) {
        console.log(`Using Mobula API Key: ${this.MOBULA_API_KEY.substring(0, 5)}...`);
        const headers = {
            'authorization': this.MOBULA_API_KEY,
            'accept': 'application/json'
        };
        try {
            console.log(`Fetching token info for: ${tokenAddress}`);
            const [holdersResponse, tradesResponse] = await Promise.all([
                axios_1.default.get(`https://api.mobula.io/api/1/market/token/holders?limit=20&blockchain=solana&asset=${tokenAddress}`, { headers }),
                axios_1.default.get(`https://api.mobula.io/api/1/market/trades/pair?sortOrder=desc&mode=pair&blockchain=solana&asset=${tokenAddress}&limit=100`, { headers })
            ]);
            const holders = holdersResponse.data.data || [];
            const trades = tradesResponse.data.data || [];
            // Sort trades by token_amount_usd and take top 10
            const topTrades = trades
                .sort((a, b) => b.token_amount_usd - a.token_amount_usd)
                .slice(0, 10);
            return { holders, trades: topTrades };
        }
        catch (error) {
            console.error('Error fetching token info from Mobula:', error);
            if (error.response) {
                console.error('Response data:', error.response.data);
                console.error('Response status:', error.response.status);
            }
            throw new Error(`Failed to fetch token info from Mobula: ${error.message}`);
        }
    }
    async getUserTrades(walletAddress, limit = 50, offset = 0, page = 1, tokenAddress) {
        console.log(`Fetching trades for wallet: ${walletAddress}${tokenAddress ? ` filtered by token: ${tokenAddress}` : ''}`);
        try {
            let query = this.supabase
                .from('trade_history')
                .select('*')
                .eq('wallet_address', walletAddress)
                .order('created_at', { ascending: false });
            // Apply token filtering if provided
            if (tokenAddress) {
                query = query.eq('token_address', tokenAddress);
            }
            // Apply pagination
            if (limit) {
                query = query.limit(limit);
            }
            if (offset) {
                query = query.range(offset, offset + limit - 1);
            }
            const { data, error } = await query;
            if (error) {
                console.error('Error fetching user trades from Supabase:', error);
                throw new Error(`Failed to fetch user trades from Supabase: ${error.message}`);
            }
            // Format response to match UserTradeResponse structure
            return {
                data: data || [],
                total: data?.length || 0,
                page: page,
                limit: limit,
                offset: offset
            };
        }
        catch (error) {
            console.error('Error fetching user trades:', error);
            throw new Error(`Failed to fetch user trades: ${error.message}`);
        }
    }
}
exports.TradingPanelService = TradingPanelService;
//# sourceMappingURL=tradingPanelService.js.map