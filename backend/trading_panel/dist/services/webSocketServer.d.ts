import { Server as HttpServer } from 'http';
export declare class WebSocketServerService {
    private wss;
    private clients;
    private poolSubscriptions;
    private readonly MAX_POOLS_PER_SERVICE;
    private readonly POOL_CLEANUP_INTERVAL;
    private readonly POOL_IDLE_TIMEOUT;
    private readonly CLIENT_DISCONNECT_GRACE_PERIOD;
    private cleanupTimer;
    private connectionStatsTimer;
    constructor(server: HttpServer);
    /**
     * Start pool cleanup timer
     */
    private startPoolCleanup;
    /**
     * Start connection monitoring for resource optimization
     */
    private startConnectionMonitoring;
    /**
     * Log connection statistics for monitoring
     */
    private logConnectionStats;
    /**
     * Optimize resource usage based on current connections
     */
    private optimizeResourceUsage;
    /**
     * Force cleanup of all pools (used when no clients are connected)
     */
    private forceCleanupAllPools;
    /**
     * Setup Mobula callbacks for a specific pool
     */
    private setupMobulaCallbacks;
    private setupWebSocketServer;
    /**
     * Handle client disconnect with proper cleanup
     */
    private handleClientDisconnect;
    private handleClientMessage;
    private handleSubscribe;
    /**
     * Create a new pool subscription with Mobula service
     */
    private createPoolSubscription;
    /**
     * Unsubscribe client from a specific pool
     */
    private unsubscribeClientFromPool;
    private handleUnsubscribe;
    /**
     * Broadcast message to clients subscribed to a specific pool
     */
    private broadcastToPoolClients;
    /**
     * Clean up idle pools that have no activity
     */
    private cleanupIdlePools;
    /**
     * Get comprehensive server status
     */
    getStatus(): {
        clientCount: number;
        poolCount: number;
        pools: Array<{
            poolAddress: string;
            clientCount: number;
            connected: boolean;
            createdAt: number;
            lastActivity: number;
            idleTime: number;
        }>;
        maxPools: number;
        memoryUsage: {
            totalClients: number;
            totalPools: number;
        };
    };
    /**
     * Get pool-specific statistics
     */
    getPoolStats(poolAddress: string): {
        exists: boolean;
        clientCount?: number;
        connected?: boolean;
        createdAt?: number;
        lastActivity?: number;
        idleTime?: number;
    };
    /**
     * Force cleanup of a specific pool
     */
    forceCleanupPool(poolAddress: string): boolean;
    /**
     * Shutdown the server with proper cleanup
     */
    shutdown(): void;
}
//# sourceMappingURL=webSocketServer.d.ts.map