import { TradingPanelConfig, UserTradeResponse } from '../types';
export declare class TradingPanelService {
    private readonly MOBULA_API_KEY;
    private supabase;
    constructor();
    private initializeSupabase;
    getConfig(): Promise<TradingPanelConfig>;
    getTokenInfo(tokenAddress: string): Promise<{
        holders: any[];
        trades: any[];
    }>;
    getUserTrades(walletAddress: string, limit?: number, offset?: number, page?: number, tokenAddress?: string): Promise<UserTradeResponse>;
}
//# sourceMappingURL=tradingPanelService.d.ts.map