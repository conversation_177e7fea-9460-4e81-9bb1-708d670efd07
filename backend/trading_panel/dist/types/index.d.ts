export interface TradingPanelConfig {
    id: string;
    name: string;
}
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
}
export interface UserTrade {
    chain_id: string;
    swap_type: string;
    raw_amount0: string;
    raw_amount1: string;
    amount0: number;
    amount1: number;
    ratio: number;
    price_usd_token0: number;
    price_usd_token1: number;
    date: string;
    amount_usd: number;
    pool_address: string;
    token0_address: string;
    token1_address: string;
    transaction_sender_address: string;
    transaction_hash: string;
    base: string;
    quote: string;
    side: 'buy' | 'sell';
    amount_quote: number;
    amount_base: number;
}
export interface UserTradeResponse {
    data: any[];
    total?: number;
    page?: number;
    limit?: number;
    offset?: number;
}
export interface Trade {
    id?: string;
    timestamp?: number;
    date?: number;
    type: 'buy' | 'sell';
    token_price?: number;
    token_amount?: number;
    token_amount_vs?: number;
    token_amount_usd?: number;
    hash?: string;
    transaction_hash?: string;
    tx_hash?: string;
    sender?: string;
    pairData?: {
        price?: number;
        token0?: {
            address?: string;
            price?: number;
            marketCap?: number;
            totalSupply?: number;
            symbol?: string;
        };
        token1?: {
            address?: string;
            price?: number;
            marketCap?: number;
            totalSupply?: number;
            symbol?: string;
        };
        liquidity?: number;
        volume24h?: number;
        volume_1min?: number;
        volume_5min?: number;
        volume_1h?: number;
        volume_6h?: number;
        volume_12h?: number;
        buy_volume_1min?: number;
        buy_volume_5min?: number;
        buy_volume_1h?: number;
        buy_volume_6h?: number;
        buy_volume_12h?: number;
        buy_volume_24h?: number;
        sell_volume_1min?: number;
        sell_volume_5min?: number;
        sell_volume_1h?: number;
        sell_volume_6h?: number;
        sell_volume_12h?: number;
        sell_volume_24h?: number;
        buyers_1min?: number;
        buyers_5min?: number;
        buyers_1h?: number;
        buyers_6h?: number;
        buyers_12h?: number;
        buyers_24h?: number;
        sellers_1min?: number;
        sellers_5min?: number;
        sellers_1h?: number;
        sellers_6h?: number;
        sellers_12h?: number;
        sellers_24h?: number;
    };
    marketCap?: number;
    amount?: number;
    usd_amount?: number;
    value_usd?: number;
    price?: number;
    symbol?: string;
    blockchain?: string;
    chain?: string;
}
export interface FormattedTrade {
    id: string;
    timestamp: number;
    type: 'buy' | 'sell';
    amount: string;
    usdAmount: string;
    mc: string;
    price: string;
    trader: string;
    age: string;
    txHash: string;
    marketCap: number;
    tokenAmount: number;
    tokenAmountUsd: number;
    actualTokenAmount: number;
    poolAddress: string;
    pairData?: {
        price?: number;
        token0?: {
            address?: string;
            price?: number;
            marketCap?: number;
            totalSupply?: number;
            symbol?: string;
        };
        token1?: {
            address?: string;
            price?: number;
            marketCap?: number;
            totalSupply?: number;
            symbol?: string;
        };
        liquidity?: number;
        volume24h?: number;
        volume_1min?: number;
        volume_5min?: number;
        volume_1h?: number;
        volume_6h?: number;
        volume_12h?: number;
        buy_volume_1min?: number;
        buy_volume_5min?: number;
        buy_volume_1h?: number;
        buy_volume_6h?: number;
        buy_volume_12h?: number;
        buy_volume_24h?: number;
        sell_volume_1min?: number;
        sell_volume_5min?: number;
        sell_volume_1h?: number;
        sell_volume_6h?: number;
        sell_volume_12h?: number;
        sell_volume_24h?: number;
        buyers_1min?: number;
        buyers_5min?: number;
        buyers_1h?: number;
        buyers_6h?: number;
        buyers_12h?: number;
        buyers_24h?: number;
        sellers_1min?: number;
        sellers_5min?: number;
        sellers_1h?: number;
        sellers_6h?: number;
        sellers_12h?: number;
        sellers_24h?: number;
    };
    token_price?: number;
    [key: string]: any;
}
export interface WebSocketMessage {
    type: 'trade' | 'error' | 'connected' | 'disconnected';
    data?: any;
    error?: string;
}
export interface SubscriptionRequest {
    action: 'subscribe' | 'unsubscribe';
    poolAddress: string;
}
//# sourceMappingURL=index.d.ts.map