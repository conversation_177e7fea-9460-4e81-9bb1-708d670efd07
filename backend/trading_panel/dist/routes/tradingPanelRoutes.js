"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const tradingPanelController_1 = require("../controllers/tradingPanelController");
const router = (0, express_1.Router)();
// Trading panel routes
router.get('/config', tradingPanelController_1.getTradingPanelConfig);
router.get('/websocket/status', tradingPanelController_1.getWebSocketStatus);
router.get('/health', tradingPanelController_1.healthCheck);
router.get('/token-info/:tokenAddress', tradingPanelController_1.getTokenInfo);
router.get('/user-trades/:walletAddress', tradingPanelController_1.getUserTrades);
// New monitoring and management routes
router.get('/memory-stats', tradingPanelController_1.getMemoryStats);
router.post('/force-cleanup', tradingPanelController_1.forceCleanup);
exports.default = router;
//# sourceMappingURL=tradingPanelRoutes.js.map