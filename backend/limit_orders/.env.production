PORT=5002
NODE_ENV=production

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Supabase Configuration
SUPABASE_URL=https://mfzyylxnuzadmsdxivib.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1menl5bHhudXphZG1zZHhpdmliIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTQ4OTM4NiwiZXhwIjoyMDYxMDY1Mzg2fQ.Y3yRBHmKO1woifofik0R4tEheFacAY8o0UiaYzpjSGE

# Mobula API Configuration (for price monitoring)
MOBULA_API_KEY=fffa68cd-6bde-4ac5-909d-eb627d8baca0
MOBULA_WSS_URL=wss://api.mobula.io

# CORS Configuration
CORS_ORIGINS=https://redfyn.crypfi.io,https://dev.crypfi.io