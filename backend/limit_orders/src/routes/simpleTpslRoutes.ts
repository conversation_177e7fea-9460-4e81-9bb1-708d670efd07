import express, { Request, Response } from 'express';
import { simpleTpslService } from '../services/simpleTpslService.js';
import { logger } from '../utils/logger.js';

// Helper function to remove 'did:privy:' prefix from wallet ID and handle URL encoding
function cleanWalletId(walletId: string): string {
  // First decode URL encoding if present
  let decodedId = walletId;
  try {
    decodedId = decodeURIComponent(walletId);
  } catch (e) {
    // If decoding fails, use original
    decodedId = walletId;
  }
  
  // Remove did:privy: prefix
  if (decodedId.startsWith('did:privy:')) {
    return decodedId.replace('did:privy:', '');
  }
  return decodedId;
}

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     SimpleTpslOrder:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         user_id:
 *           type: string
 *         token_address:
 *           type: string
 *         token_name:
 *           type: string
 *         token_symbol:
 *           type: string
 *         token_image:
 *           type: string
 *         pool_address:
 *           type: string
 *         dex_type:
 *           type: string
 *         order_type:
 *           type: string
 *           enum: [take_profit, stop_loss]
 *         direction:
 *           type: string
 *           enum: [buy, sell]
 *         trigger_price:
 *           type: number
 *         amount:
 *           type: number
 *         slippage:
 *           type: number
 *         wallet_address:
 *           type: string
 *         wallet_id:
 *           type: string
 *         status:
 *           type: string
 *           enum: [active, triggered, executed, cancelled, failed]
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/simple-tpsl/orders:
 *   post:
 *     summary: Create a new TP/SL order
 *     tags: [Simple TP/SL]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - token_address
 *               - pool_address
 *               - order_type
 *               - direction
 *               - trigger_price
 *               - amount
 *               - wallet_address
 *             properties:
 *               user_id:
 *                 type: string
 *               token_address:
 *                 type: string
 *               token_name:
 *                 type: string
 *               token_symbol:
 *                 type: string
 *               token_image:
 *                 type: string
 *               pool_address:
 *                 type: string
 *               dex_type:
 *                 type: string
 *                 default: raydium
 *               order_type:
 *                 type: string
 *                 enum: [take_profit, stop_loss]
 *               direction:
 *                 type: string
 *                 enum: [buy, sell]
 *               trigger_price:
 *                 type: number
 *               amount:
 *                 type: number
 *               slippage:
 *                 type: number
 *                 default: 1.0
 *               wallet_address:
 *                 type: string
 *               wallet_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: TP/SL order created successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Internal server error
 */
router.post('/orders', async (req: Request, res: Response) => {
  try {
    // Decode URL encoding but preserve full user_id for database storage
    if (req.body.user_id) {
      try {
        req.body.user_id = decodeURIComponent(req.body.user_id);
      } catch (e) {
        // If decoding fails, use original
      }
    }
    
    // Check if this is the new unified format (has tp_enabled or sl_enabled)
    const isNewFormat = req.body.tp_enabled !== undefined || req.body.sl_enabled !== undefined;
    
    let result;
    if (isNewFormat) {
      // Use new unified method
      result = await simpleTpslService.createOrder(req.body);
    } else {
      // Use legacy method for backward compatibility
      result = await simpleTpslService.createOrderLegacy(req.body);
    }
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: 'TP/SL order created successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in POST /api/simple-tpsl/orders:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/simple-tpsl/orders/{userId}:
 *   get:
 *     summary: Get TP/SL orders for a user (backward compatibility)
 *     tags: [Simple TP/SL]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, triggered, executed, cancelled, failed]
 *       - in: query
 *         name: order_type
 *         schema:
 *           type: string
 *           enum: [take_profit, stop_loss]
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [buy, sell]
 *       - in: query
 *         name: token_address
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/orders/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { status, order_type, direction, token_address } = req.query;
    
    // Decode URL encoding but preserve full user_id for database queries
    let decodedUserId = userId;
    try {
      decodedUserId = decodeURIComponent(userId);
    } catch (e) {
      // If decoding fails, use original
    }
    
    const filters = {
      status: status as any,
      order_type: order_type as any,
      direction: direction as any,
      token_address: token_address as string
    };
    
    const result = await simpleTpslService.getOrders(decodedUserId, filters);
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in GET /api/simple-tpsl/orders:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/simple-tpsl/orders/list:
 *   post:
 *     summary: Get TP/SL orders for a user (using request body)
 *     tags: [Simple TP/SL]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *             properties:
 *               user_id:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [active, triggered, executed, cancelled, failed]
 *               order_type:
 *                 type: string
 *                 enum: [take_profit, stop_loss]
 *               direction:
 *                 type: string
 *                 enum: [buy, sell]
 *               token_address:
 *                 type: string
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.post('/orders/list', async (req: Request, res: Response) => {
  try {
    const { user_id, status, order_type, direction, token_address } = req.body;
    
    if (!user_id) {
      return res.status(400).json({
        success: false,
        error: 'user_id is required in request body'
      });
    }
    
    // Decode URL encoding but preserve full user_id for database queries
    let decodedUserId = user_id;
    try {
      decodedUserId = decodeURIComponent(user_id);
    } catch (e) {
      // If decoding fails, use original
    }
    
    const filters = {
      status: status as any,
      order_type: order_type as any,
      direction: direction as any,
      token_address: token_address as string
    };
    
    // Try new method first, fall back to legacy if needed
    let result = await simpleTpslService.getUserOrders(decodedUserId);
    
    // If no data from new method, try legacy method for backward compatibility
    if (result.success && (!result.data || result.data.length === 0)) {
      result = await simpleTpslService.getOrdersLegacy(decodedUserId, filters);
    }
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in POST /api/simple-tpsl/orders/list:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/simple-tpsl/orders/{orderId}/cancel:
 *   post:
 *     summary: Cancel a TP/SL order
 *     tags: [Simple TP/SL]
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *             properties:
 *               user_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Order cancelled successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Internal server error
 */
router.post('/orders/:orderId/cancel', async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;
    const { user_id } = req.body;
    
    if (!user_id) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }
    
    // Decode URL encoding but preserve full user_id for database queries
    let decodedUserId = user_id;
    try {
      decodedUserId = decodeURIComponent(user_id);
    } catch (e) {
      // If decoding fails, use original
    }
    
    const result = await simpleTpslService.cancelOrder(decodedUserId, orderId);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'TP/SL order cancelled successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in POST /api/simple-tpsl/orders/cancel:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/simple-tpsl/active:
 *   get:
 *     summary: Get all active TP/SL orders for monitoring
 *     tags: [Simple TP/SL]
 *     responses:
 *       200:
 *         description: Active orders retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/active', async (req: Request, res: Response) => {
  try {
    const result = await simpleTpslService.getActiveOrders();
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in GET /api/simple-tpsl/active:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/simple-tpsl/orders/{orderId}/remove-tp:
 *   post:
 *     summary: Remove Take Profit order individually
 *     tags: [Simple TP/SL]
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *             properties:
 *               user_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Take Profit order removed successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Internal server error
 */
router.post('/orders/:orderId/remove-tp', async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;
    const { user_id } = req.body;
    
    if (!user_id) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }
    
    const result = await simpleTpslService.removeTakeProfit(user_id, orderId);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Take Profit order removed successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in POST /api/simple-tpsl/orders/remove-tp:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/simple-tpsl/orders/{orderId}/remove-sl:
 *   post:
 *     summary: Remove Stop Loss order individually
 *     tags: [Simple TP/SL]
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *             properties:
 *               user_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Stop Loss order removed successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Internal server error
 */
router.post('/orders/:orderId/remove-sl', async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;
    const { user_id } = req.body;
    
    if (!user_id) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }
    
    const result = await simpleTpslService.removeStopLoss(user_id, orderId);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Stop Loss order removed successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in POST /api/simple-tpsl/orders/remove-sl:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;