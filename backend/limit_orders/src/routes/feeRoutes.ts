import { Router, Request, Response } from 'express';
import { getDynamicFeeService } from '../services/dynamicFeeService.js';
import { logger } from '../utils/logger.js';

const router = Router();
const SOLANA_RPC_URL = process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com';
const dynamicFeeService = getDynamicFeeService(SOLANA_RPC_URL);

/**
 * GET /api/fees/network-stats
 * Get current network statistics and congestion level
 */
router.get('/network-stats', async (req: Request, res: Response) => {
  try {
    const stats = await dynamicFeeService.getNetworkStats();
    
    if (!stats) {
      return res.status(500).json({
        success: false,
        error: 'Failed to retrieve network statistics'
      });
    }
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Error getting network stats:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/fees/calculate
 * Calculate dynamic fees for a given trade amount
 */
router.post('/calculate', async (req: Request, res: Response) => {
  try {
    const { amount, isUrgent = false, exchangeType = 'default' } = req.body;
    
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid trade amount'
      });
    }
    
    const fees = await dynamicFeeService.calculateDynamicFees(amount, isUrgent, exchangeType);
    
    res.json({
      success: true,
      data: {
        tradeAmount: amount,
        isUrgent,
        exchangeType,
        priorityFee: fees.priorityFee,
        bribeAmount: fees.bribeAmount,
        priorityLevel: fees.priorityLevel,
        // Convert to values expected by Solana service
        priorityFeeFormatted: {
          sol: fees.priorityFee,
          lamports: fees.priorityFee * 1_000_000_000,
          microLamports: Math.floor((fees.priorityFee * 1_000_000_000 * 1_000_000) / 100_000)
        },
        bribeAmountFormatted: {
          sol: fees.bribeAmount,
          lamports: Math.floor(fees.bribeAmount * 1_000_000_000)
        }
      }
    });
  } catch (error) {
    logger.error('Error calculating dynamic fees:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/fees/update-config
 * Update fee configuration (admin only)
 */
router.post('/update-config', async (req: Request, res: Response) => {
  try {
    // TODO: Add authentication check for admin users
    
    const { basePriorityFee, baseBribeAmount, amountTiers, networkCongestionMultipliers } = req.body;
    
    const config: any = {};
    
    if (basePriorityFee !== undefined) {
      config.basePriorityFee = basePriorityFee;
    }
    
    if (baseBribeAmount !== undefined) {
      config.baseBribeAmount = baseBribeAmount;
    }
    
    if (amountTiers) {
      config.amountTiers = amountTiers;
    }
    
    if (networkCongestionMultipliers) {
      config.networkCongestionMultipliers = networkCongestionMultipliers;
    }
    
    dynamicFeeService.updateFeeConfiguration(config);
    
    res.json({
      success: true,
      message: 'Fee configuration updated successfully'
    });
  } catch (error) {
    logger.error('Error updating fee configuration:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;