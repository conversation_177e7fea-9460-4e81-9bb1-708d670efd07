import { supabase, setUserContext } from '../config/supabase.js';
import { logger } from '../utils/logger.js';
import axios from 'axios';

// Enhanced error handling
export class TPSLError extends Error {
  constructor(
    public code: string,
    message: string,
    public context?: any
  ) {
    super(message);
    this.name = 'TPSLError';
  }
}

// Input validation constants
const VALIDATION_LIMITS = {
  MIN_AMOUNT: 0.001, // Minimum 0.001 SOL
  MAX_AMOUNT: 1000, // Maximum 1000 SOL
  MIN_PERCENTAGE: 0.1, // Minimum 0.1%
  MAX_PERCENTAGE: 1000, // Maximum 1000%
  MIN_PRICE: 0.000000001, // Minimum price
  MAX_ORDERS_PER_USER: 100, // Maximum orders per user
};

// Enhanced interfaces with strict validation
export interface CreateTPSLOrderRequest {
  user_id: string;
  wallet_id: string;
  wallet_address: string;
  token_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  pool_address: string;
  exchange_name?: string;
  current_price: number;
  amount: number;
  total_token?: number;
  txn?: string;
  tp_percentage?: number;
  sl_percentage?: number;
}

export interface TPSLOrderResponse {
  success: boolean;
  data?: {
    order_id: string;
    tp_id?: string;
    sl_id?: string;
    main_order?: any;
    tp_orders?: any[];
    sl_orders?: any[];
  };
  error?: string;
  error_code?: string;
}

// Helper function to clean wallet ID
function cleanWalletId(walletId: string): string {
  let decodedId = walletId;
  try {
    decodedId = decodeURIComponent(walletId);
  } catch (e) {
    decodedId = walletId;
  }
  
  if (decodedId.startsWith('did:privy:')) {
    return decodedId.replace('did:privy:', '');
  }
  return decodedId;
}

export class EnhancedTPSLService {
  
  /**
   * Comprehensive input validation
   */
  private validateCreateOrderInput(data: CreateTPSLOrderRequest): void {
    // User ID validation
    if (!data.user_id || typeof data.user_id !== 'string' || data.user_id.trim() === '') {
      throw new TPSLError('INVALID_USER_ID', 'User ID is required and must be a non-empty string');
    }

    if (!data.user_id.startsWith('did:privy:')) {
      throw new TPSLError('INVALID_USER_ID_FORMAT', 'User ID must be a valid Privy DID');
    }

    // Wallet validation
    if (!data.wallet_address || !/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(data.wallet_address)) {
      throw new TPSLError('INVALID_WALLET_ADDRESS', 'Invalid Solana wallet address format');
    }

    // Token validation
    if (!data.token_address || !/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(data.token_address)) {
      throw new TPSLError('INVALID_TOKEN_ADDRESS', 'Invalid token address format');
    }

    if (!data.token_name || data.token_name.trim().length === 0) {
      throw new TPSLError('INVALID_TOKEN_NAME', 'Token name is required');
    }

    if (!data.token_symbol || data.token_symbol.trim().length === 0) {
      throw new TPSLError('INVALID_TOKEN_SYMBOL', 'Token symbol is required');
    }

    // Pool validation
    if (!data.pool_address || !/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(data.pool_address)) {
      throw new TPSLError('INVALID_POOL_ADDRESS', 'Invalid pool address format');
    }

    // Price validation
    if (typeof data.current_price !== 'number' || data.current_price <= VALIDATION_LIMITS.MIN_PRICE) {
      throw new TPSLError('INVALID_CURRENT_PRICE', `Current price must be greater than ${VALIDATION_LIMITS.MIN_PRICE}`);
    }

    // Amount validation
    if (typeof data.amount !== 'number' || data.amount < VALIDATION_LIMITS.MIN_AMOUNT || data.amount > VALIDATION_LIMITS.MAX_AMOUNT) {
      throw new TPSLError('INVALID_AMOUNT', `Amount must be between ${VALIDATION_LIMITS.MIN_AMOUNT} and ${VALIDATION_LIMITS.MAX_AMOUNT} SOL`);
    }

    // TP/SL percentage validation
    if (data.tp_percentage !== undefined) {
      if (typeof data.tp_percentage !== 'number' || data.tp_percentage < VALIDATION_LIMITS.MIN_PERCENTAGE || data.tp_percentage > VALIDATION_LIMITS.MAX_PERCENTAGE) {
        throw new TPSLError('INVALID_TP_PERCENTAGE', `Take profit percentage must be between ${VALIDATION_LIMITS.MIN_PERCENTAGE}% and ${VALIDATION_LIMITS.MAX_PERCENTAGE}%`);
      }
    }

    if (data.sl_percentage !== undefined) {
      if (typeof data.sl_percentage !== 'number' || data.sl_percentage < VALIDATION_LIMITS.MIN_PERCENTAGE || data.sl_percentage > VALIDATION_LIMITS.MAX_PERCENTAGE) {
        throw new TPSLError('INVALID_SL_PERCENTAGE', `Stop loss percentage must be between ${VALIDATION_LIMITS.MIN_PERCENTAGE}% and ${VALIDATION_LIMITS.MAX_PERCENTAGE}%`);
      }
    }

    // At least one TP or SL must be specified
    if (data.tp_percentage === undefined && data.sl_percentage === undefined) {
      throw new TPSLError('NO_TP_SL_SPECIFIED', 'At least one of Take Profit or Stop Loss percentage must be provided');
    }

    // Token amount validation if provided
    if (data.total_token !== undefined && (typeof data.total_token !== 'number' || data.total_token <= 0)) {
      throw new TPSLError('INVALID_TOKEN_AMOUNT', 'Total token amount must be a positive number');
    }
  }

  /**
   * Check if user has exceeded order limits
   */
  private async checkUserOrderLimits(userId: string): Promise<void> {
    const decodedUserId = decodeURIComponent(userId);
    await setUserContext(decodedUserId);

    const { count, error } = await supabase
      .from('tpsl_orders')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', decodedUserId)
      .eq('status', 'active');

    if (error) {
      throw new TPSLError('DATABASE_ERROR', `Failed to check user order limits: ${error.message}`);
    }

    if (count && count >= VALIDATION_LIMITS.MAX_ORDERS_PER_USER) {
      throw new TPSLError('ORDER_LIMIT_EXCEEDED', `Maximum ${VALIDATION_LIMITS.MAX_ORDERS_PER_USER} active orders allowed per user`);
    }
  }

  /**
   * Create TPSL order with atomic transaction safety
   */
  async createOrder(orderData: CreateTPSLOrderRequest): Promise<TPSLOrderResponse> {
    try {
      // Comprehensive input validation
      this.validateCreateOrderInput(orderData);

      // Clean and decode user ID
      const decodedUserId = decodeURIComponent(orderData.user_id);
      const cleanedWalletId = cleanWalletId(orderData.wallet_id);

      // Check user order limits
      await this.checkUserOrderLimits(decodedUserId);

      // Set user context for RLS
      await setUserContext(decodedUserId);

      logger.info('Creating TPSL order with atomic transaction', {
        userId: decodedUserId.substring(0, 20) + '...',
        tokenAddress: orderData.token_address,
        amount: orderData.amount,
        tpPercentage: orderData.tp_percentage,
        slPercentage: orderData.sl_percentage
      });

      // Use atomic transaction function
      const { data, error } = await supabase.rpc('create_tpsl_order_atomic', {
        p_user_id: decodedUserId,
        p_wallet_id: cleanedWalletId,
        p_wallet_address: orderData.wallet_address,
        p_token_address: orderData.token_address,
        p_token_name: orderData.token_name,
        p_token_symbol: orderData.token_symbol,
        p_token_image: orderData.token_image || null,
        p_pool_address: orderData.pool_address,
        p_exchange_name: orderData.exchange_name || 'pumpfun',
        p_current_price: orderData.current_price,
        p_amount: orderData.amount,
        p_total_token: orderData.total_token || null,
        p_txn: orderData.txn || null,
        p_tp_percentage: orderData.tp_percentage || null,
        p_sl_percentage: orderData.sl_percentage || null
      });

      if (error) {
        throw new TPSLError('TRANSACTION_FAILED', `Database transaction failed: ${error.message}`);
      }

      // Parse the result
      const result = data as any;
      if (!result.success) {
        throw new TPSLError(result.error_code || 'CREATION_FAILED', result.error || 'Order creation failed');
      }

      logger.info('TPSL order created successfully', {
        orderId: result.order_id,
        tpId: result.tp_id,
        slId: result.sl_id
      });

      // Fetch the complete order data for response
      const orderDetails = await this.getOrderById(decodedUserId, result.order_id);

      return {
        success: true,
        data: {
          order_id: result.order_id,
          tp_id: result.tp_id,
          sl_id: result.sl_id,
          main_order: orderDetails.data?.main_order,
          tp_orders: orderDetails.data?.tp_orders,
          sl_orders: orderDetails.data?.sl_orders
        }
      };

    } catch (error) {
      if (error instanceof TPSLError) {
        logger.error('TPSL order creation failed (validation)', {
          code: error.code,
          message: error.message,
          context: error.context
        });
        return {
          success: false,
          error: error.message,
          error_code: error.code
        };
      }

      logger.error('TPSL order creation failed (unexpected)', {
        error: (error as Error).message,
        stack: (error as Error).stack
      });

      return {
        success: false,
        error: 'Internal server error',
        error_code: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get order by ID with complete details
   */
  async getOrderById(userId: string, orderId: string): Promise<TPSLOrderResponse> {
    try {
      const decodedUserId = decodeURIComponent(userId);
      await setUserContext(decodedUserId);

      // Get main order
      const { data: mainOrder, error: mainError } = await supabase
        .from('tpsl_orders')
        .select('*')
        .eq('id', orderId)
        .eq('user_id', decodedUserId)
        .single();

      if (mainError) {
        if (mainError.code === 'PGRST116') {
          return {
            success: false,
            error: 'Order not found',
            error_code: 'ORDER_NOT_FOUND'
          };
        }
        throw new TPSLError('DATABASE_ERROR', mainError.message);
      }

      // Get TP orders
      const { data: tpOrders, error: tpError } = await supabase
        .from('tp_orders')
        .select('*')
        .eq('tpsl_order_id', orderId);

      if (tpError) {
        throw new TPSLError('DATABASE_ERROR', `Failed to fetch TP orders: ${tpError.message}`);
      }

      // Get SL orders
      const { data: slOrders, error: slError } = await supabase
        .from('sl_orders')
        .select('*')
        .eq('tpsl_order_id', orderId);

      if (slError) {
        throw new TPSLError('DATABASE_ERROR', `Failed to fetch SL orders: ${slError.message}`);
      }

      return {
        success: true,
        data: {
          order_id: orderId,
          main_order: mainOrder,
          tp_orders: tpOrders || [],
          sl_orders: slOrders || []
        }
      };

    } catch (error) {
      if (error instanceof TPSLError) {
        return {
          success: false,
          error: error.message,
          error_code: error.code
        };
      }

      logger.error('Failed to get order by ID', {
        error: (error as Error).message,
        orderId,
        userId: userId.substring(0, 20) + '...'
      });

      return {
        success: false,
        error: 'Internal server error',
        error_code: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Cancel order with atomic transaction safety
   */
  async cancelOrder(userId: string, orderId: string): Promise<TPSLOrderResponse> {
    try {
      const decodedUserId = decodeURIComponent(userId);
      await setUserContext(decodedUserId);

      logger.info('Cancelling TPSL order', {
        userId: decodedUserId.substring(0, 20) + '...',
        orderId
      });

      // Use atomic cancellation function
      const { data, error } = await supabase.rpc('cancel_tpsl_order_atomic', {
        p_order_id: orderId,
        p_user_id: decodedUserId
      });

      if (error) {
        throw new TPSLError('TRANSACTION_FAILED', `Cancellation transaction failed: ${error.message}`);
      }

      const result = data as any;
      if (!result.success) {
        throw new TPSLError(result.error_code || 'CANCELLATION_FAILED', result.error || 'Order cancellation failed');
      }

      logger.info('TPSL order cancelled successfully', { orderId });

      return {
        success: true,
        data: {
          order_id: orderId
        }
      };

    } catch (error) {
      if (error instanceof TPSLError) {
        return {
          success: false,
          error: error.message,
          error_code: error.code
        };
      }

      logger.error('Failed to cancel order', {
        error: (error as Error).message,
        orderId,
        userId: userId.substring(0, 20) + '...'
      });

      return {
        success: false,
        error: 'Internal server error',
        error_code: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get all orders for a user with pagination
   */
  async getUserOrders(
    userId: string, 
    status?: string, 
    limit: number = 50, 
    offset: number = 0
  ): Promise<TPSLOrderResponse> {
    try {
      const decodedUserId = decodeURIComponent(userId);
      await setUserContext(decodedUserId);

      let query = supabase
        .from('tpsl_orders')
        .select(`
          *,
          tp_orders(*),
          sl_orders(*)
        `)
        .eq('user_id', decodedUserId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error, count } = await query;

      if (error) {
        throw new TPSLError('DATABASE_ERROR', `Failed to fetch user orders: ${error.message}`);
      }

      return {
        success: true,
        data: {
          order_id: '', // Not applicable for list
          main_order: data || [],
          tp_orders: [],
          sl_orders: []
        }
      };

    } catch (error) {
      if (error instanceof TPSLError) {
        return {
          success: false,
          error: error.message,
          error_code: error.code
        };
      }

      logger.error('Failed to get user orders', {
        error: (error as Error).message,
        userId: userId.substring(0, 20) + '...'
      });

      return {
        success: false,
        error: 'Internal server error',
        error_code: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get system health metrics
   */
  async getSystemHealth(): Promise<{
    status: string;
    activeOrders: number;
    issues: string[];
  }> {
    try {
      const { count, error } = await supabase
        .from('tpsl_orders')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active');

      if (error) {
        return {
          status: 'unhealthy',
          activeOrders: 0,
          issues: [`Database error: ${error.message}`]
        };
      }

      const issues: string[] = [];
      const activeOrders = count || 0;

      if (activeOrders > 1000) {
        issues.push('High number of active orders may impact performance');
      }

      return {
        status: issues.length === 0 ? 'healthy' : 'degraded',
        activeOrders,
        issues
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        activeOrders: 0,
        issues: [`System error: ${(error as Error).message}`]
      };
    }
  }
}

// Create singleton instance
export const enhancedTpslService = new EnhancedTPSLService();
export default enhancedTpslService;