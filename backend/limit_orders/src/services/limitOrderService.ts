import { 
  supabase, 
  LimitOrder, 
  CreateLimitOrderData, 
  UpdateLimitOrderData, 
  LimitOrderFilters,
  setUserContext 
} from '../config/supabase.js';
import { logger } from '../utils/logger.js';

// Helper function to remove 'did:privy:' prefix from wallet ID and handle URL encoding
function cleanWalletId(walletId: string): string {
  // First decode URL encoding if present
  let decodedId = walletId;
  try {
    decodedId = decodeURIComponent(walletId);
  } catch (e) {
    // If decoding fails, use original
    decodedId = walletId;
  }
  
  // Remove did:privy: prefix
  if (decodedId.startsWith('did:privy:')) {
    return decodedId.replace('did:privy:', '');
  }
  return decodedId;
}

class LimitOrderService {
  
  /**
   * Create a new limit order
   */
  async createLimitOrder(orderData: CreateLimitOrderData): Promise<{ success: boolean; data?: LimitOrder; error?: string }> {
    try {
      // Decode URL encoding if present but keep full user_id for database storage
      const decodedUserId = decodeURIComponent(orderData.user_id);
      const cleanedOrderData = { ...orderData, user_id: decodedUserId };
      
      // Set user context for RLS (keeping for compatibility but adding explicit filtering)
      await setUserContext(decodedUserId);

      // Validate order data
      const validationError = this.validateOrderData(cleanedOrderData);
      if (validationError) {
        return { success: false, error: validationError };
      }

      // Insert the order
      const { data, error } = await supabase
        .from('limit_orders')
        .insert([cleanedOrderData])
        .select()
        .single();

      if (error) {
        logger.error('Error creating limit order:', error);
        return { success: false, error: error.message };
      }

      logger.info(`Limit order created successfully: ${data.id}`);
      return { success: true, data };

    } catch (error) {
      logger.error('Unexpected error creating limit order:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get limit orders for a user with filtering and pagination
   */
  async getLimitOrders(userId: string, filters: LimitOrderFilters = {}): Promise<{ success: boolean; data?: LimitOrder[]; count?: number; error?: string }> {
    try {
      // Clean the userId if it contains did:privy prefix
      const decodedUserId = decodeURIComponent(userId);
      
      // Set user context for RLS (keeping for compatibility but adding explicit filtering)
      await setUserContext(decodedUserId);

      let query = supabase
        .from('limit_orders')
        .select('*', { count: 'exact' })
        // Explicitly filter by user_id to ensure users only see their own orders
        .eq('user_id', decodedUserId);

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.token_address) {
        query = query.eq('token_address', filters.token_address);
      }

      if (filters.direction) {
        query = query.eq('direction', filters.direction);
      }

      if (filters.dex_type) {
        query = query.eq('dex_type', filters.dex_type);
      }

      // Apply ordering
      const orderBy = filters.order_by || 'created_at';
      const orderDirection = filters.order_direction || 'desc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        logger.error('Error fetching limit orders:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data: data || [], count: count || 0 };

    } catch (error) {
      logger.error('Unexpected error fetching limit orders:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get a specific limit order by ID
   */
  async getLimitOrderById(userId: string, orderId: string): Promise<{ success: boolean; data?: LimitOrder; error?: string }> {
    try {
      // Clean the userId if it contains did:privy prefix
      const decodedUserId = decodeURIComponent(userId);
      
      // Set user context for RLS (keeping for compatibility but adding explicit filtering)
      await setUserContext(decodedUserId);

      const { data, error } = await supabase
        .from('limit_orders')
        .select('*')
        .eq('id', orderId)
        .eq('user_id', decodedUserId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, error: 'Order not found' };
        }
        logger.error('Error fetching limit order:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data };

    } catch (error) {
      logger.error('Unexpected error fetching limit order:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Update a limit order
   */
  async updateLimitOrder(userId: string, orderId: string, updateData: UpdateLimitOrderData): Promise<{ success: boolean; data?: LimitOrder; error?: string }> {
    try {
      // Clean the userId if it contains did:privy prefix
      const decodedUserId = decodeURIComponent(userId);
      
      // Set user context for RLS (keeping for compatibility but adding explicit filtering)
      await setUserContext(decodedUserId);

      const { data, error } = await supabase
        .from('limit_orders')
        .update(updateData)
        .eq('id', orderId)
        .eq('user_id', decodedUserId)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, error: 'Order not found' };
        }
        logger.error('Error updating limit order:', error);
        return { success: false, error: error.message };
      }

      logger.info(`Limit order updated successfully: ${orderId}`);
      return { success: true, data };

    } catch (error) {
      logger.error('Unexpected error updating limit order:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Cancel a limit order
   */
  async cancelLimitOrder(userId: string, orderId: string): Promise<{ success: boolean; data?: LimitOrder; error?: string }> {
    // Clean the userId if it contains did:privy prefix - this will be handled by updateLimitOrder
    return this.updateLimitOrder(userId, orderId, { status: 'cancelled' });
  }

  /**
   * Delete a limit order
   */
  async deleteLimitOrder(userId: string, orderId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Clean the userId if it contains did:privy prefix
      const decodedUserId = decodeURIComponent(userId);
      
      // Set user context for RLS (keeping for compatibility but adding explicit filtering)
      await setUserContext(decodedUserId);

      const { error } = await supabase
        .from('limit_orders')
        .delete()
        .eq('id', orderId)
        .eq('user_id', decodedUserId)

      if (error) {
        logger.error('Error deleting limit order:', error);
        return { success: false, error: error.message };
      }

      logger.info(`Limit order deleted successfully: ${orderId}`);
      return { success: true };

    } catch (error) {
      logger.error('Unexpected error deleting limit order:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Expire old orders
   */
  async expireOldOrders(): Promise<{ success: boolean; expiredCount?: number; error?: string }> {
    try {
      const { error } = await supabase.rpc('expire_old_orders');

      if (error) {
        logger.error('Error expiring old orders:', error);
        return { success: false, error: error.message };
      }

      // Get count of expired orders
      const { count } = await supabase
        .from('limit_orders')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'expired')
        .gte('updated_at', new Date(Date.now() - 60000).toISOString()); // Last minute

      logger.info(`Expired ${count || 0} old orders`);
      return { success: true, expiredCount: count || 0 };

    } catch (error) {
      logger.error('Unexpected error expiring old orders:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Mark order as executed (used by monitoring service)
   */
  async markOrderExecuted(
    orderId: string,
    txHash: string,
    executionData?: any
  ): Promise<{ success: boolean; data?: LimitOrder; error?: string }> {
    try {
      const updateData: UpdateLimitOrderData = {
        status: 'executed',
        executed_at: new Date().toISOString(),
        execution_tx_hash: txHash
      };

      // Add execution data to error_message field as JSON (temporary storage)
      if (executionData) {
        updateData.error_message = JSON.stringify(executionData);
      }

      // Update the order without user context (system operation)
      const { data, error } = await supabase
        .from('limit_orders')
        .update(updateData)
        .eq('id', orderId)
        .single();

      if (error) {
        logger.error('Error marking order as executed:', error);
        return { success: false, error: error.message };
      }

      logger.info(`Limit order executed successfully: ${orderId}`);
      return { success: true, data };

    } catch (error) {
      logger.error('Unexpected error marking order as executed:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Mark order as failed (used by monitoring service)
   */
  async markOrderFailed(
    orderId: string,
    errorMessage: string
  ): Promise<{ success: boolean; data?: LimitOrder; error?: string }> {
    try {
      const updateData: UpdateLimitOrderData = {
        error_message: errorMessage
      };

      // Update the order without user context (system operation)
      const { data, error } = await supabase
        .from('limit_orders')
        .update(updateData)
        .eq('id', orderId)
        .single();

      if (error) {
        logger.error('Error marking order as failed:', error);
        return { success: false, error: error.message };
      }

      logger.info(`Limit order marked with error: ${orderId}`);
      return { success: true, data };

    } catch (error) {
      logger.error('Unexpected error marking order as failed:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get all pending orders (used by monitoring service)
   */
  async getPendingOrders(): Promise<{ success: boolean; data?: LimitOrder[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('limit_orders')
        .select('*')
        .eq('status', 'pending')
        .filter('expires_at', 'is', null)
        .or(`expires_at.gte.${new Date().toISOString()}`);

      if (error) {
        logger.error('Error fetching pending orders:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data: data || [] };

    } catch (error) {
      logger.error('Unexpected error fetching pending orders:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get order statistics for a user
   */
  async getLimitOrderStats(userId: string): Promise<{ success: boolean; data?: { pending: number; executed: number; cancelled: number; expired: number; total: number }; error?: string }> {
    try {
      // Clean the userId if it contains did:privy prefix
      const decodedUserId = decodeURIComponent(userId);
      
      // Set user context for RLS (keeping for compatibility but adding explicit filtering)
      await setUserContext(decodedUserId);

      const { data, error } = await supabase.rpc('get_limit_order_stats', { user_id_param: decodedUserId });

      if (error) {
        logger.error('Error fetching limit order stats:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data: data || { pending: 0, executed: 0, cancelled: 0, expired: 0, total: 0 } };

    } catch (error) {
      logger.error('Unexpected error fetching limit order stats:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Validate order data
   */
  private validateOrderData(orderData: CreateLimitOrderData): string | null {
    // Check required fields
    if (!orderData.token_address || !orderData.token_name || !orderData.token_symbol) {
      return 'Missing token information';
    }

    if (!orderData.pool_address) {
      return 'Missing pool address';
    }

    if (!orderData.dex_type) {
      return 'Missing DEX type';
    }

    if (!orderData.direction || !['buy', 'sell'].includes(orderData.direction)) {
      return 'Invalid direction (must be "buy" or "sell")';
    }

    if (typeof orderData.amount !== 'number' || orderData.amount <= 0) {
      return 'Invalid amount (must be a positive number)';
    }

    if (typeof orderData.target_price !== 'number' || orderData.target_price <= 0) {
      return 'Invalid target price (must be a positive number)';
    }

    if (typeof orderData.current_price !== 'number' || orderData.current_price <= 0) {
      return 'Invalid current price (must be a positive number)';
    }

    if (!orderData.wallet_address || !orderData.wallet_id) {
      return 'Missing wallet information';
    }

    // Validate buy/sell conditions - Allow flexible pricing for limit orders
    // Buy orders can have target prices higher than current (buy when price rises)
    // Sell orders can have target prices lower than current (sell when price drops)
    // Only validate for extreme differences that might indicate user error
    if (orderData.direction === 'buy' && orderData.target_price > orderData.current_price) {
      const increase = ((orderData.target_price - orderData.current_price) / orderData.current_price) * 100;
      if (increase > 100) {
        return `Buy target price is ${increase.toFixed(1)}% higher than current price. Please verify this is correct.`;
      }
    }

    if (orderData.direction === 'sell' && orderData.target_price < orderData.current_price) {
      const decrease = ((orderData.current_price - orderData.target_price) / orderData.current_price) * 100;
      if (decrease > 100) {
        return `Sell target price is ${decrease.toFixed(1)}% lower than current price. Please verify this is correct.`;
      }
    }

    return null;
  }
}

// Create singleton instance
export const limitOrderService = new LimitOrderService();