import WebSocket from 'ws';
import { redis } from '../config/redis.js';
import logger from '../utils/logger.js';
import dotenv from 'dotenv';

dotenv.config();

interface MobulaToken {
  blockchain: string;
  address: string;
}

interface MobulaSubscribeMessage {
  type: 'feed';
  authorization: string;
  kind: 'address';
  tokens: MobulaToken[];
}

interface MobulaPriceData {
  chain_id: string;
  timestamp: number;
  price: number;
  marketDepthUSDUp: number;
  marketDepthUSDDown: number;
  volume24h: number;
  baseSymbol: string;
  quoteSymbol: string;
  baseID: string;
  quoteID: string;
  extra: {
    priceUSD: number;
    quoteAssetPrice: number;
    blockNumber: number;
  };
  timestamp_sent: number;
}

export class MobulaWebSocketService {
  private ws: WebSocket | null = null;
  private reconnectInterval: NodeJS.Timeout | null = null;
  private isConnected = false;
  private subscribedTokens: Set<string> = new Set();
  private readonly MOBULA_WS_URL = 'wss://production-feed.mobula.io';
  private readonly MOBULA_API_KEY = process.env.MOBULA_API_KEY!;
  private reconnectAttempts = 0;
  private readonly MAX_RECONNECT_ATTEMPTS = 10;
  private readonly RECONNECT_DELAY = 5000;
  private priceUpdateCallbacks: Map<string, (price: number) => void> = new Map();

  constructor() {
    if (!this.MOBULA_API_KEY) {
      throw new Error('MOBULA_API_KEY is required in environment variables');
    }
  }

  public async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.MOBULA_WS_URL);

        this.ws.on('open', () => {
          logger.info('Mobula WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // Resubscribe to all tokens after reconnection
          if (this.subscribedTokens.size > 0) {
            const tokens = Array.from(this.subscribedTokens);
            this.subscribedTokens.clear();
            this.subscribeToTokens(tokens);
          }
          
          resolve();
        });

        this.ws.on('message', (data: WebSocket.Data) => {
          try {
            const priceData = JSON.parse(data.toString()) as MobulaPriceData;
            this.handlePriceUpdate(priceData);
          } catch (error) {
            logger.error('Error parsing Mobula price data:', error);
          }
        });

        this.ws.on('error', (error) => {
          logger.error('Mobula WebSocket error:', error);
          if (!this.isConnected) {
            reject(error);
          }
        });

        this.ws.on('close', (code, reason) => {
          logger.warn(`Mobula WebSocket closed. Code: ${code}, Reason: ${reason}`);
          this.isConnected = false;
          this.handleReconnect();
        });

        this.ws.on('ping', () => {
          if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.pong();
          }
        });

      } catch (error) {
        logger.error('Failed to create WebSocket connection:', error);
        reject(error);
      }
    });
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.MAX_RECONNECT_ATTEMPTS) {
      logger.error('Max reconnection attempts reached. Stopping reconnection.');
      return;
    }

    if (this.reconnectInterval) {
      clearTimeout(this.reconnectInterval);
    }

    this.reconnectInterval = setTimeout(() => {
      this.reconnectAttempts++;
      logger.info(`Attempting to reconnect to Mobula WebSocket (attempt ${this.reconnectAttempts}/${this.MAX_RECONNECT_ATTEMPTS})...`);
      this.connect().catch((error) => {
        logger.error('Reconnection failed:', error);
      });
    }, this.RECONNECT_DELAY);
  }

  public subscribeToTokens(tokenAddresses: string[]): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      logger.error('WebSocket is not connected. Cannot subscribe to tokens.');
      return;
    }

    const newTokens = tokenAddresses.filter(addr => !this.subscribedTokens.has(addr));
    if (newTokens.length === 0) {
      return;
    }

    const subscribeMessage: MobulaSubscribeMessage = {
      type: 'feed',
      authorization: this.MOBULA_API_KEY,
      kind: 'address',
      tokens: newTokens.map(address => ({
        blockchain: 'solana',
        address
      }))
    };

    try {
      this.ws.send(JSON.stringify(subscribeMessage));
      newTokens.forEach(token => this.subscribedTokens.add(token));
      logger.info(`Subscribed to ${newTokens.length} new token(s) on Mobula`);
    } catch (error) {
      logger.error('Error subscribing to tokens:', error);
    }
  }

  public unsubscribeFromTokens(tokenAddresses: string[]): void {
    // Mobula doesn't have explicit unsubscribe, so we just remove from our tracking
    tokenAddresses.forEach(token => {
      this.subscribedTokens.delete(token);
      this.priceUpdateCallbacks.delete(token);
    });
  }

  private async handlePriceUpdate(priceData: MobulaPriceData): Promise<void> {
    try {
      // Extract token address from baseID (remove "solana:solana|" prefix)
      const tokenAddress = priceData.baseID.replace('solana:solana|', '');
      const price = priceData.price;

      // Store price in Redis for quick access
      await redis.setex(
        `price:${tokenAddress}`,
        60, // TTL: 60 seconds
        JSON.stringify({
          price,
          timestamp: priceData.timestamp,
          volume24h: priceData.volume24h,
          priceUSD: priceData.extra.priceUSD
        })
      );

      // Publish price update to Redis pub/sub for real-time updates
      await redis.publish(
        'price-updates',
        JSON.stringify({
          tokenAddress,
          price,
          timestamp: priceData.timestamp
        })
      );

      // Execute any registered callbacks
      const callback = this.priceUpdateCallbacks.get(tokenAddress);
      if (callback) {
        callback(price);
      }

      logger.debug(`Price update for ${tokenAddress}: $${price}`);
    } catch (error) {
      logger.error('Error handling price update:', error);
    }
  }

  public onPriceUpdate(tokenAddress: string, callback: (price: number) => void): void {
    this.priceUpdateCallbacks.set(tokenAddress, callback);
  }

  public async getCurrentPrice(tokenAddress: string): Promise<number | null> {
    try {
      const cachedPrice = await redis.get(`price:${tokenAddress}`);
      if (cachedPrice) {
        const priceData = JSON.parse(cachedPrice);
        return priceData.price;
      }
      return null;
    } catch (error) {
      logger.error('Error getting current price from cache:', error);
      return null;
    }
  }

  public disconnect(): void {
    if (this.reconnectInterval) {
      clearTimeout(this.reconnectInterval);
      this.reconnectInterval = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.isConnected = false;
    this.subscribedTokens.clear();
    this.priceUpdateCallbacks.clear();
    logger.info('Mobula WebSocket disconnected');
  }

  public isWebSocketConnected(): boolean {
    return this.isConnected && this.ws?.readyState === WebSocket.OPEN;
  }
}

// Export singleton instance
export const mobulaWebSocketService = new MobulaWebSocketService();