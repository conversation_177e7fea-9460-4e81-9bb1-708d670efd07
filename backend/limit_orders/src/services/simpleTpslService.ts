import { supabase } from '../config/supabase.js';
import { logger } from '../utils/logger.js';
import axios from 'axios';
import { getDynamicFeeService } from './dynamicFeeService.js';

// Helper function to remove 'did:privy:' prefix from wallet ID and handle URL encoding
function cleanWalletId(walletId: string): string {
  // First decode URL encoding if present
  let decodedId = walletId;
  try {
    decodedId = decodeURIComponent(walletId);
  } catch (e) {
    // If decoding fails, use original
    decodedId = walletId;
  }
  
  // Remove did:privy: prefix
  if (decodedId.startsWith('did:privy:')) {
    return decodedId.replace('did:privy:', '');
  }
  return decodedId;
}

// Main TPSL order interface
export interface TpSlMainOrder {
  id?: string;
  user_id: string;
  wallet_id: string;
  wallet_address: string;
  current_price: number;
  amount: number; // SOL amount invested
  token_address: string;
  pool_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  exchange_name?: string;
  total_token?: number; // Tokens purchased
  txn?: string; // Initial purchase transaction
  status: 'active' | 'executed' | 'cancelled';
  created_at?: string;
  updated_at?: string;
}

// Take Profit order interface
export interface TpOrder {
  id?: string;
  tpsl_order_id: string;
  target_price: number;
  percentage: number;
  status: 'active' | 'executed' | 'cancelled';
  executed_at?: string;
  execution_txn?: string;
  created_at?: string;
  updated_at?: string;
}

// Stop Loss order interface  
export interface SlOrder {
  id?: string;
  tpsl_order_id: string;
  target_price: number;
  percentage: number;
  status: 'active' | 'executed' | 'cancelled';
  executed_at?: string;
  execution_txn?: string;
  created_at?: string;
  updated_at?: string;
}

// Combined TP/SL creation request
export interface CreateTpSlOrderRequest {
  user_id: string;
  wallet_id: string;
  wallet_address: string;
  current_price: number;
  amount: number;
  token_address: string;
  pool_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  exchange_name?: string;
  
  // Take Profit settings (optional)
  tp_enabled?: boolean;
  tp_target_price?: number;
  tp_percentage?: number;
  
  // Stop Loss settings (optional)
  sl_enabled?: boolean;
  sl_target_price?: number;
  sl_percentage?: number;
}

// Response with main order and sub-orders
export interface TpSlOrderResponse {
  mainOrder: TpSlMainOrder;
  takeProfitOrder?: TpOrder;
  stopLossOrder?: SlOrder;
}

// Solana service configuration
const SOLANA_SERVICE_URL = process.env.SOLANA_SERVICE_URL || 'http://localhost:4001';

interface SolanaSwapRequest {
  tokenAddress: string;
  poolAddress: string;
  dexType: string;
  amount: number;
  direction: 'buy' | 'sell';
  slippage: number;
  walletAddress: string;
  walletId?: string;
  mevProtection?: boolean;
  bribeAmount?: number;
  priorityLevel?: string;
  priorityFee?: number;
}

interface SolanaSwapResponse {
  success: boolean;
  data?: {
    outAmount: number;
    price: number;
    signature: string;
    bundleId?: string;
    solscanUrl?: string;
    executionMethod?: string;
    mevProtected?: boolean;
    tipAmount?: number;
    tipAmountSol?: number;
  };
  error?: string;
}

class SimpleTpSlService {
  private readonly SOLANA_RPC_URL = process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com';
  private dynamicFeeService = getDynamicFeeService(this.SOLANA_RPC_URL);
  
  /**
   * Execute token swap through Solana service
   */
  private async executeSolanaSwap(orderData: CreateTpSlOrderRequest): Promise<SolanaSwapResponse> {
    try {
      logger.info('Executing Solana swap for TP/SL order', {
        tokenAddress: orderData.token_address,
        amount: orderData.amount
      });

      // Determine DEX type based on exchange_name or default to pumpfun (send as lowercase)
      const dexType = orderData.exchange_name?.toLowerCase() || 'pumpfun';
      
      // Always buy tokens when creating the initial order
      const direction = 'buy';

      // Calculate dynamic fees based on trade amount
      const dynamicFees = await this.dynamicFeeService.calculateDynamicFees(orderData.amount, false);

      const swapRequest: SolanaSwapRequest = {
        tokenAddress: orderData.token_address,
        poolAddress: orderData.pool_address,
        dexType: dexType,
        amount: orderData.amount,
        direction: direction,
        slippage: 0.15, // Default 15% slippage
        walletAddress: orderData.wallet_address,
        walletId: cleanWalletId(orderData.wallet_id),  // Clean wallet ID (remove did:privy: prefix)
        mevProtection: orderData.amount > 100, // Enable MEV protection for large trades
        bribeAmount: dynamicFees.bribeAmount,
        priorityLevel: dynamicFees.priorityLevel,
        priorityFee: dynamicFees.priorityFee
      };

      logger.info('Making swap request to Solana service', {
        url: `${SOLANA_SERVICE_URL}/api/pump/swap`,
        request: { ...swapRequest, walletId: 'REDACTED' }
      });

      const response = await axios.post<SolanaSwapResponse>(
        `${SOLANA_SERVICE_URL}/api/pump/swap`,
        swapRequest,
        {
          timeout: 30000, // 30 second timeout
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data;
    } catch (error: any) {
      logger.error('Error executing Solana swap', {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to execute swap'
      };
    }
  }

  /**
   * Create a single TPSL order with both TP and SL sub-orders
   */
  async createOrder(orderData: CreateTpSlOrderRequest): Promise<{ success: boolean; data?: TpSlOrderResponse; error?: string }> {
    try {
      logger.info('Creating TPSL order with TP/SL sub-orders:', orderData);

      // Validate that at least one sub-order is enabled
      if (!orderData.tp_enabled && !orderData.sl_enabled) {
        return { 
          success: false, 
          error: 'At least one of Take Profit or Stop Loss must be enabled' 
        };
      }

      // Decode URL encoding but preserve full user_id for database storage
      const decodedUserId = decodeURIComponent(orderData.user_id);

      // STEP 1: Execute the swap immediately to buy tokens
      logger.info('Executing immediate token purchase via Solana service');
      const swapResult = await this.executeSolanaSwap(orderData);

      if (!swapResult.success) {
        logger.error('Failed to purchase tokens for TPSL order', { error: swapResult.error });
        return { 
          success: false, 
          error: `Failed to purchase tokens: ${swapResult.error}` 
        };
      }

      // STEP 2: Create the main TPSL order
      const cleanedWalletId = cleanWalletId(orderData.wallet_id);
      
      const { data: mainOrder, error: mainOrderError } = await supabase
        .from('tpsl_orders')
        .insert([{
          user_id: decodedUserId,
          wallet_id: cleanedWalletId,
          wallet_address: orderData.wallet_address,
          current_price: orderData.current_price,
          amount: orderData.amount,
          token_address: orderData.token_address,
          pool_address: orderData.pool_address,
          token_name: orderData.token_name,
          token_symbol: orderData.token_symbol,
          token_image: orderData.token_image,
          exchange_name: orderData.exchange_name,
          total_token: swapResult.data?.outAmount,
          txn: swapResult.data?.signature,
          status: 'active'
        }])
        .select()
        .single();

      if (mainOrderError) {
        logger.error('Error storing main TPSL order in Supabase:', mainOrderError);
        return { 
          success: false, 
          error: `Tokens purchased but failed to store order: ${mainOrderError.message}` 
        };
      }

      const response: TpSlOrderResponse = { mainOrder };

      // STEP 3: Create Take Profit sub-order if enabled
      if (orderData.tp_enabled && orderData.tp_target_price && orderData.tp_percentage) {
        const { data: tpOrder, error: tpError } = await supabase
          .from('tp_orders')
          .insert([{
            tpsl_order_id: mainOrder.id,
            target_price: orderData.tp_target_price,
            percentage: orderData.tp_percentage,
            status: 'active'
          }])
          .select()
          .single();

        if (tpError) {
          logger.error('Error creating Take Profit sub-order:', tpError);
          // Don't fail the entire operation, just log the error
        } else {
          response.takeProfitOrder = tpOrder;
        }
      }

      // STEP 4: Create Stop Loss sub-order if enabled
      if (orderData.sl_enabled && orderData.sl_target_price && orderData.sl_percentage) {
        const { data: slOrder, error: slError } = await supabase
          .from('sl_orders')
          .insert([{
            tpsl_order_id: mainOrder.id,
            target_price: orderData.sl_target_price,
            percentage: orderData.sl_percentage,
            status: 'active'
          }])
          .select()
          .single();

        if (slError) {
          logger.error('Error creating Stop Loss sub-order:', slError);
          // Don't fail the entire operation, just log the error
        } else {
          response.stopLossOrder = slOrder;
        }
      }

      // STEP 5: Send notification about successful order creation
      await this.sendCreationNotification(response);

      logger.info(`TPSL order created successfully: ${mainOrder.id}`, {
        total_token: mainOrder.total_token,
        txn: mainOrder.txn,
        hasTP: !!response.takeProfitOrder,
        hasSL: !!response.stopLossOrder
      });

      return { success: true, data: response };

    } catch (error: any) {
      logger.error('Unexpected error creating TPSL order:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get TPSL orders for a user with their sub-orders
   */
  async getUserOrders(userId: string): Promise<{ success: boolean; data?: TpSlOrderResponse[]; error?: string }> {
    try {
      logger.info('Fetching TPSL orders for user:', userId);

      const decodedUserId = decodeURIComponent(userId);

      // Get main orders
      const { data: mainOrders, error: mainError } = await supabase
        .from('tpsl_orders')
        .select('*')
        .eq('user_id', decodedUserId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (mainError) {
        logger.error('Error fetching TPSL orders:', mainError);
        return { success: false, error: mainError.message };
      }

      if (!mainOrders || mainOrders.length === 0) {
        return { success: true, data: [] };
      }

      // Get sub-orders for each main order
      const responses: TpSlOrderResponse[] = [];

      for (const mainOrder of mainOrders) {
        const response: TpSlOrderResponse = { mainOrder };

        // Get TP orders
        const { data: tpOrders } = await supabase
          .from('tp_orders')
          .select('*')
          .eq('tpsl_order_id', mainOrder.id)
          .eq('status', 'active');

        if (tpOrders && tpOrders.length > 0) {
          response.takeProfitOrder = tpOrders[0]; // Should only be one
        }

        // Get SL orders
        const { data: slOrders } = await supabase
          .from('sl_orders')
          .select('*')
          .eq('tpsl_order_id', mainOrder.id)
          .eq('status', 'active');

        if (slOrders && slOrders.length > 0) {
          response.stopLossOrder = slOrders[0]; // Should only be one
        }

        responses.push(response);
      }

      return { success: true, data: responses };

    } catch (error) {
      logger.error('Unexpected error fetching TPSL orders:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Cancel/Remove TPSL order and all its sub-orders
   */
  async cancelOrder(userId: string, orderId: string): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info('Cancelling TPSL order and sub-orders:', { orderId, userId });

      const decodedUserId = decodeURIComponent(userId);

      // Delete the main order (CASCADE will handle sub-orders automatically)
      const { error } = await supabase
        .from('tpsl_orders')
        .delete()
        .eq('id', orderId)
        .eq('user_id', decodedUserId);

      if (error) {
        logger.error('Error cancelling TPSL order:', error);
        return { success: false, error: error.message };
      }

      logger.info(`TPSL order and sub-orders cancelled: ${orderId}`);
      return { success: true };

    } catch (error) {
      logger.error('Unexpected error cancelling TPSL order:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Remove individual Take Profit order
   */
  async removeTakeProfit(userId: string, orderId: string): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info('Removing Take Profit order:', { orderId, userId });

      const decodedUserId = decodeURIComponent(userId);

      // Check if main order exists and belongs to user
      const { data: mainOrder, error: mainOrderError } = await supabase
        .from('tpsl_orders')
        .select('id')
        .eq('id', orderId)
        .eq('user_id', decodedUserId)
        .single();

      if (mainOrderError || !mainOrder) {
        return { success: false, error: 'Order not found or access denied' };
      }

      // Delete TP order
      const { error: tpError } = await supabase
        .from('tp_orders')
        .delete()
        .eq('tpsl_order_id', orderId);

      if (tpError) {
        logger.error('Error removing Take Profit order:', tpError);
        return { success: false, error: tpError.message };
      }

      // Check if SL order still exists
      const { data: slOrders } = await supabase
        .from('sl_orders')
        .select('id')
        .eq('tpsl_order_id', orderId)
        .eq('status', 'active');

      // If no SL orders remain, delete the main order
      if (!slOrders || slOrders.length === 0) {
        await supabase
          .from('tpsl_orders')
          .delete()
          .eq('id', orderId);
        
        logger.info(`No remaining sub-orders, main order deleted: ${orderId}`);
      }

      logger.info(`Take Profit order removed: ${orderId}`);
      return { success: true };

    } catch (error) {
      logger.error('Unexpected error removing Take Profit order:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Remove individual Stop Loss order
   */
  async removeStopLoss(userId: string, orderId: string): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info('Removing Stop Loss order:', { orderId, userId });

      const decodedUserId = decodeURIComponent(userId);

      // Check if main order exists and belongs to user
      const { data: mainOrder, error: mainOrderError } = await supabase
        .from('tpsl_orders')
        .select('id')
        .eq('id', orderId)
        .eq('user_id', decodedUserId)
        .single();

      if (mainOrderError || !mainOrder) {
        return { success: false, error: 'Order not found or access denied' };
      }

      // Delete SL order
      const { error: slError } = await supabase
        .from('sl_orders')
        .delete()
        .eq('tpsl_order_id', orderId);

      if (slError) {
        logger.error('Error removing Stop Loss order:', slError);
        return { success: false, error: slError.message };
      }

      // Check if TP order still exists
      const { data: tpOrders } = await supabase
        .from('tp_orders')
        .select('id')
        .eq('tpsl_order_id', orderId)
        .eq('status', 'active');

      // If no TP orders remain, delete the main order
      if (!tpOrders || tpOrders.length === 0) {
        await supabase
          .from('tpsl_orders')
          .delete()
          .eq('id', orderId);
        
        logger.info(`No remaining sub-orders, main order deleted: ${orderId}`);
      }

      logger.info(`Stop Loss order removed: ${orderId}`);
      return { success: true };

    } catch (error) {
      logger.error('Unexpected error removing Stop Loss order:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Send notification about successful TPSL order creation
   */
  private async sendCreationNotification(orderResponse: TpSlOrderResponse): Promise<void> {
    try {
      const { mainOrder, takeProfitOrder, stopLossOrder } = orderResponse;
      
      const orderTypes = [];
      if (takeProfitOrder) orderTypes.push('Take Profit');
      if (stopLossOrder) orderTypes.push('Stop Loss');

      // Store notification in database
      await supabase
        .from('notification')
        .insert({
          user_id: mainOrder.user_id,
          activity_type: 'tp_sl',
          token_address: mainOrder.token_address,
          token_symbol: mainOrder.token_symbol,
          amount: mainOrder.total_token || mainOrder.amount,
          price: mainOrder.current_price,
          tx_hash: mainOrder.txn,
          exchange_name: mainOrder.exchange_name || 'PumpFun',
          status: 'completed',
          message: `${orderTypes.join(' & ')} order created and tokens purchased for ${mainOrder.token_symbol}`,
          metadata: {
            order_id: mainOrder.id,
            current_price: mainOrder.current_price,
            tokens_purchased: mainOrder.total_token,
            tp_target_price: takeProfitOrder?.target_price,
            tp_percentage: takeProfitOrder?.percentage,
            sl_target_price: stopLossOrder?.target_price,
            sl_percentage: stopLossOrder?.percentage
          }
        });

      logger.info('Notification sent for TPSL order creation', { orderId: mainOrder.id });
    } catch (error: any) {
      logger.error('Failed to send creation notification', { 
        orderId: orderResponse.mainOrder.id, 
        error: error.message 
      });
    }
  }

  /**
   * Cleanup TPSL orders without txn data
   */
  async cleanupOrdersWithoutTxn(): Promise<{ success: boolean; deletedCount?: number; error?: string }> {
    try {
      logger.info('Starting cleanup of TPSL orders without txn data...');

      const { data: ordersWithoutTxn, error: fetchError } = await supabase
        .from('tpsl_orders')
        .select('id, user_id, token_symbol, status, created_at')
        .is('txn', null);

      if (fetchError) {
        logger.error('Error fetching TPSL orders without txn:', fetchError);
        return { success: false, error: fetchError.message };
      }

      if (!ordersWithoutTxn || ordersWithoutTxn.length === 0) {
        logger.info('No TPSL orders found without txn data');
        return { success: true, deletedCount: 0 };
      }

      logger.info(`Found ${ordersWithoutTxn.length} TPSL orders without txn data`);

      // Delete orders without txn data (CASCADE will handle sub-orders)
      const { error: deleteError } = await supabase
        .from('tpsl_orders')
        .delete()
        .is('txn', null);

      if (deleteError) {
        logger.error('Error deleting TPSL orders without txn:', deleteError);
        return { success: false, error: deleteError.message };
      }

      logger.info(`Successfully deleted ${ordersWithoutTxn.length} TPSL orders without txn data`);

      return { success: true, deletedCount: ordersWithoutTxn.length };

    } catch (error: any) {
      logger.error('Unexpected error during cleanup:', error);
      return { success: false, error: error.message };
    }
  }

  // Legacy compatibility methods for existing routes

  /**
   * Legacy method - creates order using old interface but maps to new schema
   */
  async createOrderLegacy(orderData: any): Promise<{ success: boolean; data?: any; error?: string }> {
    // Convert old interface to new interface
    const newOrderData: CreateTpSlOrderRequest = {
      user_id: orderData.user_id,
      wallet_id: orderData.wallet_id,
      wallet_address: orderData.wallet_address,
      current_price: orderData.current_price,
      amount: orderData.amount,
      token_address: orderData.token_address,
      pool_address: orderData.pool_address,
      token_name: orderData.token_name,
      token_symbol: orderData.token_symbol,
      token_image: orderData.token_image,
      exchange_name: orderData.exchange_name,
      
      // Map action to TP or SL
      tp_enabled: orderData.action === 'take_profit',
      tp_target_price: orderData.action === 'take_profit' ? orderData.target_price : undefined,
      tp_percentage: orderData.action === 'take_profit' ? orderData.percentage : undefined,
      
      sl_enabled: orderData.action === 'stop_loss',
      sl_target_price: orderData.action === 'stop_loss' ? orderData.target_price : undefined,
      sl_percentage: orderData.action === 'stop_loss' ? orderData.percentage : undefined
    };

    const result = await this.createOrder(newOrderData);
    
    if (!result.success) {
      return result;
    }

    // Return in old format for compatibility
    const legacyResponse = {
      ...result.data!.mainOrder,
      action: orderData.action,
      target_price: orderData.target_price,
      percentage: orderData.percentage
    };

    return { success: true, data: legacyResponse };
  }

  /**
   * Legacy method - get orders in old format
   */
  async getOrdersLegacy(userId: string, filters?: any): Promise<{ success: boolean; data?: any[]; error?: string }> {
    const result = await this.getUserOrders(userId);
    
    if (!result.success) {
      return result;
    }

    // Convert to legacy format (flatten TP/SL into separate entries)
    const legacyOrders: any[] = [];
    
    for (const orderResponse of result.data!) {
      const { mainOrder, takeProfitOrder, stopLossOrder } = orderResponse;
      
      if (takeProfitOrder) {
        legacyOrders.push({
          ...mainOrder,
          action: 'take_profit',
          target_price: takeProfitOrder.target_price,
          percentage: takeProfitOrder.percentage
        });
      }
      
      if (stopLossOrder) {
        legacyOrders.push({
          ...mainOrder,
          action: 'stop_loss',
          target_price: stopLossOrder.target_price,
          percentage: stopLossOrder.percentage
        });
      }
    }

    return { success: true, data: legacyOrders };
  }

  /**
   * Legacy compatibility method - alias for getOrdersLegacy
   */
  async getOrders(userId: string, filters?: any): Promise<{ success: boolean; data?: any[]; error?: string }> {
    return this.getOrdersLegacy(userId, filters);
  }

  /**
   * Get all active orders for monitoring (legacy compatibility)
   */
  async getActiveOrders(): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      logger.info('Fetching all active TPSL orders for monitoring');

      const { data: mainOrders, error } = await supabase
        .from('tpsl_orders')
        .select('*')
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error fetching active TPSL orders:', error);
        return { success: false, error: error.message };
      }

      if (!mainOrders || mainOrders.length === 0) {
        return { success: true, data: [] };
      }

      // Convert to legacy format for monitoring service compatibility
      const legacyOrders: any[] = [];

      for (const mainOrder of mainOrders) {
        // Get TP orders
        const { data: tpOrders } = await supabase
          .from('tp_orders')
          .select('*')
          .eq('tpsl_order_id', mainOrder.id)
          .eq('status', 'active');

        if (tpOrders && tpOrders.length > 0) {
          legacyOrders.push({
            ...mainOrder,
            action: 'take_profit',
            target_price: tpOrders[0].target_price,
            percentage: tpOrders[0].percentage
          });
        }

        // Get SL orders
        const { data: slOrders } = await supabase
          .from('sl_orders')
          .select('*')
          .eq('tpsl_order_id', mainOrder.id)
          .eq('status', 'active');

        if (slOrders && slOrders.length > 0) {
          legacyOrders.push({
            ...mainOrder,
            action: 'stop_loss',
            target_price: slOrders[0].target_price,
            percentage: slOrders[0].percentage
          });
        }
      }

      return { success: true, data: legacyOrders };

    } catch (error) {
      logger.error('Unexpected error fetching active TPSL orders:', error);
      return { success: false, error: 'Internal server error' };
    }
  }
}

export const simpleTpslService = new SimpleTpSlService();
export default simpleTpslService;