import { Connection } from '@solana/web3.js';
import { logger } from '../utils/logger.js';

interface ExchangeConfig {
  basePriorityFee: number;
  baseBribeAmount: number;
  speedMultiplier: number; // Additional multiplier for fast execution
  name: string;
}

interface FeeConfiguration {
  basePriorityFee: number; // Base fee in SOL (deprecated - use exchange configs)
  baseBribeAmount: number; // Base bribe in SOL (deprecated - use exchange configs)
  amountTiers: AmountTier[];
  networkCongestionMultipliers: CongestionMultiplier[];
  exchangeConfigs: { [key: string]: ExchangeConfig };
}

interface AmountTier {
  minAmount: number; // in SOL
  maxAmount: number; // in SOL
  priorityFeeMultiplier: number;
  bribeMultiplier: number;
}

interface CongestionMultiplier {
  minTps: number; // transactions per second
  maxTps: number;
  multiplier: number;
}

interface DynamicFees {
  priorityFee: number; // in SOL
  bribeAmount: number; // in SOL
  priorityLevel: 'low' | 'medium' | 'high' | 'veryHigh';
}

export class DynamicFeeService {
  private connection: Connection;
  private feeConfig: FeeConfiguration;
  private lastNetworkCheck: number = 0;
  private cachedTps: number = 0;
  private cacheTimeout: number = 30000; // 30 seconds

  constructor(rpcUrl: string) {
    this.connection = new Connection(rpcUrl, 'confirmed');
    
    // Default configuration - optimized for fast trading
    this.feeConfig = {
      basePriorityFee: 0.0001, // Fallback - use exchange configs instead
      baseBribeAmount: 0.00005, // Fallback - use exchange configs instead
      amountTiers: [
        { minAmount: 0, maxAmount: 10, priorityFeeMultiplier: 1.2, bribeMultiplier: 1.2 }, // Fast for small
        { minAmount: 10, maxAmount: 100, priorityFeeMultiplier: 1.5, bribeMultiplier: 1.5 }, // Faster for medium
        { minAmount: 100, maxAmount: 500, priorityFeeMultiplier: 2.0, bribeMultiplier: 2.0 }, // Very fast for large
        { minAmount: 500, maxAmount: 1000, priorityFeeMultiplier: 2.5, bribeMultiplier: 2.5 }, // Maximum speed for whale
        { minAmount: 1000, maxAmount: Infinity, priorityFeeMultiplier: 3.0, bribeMultiplier: 3.0 } // Ultimate speed for mega whale
      ],
      networkCongestionMultipliers: [
        { minTps: 0, maxTps: 1000, multiplier: 1.2 }, // Fast even in low congestion
        { minTps: 1000, maxTps: 2000, multiplier: 2.0 }, // Aggressive in medium congestion
        { minTps: 2000, maxTps: 3000, multiplier: 3.0 }, // Very aggressive in high congestion
        { minTps: 3000, maxTps: Infinity, multiplier: 4.0 } // Maximum speed in chaos
      ],
      exchangeConfigs: {
        pumpfun: {
          basePriorityFee: 0.00005, // Lower for bonding curve (less competition)
          baseBribeAmount: 0.00002, // Minimal bribe needed
          speedMultiplier: 1.2, // 20% speed boost
          name: 'PumpFun Bonding Curve'
        },
        pumpswap: {
          basePriorityFee: 0.0002, // Higher for graduated tokens (more competition)
          baseBribeAmount: 0.0001, // Higher bribe for MEV protection
          speedMultiplier: 1.5, // 50% speed boost for DEX competition
          name: 'PumpSwap Graduated'
        },
        launchlab: {
          basePriorityFee: 0.0001, // Medium priority for launchlab
          baseBribeAmount: 0.00005, // Standard bribe
          speedMultiplier: 1.3, // 30% speed boost
          name: 'LaunchLab'
        },
        default: {
          basePriorityFee: 0.0001, // Fallback for unknown exchanges
          baseBribeAmount: 0.00005,
          speedMultiplier: 1.0,
          name: 'Default Exchange'
        }
      }
    };
  }

  /**
   * Calculate dynamic fees based on trade amount, network conditions, and exchange type
   */
  async calculateDynamicFees(
    tradeAmountSol: number, 
    isUrgent: boolean = false,
    exchangeType: string = 'default'
  ): Promise<DynamicFees> {
    try {
      // Get exchange-specific configuration
      const exchangeConfig = this.feeConfig.exchangeConfigs[exchangeType.toLowerCase()] || 
                           this.feeConfig.exchangeConfigs.default;
      
      // Get amount-based multipliers
      const amountMultipliers = this.getAmountMultipliers(tradeAmountSol);
      
      // Get network congestion multiplier
      const networkMultiplier = await this.getNetworkCongestionMultiplier();
      
      // Calculate base fees with exchange-specific base and amount multipliers
      let priorityFee = exchangeConfig.basePriorityFee * amountMultipliers.priorityFeeMultiplier;
      let bribeAmount = exchangeConfig.baseBribeAmount * amountMultipliers.bribeMultiplier;
      
      // Apply network congestion multiplier
      priorityFee *= networkMultiplier;
      bribeAmount *= networkMultiplier;
      
      // Apply exchange-specific speed multiplier for fast execution
      priorityFee *= exchangeConfig.speedMultiplier;
      bribeAmount *= exchangeConfig.speedMultiplier;
      
      // Apply urgency multiplier if needed (additional boost)
      if (isUrgent) {
        priorityFee *= 1.5;
        bribeAmount *= 1.5;
      }
      
      // Cap maximum fees
      priorityFee = Math.min(priorityFee, 0.01); // Max 0.01 SOL priority fee
      bribeAmount = Math.min(bribeAmount, 0.005); // Max 0.005 SOL bribe
      
      // Ensure minimum fees
      priorityFee = Math.max(priorityFee, 0.0001); // Min 0.0001 SOL
      bribeAmount = Math.max(bribeAmount, 0.00005); // Min 0.00005 SOL
      
      // Determine priority level
      const priorityLevel = this.calculatePriorityLevel(priorityFee, networkMultiplier);
      
      logger.info('Calculated dynamic fees', {
        tradeAmountSol,
        exchangeType,
        exchangeConfig: exchangeConfig.name,
        priorityFee,
        bribeAmount,
        priorityLevel,
        networkMultiplier,
        amountMultipliers,
        speedMultiplier: exchangeConfig.speedMultiplier,
        isUrgent
      });
      
      return {
        priorityFee,
        bribeAmount,
        priorityLevel
      };
    } catch (error) {
      logger.error('Error calculating dynamic fees, using defaults', error);
      
      // Fallback to safe defaults
      return {
        priorityFee: 0.0002,
        bribeAmount: 0.0001,
        priorityLevel: 'medium'
      };
    }
  }

  /**
   * Get multipliers based on trade amount
   */
  private getAmountMultipliers(tradeAmountSol: number): { priorityFeeMultiplier: number; bribeMultiplier: number } {
    const tier = this.feeConfig.amountTiers.find(
      t => tradeAmountSol >= t.minAmount && tradeAmountSol < t.maxAmount
    );
    
    if (!tier) {
      // Default to highest tier if not found
      const highestTier = this.feeConfig.amountTiers[this.feeConfig.amountTiers.length - 1];
      return {
        priorityFeeMultiplier: highestTier.priorityFeeMultiplier,
        bribeMultiplier: highestTier.bribeMultiplier
      };
    }
    
    return {
      priorityFeeMultiplier: tier.priorityFeeMultiplier,
      bribeMultiplier: tier.bribeMultiplier
    };
  }

  /**
   * Get network congestion multiplier based on current TPS
   */
  private async getNetworkCongestionMultiplier(): Promise<number> {
    try {
      const currentTime = Date.now();
      
      // Use cached value if still valid
      if (currentTime - this.lastNetworkCheck < this.cacheTimeout && this.cachedTps > 0) {
        return this.getCongestionMultiplierFromTps(this.cachedTps);
      }
      
      // Get recent performance samples
      const perfSamples = await this.connection.getRecentPerformanceSamples(5);
      
      if (!perfSamples || perfSamples.length === 0) {
        return 1; // Default multiplier
      }
      
      // Calculate average TPS
      const avgTps = perfSamples.reduce((sum: number, sample: any) => {
        return sum + (sample.numTransactions / sample.samplePeriodSecs);
      }, 0) / perfSamples.length;
      
      // Update cache
      this.cachedTps = avgTps;
      this.lastNetworkCheck = currentTime;
      
      return this.getCongestionMultiplierFromTps(avgTps);
    } catch (error) {
      logger.error('Error getting network congestion', error);
      return 1; // Default multiplier on error
    }
  }

  /**
   * Get congestion multiplier from TPS value
   */
  private getCongestionMultiplierFromTps(tps: number): number {
    const congestionLevel = this.feeConfig.networkCongestionMultipliers.find(
      level => tps >= level.minTps && tps < level.maxTps
    );
    
    return congestionLevel ? congestionLevel.multiplier : 1;
  }

  /**
   * Calculate priority level based on fee and network conditions
   */
  private calculatePriorityLevel(
    priorityFee: number, 
    networkMultiplier: number
  ): 'low' | 'medium' | 'high' | 'veryHigh' {
    if (networkMultiplier >= 2.5 || priorityFee >= 0.005) {
      return 'veryHigh';
    } else if (networkMultiplier >= 2 || priorityFee >= 0.003) {
      return 'high';
    } else if (networkMultiplier >= 1.5 || priorityFee >= 0.0015) {
      return 'medium';
    }
    return 'low';
  }

  /**
   * Update fee configuration (can be called to adjust parameters)
   */
  updateFeeConfiguration(config: Partial<FeeConfiguration>) {
    this.feeConfig = {
      ...this.feeConfig,
      ...config
    };
    logger.info('Updated fee configuration', this.feeConfig);
  }

  /**
   * Get current network statistics (for monitoring)
   */
  async getNetworkStats() {
    try {
      const perfSamples = await this.connection.getRecentPerformanceSamples(10);
      const avgTps = perfSamples.reduce((sum: number, sample: any) => {
        return sum + (sample.numTransactions / sample.samplePeriodSecs);
      }, 0) / perfSamples.length;
      
      const multiplier = this.getCongestionMultiplierFromTps(avgTps);
      
      return {
        averageTps: avgTps,
        congestionMultiplier: multiplier,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error getting network stats', error);
      return null;
    }
  }
}

// Create singleton instance
let dynamicFeeService: DynamicFeeService | null = null;

export function getDynamicFeeService(rpcUrl: string): DynamicFeeService {
  if (!dynamicFeeService) {
    dynamicFeeService = new DynamicFeeService(rpcUrl);
  }
  return dynamicFeeService;
}