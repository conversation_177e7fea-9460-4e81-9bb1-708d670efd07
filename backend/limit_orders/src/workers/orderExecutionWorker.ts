import { Worker, Job } from 'bullmq';
import axios from 'axios';
import { supabase } from '../config/supabase.js';
import { redis } from '../config/redis.js';
import logger from '../utils/logger.js';
import { getDynamicFeeService } from '../services/dynamicFeeService.js';
import dotenv from 'dotenv';

dotenv.config();

interface OrderExecutionData {
  orderId: string;
  orderType: 'limit' | 'take_profit' | 'stop_loss';
  tokenAddress: string;
  poolAddress?: string;
  direction: 'buy' | 'sell';
  amount: number;
  triggerPrice: number;
  currentPrice: number;
  walletAddress: string;
  walletId?: string;
  slippage: number;
  userId: string;
  exchange_name?: string;
}

interface SolanaSwapResponse {
  success: boolean;
  data?: {
    outAmount: number;
    price: number;
    signature?: string;
    bundleId?: string;
    solscanUrl?: string;
    jitoUrl?: string;
    executionMethod: 'privy' | 'jito' | 'regular' | 'client-side';
    mevProtected: boolean;
    tipAmount: number;
    tipAmountSol: number;
    total_token?: number;
    txn?: string;
  };
  error?: string;
}

export class OrderExecutionWorker {
  private worker: Worker;
  private isRunning: boolean = false;
  private readonly SOLANA_SERVICE_URL = process.env.SOLANA_SERVICE_URL || 'http://localhost:6001';
  private readonly SOLANA_RPC_URL = process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com';
  private dynamicFeeService = getDynamicFeeService(this.SOLANA_RPC_URL);

  constructor() {
    this.worker = new Worker('order-execution', this.processOrder.bind(this), {
      connection: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0')
      },
      concurrency: 5 // Process up to 5 orders simultaneously
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.worker.on('completed', (job: Job) => {
      logger.info(`Order execution completed: ${job.id}`);
    });

    this.worker.on('failed', (job: Job | undefined, err: Error) => {
      logger.error(`Order execution failed: ${job?.id}`, err);
    });

    this.worker.on('error', (err: Error) => {
      logger.error('Worker error:', err);
    });
  }

  private async processOrder(job: Job<OrderExecutionData>): Promise<void> {
    const orderData = job.data;
    logger.info(`Processing order execution: ${orderData.orderId}`, {
      orderType: orderData.orderType,
      tokenAddress: orderData.tokenAddress,
      direction: orderData.direction,
      amount: orderData.amount,
      currentPrice: orderData.currentPrice
    });

    try {
      // For sell orders (limit sell, TP, SL), we need to execute a swap
      if (orderData.direction === 'sell') {
        await this.executeSellOrder(orderData);
      } else {
        // For buy orders, implement buy logic
        await this.executeBuyOrder(orderData);
      }
    } catch (error) {
      const errorMessage = (error as any).response?.data?.error || (error as Error).message || 'Unknown error';
      logger.error(`Error processing order ${orderData.orderId}: ${errorMessage}`);
      throw error; // This will trigger retry logic
    }
  }

  private async executeSellOrder(orderData: OrderExecutionData): Promise<void> {
    try {
      // Get the pool address if not provided
      let poolAddress = orderData.poolAddress;
      if (!poolAddress) {
        // You might need to fetch this from your database or a DEX API
        logger.warn(`No pool address provided for order ${orderData.orderId}`);
        // For now, we'll proceed without it - the Solana service might handle this
      }

      // Calculate dynamic fees based on trade amount
      const isUrgent = orderData.orderType === 'stop_loss'; // Stop loss orders are more urgent
      const dynamicFees = await this.dynamicFeeService.calculateDynamicFees(orderData.amount, isUrgent);

      // Prepare swap request for Solana service
      const swapRequest = {
        tokenAddress: orderData.tokenAddress,
        poolAddress: poolAddress || '', // Solana service should handle finding the pool
        dexType: orderData.exchange_name?.toLowerCase() || 'pumpfun', // Use exchange_name from order data, default to pumpfun
        amount: orderData.amount.toString(),
        direction: 'sell',
        slippage: (orderData.slippage * 100).toString(), // Convert to percentage
        walletAddress: orderData.walletAddress,
        walletId: orderData.walletId,
        priorityFee: dynamicFees.priorityFee,
        bribeAmount: dynamicFees.bribeAmount,
        mevProtection: orderData.amount > 100, // Enable MEV protection for large trades
        priorityLevel: dynamicFees.priorityLevel
      };

      logger.info(`Sending sell order to Solana service:`, swapRequest);

      // Call Solana service to execute the swap
      const response = await axios.post<SolanaSwapResponse>(
        `${this.SOLANA_SERVICE_URL}/api/pump/swap`,
        swapRequest,
        {
          timeout: 60000, // 60 second timeout
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success && response.data.data) {
        const swapData = response.data.data;
        
        // Map Solana service response to expected format
        // Solana service returns: outAmount (SOL/tokens received) and price (price per token)
        const executedPrice = swapData.price || 0;
        const profitVal = orderData.direction === 'sell' 
          ? (swapData.outAmount || 0)  // SOL received for sell orders
          : 0;  // No immediate profit for buy orders

        // Update order status and add transaction details
        let tableName: string;
        let updateData: any = {
          status: 'executed',
          executed_at: new Date().toISOString(),
          execution_txn: swapData.signature || swapData.txn || null
        };
        
        // For TP/SL orders, update the specific sub-order table
        if (orderData.orderType === 'limit') {
          tableName = 'limit_orders';
          updateData.executed_price = executedPrice;
          updateData.profit_val = profitVal;
          updateData.sell_txn = swapData.signature || swapData.txn || null;
        } else if (orderData.orderType === 'take_profit') {
          tableName = 'tp_orders';
        } else if (orderData.orderType === 'stop_loss') {
          tableName = 'sl_orders';
        } else {
          throw new Error(`Unknown order type: ${orderData.orderType}`);
        }

        const { error: updateError } = await supabase
          .from(tableName)
          .update(updateData)
          .eq('id', orderData.orderId);

        if (updateError) {
          logger.error('Error updating order after execution:', updateError);
        }

        // Store notification data
        await this.storeNotification(orderData, swapData, profitVal);

        logger.info(`Order ${orderData.orderId} executed successfully. TXN: ${swapData.signature}`);
      } else {
        throw new Error(response.data.error || 'Swap execution failed');
      }
    } catch (error) {
      const errorMessage = (error as any).response?.data?.error || (error as Error).message || 'Unknown error';
      logger.error(`Failed to execute sell order ${orderData.orderId}: ${errorMessage}`);
      
      // Update order status to failed
      let tableName: string;
      if (orderData.orderType === 'limit') {
        tableName = 'limit_orders';
      } else if (orderData.orderType === 'take_profit') {
        tableName = 'tp_orders';
      } else if (orderData.orderType === 'stop_loss') {
        tableName = 'sl_orders';
      } else {
        return;
      }
      
      await supabase
        .from(tableName)
        .update({
          status: 'failed',
          error_message: errorMessage
        })
        .eq('id', orderData.orderId);
      
      throw error;
    }
  }

  private async executeBuyOrder(orderData: OrderExecutionData): Promise<void> {
    try {
      // Calculate dynamic fees based on trade amount
      const isUrgent = orderData.orderType === 'stop_loss'; // Stop loss orders are more urgent
      const dynamicFees = await this.dynamicFeeService.calculateDynamicFees(orderData.amount, isUrgent);

      // Prepare buy request for Solana service
      const swapRequest = {
        tokenAddress: orderData.tokenAddress,
        poolAddress: orderData.poolAddress || '',
        dexType: orderData.exchange_name?.toLowerCase() || 'pumpfun',
        amount: orderData.amount.toString(),
        direction: 'buy',
        slippage: (orderData.slippage * 100).toString(),
        walletAddress: orderData.walletAddress,
        walletId: orderData.walletId,
        priorityFee: dynamicFees.priorityFee,
        bribeAmount: dynamicFees.bribeAmount,
        mevProtection: orderData.amount > 100, // Enable MEV protection for large trades
        priorityLevel: dynamicFees.priorityLevel
      };

      logger.info(`Sending buy order to Solana service:`, swapRequest);

      const response = await axios.post<SolanaSwapResponse>(
        `${this.SOLANA_SERVICE_URL}/api/pump/swap`,
        swapRequest,
        {
          timeout: 60000,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success && response.data.data) {
        const swapData = response.data.data;

        // Update order status using correct table name
        let tableName: string;
        let updateData: any = {
          status: 'executed',
          executed_at: new Date().toISOString(),
          execution_txn: swapData.signature || swapData.txn
        };
        
        if (orderData.orderType === 'limit') {
          tableName = 'limit_orders';
          updateData.executed_price = swapData.price;
          updateData.sell_txn = swapData.signature || swapData.txn; // Store buy txn in sell_txn field for now
        } else if (orderData.orderType === 'take_profit') {
          tableName = 'tp_orders';
        } else if (orderData.orderType === 'stop_loss') {
          tableName = 'sl_orders';
        } else {
          throw new Error(`Unknown order type: ${orderData.orderType}`);
        }
        
        const { error: updateError } = await supabase
          .from(tableName)
          .update(updateData)
          .eq('id', orderData.orderId);

        if (updateError) {
          logger.error('Error updating buy order after execution:', updateError);
        }

        // Store notification
        await this.storeNotification(orderData, swapData, 0);

        logger.info(`Buy order ${orderData.orderId} executed successfully. TXN: ${swapData.signature}`);
      } else {
        throw new Error(response.data.error || 'Buy execution failed');
      }
    } catch (error) {
      const errorMessage = (error as any).response?.data?.error || (error as Error).message || 'Unknown error';
      logger.error(`Failed to execute buy order ${orderData.orderId}: ${errorMessage}`);
      
      let tableName: string;
      if (orderData.orderType === 'limit') {
        tableName = 'limit_orders';
      } else if (orderData.orderType === 'take_profit') {
        tableName = 'tp_orders';
      } else if (orderData.orderType === 'stop_loss') {
        tableName = 'sl_orders';
      } else {
        return;
      }
      
      await supabase
        .from(tableName)
        .update({
          status: 'failed',
          error_message: errorMessage
        })
        .eq('id', orderData.orderId);
      
      throw error;
    }
  }

  private async storeNotification(
    orderData: OrderExecutionData,
    swapData: any,
    profitVal: number
  ): Promise<void> {
    try {
      const notification = {
        user_id: orderData.userId,
        type: 'order_executed',
        title: `${orderData.orderType.replace('_', ' ').toUpperCase()} Executed`,
        message: `Your ${orderData.direction} order for ${orderData.tokenAddress.slice(0, 8)}... was executed at $${swapData.price}`,
        data: {
          orderId: orderData.orderId,
          orderType: orderData.orderType,
          tokenAddress: orderData.tokenAddress,
          direction: orderData.direction,
          amount: orderData.amount,
          triggerPrice: orderData.triggerPrice,
          executedPrice: swapData.price,
          profit: profitVal,
          transactionSignature: swapData.signature || swapData.txn
        },
        read: false,
        created_at: new Date().toISOString()
      };

      // Store in notifications table
      const { error } = await supabase
        .from('notifications')
        .insert(notification);

      if (error) {
        logger.error('Error storing notification:', error);
      }

      // Also store in Redis for real-time updates
      await redis.lpush(
        `notifications:${orderData.userId}`,
        JSON.stringify(notification)
      );
      
      // Keep only last 100 notifications per user
      await redis.ltrim(`notifications:${orderData.userId}`, 0, 99);
      
      // Publish notification event
      await redis.publish(
        `user-notifications:${orderData.userId}`,
        JSON.stringify(notification)
      );
    } catch (error) {
      logger.error('Error storing notification:', error);
    }
  }

  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.info('Order execution worker is already running');
      return;
    }
    
    try {
      await this.worker.run();
      this.isRunning = true;
      logger.info('Order execution worker started');
    } catch (error) {
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('Worker is already running')) {
        logger.warn('Worker already running, skipping start');
        this.isRunning = true; // Mark as running since it's already active
        return;
      }
      logger.error('Failed to start order execution worker:', error);
      throw error;
    }
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) {
      logger.info('Order execution worker is not running');
      return;
    }
    
    try {
      await this.worker.close();
      this.isRunning = false;
      logger.info('Order execution worker stopped');
    } catch (error) {
      logger.error('Error stopping order execution worker:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const orderExecutionWorker = new OrderExecutionWorker();