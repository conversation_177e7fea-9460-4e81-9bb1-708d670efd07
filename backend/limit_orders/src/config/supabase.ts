import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_KEY || '';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseKey);

// Set user context for Row Level Security
export async function setUserContext(userId: string): Promise<void> {
  await supabase.auth.setSession({
    access_token: userId,
    refresh_token: '',
  });
}

// Limit Order Types
export interface LimitOrder {
  id: string;
  user_id: string;
  token_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  pool_address: string;
  dex_type: string;
  direction: 'buy' | 'sell';
  amount: number;
  target_price: number;
  current_price: number;
  current_market_cap?: number;
  target_market_cap?: number;
  slippage: number;
  wallet_address: string;
  wallet_id: string;
  status: 'pending' | 'executed' | 'cancelled' | 'expired';
  expires_at?: string;
  created_at: string;
  updated_at: string;
  executed_at?: string;
  execution_tx_hash?: string;
  error_message?: string;
}

export type CreateLimitOrderData = Omit<LimitOrder, 'id' | 'created_at' | 'updated_at' | 'executed_at' | 'execution_tx_hash'>;

export type UpdateLimitOrderData = Partial<Omit<LimitOrder, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;

export interface LimitOrderFilters {
  status?: 'pending' | 'executed' | 'cancelled' | 'expired';
  token_address?: string;
  direction?: 'buy' | 'sell';
  dex_type?: string;
  limit?: number;
  offset?: number;
  order_by?: 'created_at' | 'updated_at' | 'target_price';
  order_direction?: 'asc' | 'desc';
}

export interface LimitOrderStats {
  pending: number;
  executed: number;
  cancelled: number;
  expired: number;
  total: number;
} 