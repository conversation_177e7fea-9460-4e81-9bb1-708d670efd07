import { Request, Response } from 'express';
import { limitOrderService } from '../services/limitOrderService.js';
import { logger } from '../utils/logger.js';
import { CreateLimitOrderData, LimitOrderFilters } from '../config/supabase.js';

// Helper function to remove 'did:privy:' prefix from wallet ID and handle URL encoding
function cleanWalletId(walletId: string): string {
  // First decode URL encoding if present
  let decodedId = walletId;
  try {
    decodedId = decodeURIComponent(walletId);
  } catch (e) {
    // If decoding fails, use original
    decodedId = walletId;
  }
  
  // Remove did:privy: prefix
  if (decodedId.startsWith('did:privy:')) {
    return decodedId.replace('did:privy:', '');
  }
  return decodedId;
}

/**
 * Create a new limit order
 * POST /api/limit-orders
 */
export async function createLimitOrder(req: Request, res: Response): Promise<void> {
  try {
    const {
      user_id,
      token_address,
      token_name,
      token_symbol,
      token_image,
      pool_address,
      dex_type,
      direction,
      amount,
      target_price,
      current_price,
      current_market_cap,
      target_market_cap,
      slippage,
      wallet_address,
      wallet_id,
      expires_at
    } = req.body;

    // Validate required fields
    if (!user_id || !token_address || !token_name || !token_symbol || 
        !pool_address || !dex_type || !direction || !amount || 
        !target_price || !current_price || !wallet_address || !wallet_id) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields',
        required: [
          'user_id', 'token_address', 'token_name', 'token_symbol',
          'pool_address', 'dex_type', 'direction', 'amount',
          'target_price', 'current_price', 'wallet_address', 'wallet_id'
        ]
      });
      return;
    }

    // Decode URL encoding but preserve full IDs for database storage
    let decodedUserId = user_id;
    let decodedWalletId = wallet_id;
    try {
      decodedUserId = decodeURIComponent(user_id);
      decodedWalletId = decodeURIComponent(wallet_id);
    } catch (e) {
      // If decoding fails, use original
    }

    const orderData: CreateLimitOrderData = {
      user_id: decodedUserId,
      token_address,
      token_name,
      token_symbol,
      token_image,
      pool_address,
      dex_type,
      direction,
      amount: parseFloat(amount),
      target_price: parseFloat(target_price),
      current_price: parseFloat(current_price),
      current_market_cap: current_market_cap ? parseFloat(current_market_cap) : undefined,
      target_market_cap: target_market_cap ? parseFloat(target_market_cap) : undefined,
      slippage: slippage ? parseFloat(slippage) : 0.01, // Default 1% slippage
      wallet_address,
      wallet_id: decodedWalletId,
      expires_at: expires_at || undefined,
      status: 'pending' // Set default status to pending
    };

    const result = await limitOrderService.createLimitOrder(orderData);

    if (result.success) {
      res.status(201).json({
        success: true,
        data: result.data,
        message: 'Limit order created successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in createLimitOrder controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Get limit orders for a user
 * GET /api/limit-orders or POST /api/limit-orders/list
 */
export async function getLimitOrders(req: Request, res: Response): Promise<void> {
  try {
    // Support both query params (GET) and request body (POST)
    const isPostRequest = req.method === 'POST';
    const user_id = isPostRequest ? req.body.user_id : req.query.user_id;

    if (!user_id || typeof user_id !== 'string') {
      res.status(400).json({
        success: false,
        error: isPostRequest ? 'user_id in request body is required' : 'user_id query parameter is required'
      });
      return;
    }

    // Decode URL encoding but preserve full user_id for database queries
    let decodedUserId = user_id;
    try {
      decodedUserId = decodeURIComponent(user_id);
    } catch (e) {
      // If decoding fails, use original
    }

    // Parse filters from query parameters or request body
    const source = isPostRequest ? req.body : req.query;
    const filters: LimitOrderFilters = {
      status: source.status as any,
      token_address: source.token_address as string,
      direction: source.direction as any,
      dex_type: source.dex_type as string,
      limit: source.limit ? parseInt(source.limit as string) : undefined,
      offset: source.offset ? parseInt(source.offset as string) : undefined,
      order_by: source.order_by as any || 'created_at',
      order_direction: source.order_direction as any || 'desc'
    };

    // Validate numeric parameters
    if (filters.limit && (isNaN(filters.limit) || filters.limit < 1 || filters.limit > 100)) {
      res.status(400).json({
        success: false,
        error: 'limit must be a number between 1 and 100'
      });
      return;
    }

    if (filters.offset && (isNaN(filters.offset) || filters.offset < 0)) {
      res.status(400).json({
        success: false,
        error: 'offset must be a non-negative number'
      });
      return;
    }

    const result = await limitOrderService.getLimitOrders(decodedUserId, filters);

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        count: result.count,
        filters: filters
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in getLimitOrders controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Get a specific limit order by ID
 * GET /api/limit-orders/:id
 */
export async function getLimitOrderById(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id || typeof user_id !== 'string') {
      res.status(400).json({
        success: false,
        error: 'user_id query parameter is required'
      });
      return;
    }

    if (!id) {
      res.status(400).json({
        success: false,
        error: 'Order ID is required'
      });
      return;
    }

    // Decode URL encoding but preserve full user_id for database queries
    let decodedUserId = user_id;
    try {
      decodedUserId = decodeURIComponent(user_id);
    } catch (e) {
      // If decoding fails, use original
    }

    const result = await limitOrderService.getLimitOrderById(decodedUserId, id);

    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      const statusCode = result.error === 'Order not found' ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in getLimitOrderById controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Update a limit order (mainly for cancellation)
 * PUT /api/limit-orders/:id
 */
export async function updateLimitOrder(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { user_id, status, error_message } = req.body;

    if (!user_id) {
      res.status(400).json({
        success: false,
        error: 'user_id is required'
      });
      return;
    }

    // Decode URL encoding but preserve full user_id for database queries
    let decodedUserId = user_id;
    try {
      decodedUserId = decodeURIComponent(user_id);
    } catch (e) {
      // If decoding fails, use original
    }

    if (!id) {
      res.status(400).json({
        success: false,
        error: 'Order ID is required'
      });
      return;
    }

    // Only allow status updates for now (mainly cancellation)
    const updateData: any = {};
    
    if (status) {
      if (!['pending', 'cancelled'].includes(status)) {
        res.status(400).json({
          success: false,
          error: 'Only pending and cancelled status updates are allowed'
        });
        return;
      }
      updateData.status = status;
    }

    if (error_message) {
      updateData.error_message = error_message;
    }

    if (Object.keys(updateData).length === 0) {
      res.status(400).json({
        success: false,
        error: 'No valid update fields provided'
      });
      return;
    }

    const result = await limitOrderService.updateLimitOrder(decodedUserId, id, updateData);

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: 'Limit order updated successfully'
      });
    } else {
      const statusCode = result.error === 'Order not found' ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in updateLimitOrder controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Cancel a limit order
 * DELETE /api/limit-orders/:id/cancel
 */
export async function cancelLimitOrder(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id || typeof user_id !== 'string') {
      res.status(400).json({
        success: false,
        error: 'user_id query parameter is required'
      });
      return;
    }

    // Decode URL encoding but preserve full user_id for database queries
    let decodedUserId = user_id;
    try {
      decodedUserId = decodeURIComponent(user_id);
    } catch (e) {
      // If decoding fails, use original
    }

    if (!id) {
      res.status(400).json({
        success: false,
        error: 'Order ID is required'
      });
      return;
    }

    const result = await limitOrderService.cancelLimitOrder(decodedUserId, id);

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: 'Limit order cancelled successfully'
      });
    } else {
      const statusCode = result.error === 'Order not found' ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in cancelLimitOrder controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Delete a limit order
 * DELETE /api/limit-orders/:id
 */
export async function deleteLimitOrder(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id || typeof user_id !== 'string') {
      res.status(400).json({
        success: false,
        error: 'user_id query parameter is required'
      });
      return;
    }

    // Decode URL encoding but preserve full user_id for database queries
    let decodedUserId = user_id;
    try {
      decodedUserId = decodeURIComponent(user_id);
    } catch (e) {
      // If decoding fails, use original
    }

    if (!id) {
      res.status(400).json({
        success: false,
        error: 'Order ID is required'
      });
      return;
    }

    // Check if order is eligible for deletion (only cancelled or expired)
    const orderResult = await limitOrderService.getLimitOrderById(decodedUserId, id);
    
    if (!orderResult.success) {
      const statusCode = orderResult.error === 'Order not found' ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        error: orderResult.error
      });
      return;
    }

    if (!orderResult.data) {
      res.status(404).json({
        success: false,
        error: 'Order not found'
      });
      return;
    }

    // Only allow deletion of cancelled or expired orders
    if (!['cancelled', 'expired'].includes(orderResult.data.status)) {
      res.status(400).json({
        success: false,
        error: 'Only cancelled or expired orders can be deleted'
      });
      return;
    }

    const result = await limitOrderService.deleteLimitOrder(decodedUserId, id);

    if (result.success) {
      res.json({
        success: true,
        message: 'Limit order deleted successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in deleteLimitOrder controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Get limit order statistics for a user
 * GET /api/limit-orders/stats
 */
export async function getLimitOrderStats(req: Request, res: Response): Promise<void> {
  try {
    const { user_id } = req.query;

    if (!user_id || typeof user_id !== 'string') {
      res.status(400).json({
        success: false,
        error: 'user_id query parameter is required'
      });
      return;
    }

    // Decode URL encoding but preserve full user_id for database queries
    let decodedUserId = user_id;
    try {
      decodedUserId = decodeURIComponent(user_id);
    } catch (e) {
      // If decoding fails, use original
    }

    const result = await limitOrderService.getLimitOrderStats(decodedUserId);

    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in getLimitOrderStats controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
} 