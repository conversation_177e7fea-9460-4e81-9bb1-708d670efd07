-- Add missing foreign key constraint between tpsl_orders and positions tables
-- This fixes the "Could not find a relationship between 'positions' and 'tpsl_orders'" error

-- First, ensure both tables exist and have the required columns
-- The position_id in tpsl_orders should reference the id in positions

-- Add foreign key constraint
ALTER TABLE tpsl_orders 
ADD CONSTRAINT fk_tpsl_orders_position_id 
FOREIGN KEY (position_id) 
REFERENCES positions(id) 
ON DELETE CASCADE 
ON UPDATE CASCADE;

-- Verify the constraint was created
-- You can run this query to check:
-- SELECT conname, conrelid::regclass, confrelid::regclass 
-- FROM pg_constraint 
-- WHERE conname = 'fk_tpsl_orders_position_id';

-- Note: If you get an error about existing data violating the constraint,
-- you may need to clean up orphaned records first:
-- DELETE FROM tpsl_orders WHERE position_id NOT IN (SELECT id FROM positions);