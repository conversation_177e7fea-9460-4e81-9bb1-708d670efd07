-- New Simplified TP/SL System
-- This replaces the complex position-based system with a simple limit order approach

-- Drop existing complex tables
DROP TABLE IF EXISTS tpsl_orders CASCADE;
DROP TABLE IF EXISTS positions CASCADE;

-- Create simple TP/SL orders table (like limit orders)
CREATE TABLE IF NOT EXISTS tpsl_orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    
    -- Token and Pool Information
    token_address TEXT NOT NULL,
    token_name TEXT,
    token_symbol TEXT,
    token_image TEXT,
    pool_address TEXT NOT NULL,
    dex_type TEXT DEFAULT 'raydium',
    
    -- Order Details
    order_type TEXT NOT NULL CHECK (order_type IN ('take_profit', 'stop_loss')),
    direction TEXT NOT NULL CHECK (direction IN ('buy', 'sell')),
    
    -- Price and Amount
    trigger_price DECIMAL(20, 8) NOT NULL,  -- Simple price target (no percentages)
    amount DECIMAL(20, 8) NOT NULL,         -- Amount to trade
    slippage DECIMAL(5, 2) DEFAULT 1.00,    -- Slippage tolerance
    
    -- Wallet Information
    wallet_address TEXT NOT NULL,
    wallet_id TEXT,
    
    -- Status and Execution
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'triggered', 'executed', 'cancelled', 'failed')),
    executed_at TIMESTAMP WITH TIME ZONE,
    execution_tx_hash TEXT,
    execution_price DECIMAL(20, 8),
    gas_fee DECIMAL(20, 8),
    error_message TEXT,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_user_id ON tpsl_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_status ON tpsl_orders(status);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_token_address ON tpsl_orders(token_address);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_pool_address ON tpsl_orders(pool_address);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_order_type ON tpsl_orders(order_type);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_trigger_price ON tpsl_orders(trigger_price);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_created_at ON tpsl_orders(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE tpsl_orders ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own TP/SL orders" ON tpsl_orders
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own TP/SL orders" ON tpsl_orders
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own TP/SL orders" ON tpsl_orders
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own TP/SL orders" ON tpsl_orders
    FOR DELETE USING (auth.uid()::text = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_tpsl_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_tpsl_orders_updated_at BEFORE UPDATE ON tpsl_orders
    FOR EACH ROW EXECUTE FUNCTION update_tpsl_updated_at();

-- Create a view for active orders with current price monitoring
CREATE OR REPLACE VIEW active_tpsl_orders AS
SELECT 
    *,
    CASE 
        WHEN order_type = 'take_profit' AND direction = 'sell' THEN 'Execute when price >= ' || trigger_price
        WHEN order_type = 'take_profit' AND direction = 'buy' THEN 'Execute when price <= ' || trigger_price
        WHEN order_type = 'stop_loss' AND direction = 'sell' THEN 'Execute when price <= ' || trigger_price
        WHEN order_type = 'stop_loss' AND direction = 'buy' THEN 'Execute when price >= ' || trigger_price
    END as execution_condition
FROM tpsl_orders 
WHERE status = 'active'
ORDER BY created_at DESC;

-- Create sample data for testing (commented out for production)
/*
INSERT INTO tpsl_orders (
    user_id, token_address, token_name, token_symbol, pool_address,
    order_type, direction, trigger_price, amount, wallet_address
) VALUES 
-- Example: TP order to sell tokens when price reaches $0.01
('test_user_1', 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', 'Test Token', 'TEST', 'test_pool_address',
 'take_profit', 'sell', 0.01, 1000, 'test_wallet_address'),
-- Example: SL order to sell tokens if price drops to $0.005
('test_user_1', 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', 'Test Token', 'TEST', 'test_pool_address',
 'stop_loss', 'sell', 0.005, 1000, 'test_wallet_address');
*/