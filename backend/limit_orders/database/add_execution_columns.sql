-- Add profit_val and sell_txn columns to tpsl_orders table
ALTER TABLE tpsl_orders
ADD COLUMN IF NOT EXISTS profit_val DECIMAL(20, 8) DEFAULT 0,
ADD COLUMN IF NOT EXISTS sell_txn VARCHAR(255);

-- Add profit_val and sell_txn columns to limit_orders table (for consistency)
ALTER TABLE limit_orders
ADD COLUMN IF NOT EXISTS profit_val DECIMAL(20, 8) DEFAULT 0,
ADD COLUMN IF NOT EXISTS sell_txn VARCHAR(255),
ADD COLUMN IF NOT EXISTS executed_price DECIMAL(20, 8);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_sell_txn ON tpsl_orders(sell_txn);
CREATE INDEX IF NOT EXISTS idx_limit_orders_sell_txn ON limit_orders(sell_txn);

-- Add executed_at column if not exists
ALTER TABLE tpsl_orders
ADD COLUMN IF NOT EXISTS executed_at TIMESTAMP WITH TIME ZONE;

<PERSON>TER TABLE limit_orders
ADD COLUMN IF NOT EXISTS executed_at TIMESTAMP WITH TIME ZONE;

-- Add triggered_at column for limit orders (when order condition is met)
ALTER TABLE limit_orders
ADD COLUMN IF NOT EXISTS triggered_at TIMESTAMP WITH TIME ZONE;

ALTER TABLE tpsl_orders
ADD COLUMN IF NOT EXISTS triggered_at TIMESTAMP WITH TIME ZONE;

-- Add error_message column for failed orders
ALTER TABLE tpsl_orders
ADD COLUMN IF NOT EXISTS error_message TEXT;

ALTER TABLE limit_orders
ADD COLUMN IF NOT EXISTS error_message TEXT;

-- Create notifications table if not exists
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Create index on notifications for faster queries
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);

-- Add RLS policies for notifications
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Users can only see their own notifications
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own notifications (mark as read)
CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Service role can insert notifications
CREATE POLICY "Service role can insert notifications" ON notifications
    FOR INSERT WITH CHECK (true);

-- Add comments for documentation
COMMENT ON COLUMN tpsl_orders.profit_val IS 'The profit value in SOL from selling tokens';
COMMENT ON COLUMN tpsl_orders.sell_txn IS 'The transaction signature of the sell operation';
COMMENT ON COLUMN limit_orders.profit_val IS 'The profit value in SOL from selling tokens';
COMMENT ON COLUMN limit_orders.sell_txn IS 'The transaction signature of the sell/buy operation';
COMMENT ON COLUMN limit_orders.executed_price IS 'The actual price at which the order was executed';
COMMENT ON TABLE notifications IS 'Stores user notifications for order executions and other events';