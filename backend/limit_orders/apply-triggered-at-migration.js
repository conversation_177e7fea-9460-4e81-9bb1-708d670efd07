#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyMigration() {
  try {
    console.log('Applying triggered_at column migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, 'database', 'add_execution_columns.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Extract only the triggered_at column additions
    const triggeredAtMigration = `
-- Add triggered_at column for limit orders (when order condition is met)
ALTER TABLE limit_orders
ADD COLUMN IF NOT EXISTS triggered_at TIMESTAMP WITH TIME ZONE;

ALTER TABLE tpsl_orders
ADD COLUMN IF NOT EXISTS triggered_at TIMESTAMP WITH TIME ZONE;
    `;
    
    console.log('Executing SQL migration...');
    
    // Try to execute each statement separately
    const statements = [
      'ALTER TABLE limit_orders ADD COLUMN IF NOT EXISTS triggered_at TIMESTAMP WITH TIME ZONE;',
      'ALTER TABLE tpsl_orders ADD COLUMN IF NOT EXISTS triggered_at TIMESTAMP WITH TIME ZONE;'
    ];
    
    for (const statement of statements) {
      console.log('Executing:', statement);
      const { data, error } = await supabase.rpc('query', { query_text: statement });
      
      if (error) {
        console.error('Statement failed:', error);
        console.log('Trying alternative method...');
        
        // Alternative: try using SQL query directly (this may not work for DDL)
        const { data: altData, error: altError } = await supabase
          .from('information_schema.columns')
          .select('*')
          .eq('table_name', 'limit_orders')
          .eq('column_name', 'triggered_at');
          
        if (altError) {
          console.error('Could not check if column exists:', altError);
        } else if (altData.length === 0) {
          console.log('Column triggered_at does not exist. Manual migration required.');
          console.log('Please run this SQL manually in your database:');
          console.log(statement);
        } else {
          console.log('Column triggered_at already exists.');
        }
      } else {
        console.log('✅ Statement executed successfully');
      }
    }
    
    console.log('✅ Migration completed successfully!');
    console.log('The triggered_at column has been added to both limit_orders and tpsl_orders tables.');
    
  } catch (error) {
    console.error('Error applying migration:', error);
    process.exit(1);
  }
}

// Run the migration
applyMigration();