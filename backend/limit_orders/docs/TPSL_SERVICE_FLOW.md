# TPSL (Take Profit/Stop Loss) Service Flow Documentation

## Overview

The TPSL service is a complete position management system that allows users to:
1. Buy tokens with an initial investment
2. Set automatic exit conditions (Take Profit and Stop Loss)
3. Have their positions automatically sold when price targets are reached

**Key Concept**: TPSL orders involve an immediate token purchase, not just placing conditional orders.

## Architecture Overview

```
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│   Frontend UI   │────▶│  Limit Orders    │────▶│ Solana Service  │
│  (React/Next)   │     │    Service       │     │  (Token Swaps)  │
└─────────────────┘     └──────────────────┘     └─────────────────┘
                               │
                               ▼
                        ┌──────────────────┐
                        │    Supabase      │
                        │    Database      │
                        └──────────────────┘
```

## Complete Service Flow

### Phase 1: Order Creation (with Initial Buy)

1. **User Input**
   - User specifies:
     - Token to buy (address, symbol)
     - Investment amount (SOL)
     - Take Profit percentage (e.g., +14%)
     - Stop Loss percentage (e.g., -6%)

2. **Initial Token Purchase**
   ```typescript
   // Backend immediately buys tokens
   POST /api/simple-tpsl/orders
   {
     token_address: "...",
     amount: 0.1,  // SOL to invest
     tp_enabled: true,
     tp_percentage: 14,
     sl_enabled: true,
     sl_percentage: 6
   }
   ```

3. **Backend Processing** (`simpleTpslService.ts`)
   - Validates input parameters
   - Calls Solana service to buy tokens:
     ```typescript
     const swapResponse = await this.executeSolanaSwap({
       direction: 'buy',
       amount: orderData.amount,
       tokenAddress: orderData.token_address,
       walletAddress: orderData.wallet_address
     });
     ```
   - Receives: `total_token` (tokens bought) and `txn` (transaction hash)

4. **Database Storage** (only if buy succeeds)
   - Main order in `tpsl_orders`:
     - `total_token`: Actual tokens received
     - `txn`: Buy transaction hash
     - `current_price`: Entry price
     - `status`: 'active'
   
   - Sub-orders:
     - `tp_orders`: Target price calculated from entry + percentage
     - `sl_orders`: Target price calculated from entry - percentage

### Phase 2: Price Monitoring

1. **Service Initialization** (`enhancedPriceMonitorService.ts`)
   - Loads all active TPSL orders on startup
   - Groups orders by token address
   - Subscribes to price feeds

2. **Dual Monitoring System**
   - **Primary**: WebSocket connection to Mobula for real-time prices
   - **Backup**: Polling every 5 seconds
   - Redis caching for performance

3. **Trigger Detection**
   ```typescript
   // For each price update
   if (order_type === 'take_profit') {
     return currentPrice >= trigger_price;  // Sell when profit target hit
   } else if (order_type === 'stop_loss') {
     return currentPrice <= trigger_price;  // Sell when loss limit hit
   }
   ```

### Phase 3: Order Execution (Sell)

1. **Trigger Process**
   - When price target is hit:
     - Update `tp_orders` or `sl_orders` status to 'triggered'
     - Add to Redis execution queue
     - Remove from active monitoring

2. **Execution Worker** (`orderExecutionWorker.ts`)
   - Processes triggered orders from queue
   - Calculates dynamic fees based on urgency
   - Calls Solana service to sell tokens:
     ```typescript
     const swapRequest = {
       direction: 'sell',
       amount: orderData.amount,  // Token amount to sell
       tokenAddress: orderData.tokenAddress,
       // ... other parameters
     };
     ```

3. **Post-Execution Updates**
   - Updates order status to 'executed'
   - Records:
     - `execution_txn`: Sell transaction hash
     - `executed_at`: Timestamp
     - `profit_val`: SOL received from sale
   - Sends user notification

## Database Schema

### Main Tables

1. **tpsl_orders** (Parent)
   ```sql
   - id: UUID
   - user_id: User identifier
   - token_address: Token contract
   - amount: Initial SOL investment
   - total_token: Tokens received from buy
   - txn: Buy transaction hash
   - current_price: Entry price
   - status: active/executed/cancelled
   - profit_val: Profit/loss in SOL
   - sell_txn: Sell transaction hash
   ```

2. **tp_orders** (Take Profit)
   ```sql
   - id: UUID
   - tpsl_order_id: FK to parent
   - target_price: Price to trigger sell
   - percentage: Profit percentage
   - status: active/triggered/executed
   - execution_txn: Sell transaction
   ```

3. **sl_orders** (Stop Loss)
   ```sql
   - id: UUID
   - tpsl_order_id: FK to parent
   - target_price: Price to trigger sell
   - percentage: Loss percentage
   - status: active/triggered/executed
   - execution_txn: Sell transaction
   ```

## API Endpoints

### Create TPSL Order
```
POST /api/simple-tpsl/orders
POST /api/enhanced-tpsl/orders
```

### Get User Orders
```
GET /api/simple-tpsl/orders?user_id={userId}
```

### Cancel Order
```
DELETE /api/simple-tpsl/orders/{orderId}
```

## Key Services

### 1. simpleTpslService.ts
- Handles order creation with initial buy
- CRUD operations for TPSL orders
- Direct database operations

### 2. enhancedTpslService.ts
- Advanced validation and error handling
- Uses database stored procedures
- Enhanced security features

### 3. enhancedPriceMonitorService.ts
- Real-time price monitoring
- WebSocket + polling hybrid
- Trigger detection logic

### 4. orderExecutionWorker.ts
- Processes triggered orders
- Executes sell transactions
- Updates order status

## Configuration

### Environment Variables
```bash
# Limit Orders Service
SUPABASE_URL=
SUPABASE_SERVICE_KEY=
SOLANA_SERVICE_URL=http://localhost:6001
REDIS_HOST=localhost
REDIS_PORT=6379

# Solana Service
SOLANA_RPC_URL=
PRIVATE_KEY=
```

### Service Ports
- Limit Orders Service: 5002
- Solana Service: 6001
- Redis: 6379

## Error Handling

1. **Buy Failure**: Order not created if initial buy fails
2. **Network Issues**: Backup polling ensures monitoring continues
3. **Execution Failure**: Orders marked as 'failed' with error message
4. **Retry Logic**: 3 attempts with exponential backoff

## Example Flow

```
1. User creates TPSL order:
   - Buy 0.1 SOL worth of TOKEN
   - Take Profit: +14%
   - Stop Loss: -6%

2. Backend buys tokens:
   - Executes swap: 0.1 SOL → 1000 TOKEN
   - Entry price: $0.0001
   - TP target: $0.000114
   - SL target: $0.000094

3. Price monitoring begins:
   - Watches TOKEN price via WebSocket
   - Checks every price update

4. Price hits $0.000114:
   - TP order triggers
   - Sells 1000 TOKEN → 0.114 SOL
   - Profit: 0.014 SOL (14%)
   - User notified of execution
```

## Deployment

The service runs as a Docker container managed by docker-compose:

```yaml
limit-orders:
  image: ${REGISTRY_URL}/redfyn-limit-orders:latest
  container_name: redfyn-limit-orders
  environment:
    - NODE_ENV=production
    - PORT=5002
  networks:
    - app-network
```

## Monitoring & Logs

- Health check endpoint: `/health`
- Logs location: `/backend/limit_orders/logs/`
- Key metrics:
  - Active orders count
  - WebSocket connection status
  - Execution success rate

## Security Considerations

1. **Wallet Security**: Private keys handled by Solana service
2. **RLS Policies**: Users can only access their own orders
3. **Input Validation**: Strict limits on percentages and amounts
4. **MEV Protection**: Available for large trades

## Future Enhancements

1. Multiple TP/SL levels
2. Trailing stop loss
3. Time-based conditions
4. Partial position closing
5. Advanced order types (OCO, bracket orders)

---

Last Updated: January 2025