import { logger } from '../utils/logger.js';
/**
 * Global error handler middleware
 */
export function errorHandler(err, req, res, next) {
    // Log the error
    logger.error('Unhandled error:', err);
    // Send error response
    res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'production' ? undefined : err.message,
        stack: process.env.NODE_ENV === 'production' ? undefined : err.stack,
    });
}
/**
 * Not found handler middleware
 */
export function notFoundHandler(req, res) {
    logger.warn(`Route not found: ${req.method} ${req.originalUrl}`);
    res.status(404).json({
        success: false,
        error: 'Not Found',
        message: `Route ${req.method} ${req.originalUrl} not found`,
    });
}
