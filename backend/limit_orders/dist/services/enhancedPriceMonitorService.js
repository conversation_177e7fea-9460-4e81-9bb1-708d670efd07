import { supabase } from '../config/supabase.js';
import { mobulaWebSocketService } from './mobulaWebSocketService.js';
import { redis, orderExecutionQueue } from '../config/redis.js';
import logger from '../utils/logger.js';
export class EnhancedPriceMonitorService {
    isMonitoring = false;
    orderGroups = new Map();
    checkInterval = null;
    CHECK_INTERVAL_MS = 5000; // Backup polling every 5 seconds (real-time via WebSocket)
    REDIS_ORDER_KEY_PREFIX = 'orders:token:';
    REDIS_ORDER_TTL = 300; // 5 minutes TTL
    /**
     * Start the enhanced price monitoring service
     */
    async start() {
        if (this.isMonitoring) {
            logger.info('Enhanced price monitoring is already running');
            return;
        }
        try {
            logger.info('Starting enhanced price monitoring service...');
            // Connect to Mobula WebSocket
            await mobulaWebSocketService.connect();
            // Load initial orders
            await this.loadActiveOrders();
            // Register real-time price update callbacks for event-driven order checking
            this.registerPriceUpdateCallbacks();
            // Start periodic checks as backup safety mechanism
            this.startPeriodicChecks();
            // Subscribe to order updates from database
            this.subscribeToOrderChanges();
            this.isMonitoring = true;
            logger.info('Enhanced price monitoring service started with real-time + polling hybrid system');
        }
        catch (error) {
            logger.error('Failed to start enhanced price monitoring:', error);
            throw error;
        }
    }
    /**
     * Stop the price monitoring service
     */
    stop() {
        if (!this.isMonitoring) {
            return;
        }
        logger.info('Stopping enhanced price monitoring service...');
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
        mobulaWebSocketService.disconnect();
        this.orderGroups.clear();
        this.isMonitoring = false;
        logger.info('Enhanced price monitoring service stopped');
    }
    /**
     * Load all active orders from database and cache
     */
    async loadActiveOrders() {
        try {
            // Load limit orders
            const { data: limitOrders, error: limitError } = await supabase
                .from('limit_orders')
                .select('*')
                .eq('status', 'pending');
            if (limitError) {
                logger.error('Error loading limit orders:', limitError);
            }
            // Load TP/SL orders with sub-orders (multi-table approach)
            const { data: tpslOrders, error: tpslError } = await supabase
                .from('tpsl_orders')
                .select(`
          *,
          tp_orders(*),
          sl_orders(*)
        `)
                .eq('status', 'active');
            if (tpslError) {
                logger.error('Error loading TP/SL orders:', tpslError);
            }
            // Combine and group orders
            const allOrders = [];
            if (limitOrders) {
                allOrders.push(...limitOrders.map(order => ({
                    ...order,
                    order_type: 'limit',
                    trigger_price: order.target_price
                })));
            }
            if (tpslOrders) {
                // Process TP/SL orders with their sub-orders
                tpslOrders.forEach(mainOrder => {
                    // Add TP orders
                    if (mainOrder.tp_orders) {
                        mainOrder.tp_orders.forEach((tpOrder) => {
                            if (tpOrder.status === 'active') {
                                allOrders.push({
                                    id: tpOrder.id,
                                    user_id: mainOrder.user_id,
                                    token_address: mainOrder.token_address,
                                    order_type: 'take_profit',
                                    direction: 'sell', // TP orders are always sell
                                    trigger_price: tpOrder.target_price,
                                    amount: mainOrder.amount,
                                    wallet_address: mainOrder.wallet_address,
                                    wallet_id: mainOrder.wallet_id,
                                    slippage: 0.02, // Default 2%
                                    pool_address: mainOrder.pool_address,
                                    dex_type: mainOrder.exchange_name || 'pumpfun'
                                });
                            }
                        });
                    }
                    // Add SL orders
                    if (mainOrder.sl_orders) {
                        mainOrder.sl_orders.forEach((slOrder) => {
                            if (slOrder.status === 'active') {
                                allOrders.push({
                                    id: slOrder.id,
                                    user_id: mainOrder.user_id,
                                    token_address: mainOrder.token_address,
                                    order_type: 'stop_loss',
                                    direction: 'sell', // SL orders are always sell
                                    trigger_price: slOrder.target_price,
                                    amount: mainOrder.amount,
                                    wallet_address: mainOrder.wallet_address,
                                    wallet_id: mainOrder.wallet_id,
                                    slippage: 0.02, // Default 2%
                                    pool_address: mainOrder.pool_address,
                                    dex_type: mainOrder.exchange_name || 'pumpfun'
                                });
                            }
                        });
                    }
                });
            }
            // Group orders by token address
            await this.groupAndCacheOrders(allOrders);
            // Subscribe to all unique token addresses
            const tokenAddresses = Array.from(this.orderGroups.keys());
            if (tokenAddresses.length > 0) {
                mobulaWebSocketService.subscribeToTokens(tokenAddresses);
                logger.info(`Monitoring ${allOrders.length} orders across ${tokenAddresses.length} tokens`);
            }
        }
        catch (error) {
            logger.error('Error loading active orders:', error);
        }
    }
    /**
     * Group orders by token address and cache them
     */
    async groupAndCacheOrders(orders) {
        this.orderGroups.clear();
        for (const order of orders) {
            const tokenAddress = order.token_address;
            if (!this.orderGroups.has(tokenAddress)) {
                this.orderGroups.set(tokenAddress, {
                    tokenAddress,
                    orders: []
                });
            }
            this.orderGroups.get(tokenAddress).orders.push(order);
        }
        // Cache orders in Redis for quick access
        for (const [tokenAddress, group] of this.orderGroups) {
            await redis.setex(`${this.REDIS_ORDER_KEY_PREFIX}${tokenAddress}`, this.REDIS_ORDER_TTL, JSON.stringify(group.orders));
        }
    }
    /**
     * Start periodic price checks as backup safety mechanism
     */
    startPeriodicChecks() {
        this.checkInterval = setInterval(async () => {
            await this.checkAllOrders();
        }, this.CHECK_INTERVAL_MS);
        logger.info('Started backup polling system (5-second intervals) as safety net');
    }
    /**
     * Register real-time price update callbacks for each monitored token
     */
    registerPriceUpdateCallbacks() {
        for (const tokenAddress of this.orderGroups.keys()) {
            mobulaWebSocketService.onPriceUpdate(tokenAddress, async (newPrice) => {
                await this.checkOrdersForToken(tokenAddress, newPrice);
            });
        }
        logger.info(`Registered real-time callbacks for ${this.orderGroups.size} tokens`);
    }
    /**
     * Check orders for a specific token with given price (real-time triggered)
     */
    async checkOrdersForToken(tokenAddress, currentPrice) {
        try {
            const orderGroup = this.orderGroups.get(tokenAddress);
            if (!orderGroup) {
                logger.debug(`No order group found for token ${tokenAddress}`);
                return;
            }
            // Update last checked price
            orderGroup.lastCheckedPrice = currentPrice;
            orderGroup.lastCheckTime = Date.now();
            logger.debug(`Real-time price check for ${tokenAddress}: $${currentPrice}, ${orderGroup.orders.length} orders`);
            // Check each order in the group
            for (const order of orderGroup.orders) {
                if (this.shouldTriggerOrder(order, currentPrice)) {
                    logger.info(`Real-time trigger detected for order ${order.id} at price $${currentPrice}`);
                    await this.triggerOrder(order, currentPrice);
                }
            }
        }
        catch (error) {
            logger.error(`Error checking orders for token ${tokenAddress} in real-time:`, error);
        }
    }
    /**
     * Check all orders against current prices (polling backup)
     */
    async checkAllOrders() {
        for (const [tokenAddress, orderGroup] of this.orderGroups) {
            try {
                const currentPrice = await mobulaWebSocketService.getCurrentPrice(tokenAddress);
                if (currentPrice === null) {
                    logger.debug(`No price data available for token ${tokenAddress}`);
                    continue;
                }
                // Only check if price hasn't been checked recently (avoid duplicate checks)
                const timeSinceLastCheck = Date.now() - (orderGroup.lastCheckTime || 0);
                if (timeSinceLastCheck < 2000) { // Skip if checked within last 2 seconds
                    logger.debug(`Skipping polling check for ${tokenAddress} - recently checked via real-time`);
                    continue;
                }
                logger.debug(`Polling backup check for ${tokenAddress}: $${currentPrice}`);
                await this.checkOrdersForToken(tokenAddress, currentPrice);
            }
            catch (error) {
                logger.error(`Error checking orders for token ${tokenAddress} via polling:`, error);
            }
        }
    }
    /**
     * Determine if an order should be triggered
     */
    shouldTriggerOrder(order, currentPrice) {
        const { order_type, trigger_price, direction } = order;
        if (order_type === 'limit') {
            // For limit orders
            if (direction === 'buy') {
                // Buy when price drops to or below target
                return currentPrice <= trigger_price;
            }
            else {
                // Sell when price rises to or above target
                return currentPrice >= trigger_price;
            }
        }
        else if (order_type === 'take_profit') {
            // Take Profit - triggers when price reaches profit target
            // TP orders are always sell orders that trigger when price goes UP
            return currentPrice >= trigger_price;
        }
        else if (order_type === 'stop_loss') {
            // Stop Loss - triggers when price reaches loss limit
            // SL orders are always sell orders that trigger when price goes DOWN
            return currentPrice <= trigger_price;
        }
        return false;
    }
    /**
     * Trigger an order execution
     */
    async triggerOrder(order, currentPrice) {
        try {
            logger.info(`Triggering ${order.order_type} order ${order.id} for token ${order.token_address} at price $${currentPrice}`);
            // Update order status to triggered
            let tableName;
            let updateData = {
                status: 'triggered',
                triggered_at: new Date().toISOString()
            };
            if (order.order_type === 'limit') {
                tableName = 'limit_orders';
                updateData.current_price = currentPrice;
            }
            else if (order.order_type === 'take_profit') {
                tableName = 'tp_orders';
            }
            else if (order.order_type === 'stop_loss') {
                tableName = 'sl_orders';
            }
            else {
                logger.error(`Unknown order type: ${order.order_type}`);
                return;
            }
            const { error: updateError } = await supabase
                .from(tableName)
                .update(updateData)
                .eq('id', order.id);
            if (updateError) {
                logger.error('Error updating order status:', updateError);
                return;
            }
            // Add to execution queue
            await orderExecutionQueue.add('execute-order', {
                orderId: order.id,
                orderType: order.order_type,
                tokenAddress: order.token_address,
                poolAddress: order.pool_address,
                direction: order.direction,
                amount: order.amount,
                triggerPrice: order.trigger_price,
                currentPrice: currentPrice,
                walletAddress: order.wallet_address,
                walletId: order.wallet_id,
                slippage: order.slippage || 0.02,
                userId: order.user_id,
                exchange_name: order.dex_type
            }, {
                attempts: 3,
                backoff: {
                    type: 'exponential',
                    delay: 2000
                },
                removeOnComplete: false,
                removeOnFail: false
            });
            // Remove order from monitoring
            this.removeOrderFromMonitoring(order);
            logger.info(`Order ${order.id} added to execution queue`);
        }
        catch (error) {
            logger.error(`Error triggering order ${order.id}:`, error);
            // Revert order status if execution failed
            let tableName;
            if (order.order_type === 'limit') {
                tableName = 'limit_orders';
            }
            else if (order.order_type === 'take_profit') {
                tableName = 'tp_orders';
            }
            else if (order.order_type === 'stop_loss') {
                tableName = 'sl_orders';
            }
            else {
                return;
            }
            await supabase
                .from(tableName)
                .update({ status: order.order_type === 'limit' ? 'pending' : 'active' })
                .eq('id', order.id);
        }
    }
    /**
     * Remove an order from monitoring
     */
    removeOrderFromMonitoring(order) {
        const orderGroup = this.orderGroups.get(order.token_address);
        if (orderGroup) {
            orderGroup.orders = orderGroup.orders.filter(o => o.id !== order.id);
            // If no more orders for this token, remove the group and unsubscribe
            if (orderGroup.orders.length === 0) {
                this.orderGroups.delete(order.token_address);
                mobulaWebSocketService.unsubscribeFromTokens([order.token_address]);
            }
        }
    }
    /**
     * Subscribe to real-time order changes
     */
    subscribeToOrderChanges() {
        // Subscribe to limit order changes
        supabase
            .channel('limit-orders-changes')
            .on('postgres_changes', { event: '*', schema: 'public', table: 'limit_orders' }, async (payload) => {
            logger.info('Limit order change detected:', payload.eventType);
            await this.handleOrderChange(payload);
        })
            .subscribe();
        // Subscribe to TP/SL order changes
        supabase
            .channel('tpsl-orders-changes')
            .on('postgres_changes', { event: '*', schema: 'public', table: 'tpsl_orders' }, async (payload) => {
            logger.info('TP/SL order change detected:', payload.eventType);
            await this.handleOrderChange(payload);
        })
            .subscribe();
    }
    /**
     * Handle real-time order changes
     */
    async handleOrderChange(payload) {
        try {
            const { eventType, new: newRecord, old: oldRecord } = payload;
            if (eventType === 'INSERT' || eventType === 'UPDATE') {
                const order = newRecord;
                const isActive = order.status === 'pending' || order.status === 'active';
                if (isActive) {
                    // Add or update order in monitoring
                    await this.addOrderToMonitoring(order);
                }
                else {
                    // Remove from monitoring if not active
                    this.removeOrderFromMonitoring(order);
                }
            }
            else if (eventType === 'DELETE') {
                // Remove from monitoring
                this.removeOrderFromMonitoring(oldRecord);
            }
        }
        catch (error) {
            logger.error('Error handling order change:', error);
        }
    }
    /**
     * Add a new order to monitoring
     */
    async addOrderToMonitoring(order) {
        const monitoredOrder = {
            ...order,
            order_type: order.order_type || 'limit',
            trigger_price: order.trigger_price || order.target_price
        };
        const tokenAddress = order.token_address;
        const isNewToken = !this.orderGroups.has(tokenAddress);
        if (isNewToken) {
            this.orderGroups.set(tokenAddress, {
                tokenAddress,
                orders: []
            });
            // Subscribe to token price updates
            mobulaWebSocketService.subscribeToTokens([tokenAddress]);
            // Register real-time callback for new token
            mobulaWebSocketService.onPriceUpdate(tokenAddress, async (newPrice) => {
                await this.checkOrdersForToken(tokenAddress, newPrice);
            });
            logger.info(`Added real-time monitoring for new token: ${tokenAddress}`);
        }
        const orderGroup = this.orderGroups.get(tokenAddress);
        // Remove existing order if updating
        orderGroup.orders = orderGroup.orders.filter(o => o.id !== order.id);
        // Add the new/updated order
        orderGroup.orders.push(monitoredOrder);
        // Update Redis cache
        await redis.setex(`${this.REDIS_ORDER_KEY_PREFIX}${tokenAddress}`, this.REDIS_ORDER_TTL, JSON.stringify(orderGroup.orders));
    }
    /**
     * Get monitoring statistics
     */
    getMonitoringStats() {
        let totalOrders = 0;
        const ordersByType = {
            limit: 0,
            take_profit: 0,
            stop_loss: 0
        };
        for (const group of this.orderGroups.values()) {
            for (const order of group.orders) {
                totalOrders++;
                ordersByType[order.order_type]++;
            }
        }
        return {
            isMonitoring: this.isMonitoring,
            totalOrders,
            tokenCount: this.orderGroups.size,
            ordersByType,
            webSocketConnected: mobulaWebSocketService.isWebSocketConnected()
        };
    }
    /**
     * Manually refresh orders for a specific token
     */
    async refreshTokenOrders(tokenAddress) {
        await this.loadActiveOrders();
    }
}
// Export singleton instance
export const enhancedPriceMonitorService = new EnhancedPriceMonitorService();
