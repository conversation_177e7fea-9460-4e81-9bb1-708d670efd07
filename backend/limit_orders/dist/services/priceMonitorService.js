import { supabase } from '../config/supabase.js';
class PriceMonitorService {
    monitoringInterval = null;
    MONITORING_INTERVAL_MS = 10000; // 10 seconds
    isMonitoring = false;
    /**
     * Start monitoring TP/SL orders for price triggers
     */
    startMonitoring() {
        if (this.isMonitoring) {
            console.log('Price monitoring is already running');
            return;
        }
        console.log('Starting TP/SL price monitoring...');
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(async () => {
            try {
                await this.checkTPSLTriggers();
            }
            catch (error) {
                console.error('Error in price monitoring:', error);
            }
        }, this.MONITORING_INTERVAL_MS);
    }
    /**
     * Stop monitoring TP/SL orders
     */
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
            this.isMonitoring = false;
            console.log('TP/SL price monitoring stopped');
        }
    }
    /**
     * Check all active TP/SL orders for trigger conditions
     */
    async checkTPSLTriggers() {
        try {
            // Get all active TP/SL orders
            const { data: orders, error } = await supabase
                .from('tpsl_orders')
                .select('*')
                .eq('status', 'active');
            if (error) {
                console.error('Error fetching active TP/SL orders:', error);
                return;
            }
            if (!orders || orders.length === 0) {
                return;
            }
            // Group orders by token address for efficient price fetching
            const tokenAddresses = [...new Set(orders.map(order => order.token_address))];
            // Fetch current prices for all tokens
            const priceData = await this.fetchCurrentPrices(tokenAddresses);
            // Check each order for trigger conditions
            for (const order of orders) {
                const currentPrice = priceData.get(order.token_address);
                if (currentPrice) {
                    await this.checkOrderTrigger(order, currentPrice);
                }
            }
        }
        catch (error) {
            console.error('Error checking TP/SL triggers:', error);
        }
    }
    /**
     * Fetch current prices for given token addresses
     */
    async fetchCurrentPrices(tokenAddresses) {
        const priceMap = new Map();
        try {
            // This would integrate with your existing price service
            // For now, we'll simulate price fetching
            for (const tokenAddress of tokenAddresses) {
                // In a real implementation, you would:
                // 1. Query DEX pools for current prices
                // 2. Use price oracles
                // 3. Integrate with existing price services
                // Simulated price fetch - replace with actual implementation
                const mockPrice = await this.getMockPrice(tokenAddress);
                priceMap.set(tokenAddress, mockPrice);
            }
        }
        catch (error) {
            console.error('Error fetching current prices:', error);
        }
        return priceMap;
    }
    /**
     * Mock price fetching - replace with actual price service integration
     */
    async getMockPrice(tokenAddress) {
        // This is a placeholder - integrate with your actual price service
        // You might have existing price fetching logic in your spot trading backend
        // For demonstration, return a random price variation
        const basePrice = 2000; // Base price for simulation
        const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
        return basePrice * (1 + variation);
    }
    /**
     * Check if an individual order should be triggered
     */
    async checkOrderTrigger(order, currentPrice) {
        try {
            const shouldTrigger = this.shouldTriggerOrder(order, currentPrice);
            if (shouldTrigger) {
                console.log(`Triggering ${order.order_type} order ${order.id} at price ${currentPrice}`);
                await this.triggerOrder(order, currentPrice);
            }
        }
        catch (error) {
            console.error(`Error checking trigger for order ${order.id}:`, error);
        }
    }
    /**
     * Determine if an order should be triggered based on current price
     */
    shouldTriggerOrder(order, currentPrice) {
        const { order_type, trigger_price, direction } = order;
        if (order_type === 'take_profit') {
            // Take Profit: trigger when price reaches or exceeds target
            if (direction === 'buy') {
                // For buy positions, TP triggers when price goes up
                return currentPrice >= trigger_price;
            }
            else {
                // For sell positions, TP triggers when price goes down
                return currentPrice <= trigger_price;
            }
        }
        else if (order_type === 'stop_loss') {
            // Stop Loss: trigger when price reaches or falls below target
            if (direction === 'buy') {
                // For buy positions, SL triggers when price goes down
                return currentPrice <= trigger_price;
            }
            else {
                // For sell positions, SL triggers when price goes up
                return currentPrice >= trigger_price;
            }
        }
        return false;
    }
    /**
     * Execute the triggered order
     */
    async triggerOrder(order, currentPrice) {
        try {
            // Update order status to triggered
            const { error: updateError } = await supabase
                .from('tpsl_orders')
                .update({
                status: 'triggered',
                current_price: currentPrice,
                triggered_at: new Date().toISOString()
            })
                .eq('id', order.id);
            if (updateError) {
                console.error('Error updating triggered order:', updateError);
                return;
            }
            // Execute the actual trade
            await this.executeTrade(order, currentPrice);
            // Update position flags
            await this.updatePositionFlags(order.position_id, order.order_type);
        }
        catch (error) {
            console.error(`Error triggering order ${order.id}:`, error);
            // Revert order status if execution failed
            await supabase
                .from('tpsl_orders')
                .update({ status: 'active' })
                .eq('id', order.id);
        }
    }
    /**
     * Execute the actual trade when TP/SL is triggered
     */
    async executeTrade(order, currentPrice) {
        try {
            // This would integrate with your existing trading execution logic
            // For now, we'll log the trade execution
            console.log(`Executing ${order.order_type} trade:`, {
                orderId: order.id,
                tokenAddress: order.token_address,
                amount: order.amount,
                triggerPrice: order.trigger_price,
                currentPrice: currentPrice,
                slippage: order.slippage
            });
            // In a real implementation, you would:
            // 1. Calculate the exact trade parameters
            // 2. Execute the trade through your DEX integration
            // 3. Handle slippage and gas fees
            // 4. Update position amounts
            // 5. Record the trade in your transaction history
            // Placeholder for actual trade execution
            // await this.executeActualTrade(order, currentPrice);
        }
        catch (error) {
            console.error('Error executing trade:', error);
            throw error;
        }
    }
    /**
     * Update position TP/SL flags after order execution
     */
    async updatePositionFlags(positionId, orderType) {
        try {
            const updateField = orderType === 'take_profit' ? 'has_take_profit' : 'has_stop_loss';
            const { error } = await supabase
                .from('positions')
                .update({ [updateField]: false })
                .eq('id', positionId);
            if (error) {
                console.error('Error updating position flags:', error);
            }
        }
        catch (error) {
            console.error('Error updating position flags:', error);
        }
    }
    /**
     * Get monitoring status
     */
    getMonitoringStatus() {
        return {
            isMonitoring: this.isMonitoring,
            intervalMs: this.MONITORING_INTERVAL_MS
        };
    }
}
// Export singleton instance
export const priceMonitorService = new PriceMonitorService();
export default priceMonitorService;
