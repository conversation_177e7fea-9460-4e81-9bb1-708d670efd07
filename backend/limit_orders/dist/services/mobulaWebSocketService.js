import WebSocket from 'ws';
import { redis } from '../config/redis.js';
import logger from '../utils/logger.js';
import dotenv from 'dotenv';
dotenv.config();
export class MobulaWebSocketService {
    ws = null;
    reconnectInterval = null;
    isConnected = false;
    subscribedTokens = new Set();
    MOBULA_WS_URL = 'wss://production-feed.mobula.io';
    MOBULA_API_KEY = process.env.MOBULA_API_KEY;
    reconnectAttempts = 0;
    MAX_RECONNECT_ATTEMPTS = 10;
    RECONNECT_DELAY = 5000;
    priceUpdateCallbacks = new Map();
    constructor() {
        if (!this.MOBULA_API_KEY) {
            throw new Error('MOBULA_API_KEY is required in environment variables');
        }
    }
    async connect() {
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(this.MOBULA_WS_URL);
                this.ws.on('open', () => {
                    logger.info('Mobula WebSocket connected');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    // Resubscribe to all tokens after reconnection
                    if (this.subscribedTokens.size > 0) {
                        const tokens = Array.from(this.subscribedTokens);
                        this.subscribedTokens.clear();
                        this.subscribeToTokens(tokens);
                    }
                    resolve();
                });
                this.ws.on('message', (data) => {
                    try {
                        const priceData = JSON.parse(data.toString());
                        this.handlePriceUpdate(priceData);
                    }
                    catch (error) {
                        logger.error('Error parsing Mobula price data:', error);
                    }
                });
                this.ws.on('error', (error) => {
                    logger.error('Mobula WebSocket error:', error);
                    if (!this.isConnected) {
                        reject(error);
                    }
                });
                this.ws.on('close', (code, reason) => {
                    logger.warn(`Mobula WebSocket closed. Code: ${code}, Reason: ${reason}`);
                    this.isConnected = false;
                    this.handleReconnect();
                });
                this.ws.on('ping', () => {
                    if (this.ws?.readyState === WebSocket.OPEN) {
                        this.ws.pong();
                    }
                });
            }
            catch (error) {
                logger.error('Failed to create WebSocket connection:', error);
                reject(error);
            }
        });
    }
    handleReconnect() {
        if (this.reconnectAttempts >= this.MAX_RECONNECT_ATTEMPTS) {
            logger.error('Max reconnection attempts reached. Stopping reconnection.');
            return;
        }
        if (this.reconnectInterval) {
            clearTimeout(this.reconnectInterval);
        }
        this.reconnectInterval = setTimeout(() => {
            this.reconnectAttempts++;
            logger.info(`Attempting to reconnect to Mobula WebSocket (attempt ${this.reconnectAttempts}/${this.MAX_RECONNECT_ATTEMPTS})...`);
            this.connect().catch((error) => {
                logger.error('Reconnection failed:', error);
            });
        }, this.RECONNECT_DELAY);
    }
    subscribeToTokens(tokenAddresses) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            logger.error('WebSocket is not connected. Cannot subscribe to tokens.');
            return;
        }
        const newTokens = tokenAddresses.filter(addr => !this.subscribedTokens.has(addr));
        if (newTokens.length === 0) {
            return;
        }
        const subscribeMessage = {
            type: 'feed',
            authorization: this.MOBULA_API_KEY,
            kind: 'address',
            tokens: newTokens.map(address => ({
                blockchain: 'solana',
                address
            }))
        };
        try {
            this.ws.send(JSON.stringify(subscribeMessage));
            newTokens.forEach(token => this.subscribedTokens.add(token));
            logger.info(`Subscribed to ${newTokens.length} new token(s) on Mobula`);
        }
        catch (error) {
            logger.error('Error subscribing to tokens:', error);
        }
    }
    unsubscribeFromTokens(tokenAddresses) {
        // Mobula doesn't have explicit unsubscribe, so we just remove from our tracking
        tokenAddresses.forEach(token => {
            this.subscribedTokens.delete(token);
            this.priceUpdateCallbacks.delete(token);
        });
    }
    async handlePriceUpdate(priceData) {
        try {
            // Extract token address from baseID (remove "solana:solana|" prefix)
            const tokenAddress = priceData.baseID.replace('solana:solana|', '');
            const price = priceData.price;
            // Store price in Redis for quick access
            await redis.setex(`price:${tokenAddress}`, 60, // TTL: 60 seconds
            JSON.stringify({
                price,
                timestamp: priceData.timestamp,
                volume24h: priceData.volume24h,
                priceUSD: priceData.extra.priceUSD
            }));
            // Publish price update to Redis pub/sub for real-time updates
            await redis.publish('price-updates', JSON.stringify({
                tokenAddress,
                price,
                timestamp: priceData.timestamp
            }));
            // Execute any registered callbacks
            const callback = this.priceUpdateCallbacks.get(tokenAddress);
            if (callback) {
                callback(price);
            }
            logger.debug(`Price update for ${tokenAddress}: $${price}`);
        }
        catch (error) {
            logger.error('Error handling price update:', error);
        }
    }
    onPriceUpdate(tokenAddress, callback) {
        this.priceUpdateCallbacks.set(tokenAddress, callback);
    }
    async getCurrentPrice(tokenAddress) {
        try {
            const cachedPrice = await redis.get(`price:${tokenAddress}`);
            if (cachedPrice) {
                const priceData = JSON.parse(cachedPrice);
                return priceData.price;
            }
            return null;
        }
        catch (error) {
            logger.error('Error getting current price from cache:', error);
            return null;
        }
    }
    disconnect() {
        if (this.reconnectInterval) {
            clearTimeout(this.reconnectInterval);
            this.reconnectInterval = null;
        }
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.isConnected = false;
        this.subscribedTokens.clear();
        this.priceUpdateCallbacks.clear();
        logger.info('Mobula WebSocket disconnected');
    }
    isWebSocketConnected() {
        return this.isConnected && this.ws?.readyState === WebSocket.OPEN;
    }
}
// Export singleton instance
export const mobulaWebSocketService = new MobulaWebSocketService();
