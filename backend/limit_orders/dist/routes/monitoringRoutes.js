import { Router } from 'express';
import { enhancedPriceMonitorService } from '../services/enhancedPriceMonitorService.js';
import { mobulaWebSocketService } from '../services/mobulaWebSocketService.js';
import { redis } from '../config/redis.js';
import logger from '../utils/logger.js';
const router = Router();
/**
 * Get monitoring status
 */
router.get('/status', async (req, res) => {
    try {
        const stats = enhancedPriceMonitorService.getMonitoringStats();
        const redisStatus = redis.status === 'ready' ? 'connected' : 'disconnected';
        res.json({
            success: true,
            data: {
                ...stats,
                redis: redisStatus
            }
        });
    }
    catch (error) {
        logger.error('Error getting monitoring status:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get monitoring status'
        });
    }
});
/**
 * Get current prices for monitored tokens
 */
router.get('/prices', async (req, res) => {
    try {
        const keys = await redis.keys('price:*');
        const prices = {};
        for (const key of keys) {
            const tokenAddress = key.replace('price:', '');
            const priceData = await redis.get(key);
            if (priceData) {
                prices[tokenAddress] = JSON.parse(priceData);
            }
        }
        res.json({
            success: true,
            data: prices,
            count: Object.keys(prices).length
        });
    }
    catch (error) {
        logger.error('Error getting prices:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get prices'
        });
    }
});
/**
 * Get current price for a specific token
 */
router.get('/price/:tokenAddress', async (req, res) => {
    try {
        const { tokenAddress } = req.params;
        const price = await mobulaWebSocketService.getCurrentPrice(tokenAddress);
        if (price === null) {
            res.status(404).json({
                success: false,
                error: 'Price not found for token'
            });
            return;
        }
        res.json({
            success: true,
            data: {
                tokenAddress,
                price
            }
        });
    }
    catch (error) {
        logger.error('Error getting token price:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get token price'
        });
    }
});
/**
 * Manually refresh orders for a token
 */
router.post('/refresh/:tokenAddress', async (req, res) => {
    try {
        const { tokenAddress } = req.params;
        await enhancedPriceMonitorService.refreshTokenOrders(tokenAddress);
        res.json({
            success: true,
            message: `Orders refreshed for token ${tokenAddress}`
        });
    }
    catch (error) {
        logger.error('Error refreshing token orders:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to refresh token orders'
        });
    }
});
/**
 * Get queue statistics
 */
router.get('/queue/stats', async (req, res) => {
    try {
        const queueName = 'order-execution';
        const [waiting, active, completed, failed] = await Promise.all([
            redis.llen(`bull:${queueName}:wait`),
            redis.llen(`bull:${queueName}:active`),
            redis.llen(`bull:${queueName}:completed`),
            redis.llen(`bull:${queueName}:failed`)
        ]);
        res.json({
            success: true,
            data: {
                queueName,
                waiting,
                active,
                completed,
                failed,
                total: waiting + active + completed + failed
            }
        });
    }
    catch (error) {
        logger.error('Error getting queue stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get queue statistics'
        });
    }
});
export default router;
