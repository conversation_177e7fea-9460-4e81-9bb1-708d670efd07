import { Router } from 'express';
import { enhancedTpslService } from '../services/enhancedTpslService.js';
import { supabase } from '../config/supabase.js';
import { logger } from '../utils/logger.js';
const router = Router();
// Rate limiting configuration (implement with express-rate-limit if needed)
const RATE_LIMITS = {
    CREATE_ORDER: { windowMs: 60000, max: 10 }, // 10 orders per minute
    GET_ORDERS: { windowMs: 60000, max: 100 }, // 100 requests per minute
    CANCEL_ORDER: { windowMs: 60000, max: 20 }, // 20 cancellations per minute
};
// Middleware for request validation
const validateRequest = (req, res, next) => {
    const contentType = req.get('Content-Type');
    if (req.method === 'POST' && (!contentType || !contentType.includes('application/json'))) {
        return res.status(400).json({
            success: false,
            error: 'Content-Type must be application/json',
            error_code: 'INVALID_CONTENT_TYPE'
        });
    }
    // Basic security headers
    res.header('X-Content-Type-Options', 'nosniff');
    res.header('X-Frame-Options', 'DENY');
    res.header('X-XSS-Protection', '1; mode=block');
    next();
};
// Apply validation middleware to all routes
router.use(validateRequest);
/**
 * @route POST /orders
 * @description Create a new TPSL order with enhanced validation
 */
router.post('/orders', async (req, res) => {
    const startTime = Date.now();
    try {
        logger.info('Enhanced TPSL order creation request', {
            userAgent: req.get('User-Agent'),
            ip: req.ip,
            hasBody: !!req.body
        });
        // Validate required fields
        const requiredFields = [
            'user_id', 'wallet_id', 'wallet_address', 'token_address',
            'token_name', 'token_symbol', 'pool_address', 'current_price', 'amount'
        ];
        const missingFields = requiredFields.filter(field => !req.body[field]);
        if (missingFields.length > 0) {
            return res.status(400).json({
                success: false,
                error: `Missing required fields: ${missingFields.join(', ')}`,
                error_code: 'MISSING_REQUIRED_FIELDS'
            });
        }
        // Validate at least one TP or SL is provided
        if (!req.body.tp_percentage && !req.body.sl_percentage) {
            return res.status(400).json({
                success: false,
                error: 'At least one of tp_percentage or sl_percentage must be provided',
                error_code: 'NO_TP_SL_SPECIFIED'
            });
        }
        const result = await enhancedTpslService.createOrder(req.body);
        const responseTime = Date.now() - startTime;
        logger.info('Enhanced TPSL order creation completed', {
            success: result.success,
            responseTime: `${responseTime}ms`,
            orderId: result.data?.order_id
        });
        const statusCode = result.success ? 201 : 400;
        res.status(statusCode).json(result);
    }
    catch (error) {
        const responseTime = Date.now() - startTime;
        logger.error('Enhanced TPSL order creation failed', {
            error: error.message,
            stack: error.stack,
            responseTime: `${responseTime}ms`
        });
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            error_code: 'INTERNAL_ERROR'
        });
    }
});
/**
 * @route GET /orders/:orderId
 * @description Get a specific TPSL order by ID
 */
router.get('/orders/:orderId', async (req, res) => {
    try {
        const { orderId } = req.params;
        const { user_id } = req.query;
        if (!user_id || typeof user_id !== 'string') {
            return res.status(400).json({
                success: false,
                error: 'user_id query parameter is required',
                error_code: 'MISSING_USER_ID'
            });
        }
        // UUID validation
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(orderId)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid order ID format',
                error_code: 'INVALID_ORDER_ID'
            });
        }
        const result = await enhancedTpslService.getOrderById(user_id, orderId);
        const statusCode = result.success ? 200 : (result.error_code === 'ORDER_NOT_FOUND' ? 404 : 400);
        res.status(statusCode).json(result);
    }
    catch (error) {
        logger.error('Failed to get TPSL order', {
            error: error.message,
            orderId: req.params.orderId
        });
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            error_code: 'INTERNAL_ERROR'
        });
    }
});
/**
 * @route GET /orders
 * @description Get all TPSL orders for a user with pagination
 */
router.get('/orders', async (req, res) => {
    try {
        const { user_id, status, limit = '50', offset = '0' } = req.query;
        if (!user_id || typeof user_id !== 'string') {
            return res.status(400).json({
                success: false,
                error: 'user_id query parameter is required',
                error_code: 'MISSING_USER_ID'
            });
        }
        // Validate pagination parameters
        const limitNum = parseInt(limit, 10);
        const offsetNum = parseInt(offset, 10);
        if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
            return res.status(400).json({
                success: false,
                error: 'limit must be between 1 and 100',
                error_code: 'INVALID_LIMIT'
            });
        }
        if (isNaN(offsetNum) || offsetNum < 0) {
            return res.status(400).json({
                success: false,
                error: 'offset must be 0 or greater',
                error_code: 'INVALID_OFFSET'
            });
        }
        // Validate status if provided
        if (status && !['active', 'executed', 'cancelled'].includes(status)) {
            return res.status(400).json({
                success: false,
                error: 'status must be one of: active, executed, cancelled',
                error_code: 'INVALID_STATUS'
            });
        }
        const result = await enhancedTpslService.getUserOrders(user_id, status, limitNum, offsetNum);
        res.status(result.success ? 200 : 400).json(result);
    }
    catch (error) {
        logger.error('Failed to get user TPSL orders', {
            error: error.message,
            userId: typeof req.query.user_id === 'string' ? req.query.user_id.substring(0, 20) + '...' : 'unknown'
        });
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            error_code: 'INTERNAL_ERROR'
        });
    }
});
/**
 * @route POST /orders/:orderId/cancel
 * @description Cancel a TPSL order
 */
router.post('/orders/:orderId/cancel', async (req, res) => {
    try {
        const { orderId } = req.params;
        const { user_id } = req.body;
        if (!user_id || typeof user_id !== 'string') {
            return res.status(400).json({
                success: false,
                error: 'user_id is required in request body',
                error_code: 'MISSING_USER_ID'
            });
        }
        // UUID validation
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(orderId)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid order ID format',
                error_code: 'INVALID_ORDER_ID'
            });
        }
        const result = await enhancedTpslService.cancelOrder(user_id, orderId);
        const statusCode = result.success ? 200 : (result.error_code === 'ORDER_NOT_FOUND' ? 404 : 400);
        res.status(statusCode).json(result);
    }
    catch (error) {
        logger.error('Failed to cancel TPSL order', {
            error: error.message,
            orderId: req.params.orderId
        });
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            error_code: 'INTERNAL_ERROR'
        });
    }
});
/**
 * @route GET /health
 * @description Get system health status
 */
router.get('/health', async (req, res) => {
    try {
        const health = await enhancedTpslService.getSystemHealth();
        const statusCode = health.status === 'healthy' ? 200 :
            health.status === 'degraded' ? 200 : 503;
        res.status(statusCode).json({
            success: true,
            data: health
        });
    }
    catch (error) {
        logger.error('Failed to get system health', {
            error: error.message
        });
        res.status(503).json({
            success: false,
            error: 'Health check failed',
            error_code: 'HEALTH_CHECK_FAILED'
        });
    }
});
/**
 * @route POST /orders/:orderId/remove-tp
 * @description Remove Take Profit from an order
 */
router.post('/orders/:orderId/remove-tp', async (req, res) => {
    try {
        const { orderId } = req.params;
        const { user_id } = req.body;
        if (!user_id) {
            return res.status(400).json({
                success: false,
                error: 'user_id is required',
                error_code: 'MISSING_USER_ID'
            });
        }
        // Update TP orders to cancelled status
        const { error } = await supabase
            .from('tp_orders')
            .update({ status: 'cancelled', updated_at: new Date().toISOString() })
            .eq('tpsl_order_id', orderId)
            .eq('status', 'active');
        if (error) {
            return res.status(400).json({
                success: false,
                error: 'Failed to remove take profit',
                error_code: 'REMOVE_TP_FAILED'
            });
        }
        res.status(200).json({
            success: true,
            message: 'Take profit removed successfully'
        });
    }
    catch (error) {
        logger.error('Failed to remove take profit', {
            error: error.message,
            orderId: req.params.orderId
        });
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            error_code: 'INTERNAL_ERROR'
        });
    }
});
/**
 * @route POST /orders/:orderId/remove-sl
 * @description Remove Stop Loss from an order
 */
router.post('/orders/:orderId/remove-sl', async (req, res) => {
    try {
        const { orderId } = req.params;
        const { user_id } = req.body;
        if (!user_id) {
            return res.status(400).json({
                success: false,
                error: 'user_id is required',
                error_code: 'MISSING_USER_ID'
            });
        }
        // Update SL orders to cancelled status
        const { error } = await supabase
            .from('sl_orders')
            .update({ status: 'cancelled', updated_at: new Date().toISOString() })
            .eq('tpsl_order_id', orderId)
            .eq('status', 'active');
        if (error) {
            return res.status(400).json({
                success: false,
                error: 'Failed to remove stop loss',
                error_code: 'REMOVE_SL_FAILED'
            });
        }
        res.status(200).json({
            success: true,
            message: 'Stop loss removed successfully'
        });
    }
    catch (error) {
        logger.error('Failed to remove stop loss', {
            error: error.message,
            orderId: req.params.orderId
        });
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            error_code: 'INTERNAL_ERROR'
        });
    }
});
export default router;
