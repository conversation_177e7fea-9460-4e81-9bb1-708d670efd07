import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';
import limitOrderRoutes from './routes/limitOrderRoutes.js';
import simpleTpslRoutes from './routes/simpleTpslRoutes.js';
import enhancedTpslRoutes from './routes/enhancedTpslRoutes.js';
import monitoringRoutes from './routes/monitoringRoutes.js';
import feeRoutes from './routes/feeRoutes.js';
import { logger } from './utils/logger.js';
import { enhancedPriceMonitorService } from './services/enhancedPriceMonitorService.js';
import { orderExecutionWorker } from './workers/orderExecutionWorker.js';
import { redis } from './config/redis.js';
// Load environment variables
dotenv.config();
// Create Express app
const app = express();
const PORT = process.env.PORT || 5002;
// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // CORS support
app.use(express.json()); // JSON body parser
app.use(morgan('dev')); // Request logging
// Routes
app.use('/api/limit-orders', limitOrderRoutes);
app.use('/api/simple-tpsl', simpleTpslRoutes); // Legacy compatibility
app.use('/api/enhanced-tpsl', enhancedTpslRoutes); // New enhanced API
app.use('/api/monitoring', monitoringRoutes);
app.use('/api/fees', feeRoutes); // Dynamic fee management
// Health check endpoint
app.get('/health', async (_req, res) => {
    const enhancedStatus = enhancedPriceMonitorService.getMonitoringStats();
    res.json({
        status: 'ok',
        service: 'limit-orders-service',
        timestamp: new Date().toISOString(),
        priceMonitoring: enhancedStatus,
        redis: redis.status === 'ready' ? 'connected' : 'disconnected'
    });
});
// Error handling
app.use(notFoundHandler);
app.use(errorHandler);
// Price monitoring service is available but not auto-started
// Use priceMonitorService.startMonitoring() manually if needed
// Start server
app.listen(PORT, async () => {
    logger.info(`🚀 Limit Orders Service running on port ${PORT}`);
    logger.info(`📝 API Documentation: http://localhost:${PORT}/api-docs`);
    logger.info(`🔍 Health Check: http://localhost:${PORT}/health`);
    try {
        // Start enhanced price monitoring service
        await enhancedPriceMonitorService.start();
        logger.info('✅ Enhanced Price Monitoring Service started successfully');
        // Start order execution worker
        await orderExecutionWorker.start();
        logger.info('✅ Order Execution Worker started successfully');
    }
    catch (error) {
        logger.error('Failed to start services:', error);
    }
});
// Handle graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('SIGTERM received. Shutting down gracefully...');
    enhancedPriceMonitorService.stop();
    await orderExecutionWorker.stop();
    await redis.quit();
    process.exit(0);
});
process.on('SIGINT', async () => {
    logger.info('SIGINT received. Shutting down gracefully...');
    enhancedPriceMonitorService.stop();
    await orderExecutionWorker.stop();
    await redis.quit();
    process.exit(0);
});
export default app;
