import { supabase } from '../config/supabase.js';
import { logger } from '../utils/logger.js';
async function cleanupTpslOrders() {
    try {
        logger.info('Starting cleanup of TPSL orders without txn data...');
        // First, let's see how many orders we have without txn
        const { data: ordersWithoutTxn, error: fetchError } = await supabase
            .from('tpsl_orders')
            .select('id, user_id, token_symbol, status, created_at')
            .is('txn', null);
        if (fetchError) {
            logger.error('Error fetching TPSL orders without txn:', fetchError);
            return;
        }
        if (!ordersWithoutTxn || ordersWithoutTxn.length === 0) {
            logger.info('No TPSL orders found without txn data');
            return;
        }
        logger.info(`Found ${ordersWithoutTxn.length} TPSL orders without txn data`);
        // Delete orders without txn data
        const { error: deleteError } = await supabase
            .from('tpsl_orders')
            .delete()
            .is('txn', null);
        if (deleteError) {
            logger.error('Error deleting TPSL orders without txn:', deleteError);
            return;
        }
        logger.info(`Successfully deleted ${ordersWithoutTxn.length} TPSL orders without txn data`);
        // Log details of deleted orders for audit
        ordersWithoutTxn.forEach(order => {
            logger.info('Deleted order:', {
                id: order.id,
                user_id: order.user_id,
                token_symbol: order.token_symbol,
                status: order.status,
                created_at: order.created_at
            });
        });
    }
    catch (error) {
        logger.error('Unexpected error during cleanup:', error);
    }
}
// Run the cleanup
cleanupTpslOrders().then(() => {
    logger.info('Cleanup completed');
    process.exit(0);
}).catch(error => {
    logger.error('Cleanup failed:', error);
    process.exit(1);
});
