import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
// Load environment variables
dotenv.config();
// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_KEY || '';
// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseKey);
// Set user context for Row Level Security
export async function setUserContext(userId) {
    await supabase.auth.setSession({
        access_token: userId,
        refresh_token: '',
    });
}
