import { Redis } from 'ioredis';
import { Queue } from 'bullmq';
import logger from '../utils/logger.js';
const redisConfig = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        return delay;
    }
};
export const redis = new Redis(redisConfig);
export const orderExecutionQueue = new Queue('order-execution', {
    connection: redisConfig
});
redis.on('connect', () => {
    logger.info('Redis client connected');
});
redis.on('error', (err) => {
    logger.error('Redis client error:', err);
});
redis.on('ready', () => {
    logger.info('Redis client ready');
});
