## Web Search Rules & Perplexity Model Selection

### Available Perplexity Models:
- **sonar-pro** (Default): General purpose searches and quick lookups
- **sonar-reasoning-pro**: Complex reasoning, analysis, and problem-solving tasks
- **sonar-deep-research**: In-depth research requiring comprehensive information gathering

### Model Usage Guidelines:
1. **Use sonar-pro (<PERSON><PERSON>ult)** for:
   - Quick fact-checking and verification
   - Simple technical documentation lookups
   - General web searches
   - Current news and events
   - Basic market information

2. **Use sonar-reasoning-pro** for:
   - Complex technical problem solving
   - Analyzing multiple sources for patterns
   - Comparative analysis tasks
   - Logic-based queries requiring step-by-step reasoning
   - Debugging complex issues with multiple potential causes

3. **Use sonar-deep-research** for:
   - Comprehensive market research and trends analysis
   - In-depth technical architecture research
   - Gathering extensive documentation across multiple sources
   - Creating detailed reports requiring thorough investigation
   - Research tasks requiring synthesis of many information sources

### General Perplexity Rules:
1. **ALWAYS use Perplexity MCP** (mcp__perplexity-ask__perplexity_ask) for any web searches
2. **DO NOT use WebSearch or WebFetch** for general web searches - only use these for specific URLs provided by the user
3. When user asks to search, research, or find information online, immediately use Perplexity MCP
4. Structure Perplexity queries clearly and specifically for best results
5. Select the appropriate model based on task complexity and depth requirements

## MCP Tool Usage Guidelines

### Context7 MCP (mcp__context7__)
**Purpose:** Get up-to-date documentation for libraries and frameworks

**When to use:**
- User asks about specific library usage (React, Vue, Next.js, etc.)
- Need latest API documentation or code examples
- Troubleshooting library-specific issues
- Learning new framework features

**Usage pattern:**
1. First call `resolve-library-id` with the library name
2. Then call `get-library-docs` with the returned ID
3. Can specify topics for focused documentation

### Sequential Thinking MCP (mcp__sequential-thinking__)
**Purpose:** Complex problem-solving through dynamic reasoning

**When to use:**
- Debugging complex issues with multiple potential causes
- Designing system architectures
- Planning multi-step implementations
- Algorithm optimization problems
- Any task requiring careful analysis with potential revisions

**Key features:**
- Adjustable thinking steps
- Can revise and branch thoughts
- Hypothesis generation and verification
- Maintains context across steps

### Supabase MCP (mcp__supabase__)
**Purpose:** Direct interaction with Supabase projects and databases

**When to use:**
- Creating/managing Supabase projects
- Database operations (tables, migrations, queries)
- Deploying edge functions
- Checking logs for debugging
- Getting project configuration (URLs, keys)
- Searching Supabase documentation

**Key operations:**
- Project management: create, pause, restore, list
- Database: execute SQL, apply migrations, list tables
- Development: create/manage branches, deploy functions
- Monitoring: get logs, check advisors for security/performance

**Important:** Always confirm costs before creating projects/branches

##Standard Workflow 
1. First think through the problem, read the codebase for relevant files, and write a plan to tasks/todo.md.
2. The plan should have a list of todo items that you can check off as you complete them
3. Before you begin working, check in with me and I will verify the plan.
4. Then, begin working on the todo items, marking them as complete as you go.
5. Please every step of the way just give me a high level explanation of what changes you made
6. Make every task and code change you do as simple as possible. We want to avoid making any massive or complex changes. Every change should impact as little code as possible. Everything is about simplicity.
7. Finally, add a review section to the todo.md file with a summary of the changes you made and any other relevant information.