# Production Docker Compose - Pull and Deploy from Docker Hub
# This file pulls pre-built images from Docker Hub and deploys them in production

services:
  # Traefik reverse proxy with automatic SSL
  traefik:
    image: traefik:v2.10
    container_name: redfyn-traefik-prod
    restart: unless-stopped
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --certificatesresolvers.letsencrypt.acme.httpchallenge=true
      - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
      - --entrypoints.web.http.redirections.entrypoint.to=websecure
      - --entrypoints.web.http.redirections.entrypoint.scheme=https
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-ssl-certs-prod:/letsencrypt
    networks:
      - redfyn-prod
  # Frontend Service
  frontend:
    image: prasanthats/redfyn:spot_frontend
    container_name: redfyn-frontend-prod
    environment:
      - NODE_ENV=production
    networks:
      - redfyn-prod
    restart: unless-stopped
    depends_on:
      - spot-backend
      - solana
      - trading-panel
      - limit-orders
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`redfyn.crypfi.io`)"
      - "traefik.http.routers.frontend.priority=1"
      - "traefik.http.routers.frontend.entrypoints=websecure"
      - "traefik.http.routers.frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"

  # Spot Backend Service
  spot-backend:
    image: prasanthats/redfyn:spot_backend
    container_name: redfyn-spot-backend-prod
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - PORT=5001
      - REDIS_URL=redis://redis:6379
    env_file:
      - ./env/.env.backend
    networks:
      - redfyn-prod
    restart: unless-stopped
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`redfyn.crypfi.io`) && (PathPrefix(`/api/home`) || PathPrefix(`/api/wallet`) || PathPrefix(`/api/activity`) || PathPrefix(`/api/notifications`) || PathPrefix(`/api/performance`) || PathPrefix(`/api/websocket`) || PathPrefix(`/socket.io/`))"
      - "traefik.http.routers.backend.priority=70"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.backend.loadbalancer.server.port=5001"

  # Solana Service
  solana:
    image: prasanthats/redfyn:solana
    container_name: redfyn-solana-prod
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - SOLANA_NETWORK=mainnet
      - PORT=6001
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    env_file:
      - ./env/.env.solana
    networks:
      - redfyn-prod
    restart: unless-stopped
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.solana.rule=Host(`redfyn.crypfi.io`) && PathPrefix(`/solana-api`)"
      - "traefik.http.routers.solana.entrypoints=websecure"
      - "traefik.http.routers.solana.tls.certresolver=letsencrypt"
      - "traefik.http.services.solana.loadbalancer.server.port=6001"
      - "traefik.http.middlewares.solana-stripprefix.stripprefix.prefixes=/solana-api"
      - "traefik.http.middlewares.solana-addprefix.addprefix.prefix=/api"
      - "traefik.http.routers.solana.middlewares=solana-stripprefix,solana-addprefix"

  # Trading Panel Service
  trading-panel:
    image: prasanthats/redfyn:trading_panel
    container_name: redfyn-trading-panel-prod
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - SOLANA_NETWORK=mainnet
      - PORT=5003
    env_file:
      - ./env/.env.trading_panel
    networks:
      - redfyn-prod
    restart: unless-stopped
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      # Main router for direct API paths
      - "traefik.http.routers.trading-panel.rule=Host(`redfyn.crypfi.io`) && PathPrefix(`/api/trading-panel`)"
      - "traefik.http.routers.trading-panel.priority=100"
      - "traefik.http.routers.trading-panel.entrypoints=websecure"
      - "traefik.http.routers.trading-panel.tls.certresolver=letsencrypt"
      - "traefik.http.services.trading-panel.loadbalancer.server.port=5003"
      # Separate router for /trading-panel-ws path that needs rewriting
      - "traefik.http.routers.trading-panel-ws.rule=Host(`redfyn.crypfi.io`) && PathPrefix(`/trading-panel-ws`)"
      - "traefik.http.routers.trading-panel-ws.priority=101"
      - "traefik.http.routers.trading-panel-ws.entrypoints=websecure"
      - "traefik.http.routers.trading-panel-ws.tls.certresolver=letsencrypt"
      - "traefik.http.routers.trading-panel-ws.service=trading-panel"
      - "traefik.http.middlewares.trading-panel-ws-stripprefix.stripprefix.prefixes=/trading-panel-ws"
      - "traefik.http.middlewares.trading-panel-ws-addprefix.addprefix.prefix=/api"
      # WebSocket configuration
      - "traefik.http.middlewares.trading-panel-ws-headers.headers.customrequestheaders.X-Forwarded-Proto=https"
      - "traefik.http.middlewares.trading-panel-ws-headers.headers.customrequestheaders.X-Forwarded-Host=redfyn.crypfi.io"
      - "traefik.http.routers.trading-panel-ws.middlewares=trading-panel-ws-stripprefix,trading-panel-ws-addprefix,trading-panel-ws-headers"

  # Limit Orders Service
  limit-orders:
    image: prasanthats/redfyn:limit_orders
    container_name: redfyn-limit-orders-prod
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - SOLANA_NETWORK=mainnet
      - PORT=5002
    env_file:
      - ./env/.env.limit_orders
    networks:
      - redfyn-prod
    restart: unless-stopped
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      # Main router for direct API paths
      - "traefik.http.routers.limit-orders.rule=Host(`redfyn.crypfi.io`) && (PathPrefix(`/api/limit-orders`) || PathPrefix(`/api/fees`) || PathPrefix(`/api/simple-tpsl`) || PathPrefix(`/api/enhanced-tpsl`) || PathPrefix(`/api/monitoring`) || PathPrefix(`/health`))"
      - "traefik.http.routers.limit-orders.priority=100"
      - "traefik.http.routers.limit-orders.entrypoints=websecure"
      - "traefik.http.routers.limit-orders.tls.certresolver=letsencrypt"
      - "traefik.http.services.limit-orders.loadbalancer.server.port=5002"
      # Separate router for /limit-orders-api path that needs rewriting
      - "traefik.http.routers.limit-orders-api.rule=Host(`redfyn.crypfi.io`) && PathPrefix(`/limit-orders-api`)"
      - "traefik.http.routers.limit-orders-api.priority=101"
      - "traefik.http.routers.limit-orders-api.entrypoints=websecure"
      - "traefik.http.routers.limit-orders-api.tls.certresolver=letsencrypt"
      - "traefik.http.routers.limit-orders-api.service=limit-orders"
      - "traefik.http.middlewares.limit-orders-stripprefix.stripprefix.prefixes=/limit-orders-api"
      - "traefik.http.middlewares.limit-orders-addprefix.addprefix.prefix=/api"
      - "traefik.http.routers.limit-orders-api.middlewares=limit-orders-stripprefix,limit-orders-addprefix"

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: redfyn-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis-prod-data:/data
    networks:
      - redfyn-prod
    command: redis-server --appendonly yes
    restart: unless-stopped

networks:
  redfyn-prod:
    name: redfyn-prod-network
    driver: bridge

volumes:
  redis-prod-data:
    name: redfyn-redis-prod-data
  traefik-ssl-certs-prod:
    name: redfyn-traefik-ssl-certs-prod