{"permissions": {"allow": ["mcp__supabase-mcp-server__execute_sql", "Bash(grep:*)", "Bash(npm run build:*)", "Bash(find:*)", "Bash(PGPASSWORD='Visionboard1#' psql -h aws-0-ap-south-1.pooler.supabase.com -p 5432 -U postgres.vwwocahlnpcnphisodhx -d postgres -f database/create_simple_tpsl.sql)", "<PERSON><PERSON>(curl:*)", "mcp__supabase-mcp-server__list_projects", "mcp__supabase-mcp-server__list_tables", "mcp__supabase-mcp-server__apply_migration", "Bash(npm install:*)", "Bash(ls:*)", "<PERSON><PERSON>(timeout:*)", "Bash(rg:*)", "Bash(npm run typecheck:*)", "mcp__ide__getDiagnostics", "mcp__supabase-mcp-server__get_logs", "mcp__ide__executeCode", "mcp__perplexity-ask__perplexity_ask"], "deny": []}}