# Removed version as it's obsolete in newer Docker Compose

# Production Build Docker Compose - Builds images locally and pushes to Docker Hub
services:
  # Frontend Service
  frontend:
    build:
      context: ./spot_frontend
      dockerfile: Dockerfile
    image: prasanthats/redfyn:spot_frontend
    container_name: redfyn-frontend-build
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=https://redfyn.crypfi.io/api
      - REACT_APP_SOLANA_API_URL=https://redfyn.crypfi.io/solana-api
      - REACT_APP_TRADING_API_URL=https://redfyn.crypfi.io/api/trading-panel-api
      - REACT_APP_LIMIT_ORDERS_API_URL=https://redfyn.crypfi.io/api/limit-orders-api
    networks:
      - redfyn-build

  # Spot Backend Service
  spot-backend:
    build:
      context: ./backend/spot_backend
      dockerfile: Dockerfile
    image: prasanthats/redfyn:spot_backend
    container_name: redfyn-spot-backend-build
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=production
      - PORT=5001
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - LOG_LEVEL=info
    depends_on:
      - redis
    networks:
      - redfyn-build
    volumes:
      - ./backend/spot_backend/logs:/app/logs

  # Solana Service
  solana:
    build:
      context: ./backend/solana
      dockerfile: Dockerfile
    image: prasanthats/redfyn:solana
    container_name: redfyn-solana-build
    ports:
      - "6001:6001"
    environment:
      - NODE_ENV=production
      - PORT=6001
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SOLANA_NETWORK=mainnet
      - LOG_LEVEL=info
    depends_on:
      - redis
    networks:
      - redfyn-build
    volumes:
      - ./backend/solana/logs:/app/logs

  # Trading Panel Service
  trading-panel:
    build:
      context: ./backend/trading_panel
      dockerfile: Dockerfile
    image: prasanthats/redfyn:trading_panel
    container_name: redfyn-trading-panel-build
    ports:
      - "5003:5003"
    environment:
      - NODE_ENV=production
      - PORT=5003
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - LOG_LEVEL=info
    depends_on:
      - redis
    networks:
      - redfyn-build
    volumes:
      - ./backend/trading_panel/logs:/app/logs

  # Limit Orders Service
  limit-orders:
    build:
      context: ./backend/limit_orders
      dockerfile: Dockerfile
    image: prasanthats/redfyn:limit_orders
    container_name: redfyn-limit-orders-build
    ports:
      - "5002:5002"
    environment:
      - NODE_ENV=production
      - PORT=5002
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SOLANA_NETWORK=mainnet
      - LOG_LEVEL=info
    depends_on:
      - redis
    networks:
      - redfyn-build
    volumes:
      - ./backend/limit_orders/logs:/app/logs

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: redfyn-redis-build
    ports:
      - "6379:6379"
    volumes:
      - redis-build-data:/data
    networks:
      - redfyn-build
    command: redis-server --appendonly yes

networks:
  redfyn-build:
    name: redfyn-build-network
    driver: bridge

volumes:
  redis-build-data:
    name: redfyn-redis-build-data