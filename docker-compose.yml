# Removed version as it's obsolete in newer Docker Compose

# Production Docker Compose - Pulls images from Docker Hub
services:
  # Traefik reverse proxy with automatic SSL
  traefik:
    image: traefik:v2.10
    container_name: redfyn-traefik
    restart: unless-stopped
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --certificatesresolvers.letsencrypt.acme.httpchallenge=true
      - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-ssl-certs:/letsencrypt
    networks:
      - app-network

  # Watchtower for automatic container updates
  watchtower:
    image: containrrr/watchtower
    container_name: redfyn-watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /root/.docker/config.json:/config.json
    command: --interval 300 --cleanup --label-enable
    environment:
      - TZ=UTC
      - WATCHTOWER_LABEL_ENABLE=true
    labels:
      - "com.centurylinklabs.watchtower.enable=false"
    networks:
      - app-network

  # Redis service
  redis:
    image: redis:7-alpine
    container_name: redfyn-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - app-network
    command: redis-server --appendonly yes --save 60 1000 --maxclients 10000
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # Frontend Service
  frontend:
    image: prasanthats/redfyn:spot_frontend
    container_name: redfyn-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    networks:
      - app-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`redfyn.crypfi.io`)"
      - "traefik.http.routers.frontend.entrypoints=websecure"
      - "traefik.http.routers.frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"
      - "com.centurylinklabs.watchtower.enable=true"

  # Solana Service
  solana:
    image: prasanthats/redfyn:solana
    container_name: redfyn-solana
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=6001
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CRYPTO_WORKER_POOL_SIZE=4
    env_file:
      - ./backend/solana/.env.production
    networks:
      - app-network
    depends_on:
      - redis
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:6001/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.solana.rule=Host(`redfyn.crypfi.io`) && PathPrefix(`/solana-api`)"
      - "traefik.http.routers.solana.priority=90"
      - "traefik.http.routers.solana.entrypoints=websecure"
      - "traefik.http.routers.solana.tls.certresolver=letsencrypt"
      - "traefik.http.services.solana.loadbalancer.server.port=5000"
      - "traefik.http.middlewares.solana-stripprefix.stripprefix.prefixes=/solana-api"
      - "traefik.http.routers.solana.middlewares=solana-stripprefix"
      - "com.centurylinklabs.watchtower.enable=true"

  # Spot Backend Service
  spot-backend:
    image: prasanthats/redfyn:spot_backend
    container_name: redfyn-spot-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5001
      - REDIS_URL=redis://redis:6379
    env_file:
      - ./backend/spot_backend/.env.production
    networks:
      - app-network
    depends_on:
      - redis
      - solana
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`redfyn.crypfi.io`) && (PathPrefix(`/api`) || PathPrefix(`/socket.io/`))"
      - "traefik.http.routers.backend.priority=70"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.backend.loadbalancer.server.port=5001"
      - "com.centurylinklabs.watchtower.enable=true"

  # Trading Panel Service
  trading-panel:
    image: prasanthats/redfyn:trading_panel
    container_name: redfyn-trading-panel
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5003
    env_file:
      - ./backend/trading_panel/.env.production
    networks:
      - app-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.trading-panel.rule=Host(`redfyn.crypfi.io`) && (PathPrefix(`/trading-panel-api`) || PathPrefix(`/trading-panel-ws`))"
      - "traefik.http.routers.trading-panel.priority=85"
      - "traefik.http.routers.trading-panel.entrypoints=websecure"
      - "traefik.http.routers.trading-panel.tls.certresolver=letsencrypt"
      - "traefik.http.services.trading-panel.loadbalancer.server.port=5003"
      - "com.centurylinklabs.watchtower.enable=true"

  # Limit Orders Service
  limit-orders:
    image: prasanthats/redfyn:limit_orders
    container_name: redfyn-limit-orders
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5002
    env_file:
      - ./backend/limit_orders/.env.production
    networks:
      - app-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.limit-orders.rule=Host(`redfyn.crypfi.io`) && PathPrefix(`/limit-orders-api`)"
      - "traefik.http.routers.limit-orders.priority=80"
      - "traefik.http.routers.limit-orders.entrypoints=websecure"
      - "traefik.http.routers.limit-orders.tls.certresolver=letsencrypt"
      - "traefik.http.services.limit-orders.loadbalancer.server.port=5002"
      - "com.centurylinklabs.watchtower.enable=true"

networks:
  app-network:
    name: redfyn-app-network
    driver: bridge

volumes:
  redis_data:
    name: redfyn-redis-data
  traefik-ssl-certs:
    name: redfyn-traefik-ssl-certs
