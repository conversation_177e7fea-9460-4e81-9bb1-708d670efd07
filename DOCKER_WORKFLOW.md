# Docker Workflow Guide

This guide explains the streamlined Docker workflow for the Redfyn Spot Trading Platform.

## Overview

The project now uses a simplified two-environment approach:

1. **Development Environment** - Builds images locally and pushes to Docker Hub
2. **Production Environment** - Pulls images from Docker Hub and runs them

## File Structure

### Docker Compose Files

- `docker-compose.dev.yml` - Development environment (builds images locally)
- `docker-compose.yml` - Production environment (pulls images from Docker Hub)

### Scripts

- `scripts/dev-build-push.sh` - Build all services and push to Docker Hub
- `scripts/prod-deploy.sh` - Pull images from Docker Hub and deploy to production

## Development Workflow

### Prerequisites

1. Docker and Docker Compose installed
2. Docker Hub account with access to `prasanthats/redfyn` repository
3. Environment files configured for each service

### Building and Pushing Images

```bash
# Build all services and push to Docker Hub
./scripts/dev-build-push.sh
```

This script will:
- Stop any running development containers
- Build all services locally using `docker-compose.dev.yml`
- Push the built images to Docker Hub with these tags:
  - `prasanthats/redfyn:spot_frontend`
  - `prasanthats/redfyn:spot_backend`
  - `prasanthats/redfyn:solana`
  - `prasanthats/redfyn:trading_panel`
  - `prasanthats/redfyn:limit_orders`

### Running Development Environment

```bash
# Start development environment (builds locally)
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop development environment
docker-compose -f docker-compose.dev.yml down
```

**Development URLs:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:5001
- Solana API: http://localhost:6001
- Trading Panel: http://localhost:5003
- Limit Orders: http://localhost:5002
- Redis: localhost:6379

## Production Workflow

### Prerequisites

1. Production server with Docker and Docker Compose
2. Environment files configured:
   - `./backend/solana/.env.production`
   - `./backend/spot_backend/.env.production`
   - `./backend/trading_panel/.env.production`
   - `./backend/limit_orders/.env.production`

### Deploying to Production

```bash
# Deploy latest images from Docker Hub
./scripts/prod-deploy.sh
```

This script will:
- Stop existing containers
- Pull latest images from Docker Hub
- Start all production services with SSL and reverse proxy
- Show service status

### Production Services

- **Frontend**: https://redfyn.crypfi.io
- **All APIs**: Available via Traefik reverse proxy with SSL
- **Traefik Dashboard**: http://localhost:8080
- **Automatic SSL**: Let's Encrypt certificates
- **Auto-updates**: Watchtower monitors for image updates

### Managing Production

```bash
# View all services status
docker-compose ps

# View logs for specific service
docker-compose logs -f [service_name]

# View logs for all services
docker-compose logs -f

# Stop all services
docker-compose down

# Restart specific service
docker-compose restart [service_name]
```

## Environment Configuration

### Development Environment Variables

Each service in development uses:
- `NODE_ENV=development`
- `LOG_LEVEL=debug`
- Local Redis connection
- Development-specific settings

### Production Environment Variables

Each service in production uses:
- `NODE_ENV=production`
- Production Redis connection
- SSL/TLS configurations
- Production-specific settings

## Docker Hub Images

All images are stored in the `prasanthats/redfyn` repository:

- `prasanthats/redfyn:spot_frontend` - React frontend with Nginx
- `prasanthats/redfyn:spot_backend` - Main backend API
- `prasanthats/redfyn:solana` - Solana blockchain service
- `prasanthats/redfyn:trading_panel` - Trading panel service
- `prasanthats/redfyn:limit_orders` - Limit orders service

## Security Notes

1. **Environment Files**: Never commit `.env.production` files to version control
2. **Docker Hub**: Ensure proper access controls on Docker Hub repository
3. **SSL Certificates**: Production uses Let's Encrypt for automatic SSL
4. **Secrets**: Use Docker secrets or environment variables for sensitive data

## Troubleshooting

### Common Issues

1. **Build Failures**: Check Dockerfile syntax and dependencies
2. **Push Failures**: Verify Docker Hub credentials and repository access
3. **Deployment Failures**: Check environment files and network connectivity
4. **SSL Issues**: Verify domain DNS and Let's Encrypt configuration

### Useful Commands

```bash
# Check Docker system info
docker system info

# Clean up unused images and containers
docker system prune -a

# View Docker Hub login status
docker info | grep Username

# Force rebuild without cache
docker-compose -f docker-compose.dev.yml build --no-cache

# Pull latest images manually
docker-compose pull
```

## Migration from Old Setup

The following files have been removed as they were redundant:

- `docker-compose.build.yml` - Replaced by `docker-compose.dev.yml`
- `deploy.sh` - Replaced by `scripts/prod-deploy.sh`
- `prepare-deployment.sh` - No longer needed
- Various other scripts in `/scripts/` - Consolidated into two main scripts

This streamlined approach provides:
- Clear separation between dev and prod environments
- Simplified deployment process
- Consistent image management
- Reduced complexity and maintenance overhead