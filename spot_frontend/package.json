{"name": "react-redfyn", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host localhost --port 4001 --strictPort", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@apollo/client": "^3.8.8", "@headlessui/react": "^2.2.1", "@privy-io/chains": "^0.0.2", "@privy-io/react-auth": "^2.17.1", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.0", "@supabase/supabase-js": "^2.49.4", "@wagmi/core": "~2.7.0", "abitype": "^1.0.8", "apexcharts": "^3.45.1", "axios": "^1.8.4", "bignumber.js": "^9.2.0", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "ethers": "^5.7.2", "graphql": "^16.8.1", "lightweight-charts": "^5.0.5", "lucide-react": "^0.293.0", "patch-package": "^7.0.2", "permissionless": "^0.2.45", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-loading-skeleton": "^3.5.0", "react-qr-code": "^2.0.15", "react-router-dom": "^6.20.1", "react-toastify": "^11.0.5", "react-window": "^1.8.11", "recharts": "^2.15.2", "siwe": "^3.0.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "viem": "^2.28.4", "wagmi": "^2.15.2"}, "devDependencies": {"@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "glob": "^11.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.8.3", "vite": "^5.0.0", "vite-plugin-node-polyfills": "^0.23.0"}}