import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { nodePolyfills } from 'vite-plugin-node-polyfills';

// Get production host from environment variable or fallback to hardcoded IP
const PRODUCTION_HOST = process.env.VITE_PRODUCTION_HOST || '*************';

export default defineConfig({
  plugins: [
    react({
      // Enable JSX processing for .js and .jsx files
      include: '**/*.{jsx,js,tsx,ts}',
    }),
    nodePolyfills()
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
    extensions: ['.mjs', '.js', '.jsx', '.ts', '.tsx', '.json']
  },
  optimizeDeps: {
    esbuildOptions: {
      loader: {
        '.js': 'jsx',
        '.ts': 'tsx',
      },
      define: {
        global: 'globalThis'
      }
    },
  },
  server: {
    port: 4001,
    strictPort: true, // Fail if port is already in use instead of trying another port
    open: false, // Disable auto-opening browser to avoid xdg-open error
    host: true, // Listen on all addresses
    allowedHosts: ['dev.crypfi.io', 'redfyn.crypfi.io','localhost', PRODUCTION_HOST],
    proxy: {
      // Order matters! More specific paths must come first
      
      // Fee API proxy (highest priority - most specific)
      '/api/fees': {
        target: process.env.NODE_ENV === 'production' ? `http://${PRODUCTION_HOST}:5002` : 'http://localhost:5002',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Fee API proxy request:', req.url, '-> target:', proxyReq.path);
          });
          proxy.on('error', (err, _req, _res) => {
            console.log('Fee API proxy error:', err);
          });
        }
      },

      // Simple TP/SL API proxy
      '/api/simple-tpsl': {
        target: process.env.NODE_ENV === 'production' ? `http://${PRODUCTION_HOST}:5002` : 'http://localhost:5002',
        changeOrigin: true,
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Simple TP/SL API proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },

      // Enhanced TP/SL API proxy
      '/api/enhanced-tpsl': {
        target: process.env.NODE_ENV === 'production' ? `http://${PRODUCTION_HOST}:5002` : 'http://localhost:5002',
        changeOrigin: true,
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Enhanced TP/SL API proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },

      // Limit Orders API proxy
      '/api/limit-orders': {
        target: process.env.NODE_ENV === 'production' ? `http://${PRODUCTION_HOST}:5002` : 'http://localhost:5002',
        changeOrigin: true,
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Limit Orders API proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },

      // Trading Panel API proxy
      '/api/trading-panel': {
        target: process.env.NODE_ENV === 'production' ? `http://${PRODUCTION_HOST}:5003` : 'http://localhost:5003',
        changeOrigin: true,
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Trading Panel API proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },

      // Solana API proxy
      '/solana-api': {
        target: process.env.NODE_ENV === 'production' ? `http://${PRODUCTION_HOST}:6001` : 'http://localhost:6001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/solana-api/, '/api'),
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Solana API proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },

      // Liquidity API proxy
      '/liquidity-api': {
        target: process.env.NODE_ENV === 'production' ? `http://${PRODUCTION_HOST}:3047` : 'http://localhost:3047',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/liquidity-api/, '/api'),
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Liquidity API proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },

      // Main API proxy (lowest priority - least specific)
      '/api': {
        target: process.env.NODE_ENV === 'production' ? `http://${PRODUCTION_HOST}:5001` : 'http://localhost:5001',
        changeOrigin: true,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Main API proxy error:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            proxyReq.setHeader('Origin', req.headers.host);
          });
        }
      },
      
      // Direct wallet API routes (for backward compatibility)
      '/wallet': {
        target: process.env.NODE_ENV === 'production' ? `http://${PRODUCTION_HOST}:5001` : 'http://localhost:5001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/wallet/, '/api/wallet'),
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Wallet API proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },

      // WebSocket proxy for Socket.IO
      '/socket.io': {
        target: process.env.NODE_ENV === 'production' ? `http://${PRODUCTION_HOST}:5001` : 'http://localhost:5001',
        changeOrigin: true,
        ws: true, // Enable WebSocket proxying
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('WebSocket proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('WebSocket proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },

      // Trading Panel WebSocket proxy
      '/api/trading-panel-ws': {
        target: process.env.NODE_ENV === 'production' ? `ws://${PRODUCTION_HOST}:5003` : 'ws://localhost:5003',
        changeOrigin: true,
        ws: true, // Enable WebSocket proxying
        rewrite: (path) => path.replace(/^\/api\/trading-panel-ws/, ''),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Trading Panel WebSocket proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Trading Panel WebSocket proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },




      // External API proxies
      '/external-api/coingecko': {
        target: 'https://api.coingecko.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/external-api\/coingecko/, ''),
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('CoinGecko API proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },

      '/external-api/mobula': {
        target: 'https://api.mobula.io',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/external-api\/mobula/, ''),
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Mobula API proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      }
    },
  },
  build: {
    rollupOptions: {
      external: [
        'rpc-websockets/dist/lib/client',
        'rpc-websockets/dist/lib/client/websocket.browser'
      ]
    }
  }
});