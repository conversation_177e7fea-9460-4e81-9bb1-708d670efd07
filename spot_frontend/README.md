# RedFyn Spot Frontend

A comprehensive decentralized exchange (DEX) aggregator frontend for trading cryptocurrencies across multiple blockchains with advanced features including multi-chain support, smart wallet integration, and real-time trading capabilities.

## 🚀 Features

### Core Trading Features
- **Multi-Chain Support**: Ethereum, BSC, Solana, and Polygon networks
- **DEX Aggregation**: Integrated with PancakeSwap, Uniswap, SushiSwap, Raydium, Meteora, and more
- **Real-Time Quotes**: Live price quotes from multiple liquidity sources
- **Smart Routing**: Automatic best price discovery across DEXs
- **Slippage Protection**: Configurable slippage tolerance and price impact warnings

### Wallet & Authentication
- **Privy Integration**: Seamless wallet connection and authentication
- **Smart Wallet Support**: ERC-4337 account abstraction with gas sponsorship
- **Multi-Wallet Management**: Support for MetaMask, Phantom, embedded wallets
- **Cross-Chain Balances**: Real-time token balance tracking across all chains

### Advanced Features
- **Account Abstraction**: Gas-sponsored transactions via Pimlico paymaster
- **Trade History**: Comprehensive transaction logging and history
- **Token Discovery**: Dynamic token search and pair discovery
- **Price Analytics**: Real-time price charts and market data
- **Mobile Responsive**: Optimized for desktop and mobile devices

## 🏗️ Project Structure

```
spot_frontend/
├── src/
│   ├── components/           # Reusable UI components
│   ├── Home/                 # Main application pages
│   │   ├── Navbar/          # Navigation and search components
│   │   │   ├── Navbar.tsx           # Main navigation bar
│   │   │   ├── SearchModal.tsx      # Token search modal
│   │   │   ├── AllCoinsTable.tsx    # All tokens table
│   │   │   ├── FavoritesTable.tsx   # Favorites management
│   │   │   ├── HighlightsTable.tsx  # Featured tokens
│   │   │   └── RadarTable.tsx       # Trending tokens
│   │   ├── TradingPanel/    # Core trading interface
│   │   │   ├── TradingPanel.tsx     # Main trading component
│   │   │   ├── BalanceDropdown.tsx  # Token selection dropdown
│   │   │   ├── DexSelector.tsx      # DEX selection component
│   │   │   ├── SettingsModal.tsx    # Trading settings
│   │   │   ├── Tpslmodal.tsx        # Take profit/stop loss
│   │   │   └── CustomWalletSelector.tsx # Wallet selection
│   │   └── Table/           # Data tables and trade history
│   │       ├── Trades.tsx           # Trade history table
│   │       └── InstantTrade.tsx     # Quick trade component
│   ├── utils/               # Utility functions and services
│   │   ├── trading-api.ts           # DEX quote aggregation
│   │   ├── swap.ts                  # Transaction execution
│   │   ├── quote.ts                 # Price quote utilities
│   │   ├── fetchQuote.ts            # Quote fetching logic
│   │   ├── api.ts                   # Backend API integration
│   │   ├── chainUtils.ts            # Chain detection utilities
│   │   ├── bundlerPaymasterService.ts # ERC-4337 integration
│   │   ├── paymasterClient.ts       # Gas sponsorship
│   │   ├── smartWalletUtils.ts      # Smart wallet utilities
│   │   ├── raydium.ts               # Raydium DEX integration
│   │   ├── meteora.ts               # Meteora DEX integration
│   │   └── fourmeme.ts              # FourMeme DEX integration
│   ├── hooks/               # Custom React hooks
│   │   ├── useTokenSelection.ts     # Token selection logic
│   │   ├── useSmartWallet.ts        # Smart wallet management
│   │   └── usePulseData.ts          # Real-time data updates
│   ├── services/            # External service integrations
│   │   └── trade-history-service.ts # Trade history management
│   ├── types/               # TypeScript type definitions
│   │   ├── trading.ts               # Trading-related types
│   │   └── trade-history.ts         # Trade history types
│   ├── constants.ts         # Application constants
│   ├── App.tsx             # Main application component
│   └── main.tsx            # Application entry point
├── public/                 # Static assets
├── .env                   # Environment variables
├── package.json           # Dependencies and scripts
├── vite.config.js         # Vite configuration
├── tailwind.config.js     # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
└── README.md              # This file
```

## 🚀 Getting Started

### Prerequisites

- **Node.js**: v18.0.0 or higher
- **npm**: v8.0.0 or higher
- **Git**: For version control

### Quick Start

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd spot_frontend

# Install dependencies
npm install --legacy-peer-deps

# Start the development server
npm run dev
```

The application will be available at `http://localhost:4001`

### Development Scripts

```bash
# Development
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run preview      # Preview production build locally

# Code Quality
npm run lint         # Run ESLint for code quality checks
npm run type-check   # Run TypeScript type checking

# Testing
npm test            # Run test suite (if configured)
```

## ⚙️ Environment Configuration

Create a `.env` file in the root directory with the following variables:

```bash
# Core Configuration
VITE_PRIVY_APP_ID=your_privy_app_id
VITE_API_URL=http://localhost:5001
VITE_API_LIQUIDITY_URL=http://localhost:3047/api

# Account Abstraction & Gas Sponsorship
VITE_NODEREAL_API_KEY=your_nodereal_api_key
VITE_PIMLICO_API_KEY=your_pimlico_api_key

# Blockchain Explorer APIs
VITE_ETHERSCAN_API_KEY=your_etherscan_api_key
VITE_BSCSCAN_API_KEY=your_bscscan_api_key
VITE_POLYGONSCAN_API_KEY=your_polygonscan_api_key

# Database & Analytics
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# External APIs
VITE_RAYDIUM_API_URL=http://localhost:3000
VITE_PUMPFUN_API_URL=http://localhost:6001
```

## 🏗️ Technical Architecture

### Core Technologies

- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **Styling**: Tailwind CSS for utility-first styling
- **State Management**: React hooks and context for local state
- **Wallet Integration**: Privy SDK for authentication and wallet management
- **Blockchain Interaction**: ethers.js (EVM) and @solana/web3.js (Solana)

### Key Dependencies

```json
{
  "react": "^18.2.0",
  "typescript": "^5.0.0",
  "vite": "^4.4.0",
  "tailwindcss": "^3.3.0",
  "@privy-io/react-auth": "^1.x.x",
  "ethers": "^6.x.x",
  "@solana/web3.js": "^1.x.x",
  "@headlessui/react": "^1.x.x",
  "@heroicons/react": "^2.x.x"
}
```

## 🔄 Trading Flow Architecture

The application implements a sophisticated trading system with the following flow:

### 1. Token Selection & Quote Fetching
```
User Input → Token Selection → Multi-DEX Quote Fetching → Best Price Discovery
```

### 2. Transaction Execution Pipeline
```
Quote Confirmation → Route Optimization → Transaction Preparation → Execution → Confirmation
```

### 3. Multi-Chain Support
- **Ethereum**: Uniswap, SushiSwap integration
- **BSC**: PancakeSwap, FourMeme integration
- **Solana**: Raydium, Meteora, Jupiter integration
- **Polygon**: QuickSwap and other DEXs

### 4. Smart Wallet Integration
- **ERC-4337 Support**: Account abstraction for gasless transactions
- **Paymaster Integration**: Pimlico for gas sponsorship
- **Multi-Wallet Support**: MetaMask, Phantom, embedded wallets

## 📡 API Integration

### Backend Services Integration

The frontend integrates with multiple backend services:

#### Spot Backend API (`http://localhost:5001`)
- **Token Balances**: `/api/wallet/balances`
- **Token Metadata**: `/api/tokens/metadata`
- **Price Data**: `/api/tokens/prices`

#### Liquidity Pool API (`http://localhost:3047`)
- **DEX Quotes**: `/api/quotes`
- **Pair Discovery**: `/api/pairs`
- **Liquidity Data**: `/api/liquidity`

#### Solana Service API (`http://localhost:6001`)
- **Solana Transactions**: `/api/solana/swap`
- **Token Accounts**: `/api/solana/accounts`
- **Pump.fun Integration**: `/api/pumpfun`

### External API Integrations

- **DexScreener**: Token pair discovery and market data
- **CoinGecko**: Token metadata and pricing
- **Mobula**: Alternative pricing data source
- **Supabase**: Trade history and user data storage

## 🔧 Development Guidelines

### Code Structure

- **Components**: Use functional components with TypeScript
- **Hooks**: Custom hooks for reusable logic
- **Utils**: Pure functions for data processing
- **Types**: Comprehensive TypeScript definitions
- **Constants**: Centralized configuration values

### Best Practices

1. **Type Safety**: Always use TypeScript interfaces
2. **Error Handling**: Implement comprehensive error boundaries
3. **Performance**: Use React.memo and useMemo for optimization
4. **Accessibility**: Follow WCAG guidelines for UI components
5. **Testing**: Write unit tests for critical functions

### File Naming Conventions

- **Components**: PascalCase (e.g., `TradingPanel.tsx`)
- **Hooks**: camelCase with 'use' prefix (e.g., `useTokenSelection.ts`)
- **Utils**: camelCase (e.g., `tradingApi.ts`)
- **Types**: camelCase (e.g., `trading.ts`)

## 🐛 Troubleshooting

### Common Issues

#### 1. Wallet Connection Issues
```bash
# Clear browser cache and local storage
# Disconnect and reconnect wallet
# Check Privy app configuration
```

#### 2. Transaction Failures
```bash
# Check network connectivity
# Verify sufficient token balance
# Confirm gas settings
# Check slippage tolerance
```

#### 3. Quote Fetching Errors
```bash
# Verify backend services are running
# Check API endpoints in .env file
# Confirm token addresses are valid
```

#### 4. Build Issues
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps

# Clear Vite cache
npm run dev -- --force
```

### Debug Mode

Enable debug logging by setting:
```bash
VITE_DEBUG=true
```

### Performance Monitoring

The app includes performance monitoring for:
- Quote fetching times
- Transaction execution duration
- Component render performance
- API response times

## 🚀 Deployment

### Production Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

### Environment-Specific Builds

```bash
# Development
npm run build:dev

# Staging
npm run build:staging

# Production
npm run build:prod
```

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --legacy-peer-deps
COPY . .
RUN npm run build
EXPOSE 4001
CMD ["npm", "run", "preview", "--", "--host", "0.0.0.0"]
```

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**:
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Make your changes**:
   - Follow code style guidelines
   - Add tests for new features
   - Update documentation
4. **Commit your changes**:
   ```bash
   git commit -m 'Add amazing feature'
   ```
5. **Push to your branch**:
   ```bash
   git push origin feature/amazing-feature
   ```
6. **Open a Pull Request**

### Code Review Process

- All PRs require review from maintainers
- Automated tests must pass
- Code coverage should not decrease
- Documentation must be updated for new features

### Issue Reporting

When reporting issues, please include:
- Browser and version
- Steps to reproduce
- Expected vs actual behavior
- Console errors (if any)
- Network/wallet configuration

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Privy**: For wallet authentication infrastructure
- **Vite**: For fast development experience
- **Tailwind CSS**: For utility-first styling
- **DEX Protocols**: PancakeSwap, Uniswap, Raydium, and others
- **Community**: Contributors and users who make this project possible

---

**Built with ❤️ by the RedFyn Team**
