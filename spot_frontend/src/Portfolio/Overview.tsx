import React, { useState, useEffect } from "react";
import Sparkline<PERSON>hart from "./Sparkline";
import EmptyState from "./components/EmptyState";
import MetricCard from "./components/MetricCard";
import { Line } from "react-chartjs-2";
import { useNavigate } from "react-router-dom";
import { toast } from 'react-toastify';
import { Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import {
  Chart as ChartJS,
  LineElement,
  CategoryScale,
  LinearScale,
  PointElement,
  Tooltip,
  Filler,
} from "chart.js";

ChartJS.register(LineElement, CategoryScale, LinearScale, PointElement, Tooltip, Filler);

interface PulseToken {
  id: string;
  address: string;
  name: string;
  symbol: string;
  imageUrl: string;
  market_cap: number;
  bonding: number;
  bonding_percent: number;
  price: number;
  price_change_24h: number;
  volume: number;
  liquidity: number;
  supply: number;
  exchange_name: string;
  exchange_logo: string;
  pool_address: string;
  network: string;
}

const gainers = [
  {
    name: "AERO-USD",
    price: 0.5911,
    change: "+28.06%",
    data: [0.3, 0.4, 0.35, 0.5, 0.6, 0.59],
  },
  // Add more as needed
];

const losers = [
  {
    name: "XYZ-USD",
    price: 0.4123,
    change: "-13.56%",
    data: [0.5, 0.47, 0.45, 0.44, 0.42, 0.41],
  },
  // Add more as needed
];

const Overview = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("24H");
  const [pulseTokens, setPulseTokens] = useState<PulseToken[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const navigate = useNavigate();
  
  useEffect(() => {
    const loadPulseTokens = () => {
      const storedTokens = JSON.parse(localStorage.getItem('pulseTokens') || '[]');
      setPulseTokens(storedTokens);
    };
    
    loadPulseTokens();
    
    const handlePulseDataChanged = (event: any) => {
      const updatedTokens = event.detail?.pulseTokens || [];
      setPulseTokens(updatedTokens);
    };
    
    window.addEventListener('pulseDataChanged', handlePulseDataChanged);
    window.addEventListener('storage', loadPulseTokens);
    
    return () => {
      window.removeEventListener('pulseDataChanged', handlePulseDataChanged);
      window.removeEventListener('storage', loadPulseTokens);
    };
  }, []);
  
  const handleTokenClick = (token: PulseToken) => {
    localStorage.setItem('activePulseToken', JSON.stringify(token));
    navigate(`/pulse-trade/${token.id}`);
  };
  
  const handleRemoveToken = (e: React.MouseEvent, tokenId: string) => {
    e.stopPropagation();
    
    const updatedTokens = pulseTokens.filter(t => t.id !== tokenId);
    localStorage.setItem('pulseTokens', JSON.stringify(updatedTokens));
    setPulseTokens(updatedTokens);
    
    const event = new CustomEvent('pulseDataChanged', {
      detail: { pulseTokens: updatedTokens }
    });
    window.dispatchEvent(event);
    
    toast.success("Token removed from watchlist", {
      position: "top-right",
      autoClose: 2000
    });
    
    // Reset to page 1 if current page is empty after removal
    const newTotalPages = Math.ceil(updatedTokens.length / itemsPerPage);
    if (currentPage > newTotalPages && newTotalPages > 0) {
      setCurrentPage(newTotalPages);
    }
  };
  
  const handleClearAll = () => {
    localStorage.setItem('pulseTokens', JSON.stringify([]));
    setPulseTokens([]);
    setCurrentPage(1);
    
    const event = new CustomEvent('pulseDataChanged', {
      detail: { pulseTokens: [] }
    });
    window.dispatchEvent(event);
    
    toast.success("All tokens have been removed from your watchlist", {
      position: "top-right",
      autoClose: 3000
    });
  };
  
  // Pagination calculations
  const totalPages = Math.ceil(pulseTokens.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTokens = pulseTokens.slice(startIndex, endIndex);
  
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  
  // Format market cap display
  const formatMarketCap = (marketCap: number) => {
    if (marketCap >= 1000000) {
      return `$${(marketCap / 1000000).toFixed(2)}M`;
    } else if (marketCap >= 1000) {
      return `$${(marketCap / 1000).toFixed(2)}K`;
    } else {
      return `$${marketCap.toFixed(2)}`;
    }
  };
  
  // Dummy chart data for portfolio value over time
  const chartData = {
    labels: Array(24).fill("").map((_, i) => `${i}:00`),
    datasets: [
      {
        data: Array(24).fill(0).map(() => Math.random() * 1000 + 5000),
        borderColor: "#7FFFD4",
        backgroundColor: "rgba(127, 255, 212, 0.1)",
        borderWidth: 2,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 6,
        pointHoverBackgroundColor: "#7FFFD4",
        fill: true,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#7FFFD4',
        bodyColor: '#fff',
        borderColor: '#7FFFD4',
        borderWidth: 1,
        padding: 10,
        displayColors: false,
        callbacks: {
          label: (context: any) => `$${context.parsed.y.toFixed(2)}`
        }
      }
    },
    scales: {
      x: {
        grid: { 
          display: false,
          borderColor: 'rgba(255, 255, 255, 0.1)'
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.5)',
          maxTicksLimit: 6
        }
      },
      y: {
        grid: { 
          color: 'rgba(255, 255, 255, 0.05)',
          borderColor: 'rgba(255, 255, 255, 0.1)'
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.5)',
          callback: (value: any) => `$${value}`
        }
      }
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false
    }
  };

  return (
    <div className="space-y-6">
      {/* Top Section - Value and Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Portfolio Value Card */}
        <div className="lg:col-span-1">
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 h-full">
            <h3 className="text-sm font-medium text-gray-400 mb-4">Total Portfolio Value</h3>
            <div className="space-y-6">
              <div>
                <div className="text-4xl font-bold text-white mb-2">$6,234.56</div>
                <div className="flex items-center gap-2">
                  <span className="text-[#14FFA2] text-sm font-medium">↑ +$234.56</span>
                  <span className="text-[#14FFA2] text-sm">(+3.91%)</span>
                </div>
              </div>
              
              <div className="space-y-3 pt-4 border-t border-gray-700/50">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">24h Change</span>
                  <span className="text-sm font-medium text-[#14FFA2]">+3.91%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">7d Change</span>
                  <span className="text-sm font-medium text-[#14FFA2]">+12.34%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">30d Change</span>
                  <span className="text-sm font-medium text-[#FF329B]">-2.15%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Portfolio Chart */}
        <div className="lg:col-span-2">
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 h-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">Portfolio Performance</h3>
              <div className="flex gap-1 bg-[#1D2226] rounded-lg p-1">
                {["24H", "7D", "30D", "ALL"].map((period) => (
                  <button
                    key={period}
                    onClick={() => setSelectedPeriod(period)}
                    className={`px-3 py-1 rounded-md text-xs font-medium transition-all duration-200 ${
                      selectedPeriod === period
                        ? "bg-[#7FFFD4]/20 text-[#7FFFD4]"
                        : "text-gray-400 hover:text-white"
                    }`}
                  >
                    {period}
                  </button>
                ))}
              </div>
            </div>
            <div className="h-[250px]">
              <Line data={chartData} options={chartOptions} />
            </div>
          </div>
        </div>
      </div>

      {/* Middle Section - Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <MetricCard
          label="Available Margin"
          value="$2,450.00"
          icon={
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />
        <MetricCard
          label="Total P&L"
          value="$1,234.56"
          change={{ value: "12.34%", isPositive: true }}
          icon={
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          }
        />
        <MetricCard
          label="Win Rate"
          value="68.5%"
          change={{ value: "5.2%", isPositive: true }}
          icon={
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
            </svg>
          }
        />
        <MetricCard
          label="Total Trades"
          value="156"
          icon={
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
            </svg>
          }
        />
      </div>

      {/* Bottom Section - Watchlist and Market Movers */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Watchlist */}
        <div className="lg:col-span-2">
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-white">Watchlist</h3>
              {pulseTokens.length > 0 && (
                <button 
                  onClick={handleClearAll}
                  className="text-sm text-[#FF329B] hover:text-[#FF329B]/80 transition-colors"
                >
                  Remove All
                </button>
              )}
            </div>
            {pulseTokens.length === 0 ? (
              <div className="h-[200px] flex items-center justify-center">
                <EmptyState
                  title="No tokens in watchlist"
                  description="Add tokens to monitor their performance"
                  icon="chart"
                />
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="text-left border-b border-gray-700/50">
                        <th className="text-xs font-medium text-gray-400 pb-3">Token</th>
                        <th className="text-xs font-medium text-gray-400 pb-3 text-right">Price</th>
                        <th className="text-xs font-medium text-gray-400 pb-3 text-right">24h Change</th>
                        <th className="text-xs font-medium text-gray-400 pb-3 text-right">Market Cap</th>
                        <th className="text-xs font-medium text-gray-400 pb-3 text-right">Volume</th>
                        <th className="text-xs font-medium text-gray-400 pb-3 text-center">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {currentTokens.map((token, index) => (
                        <tr 
                          key={token.id} 
                          onClick={() => handleTokenClick(token)}
                          className={`border-b border-gray-700/30 hover:bg-gray-700/20 cursor-pointer transition-colors ${
                            index % 2 === 0 ? 'bg-gray-800/10' : ''
                          }`}
                        >
                          <td className="py-3">
                            <div className="flex items-center gap-3">
                              <img
                                src={token.imageUrl}
                                alt={token.symbol}
                                className="w-8 h-8 rounded-full object-cover"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"%3E%3Ccircle cx="16" cy="16" r="16" fill="%237FFFD4"/%3E%3Ctext x="16" y="20" text-anchor="middle" font-size="14" fill="%23000"%3E?%3C/text%3E%3C/svg%3E';
                                }}
                              />
                              <div>
                                <span className="text-sm font-medium text-white block">{token.name}</span>
                                <span className="text-xs text-gray-400">{token.symbol}</span>
                              </div>
                            </div>
                          </td>
                          <td className="py-3 text-right">
                            <span className="text-sm text-white">${token.price?.toFixed(6) || '0.00'}</span>
                          </td>
                          <td className="py-3 text-right">
                            <span className={`text-sm font-medium ${
                              token.price_change_24h >= 0 ? 'text-[#14FFA2]' : 'text-[#FF329B]'
                            }`}>
                              {token.price_change_24h >= 0 ? '+' : ''}{token.price_change_24h?.toFixed(2) || '0.00'}%
                            </span>
                          </td>
                          <td className="py-3 text-right">
                            <span className="text-sm text-gray-300">
                              {formatMarketCap(token.market_cap)}
                            </span>
                          </td>
                          <td className="py-3 text-right">
                            <span className="text-sm text-gray-300">
                              ${(token.volume / 1000).toFixed(0)}K
                            </span>
                          </td>
                          <td className="py-3 text-center">
                            <button
                              onClick={(e) => handleRemoveToken(e, token.id)}
                              className="p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-400/10 rounded transition-colors"
                              title="Remove from watchlist"
                            >
                              <Trash2 size={16} />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-4 flex items-center justify-between">
                    <div className="text-sm text-gray-400">
                      Showing {startIndex + 1}-{Math.min(endIndex, pulseTokens.length)} of {pulseTokens.length} tokens
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`p-1.5 rounded transition-colors ${
                          currentPage === 1 
                            ? 'text-gray-600 cursor-not-allowed' 
                            : 'text-gray-400 hover:text-white hover:bg-gray-700'
                        }`}
                      >
                        <ChevronLeft size={18} />
                      </button>
                      
                      <div className="flex items-center gap-1">
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                          <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`px-3 py-1 text-sm rounded transition-colors ${
                              currentPage === page
                                ? 'bg-[#7FFFD4]/20 text-[#7FFFD4]'
                                : 'text-gray-400 hover:text-white hover:bg-gray-700'
                            }`}
                          >
                            {page}
                          </button>
                        ))}
                      </div>
                      
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`p-1.5 rounded transition-colors ${
                          currentPage === totalPages 
                            ? 'text-gray-600 cursor-not-allowed' 
                            : 'text-gray-400 hover:text-white hover:bg-gray-700'
                        }`}
                      >
                        <ChevronRight size={18} />
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Market Movers */}
        <div className="lg:col-span-1 space-y-6">
          {/* Top Gainers */}
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
            <h3 className="text-md font-semibold text-white mb-4">Top Gainers</h3>
            <div className="space-y-3">
              {gainers.map((coin, i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-[#7FFFD4]/20 to-[#7FFFD4]/10 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-[#7FFFD4]">{coin.name.charAt(0)}</span>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-white">{coin.name}</div>
                      <div className="text-xs text-gray-400">${coin.price.toFixed(4)}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-16 h-6">
                      <SparklineChart dataPoints={coin.data} color="#14FFA2" />
                    </div>
                    <span className="text-xs font-medium text-[#14FFA2] min-w-[50px] text-right">{coin.change}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Losers */}
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
            <h3 className="text-md font-semibold text-white mb-4">Top Losers</h3>
            <div className="space-y-3">
              {losers.map((coin, i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-[#FF329B]/20 to-[#FF329B]/10 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-[#FF329B]">{coin.name.charAt(0)}</span>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-white">{coin.name}</div>
                      <div className="text-xs text-gray-400">${coin.price.toFixed(4)}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-16 h-6">
                      <SparklineChart dataPoints={coin.data} color="#FF329B" />
                    </div>
                    <span className="text-xs font-medium text-[#FF329B] min-w-[50px] text-right">{coin.change}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Overview;
