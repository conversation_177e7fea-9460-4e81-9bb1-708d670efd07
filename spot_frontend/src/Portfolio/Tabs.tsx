import React, { useState } from "react";
import Spot from "./Spot";
import Overview from "./Overview";
import Wallet from "./Wallet";

interface TabConfig {
  id: "Overview" | "Spot" | "Wallet";
  label: string;
  icon: React.ReactNode;
}

const tabs: TabConfig[] = [
  {
    id: "Overview",
    label: "Overview",
    icon: (
      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
  },
  {
    id: "Spot",
    label: "Spot",
    icon: (
      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
      </svg>
    ),
  },
  {
    id: "Wallet",
    label: "Wallet",
    icon: (
      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h11a2 2 0 012 2v6a2 2 0 01-2 2H3m0-10v10m0-10l6.5-6M9 22V4a2 2 0 012-2h9.5M20 7v10a2 2 0 01-2 2h-6" />
      </svg>
    ),
  },
];

const Tabs = () => {
  const [activeTab, setActiveTab] = useState<"Overview" | "Spot" | "Wallet">("Spot");

  const renderContent = () => {
    switch (activeTab) {
      case "Overview":
        return <Overview />;
      case "Spot":
        return <Spot />;
      case "Wallet":
        return <Wallet />;
      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Modern pill-style tabs */}
      <div className="flex items-center mb-6">
        <div className="relative bg-[#181C20] rounded-full p-1 flex">
          {/* Animated background pill */}
          <div
            className="absolute top-1 bottom-1 bg-gradient-to-r from-[#7FFFD4]/20 to-[#7FFFD4]/10 rounded-full transition-all duration-300 ease-out"
            style={{
              left: activeTab === "Overview" ? "4px" : activeTab === "Spot" ? "33.33%" : "66.66%",
              width: "calc(33.33% - 4px)",
            }}
          />
          
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`relative z-10 flex items-center gap-2 px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeTab === tab.id
                  ? "text-white"
                  : "text-gray-400 hover:text-gray-200"
              }`}
            >
              <span className={`transition-all duration-300 ${
                activeTab === tab.id ? "text-[#7FFFD4]" : ""
              }`}>
                {tab.icon}
              </span>
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      <div className="flex-1 h-full overflow-auto">{renderContent()}</div>

    </div>
  );
};

export default Tabs;
