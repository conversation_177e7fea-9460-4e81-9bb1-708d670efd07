// components/SparklineChart.js
import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  LineElement,
  CategoryScale,
  LinearScale,
  PointElement,
  Tooltip,
} from 'chart.js';

ChartJS.register(LineElement, CategoryScale, LinearScale, PointElement, Tooltip);

const SparklineChart = ({ dataPoints = [], color = 'green' }: { dataPoints?: number[], color?: string }) => {
  const data = {
    labels: dataPoints.map((_, i) => i),
    datasets: [
      {
        data: dataPoints,
        borderColor: color,
        borderWidth: 1.5,
        tension: 0.4,
        pointRadius: 0,
        fill: false,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: { display: false },
      y: { display: false },
    },
    plugins: {
      legend: { display: false },
      tooltip: {
        mode: 'nearest' as const,
        intersect: false,
      },
    },
  };

  return <Line data={data} options={options} />;
};

export default SparklineChart;
