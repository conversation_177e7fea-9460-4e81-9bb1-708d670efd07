import React from 'react';

interface EmptyStateProps {
  title: string;
  description?: string;
  icon?: 'chart' | 'wallet' | 'assets';
}

const EmptyState: React.FC<EmptyStateProps> = ({ 
  title, 
  description,
  icon = 'chart'
}) => {
  const renderIcon = () => {
    switch (icon) {
      case 'wallet':
        return (
          <svg className="w-24 h-24 mx-auto mb-4" viewBox="0 0 100 100" fill="none">
            <rect x="15" y="30" width="70" height="50" rx="8" className="fill-gray-700/20 stroke-gray-600" strokeWidth="2"/>
            <rect x="25" y="20" width="50" height="35" rx="6" className="fill-gray-700/30 stroke-gray-600" strokeWidth="2"/>
            <circle cx="70" cy="55" r="8" className="fill-gray-600/50"/>
            <path d="M35 40 L65 40 M35 45 L55 45" className="stroke-gray-500" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        );
      case 'assets':
        return (
          <svg className="w-24 h-24 mx-auto mb-4" viewBox="0 0 100 100" fill="none">
            <circle cx="30" cy="50" r="15" className="fill-gray-700/20 stroke-gray-600" strokeWidth="2"/>
            <circle cx="50" cy="40" r="15" className="fill-gray-700/30 stroke-gray-600" strokeWidth="2"/>
            <circle cx="70" cy="50" r="15" className="fill-gray-700/20 stroke-gray-600" strokeWidth="2"/>
            <path d="M30 65 L30 75 M50 55 L50 75 M70 65 L70 75" className="stroke-gray-500" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        );
      default:
        return (
          <svg className="w-24 h-24 mx-auto mb-4" viewBox="0 0 100 100" fill="none">
            <path d="M20 70 L30 50 L45 60 L60 30 L80 45" className="stroke-gray-600" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round"/>
            <circle cx="30" cy="50" r="3" className="fill-gray-500"/>
            <circle cx="45" cy="60" r="3" className="fill-gray-500"/>
            <circle cx="60" cy="30" r="3" className="fill-gray-500"/>
            <circle cx="80" cy="45" r="3" className="fill-gray-500"/>
            <rect x="10" y="10" width="80" height="80" rx="8" className="stroke-gray-600" strokeWidth="2" fill="none" strokeDasharray="5 5" opacity="0.3"/>
          </svg>
        );
    }
  };

  return (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="animate-pulse">
        {renderIcon()}
      </div>
      <h3 className="text-lg font-medium text-gray-400 mb-2">{title}</h3>
      {description && (
        <p className="text-sm text-gray-500 text-center max-w-sm">{description}</p>
      )}
    </div>
  );
};

export default EmptyState;