import React from 'react';

interface MetricCardProps {
  label: string;
  value: string | number;
  change?: {
    value: string;
    isPositive: boolean;
  };
  icon?: React.ReactNode;
  loading?: boolean;
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  label, 
  value, 
  change, 
  icon,
  loading = false 
}) => {
  return (
    <div className="group relative overflow-hidden">
      {/* Glass effect background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#1D2226] to-[#181C20] opacity-90" />
      
      {/* Border gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-[#7FFFD4]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      
      {/* Content */}
      <div className="relative backdrop-blur-sm rounded-xl border border-gray-800/50 p-4 h-full transition-all duration-300 group-hover:border-gray-700/50 group-hover:shadow-lg group-hover:shadow-[#7FFFD4]/5">
        <div className="flex items-start justify-between mb-2">
          <div className="text-xs text-[#BBBBBB] uppercase tracking-wider">{label}</div>
          {icon && <div className="text-[#7FFFD4]/50">{icon}</div>}
        </div>
        
        {loading ? (
          <div className="space-y-2">
            <div className="h-6 bg-gray-700/30 rounded animate-pulse" />
            {change && <div className="h-4 w-16 bg-gray-700/30 rounded animate-pulse" />}
          </div>
        ) : (
          <>
            <div className="text-xl font-semibold text-white">
              {value}
            </div>
            {change && (
              <div className={`text-xs mt-1 font-medium ${
                change.isPositive ? 'text-[#14FFA2]' : 'text-[#FF329B]'
              }`}>
                {change.isPositive ? '↑' : '↓'} {change.value}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default MetricCard;