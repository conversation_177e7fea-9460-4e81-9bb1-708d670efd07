import React, { useState, useEffect, useMemo } from 'react';
import { ChevronUp, ChevronDown, DollarSign, RefreshCcw, ArrowLeftRight, Filter, User } from 'lucide-react';
import { useUserTradeData } from '@/hooks/useUserTradeData';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { getDefaultSolanaWalletAddress } from '@/utils/walletUtils';
import { useActiveToken } from '@/Pulse_Trade/context/ActiveTokenContext';
import VirtualizedList from './components/VirtualizedList';

interface TradesProps {
  tradeData?: {
    trades: any[];
    isLoading: boolean;
    isConnected: boolean;
    error: string | null;
    lastUpdate: number | null;
    latestRawTrade: any;
  };
}

const Trades: React.FC<TradesProps> = ({ tradeData }) => {
  const [activeTab, setActiveTab] = useState('TRADES');
  const [sortAsc, setSortAsc] = useState(true); // true = newest first (desc), false = oldest first (asc)
  const [showPrice, setShowPrice] = useState(false);
  const [showUSD, setShowUSD] = useState(false);
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [filterByActiveToken, setFilterByActiveToken] = useState(false);
  
  // Get active token for filtering
  const { activeToken } = useActiveToken();
  
  // Get Solana wallet address for "YOU" tab
  const { user, authenticated } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  
  // Get Solana wallet address instead of Ethereum wallet address
  const solanaWalletAddress = useMemo(() => {
    // First try the default wallet from localStorage
    const defaultAddress = getDefaultSolanaWalletAddress(authenticated, solanaWallets);
    if (defaultAddress) return defaultAddress;
    
    // Fallback to the first Solana wallet if available
    if (solanaWallets && solanaWallets.length > 0 && solanaWallets[0].address) {
      return solanaWallets[0].address;
    }
    
    // If no specific Solana wallet found, check user's linked accounts
    if (user?.linkedAccounts) {
      const solanaAccount = user.linkedAccounts.find((account) => 
        account.type === 'wallet' && 
        'address' in account && 
        typeof account.address === 'string' && 
        !account.address.startsWith('0x')
      );
      
      if (solanaAccount && 'address' in solanaAccount && typeof solanaAccount.address === 'string') {
        return solanaAccount.address;
      }
    }
    
    return null;
  }, [user, authenticated, solanaWallets]);
  
  // Use user-specific trade data for 'YOU' tab with Solana wallet address
  const userTradeData = useUserTradeData(
    activeTab === 'YOU' ? solanaWalletAddress : null,
    filterByActiveToken && activeToken ? activeToken.address : null
  );

  // Use trade data from props (shared with bottom table)
  const { trades = [], isLoading = false, isConnected = false, error = null, lastUpdate = null } = tradeData || {};

  // Real-time age updates (similar to Pulse table implementation)
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, []);

  // Format age with real-time updates and robust error handling
  const formatRealTimeAge = (timestamp: number): string => {
    // Handle invalid timestamps - show "just now" instead of "unknown"
    if (!timestamp || isNaN(timestamp) || timestamp <= 0) {
      console.warn('⚠️ Invalid timestamp in formatRealTimeAge:', timestamp);
      return 'just now';
    }

    const diffSeconds = Math.floor((currentTime - timestamp) / 1000);

    // Handle negative or invalid time differences - show "just now" instead of "unknown"
    if (isNaN(diffSeconds) || diffSeconds < 0) {
      console.warn('⚠️ Invalid time difference:', { currentTime, timestamp, diffSeconds });
      return 'just now';
    }

    if (diffSeconds < 1) {
      return 'just now';
    } else if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else if (diffSeconds < 86400) {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffSeconds / 86400)}d ago`;
    }
  };

  // Handle trader hash click to open Solscan
  const handleTraderClick = (txHash: string) => {
    if (txHash && txHash !== 'N/A') {
      const solscanUrl = `https://solscan.io/tx/${txHash}`;
      window.open(solscanUrl, '_blank', 'noopener,noreferrer');
    }
  };



  const getCurrentData = () => {
    let data;

    if (activeTab === 'YOU') {
      // Use data from useUserTradeData hook for the YOU tab
      data = userTradeData.trades || [];
    } else {
      // Use real trade data for TRADES tab
      data = trades;
    }

    // Default to descending order (newest first) - flip the sort logic
    return [...data].sort((a, b) => {
      return sortAsc ? b.timestamp - a.timestamp : a.timestamp - b.timestamp;
    });
  };

  return (
    <div className="text-white h-full font-mono text-sm flex flex-col">
      {/* Header Tabs */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        {/* Connection Status Indicator */}
        {activeTab === 'TRADES' && (
          <div className="flex items-center space-x-2 text-xs">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span className="text-gray-400">
              {isLoading ? 'Loading...' : isConnected ? `Live • ${trades.length} trades` : 'Disconnected'}
            </span>
            {error && <span className="text-red-400">Error: {error}</span>}
          </div>
        )}
        {activeTab === 'YOU' && (
          <div className="flex items-center space-x-2 text-xs">
            <div className={`w-2 h-2 rounded-full ${authenticated ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span className="text-gray-400">
              {userTradeData.isLoading 
                ? 'Loading your trades...' 
                : solanaWalletAddress 
                  ? `Your Trades • ${userTradeData.trades.length} trades` 
                  : 'Connect your Solana wallet'}
            </span>
            {userTradeData.error && <span className="text-red-400">Error: {userTradeData.error}</span>}
          </div>
        )}

        {/* Left side: TRADES */}
        <div>
          <button
            onClick={() => setActiveTab('TRADES')}
            className={`text-lg font-bold tracking-wide transition-colors ${
              activeTab === 'TRADES' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
            }`}
          >
            Trades
          </button>
        </div>

        {/* Right side: YOU and token filter */}
        <div className="flex space-x-6 items-center">
          <button
            onClick={() => setActiveTab('YOU')}
            className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
              activeTab === 'YOU' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
            }`}
          >
            <User size={18} className="mr-1" />
            YOU
          </button>
          
          {/* Token filter toggle (only show when on YOU tab and activeToken exists) */}
          {activeTab === 'YOU' && activeToken && (
            <button
              onClick={() => setFilterByActiveToken(!filterByActiveToken)}
              className={`flex items-center text-sm font-medium px-3 py-1 rounded-full transition-colors ${
                filterByActiveToken 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              title={filterByActiveToken ? `Showing only ${activeToken.name || activeToken.symbol} trades` : `Show only ${activeToken.name || activeToken.symbol} trades`}
            >
              {filterByActiveToken && activeToken.imageUrl && (
                <img 
                  src={activeToken.imageUrl} 
                  alt={activeToken.symbol}
                  className="w-4 h-4 mr-1 rounded-full"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              )}
              <Filter size={14} className="mr-1" />
              {filterByActiveToken ? activeToken.symbol : 'All Tokens'}
            </button>
          )}
        </div>
      </div>

      {/* Fixed Column Headers - Transparent */}
      <div className="flex items-center px-2 py-3 text-gray-400 text-xs border-b border-gray-800 sticky top-0 z-10">
        {/* Amount / USD Toggle */}
        <div className="flex-1 flex items-center justify-center space-x-2 pl-1">
          <span>{activeTab === 'YOU' ? 'Token' : 'Amount'}</span>
          {activeTab !== 'YOU' && (
            <button
              onClick={() => setShowUSD(!showUSD)}
              className={`w-5 h-5 flex items-center justify-center rounded-full border transition-transform ${
                showUSD ? 'border-green-500 text-green-400' : 'border-gray-500 text-gray-400'
              } hover:scale-110`}
              title="Toggle USD/SOL"
            >
              <DollarSign size={10} />
            </button>
          )}
        </div>

        {/* Market Cap / Price Toggle - Hide in YOU tab */}
        {activeTab !== 'YOU' && (
          <div className="w-24 flex items-center justify-center space-x-1 px-2">
            <span>{showPrice ? 'Price' : 'MC'}</span>
            <button
              onClick={() => setShowPrice(!showPrice)}
              className="hover:text-white transition-colors"
              title="Toggle MC / Price"
            >
              <ArrowLeftRight size={14} />
            </button>
          </div>
        )}
        {activeTab === 'YOU' && (
          <div className="w-24 flex items-center justify-center space-x-1 px-2">
            <div className="flex items-center">
              <img
                src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
                alt="SOL"
                className="w-3 h-3 mr-1"
              />
              <span>SOL</span>
            </div>
          </div>
        )}

        {/* Trader */}
        <div className="w-20 text-center px-1">Trader</div>

        {/* Age/Date Sort */}
        <div
          className="w-20 flex items-center justify-center cursor-pointer pr-1"
          onClick={() => setSortAsc(!sortAsc)}
        >
          <span>{activeTab === 'YOU' ? 'Date' : 'Age'}</span>
          {sortAsc ? <ChevronDown size={12} className="ml-1" /> : <ChevronUp size={12} className="ml-1" />}
        </div>
      </div>

      {/* Scrollable Trade Entries - Full Height with Virtual Scrolling */}
      <div className="flex-1">
        {/* Loading state for TRADES tab */}
        {activeTab === 'TRADES' && isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-400">Loading trade data...</div>
          </div>
        )}

        {/* Loading state for YOU tab */}
        {activeTab === 'YOU' && userTradeData.isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-400">Loading your trades...</div>
          </div>
        )}

        {/* No data state for TRADES tab */}
        {activeTab === 'TRADES' && !isLoading && getCurrentData().length === 0 && (
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-400">
              {error ? 'Failed to load trade data' : 'No trades available'}
            </div>
          </div>
        )}
        
        {/* No data state for YOU tab */}
        {activeTab === 'YOU' && !userTradeData.isLoading && getCurrentData().length === 0 && (
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-400">
              {userTradeData.error 
                ? 'Failed to load your trades' 
                : !solanaWalletAddress 
                  ? 'Connect your Solana wallet to view your trades'
                  : 'No trades found for your wallet'}
            </div>
            {solanaWalletAddress && (
              <div className="text-xs text-blue-400 mt-2">
                Checking trades for wallet: {solanaWalletAddress.slice(0, 6)}...{solanaWalletAddress.slice(-4)}
              </div>
            )}
          </div>
        )}

        {/* Virtualized Trade List */}
        {getCurrentData().length > 0 && (
          <VirtualizedList
            items={getCurrentData()}
            itemHeight={48} // Height of each trade row (py-2 + content)
            containerHeight="auto" // Auto-calculate based on available space
            className="divide-y divide-gray-800"
            renderItem={(trade) => (
              <div className="flex items-center px-2 py-2 hover:bg-gray-800/50 transition-colors border-b border-gray-800">
                <div
                  className={`flex-1 font-medium flex items-center justify-center space-x-1 pl-1 ${
                    trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
                  }`}
                >
                  {!showUSD && activeTab !== 'YOU' && (
                    <img
                      src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
                      alt="sol"
                      className="w-3 h-3"
                    />
                  )}
                  <span className="text-center">
                    {activeTab === 'YOU' 
                      ? trade.amount  // Always show token amount for YOU tab
                      : (showUSD ? trade.usdAmount : trade.amount)  // Regular behavior for TRADES tab
                    }
                  </span>
                </div>
                <div className="w-24 text-center text-gray-300 font-medium px-2">
                  {activeTab === 'YOU' ? (
                    <div className="flex items-center justify-center">
                      <img
                        src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
                        alt="SOL"
                        className="w-3 h-3 mr-1"
                      />
                      <span>{trade.solAmount}</span>
                    </div>
                  ) : (
                    showPrice ? trade.price : trade.mc
                  )}
                </div>
                <div
                  className="w-20 text-center text-gray-300 font-medium cursor-pointer hover:text-blue-400 transition-colors px-1 truncate"
                  onClick={() => handleTraderClick(trade.txHash)}
                  title={`Click to view transaction on Solscan: ${trade.txHash}`}
                >
                  {trade.trader}
                </div>
                <div className="w-20 text-center text-gray-400 font-medium pr-1 truncate">
                  {activeTab === 'YOU' 
                    ? new Date(trade.timestamp).toLocaleDateString() 
                    : formatRealTimeAge(trade.timestamp)}
                </div>
              </div>
            )}
          />
        )}
      </div>

    </div>
  );
};

export default Trades;
