import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { globalTradeWebSocketService } from '@/services/globalTradeWebSocketService';

interface ActiveToken {
  id: string;
  address: string;
  symbol: string;
  name: string;
  imageUrl?: string;
  price: number;
  market_cap?: number;
  pool_address?: string;
  exchange_name?: string;
  network?: string;
  balance?: string;
  decimals?: number;
}

interface LiveTokenData {
  price: number;
  market_cap: number;
  lastUpdated: number;
  isLive: boolean;
}

interface ActiveTokenContextType {
  activeToken: ActiveToken | null;
  liveData: LiveTokenData | null;
  setActiveToken: (token: ActiveToken | null) => void;
  getCurrentPrice: () => number;
  getCurrentMarketCap: () => number;
}

const ActiveTokenContext = createContext<ActiveTokenContextType | undefined>(undefined);

export const useActiveToken = () => {
  const context = useContext(ActiveTokenContext);
  if (context === undefined) {
    throw new Error('useActiveToken must be used within an ActiveTokenProvider');
  }
  return context;
};

interface ActiveTokenProviderProps {
  children: ReactNode;
}

export const ActiveTokenProvider: React.FC<ActiveTokenProviderProps> = ({ children }) => {
  const [activeToken, setActiveTokenState] = useState<ActiveToken | null>(null);
  const [liveData, setLiveData] = useState<LiveTokenData | null>(null);

  // Load active token from localStorage on mount
  useEffect(() => {
    const storedToken = localStorage.getItem('activePulseToken');
    if (storedToken) {
      try {
        const parsedToken = JSON.parse(storedToken);
        setActiveTokenState(parsedToken);
      } catch (error) {
        console.error('Error parsing stored active token:', error);
      }
    }
  }, []);

  // Listen for changes to activePulseToken in localStorage
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'activePulseToken') {
        if (e.newValue) {
          try {
            const parsedToken = JSON.parse(e.newValue);
            setActiveTokenState(parsedToken);
          } catch (error) {
            console.error('Error parsing updated active token:', error);
          }
        } else {
          setActiveTokenState(null);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Listen for custom events from other components
  useEffect(() => {
    const handlePulseDataChange = (event: CustomEvent) => {
      if (event.detail?.activePulseToken) {
        setActiveTokenState(event.detail.activePulseToken);
      }
    };

    window.addEventListener('pulseDataChanged', handlePulseDataChange as EventListener);
    return () => window.removeEventListener('pulseDataChanged', handlePulseDataChange as EventListener);
  }, []);

  // WebSocket integration for live data updates
  useEffect(() => {
    if (!activeToken?.pool_address) {
      setLiveData(null);
      return;
    }

    console.log('ActiveTokenContext: Subscribing to live data for pool:', activeToken.pool_address);
    
    const subscriberId = `active_token_context_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    // Subscribe to WebSocket data using the correct method
    const unsubscribePromise = globalTradeWebSocketService.subscribeToPool(
      activeToken.pool_address,
      subscriberId,
      (update) => {
        console.log('ActiveTokenContext: Received live data update:', update);
        
        // Extract price and market cap from the latest trade data
        if (update.latestRawTrade) {
          const trade = update.latestRawTrade;
          let price = 0;
          let marketCap = 0;

          // Extract price from various possible fields
          if (trade.token_price && typeof trade.token_price === 'number') {
            price = trade.token_price;
          } else if (trade.pairData?.price) {
            price = trade.pairData.price;
          } else if (trade.price) {
            // Handle string price
            const priceStr = typeof trade.price === 'string' ? trade.price.replace(/[$,]/g, '') : trade.price;
            const parsedPrice = parseFloat(priceStr);
            if (!isNaN(parsedPrice) && parsedPrice > 0) {
              price = parsedPrice;
            }
          }

          // Extract market cap
          if (trade.marketCap) {
            marketCap = trade.marketCap;
          } else if (trade.pairData?.token0?.marketCap && trade.pairData.token0.address === activeToken.address) {
            marketCap = trade.pairData.token0.marketCap;
          } else if (trade.pairData?.token1?.marketCap && trade.pairData.token1.address === activeToken.address) {
            marketCap = trade.pairData.token1.marketCap;
          } else {
            marketCap = activeToken.market_cap || 0; // Keep existing if not available
          }

          if (price > 0) {
            setLiveData({
              price,
              market_cap: marketCap,
              lastUpdated: Date.now(),
              isLive: true
            });
          }
        }
      }
    );

    // Handle the subscription and return cleanup function
    unsubscribePromise.catch((error) => {
      console.error('ActiveTokenContext: Failed to subscribe to pool:', error);
    });

    return () => {
      console.log('ActiveTokenContext: Unsubscribing from pool:', activeToken.pool_address);
      if (activeToken?.pool_address) {
        globalTradeWebSocketService.unsubscribeFromPool(activeToken.pool_address, subscriberId);
      }
    };
  }, [activeToken?.pool_address, activeToken?.address, activeToken?.market_cap]);

  const setActiveToken = (token: ActiveToken | null) => {
    setActiveTokenState(token);
    if (token) {
      localStorage.setItem('activePulseToken', JSON.stringify(token));
    } else {
      localStorage.removeItem('activePulseToken');
      setLiveData(null);
    }
  };

  // Helper functions to get current price and market cap
  const getCurrentPrice = (): number => {
    if (liveData?.isLive) {
      return liveData.price;
    }
    return activeToken?.price || 0;
  };

  const getCurrentMarketCap = (): number => {
    if (liveData?.isLive) {
      return liveData.market_cap;
    }
    return activeToken?.market_cap || 0;
  };

  const value = {
    activeToken,
    liveData,
    setActiveToken,
    getCurrentPrice,
    getCurrentMarketCap,
  };

  return (
    <ActiveTokenContext.Provider value={value}>
      {children}
    </ActiveTokenContext.Provider>
  );
};

export default ActiveTokenContext;