import React, { useState, useEffect, useRef, useCallback } from 'react';

interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number | 'auto';
  renderItem: (item: T, index: number, style: React.CSSProperties) => React.ReactNode;
  className?: string;
  overscan?: number; // Number of items to render outside visible area
}

function VirtualizedList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  className = '',
  overscan = 5
}: VirtualizedListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const [dynamicHeight, setDynamicHeight] = useState(400);
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle dynamic height calculation
  useEffect(() => {
    if (containerHeight === 'auto' && containerRef.current) {
      const updateHeight = () => {
        const rect = containerRef.current?.getBoundingClientRect();
        if (rect) {
          const viewportHeight = window.innerHeight;
          const topOffset = rect.top;
          // Get parent container's height if available
          const parentHeight = containerRef.current?.parentElement?.clientHeight || 0;
          
          // Use the larger of: calculated available height or parent container height
          const calculatedHeight = viewportHeight - topOffset - 20;
          const availableHeight = Math.max(calculatedHeight, parentHeight - 20);
          
          // Ensure minimum height of 400px for better visibility
          setDynamicHeight(Math.max(400, availableHeight));
        }
      };
      
      updateHeight();
      window.addEventListener('resize', updateHeight);
      return () => window.removeEventListener('resize', updateHeight);
    }
  }, [containerHeight]);

  const actualContainerHeight = containerHeight === 'auto' ? dynamicHeight : containerHeight;

  // Calculate visible range
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.floor((scrollTop + actualContainerHeight) / itemHeight) + overscan
  );

  // Calculate total height and visible items
  const totalHeight = items.length * itemHeight;
  const visibleItems = items.slice(startIndex, endIndex + 1);

  // Handle scroll events
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  // Auto-scroll to bottom when new items are added (for live trading data)
  const prevItemsLength = useRef(items.length);
  const isScrolledToBottom = useRef(true);

  useEffect(() => {
    if (containerRef.current) {
      const container = containerRef.current;
      const wasAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 10;
      isScrolledToBottom.current = wasAtBottom;
    }
  }, [scrollTop]);

  useEffect(() => {
    if (items.length > prevItemsLength.current && isScrolledToBottom.current && containerRef.current) {
      // New items added and user was at bottom, auto-scroll to bottom
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
    prevItemsLength.current = items.length;
  }, [items.length]);

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight === 'auto' ? dynamicHeight : containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {visibleItems.map((item, index) => {
          const actualIndex = startIndex + index;
          const style: React.CSSProperties = {
            position: 'absolute',
            top: actualIndex * itemHeight,
            left: 0,
            right: 0,
            height: itemHeight,
          };
          return (
            <div key={actualIndex} style={style}>
              {renderItem(item, actualIndex, style)}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default VirtualizedList;