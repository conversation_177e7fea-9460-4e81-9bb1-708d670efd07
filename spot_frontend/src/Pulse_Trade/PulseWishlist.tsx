import { useEffect, useState } from 'react';
import { StarIcon, Trash2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import usePulseData from '@/hooks/usePulseData';
import { toast } from 'react-toastify';

interface PulseToken {
  id: string;
  symbol: string;
  imageUrl: string;
  market_cap: number;
  bonding_percent: number;
}

export default function PulseWishList() {
  const [activeToken, setActiveToken2] = useState<PulseToken | null>(null);
  const [hoveredId, setHoveredId] = useState<string | null>(null);
  const [showDeleteAll, setShowDeleteAll] = useState(false);
  const [activeTab, setActiveTab] = useState<'watchlist' | 'positions' | null>('watchlist');
  const [pulseTokens, setPulseTokens] = useState<PulseToken[]>([]);
  const navigate = useNavigate();
  const {setActiveToken} = usePulseData();
  
  useEffect(() => {
    const storedTokens = JSON.parse(localStorage.getItem('pulseTokens') || '[]');
    setPulseTokens(storedTokens);
    
    // Listen for changes to pulseTokens
    const handlePulseDataChanged = (event: any) => {
      const updatedTokens = event.detail?.pulseTokens || [];
      setPulseTokens(updatedTokens);
    };
    
    window.addEventListener('pulseDataChanged', handlePulseDataChanged);
    return () => {
      window.removeEventListener('pulseDataChanged', handlePulseDataChanged);
    };
  }, []);

  useEffect(() => {
    const stored = localStorage.getItem('activePulseToken');
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        if (parsed?.id) setActiveToken2(parsed);
      } catch (err) {
        console.error("Invalid activePulseToken in localStorage", err);
      }
    }
  }, []);

  useEffect(() => {
    if (activeToken?.id) {
      const targetPath = `/pulse-trade/${activeToken.id}`;
      if (window.location.pathname !== targetPath) {
        navigate(targetPath);
      }
    }
  }, [activeToken, navigate]);

  const handleTokenSelect = (e:React.MouseEvent,token: PulseToken) => {
    console.log('Selected token:', token);
    if (activeToken?.id === token.id) return; // Prevent re-selection
    setActiveToken(e,token);
    setActiveToken2(token);
    localStorage.setItem('activePulseToken', JSON.stringify(token));
  };

  const handleTokenRemove = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();

    const updatedTokens = pulseTokens.filter(t => t.id !== id);
    setPulseTokens(updatedTokens);
    localStorage.setItem('pulseTokens', JSON.stringify(updatedTokens));

    try {
      const lastSearchStr = localStorage.getItem('lastSearchNavigation');
      if (lastSearchStr) {
        const lastSearch = JSON.parse(lastSearchStr);
        if (lastSearch?.extractedAddress === id) {
          localStorage.removeItem('lastSearchNavigation');
        }
      }
    } catch (error) {
      console.error('Failed to clear lastSearchNavigation:', error);
    }

    if (activeToken?.id === id) {
      const nextToken = updatedTokens[0] || null;
      setActiveToken2(nextToken);
      setActiveToken(e,nextToken);

      if (nextToken) {
        localStorage.setItem('activePulseToken', JSON.stringify(nextToken));
        navigate(`/pulse-trade/${nextToken.id}`);
      } else {
        localStorage.removeItem('activePulseToken');
        navigate(`/pulse-trade`);
      }
    }
  };
  
  // Function to clear all tokens from pulseTokens
  const handleClearAllTokens = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling
    
    try {
      // Clear pulseTokens in localStorage
      localStorage.setItem("pulseTokens", JSON.stringify([]));
      
      // Update state
      setPulseTokens([]);
      
      // Dispatch custom event to notify other components
      const event = new CustomEvent('pulseDataChanged', {
        detail: { pulseTokens: [] }
      });
      window.dispatchEvent(event);
      
      // Show success message
      toast.success("All tokens have been cleared from your watchlist", {
        position: "top-right",
        autoClose: 3000
      });
      
      // Clear active token if needed, but don't navigate away
      localStorage.removeItem('activePulseToken');
      setActiveToken2(null);
      
      // Don't navigate away from current page
      // navigate(`/trade`); - removed this line
    } catch (error) {
      console.error("Failed to clear tokens:", error);
      toast.error("Failed to clear tokens");
    }
  };

  return (
    <div 
      className="flex items-center border-t border-gray-700 px-4 rounded-md overflow-x-auto custom-scrollbar"
      onMouseEnter={() => setShowDeleteAll(true)}
      onMouseLeave={() => setShowDeleteAll(false)}
    >
      {/* Tabs */}
      <div className="flex items-center gap-4 mr-4">
        <div className="relative group">
          <button
            onClick={() => setActiveTab('watchlist')}
            className="p-2 hover:bg-white/10 rounded-md"
          >
            <StarIcon size={18} color={activeTab === 'watchlist' ? 'white' : '#333333'} />
          </button>
          <span className="absolute bottom-full mb-1 left-1/2 -translate-x-1/2 text-xs text-white bg-gray-800 px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
            Watchlist
          </span>
        </div>

        <div className="relative group">
          <button
            onClick={() => setActiveTab('positions')}
            className="p-2 hover:bg-white/10 rounded-md"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              stroke={activeTab === 'positions' ? 'white' : '#333333'}
              fill="none"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M3 3v18h18" />
              <polyline points="6 15 10 10 14 14 21 7" />
            </svg>
          </button>
          <span className="absolute bottom-full mb-1 left-1/2 -translate-x-1/2 text-xs text-white bg-gray-800 px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
            Positions
          </span>
        </div>
        
        {/* Delete All Button - Only show when hovering */}
        {pulseTokens.length > 0 && showDeleteAll && (
          <div className="relative group">
            <button
              onClick={handleClearAllTokens}
              className="p-2 text-red-400 hover:text-red-500 hover:bg-red-500/10 rounded-md transition-colors"
            >
              <Trash2 size={18} />
            </button>
            <span className="absolute bottom-full mb-1 left-1/2 -translate-x-1/2 text-xs text-white bg-gray-800 px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
              Clear All
            </span>
          </div>
        )}
        
        <div className="text-gray-500">|</div>
      </div>

      {/* Watchlist Tokens */}
      {activeTab === 'watchlist' &&
        pulseTokens.map((token) => {
          const isActive = activeToken?.id === token.id;

          return (
            <div
              key={token.id}
              onClick={(e) => handleTokenSelect(e,token)}
              onMouseEnter={() => setHoveredId(token.id)}
              onMouseLeave={() => setHoveredId(null)}
              className={`relative flex items-center gap-2 px-3 py-1 rounded-lg cursor-pointer transition-colors ${
                isActive ? 'bg-[#222222]' : 'hover:bg-[#333333]'
              }`}
            >
              <img
                src={token.imageUrl}
                alt={token.symbol}
                className="w-6 h-6 rounded-full object-cover"
                onError={(e) => ((e.target as HTMLImageElement).style.display = 'none')}
              />
              <span className="text-sm text-white truncate max-w-[80px]">{token.symbol}</span>
              <span className="text-sm text-slate-400">
                ${(token.market_cap / 1000).toFixed(2)}K
              </span>
              <span className="text-sm text-slate-400">+{token.bonding_percent}%</span>

              {hoveredId === token.id && (
                <button
                  onClick={(e) => handleTokenRemove(e, token.id)}
                  className="ml-auto p-1 text-red-400 hover:text-red-500 hover:bg-red-500/10 rounded transition-colors"
                >
                  <Trash2 size={14} />
                </button>
              )}
              
            </div>
          );
        })}
    </div>
  );
}
