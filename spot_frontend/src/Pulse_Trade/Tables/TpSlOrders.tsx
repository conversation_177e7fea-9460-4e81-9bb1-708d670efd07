import React, { useState, useEffect } from 'react';
import { Target, Shield, X, Eye, AlertCircle, Copy, ExternalLink, Minus } from 'lucide-react';
import { usePrivy } from '@privy-io/react-auth';
import { tpSlService } from '../../services/newTpslService';
import { TpSlOrder } from '../TradingPanel/NewTpSlSettings';
import { showSwapErrorToast, showSwapInfoToast } from '../../utils/swapToast';
import { useActiveToken } from '../context/ActiveTokenContext';

const TpSlOrders: React.FC = () => {
  const [orders, setOrders] = useState<TpSlOrder[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [removingOrderId, setRemovingOrderId] = useState<string | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<TpSlOrder | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<{ type: 'cancel' | 'close' | 'remove_tp' | 'remove_sl', orderId?: string } | null>(null);
  const { user } = usePrivy();
  const { activeToken, getCurrentPrice } = useActiveToken();

  // Helper function to get current price for a specific token
  const getCurrentPriceForToken = (tokenAddress: string, fallbackPrice: number): number => {
    // If this token matches the active token, use live price
    if (activeToken && (activeToken.address === tokenAddress || activeToken.id === tokenAddress)) {
      const livePrice = getCurrentPrice();
      if (livePrice && livePrice > 0) {
        return livePrice;
      }
    }
    // Otherwise use the fallback price from the order
    return fallbackPrice;
  };

  // Fetch TP/SL orders
  const fetchOrders = async () => {
    if (!user?.id) return;
    
    setIsLoading(true);
    try {
      const response = await tpSlService.getUserOrders(user.id);
      
      if (response.success) {
        // Filter only active orders
        const activeOrders = (response.data || []).filter(order => order.status === 'active');
        setOrders(activeOrders);
      } else {
        console.error('Failed to fetch TP/SL orders:', response.error);
        setOrders([]);
      }
    } catch (error) {
      console.error('Failed to fetch TP/SL orders:', error);
      setOrders([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, [user?.id]);

  // Listen for new orders created
  useEffect(() => {
    const handleOrderCreated = () => {
      fetchOrders();
    };

    window.addEventListener('tpslOrderCreated', handleOrderCreated);
    
    return () => {
      window.removeEventListener('tpslOrderCreated', handleOrderCreated);
    };
  }, []);

  // Handle order removal
  const handleRemoveOrder = async (orderId: string) => {
    if (!user?.id) return;

    setConfirmAction({ type: 'cancel', orderId });
    setConfirmModalOpen(true);
  };

  // Handle individual TP removal
  const handleRemoveTP = async (orderId: string) => {
    if (!user?.id) return;

    setConfirmAction({ type: 'remove_tp', orderId });
    setConfirmModalOpen(true);
  };

  // Handle individual SL removal
  const handleRemoveSL = async (orderId: string) => {
    if (!user?.id) return;

    setConfirmAction({ type: 'remove_sl', orderId });
    setConfirmModalOpen(true);
  };

  const executeOrderCancel = async () => {
    if (!confirmAction?.orderId || !user?.id) return;

    setRemovingOrderId(confirmAction.orderId);
    setConfirmModalOpen(false);
    
    try {
      let result;
      let successMessage = '';
      
      switch (confirmAction.type) {
        case 'cancel':
          result = await tpSlService.cancelOrder(confirmAction.orderId, user.id);
          successMessage = 'TP/SL order cancelled successfully';
          break;
        case 'remove_tp':
          result = await tpSlService.removeTakeProfit(confirmAction.orderId, user.id);
          successMessage = 'Take Profit order removed successfully';
          break;
        case 'remove_sl':
          result = await tpSlService.removeStopLoss(confirmAction.orderId, user.id);
          successMessage = 'Stop Loss order removed successfully';
          break;
        default:
          throw new Error('Unknown action type');
      }
      
      if (result.success) {
        await fetchOrders(); // Refresh orders
        showSwapInfoToast(successMessage);
      } else {
        console.error('Failed to execute action:', result.error);
        showSwapErrorToast('Failed to execute action: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Failed to execute action:', error);
      showSwapErrorToast('Failed to execute action: ' + (error instanceof Error ? error.message : 'Network error'));
    } finally {
      setRemovingOrderId(null);
      setConfirmAction(null);
    }
  };

  const handleViewDetails = (order: TpSlOrder) => {
    setSelectedOrder(order);
    setIsDetailsModalOpen(true);
  };

  const copyToClipboard = (address: string) => {
    navigator.clipboard.writeText(address);
    showSwapInfoToast('Address copied to clipboard');
  };

  const openSolscan = (address: string) => {
    window.open(`https://solscan.io/token/${address}`, '_blank');
  };

  const closeDetailsModal = async () => {
    // Task 4: When user closes position modal, remove from database
    if (selectedOrder && user?.id) {
      setConfirmAction({ type: 'close' });
      setConfirmModalOpen(true);
      return;
    }
    
    setIsDetailsModalOpen(false);
    setSelectedOrder(null);
  };

  const executeModalClose = async () => {
    if (!selectedOrder || !user?.id) return;

    setConfirmModalOpen(false);
    
    try {
      const result = await tpSlService.cancelOrder(selectedOrder.id, user.id);
      if (result.success) {
        showSwapInfoToast('Order removed from database');
        await fetchOrders(); // Refresh the orders list
        setIsDetailsModalOpen(false);
        setSelectedOrder(null);
      } else {
        showSwapErrorToast('Failed to remove order: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      showSwapErrorToast('Failed to remove order: ' + (error instanceof Error ? error.message : 'Network error'));
    } finally {
      setConfirmAction(null);
    }
  };

  const handleConfirmCancel = () => {
    setConfirmModalOpen(false);
    setConfirmAction(null);
  };

  const handleConfirmAction = () => {
    if (confirmAction?.type === 'cancel' || confirmAction?.type === 'remove_tp' || confirmAction?.type === 'remove_sl') {
      executeOrderCancel();
    } else if (confirmAction?.type === 'close') {
      executeModalClose();
    }
  };

  const formatPrice = (price: number) => {
    if (price >= 1) {
      return `$${price.toFixed(4)}`;
    } else if (price >= 0.01) {
      return `$${price.toFixed(6)}`;
    } else {
      return `$${price.toFixed(8)}`;
    }
  };
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInHours = diffInMs / (1000 * 60 * 60);
    const diffInDays = diffInMs / (1000 * 60 * 60 * 24);

    if (diffInHours < 24) {
      // Less than 24 hours - show relative time
      if (diffInHours < 1) {
        const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
        return `${diffInMinutes}m ago`;
      }
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInDays < 7) {
      // Less than 7 days - show days ago
      return `${Math.floor(diffInDays)}d ago`;
    } else {
      // More than 7 days - show date
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  };


  return (
    <>
      <div className="rounded-2xl border border-neutral-800 overflow-hidden">
        <div className="border-b border-neutral-700 bg-neutral-900/40 p-4">
          <h3 className="text-white font-medium">TP/SL Orders</h3>
        </div>
        
        {isLoading ? (
          <div className="text-center py-8">
            <div className="text-gray-400">Loading TP/SL orders...</div>
          </div>
        ) : orders.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400">No active TP/SL orders</div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-neutral-700 bg-neutral-900/40 backdrop-blur-md">
                <tr>
                  <th className="px-4 py-2 text-xs font-semibold text-neutral-400 uppercase tracking-wider">Token</th>
                  <th className="px-3 py-2 text-xs font-semibold text-neutral-400 uppercase tracking-wider text-center">SOL</th>
                  <th className="px-3 py-2 text-xs font-semibold text-neutral-400 uppercase tracking-wider text-center">Tokens</th>
                  <th className="px-3 py-2 text-xs font-semibold text-neutral-400 uppercase tracking-wider text-center">TP</th>
                  <th className="px-3 py-2 text-xs font-semibold text-neutral-400 uppercase tracking-wider text-center">SL</th>
                  <th className="px-3 py-2 text-xs font-semibold text-neutral-400 uppercase tracking-wider text-center">Date</th>
                  <th className="px-3 py-2 text-xs font-semibold text-neutral-400 uppercase tracking-wider text-center">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-neutral-800/60">
                {orders.map((order) => {
                  const openTransaction = (txn: string) => {
                    window.open(`https://solscan.io/tx/${txn}`, '_blank');
                  };

                  return (
                    <tr key={order.id} className="hover:bg-neutral-800/40 transition-colors">
                      <td className="px-4 py-3">
                        <div className="flex items-center gap-2">
                          {order.token_image && (
                            <img src={order.token_image} alt={order.token_symbol} className="w-6 h-6 rounded-full flex-shrink-0" />
                          )}
                          <div className="min-w-0">
                            <div className="flex items-center gap-1">
                              <span className="text-white font-medium text-sm truncate">{order.token_symbol}</span>
                              <button 
                                onClick={() => copyToClipboard(order.token_address)}
                                className="text-gray-400 hover:text-white transition-colors flex-shrink-0"
                                title="Copy token address"
                              >
                                <Copy size={10} />
                              </button>
                              <button 
                                onClick={() => openSolscan(order.token_address)}
                                className="text-gray-400 hover:text-blue-400 transition-colors flex-shrink-0"
                                title="View on Solscan"
                              >
                                <ExternalLink size={10} />
                              </button>
                            </div>
                            <div className="text-neutral-400 text-xs truncate">
                              {formatPrice(getCurrentPriceForToken(order.token_address, order.current_price))}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-3 py-3 text-center">
                        <div className="flex items-center justify-center gap-1">
                          <svg width="12" height="12" viewBox="0 0 397.7 311.7" className="flex-shrink-0">
                            <defs>
                              <linearGradient id="solanaGradient" x1="360.8791" y1="351.4553" x2="141.213" y2="-69.2936" gradientUnits="userSpaceOnUse">
                                <stop offset="0" stopColor="#00FFA3"/>
                                <stop offset="1" stopColor="#DC1FFF"/>
                              </linearGradient>
                            </defs>
                            <path d="M64.6,237.9c2.4-2.4,5.7-3.8,9.2-3.8h317.4c5.8,0,8.7,7,4.6,11.1l-62.7,62.7c-2.4,2.4-5.7,3.8-9.2,3.8H6.5 c-5.8,0-8.7-7-4.6-11.1L64.6,237.9z" fill="url(#solanaGradient)"/>
                            <path d="M64.6,3.8C67.1,1.4,70.4,0,73.8,0h317.4c5.8,0,8.7,7,4.6,11.1l-62.7,62.7c-2.4,2.4-5.7,3.8-9.2,3.8H6.5 c-5.8,0-8.7-7-4.6-11.1L64.6,3.8z" fill="url(#solanaGradient)"/>
                            <path d="M333.1,120.1c-2.4-2.4-5.7-3.8-9.2-3.8H6.5c-5.8,0-8.7,7-4.6,11.1l62.7,62.7c2.4,2.4,5.7,3.8,9.2,3.8h317.4 c5.8,0,8.7-7,4.6-11.1L333.1,120.1z" fill="url(#solanaGradient)"/>
                          </svg>
                          <span className="text-white font-medium text-sm">
                            {order.amount ? Number(order.amount).toFixed(3) : '0'}
                          </span>
                        </div>
                      </td>
                      <td className="px-3 py-3 text-center">
                        <div className="flex flex-col items-center gap-1">
                          <div className="text-white font-medium text-sm">
                            {order.total_token ? Number(order.total_token).toFixed(3) : 'N/A'}
                          </div>
                          {order.txn && (
                            <button
                              onClick={() => openTransaction(order.txn!)}
                              className="text-gray-400 hover:text-blue-400 transition-colors p-0.5 rounded"
                              title="View transaction"
                            >
                              <ExternalLink size={10} />
                            </button>
                          )}
                        </div>
                      </td>
                      <td className="px-3 py-3 text-center">
                        {order.hasTP ? (
                          <div className="flex flex-col items-center gap-1">
                            <div className="text-emerald-400 font-medium text-xs">
                              {formatPrice(order.tp_target_price || 0)}
                            </div>
                            <div className="text-xs text-gray-400">
                              {(order.tp_percentage || 0) > 0 ? '+' : ''}{(order.tp_percentage || 0).toFixed(1)}%
                            </div>
                            <button
                              onClick={() => handleRemoveTP(order.id)}
                              className="flex items-center gap-1 px-2 py-1 text-xs bg-red-900/30 hover:bg-red-900/50 text-red-400 hover:text-red-300 rounded border border-red-800/40 transition-colors"
                              title="Remove Take Profit"
                            >
                              <Minus size={10} />
                              <span>TP</span>
                            </button>
                          </div>
                        ) : (
                          <span className="text-gray-500 text-xs">-</span>
                        )}
                      </td>
                      <td className="px-3 py-3 text-center">
                        {order.hasSL ? (
                          <div className="flex flex-col items-center gap-1">
                            <div className="text-red-400 font-medium text-xs">
                              {formatPrice(order.sl_target_price || 0)}
                            </div>
                            <div className="text-xs text-gray-400">
                              {(order.sl_percentage || 0) > 0 ? '+' : ''}{(order.sl_percentage || 0).toFixed(1)}%
                            </div>
                            <button
                              onClick={() => handleRemoveSL(order.id)}
                              className="flex items-center gap-1 px-2 py-1 text-xs bg-red-900/30 hover:bg-red-900/50 text-red-400 hover:text-red-300 rounded border border-red-800/40 transition-colors"
                              title="Remove Stop Loss"
                            >
                              <Minus size={10} />
                              <span>SL</span>
                            </button>
                          </div>
                        ) : (
                          <span className="text-gray-500 text-xs">-</span>
                        )}
                      </td>
                      <td className="px-3 py-3 text-center">
                        <div className="text-neutral-300 text-xs" title={new Date(order.created_at).toLocaleString()}>
                          {formatDate(order.created_at)}
                        </div>
                      </td>
                      <td className="px-3 py-3 text-center">
                        <div className="flex items-center justify-center space-x-1">
                          <button 
                            onClick={() => handleViewDetails(order)}
                            className="p-1.5 rounded hover:bg-neutral-800/40 text-neutral-400 hover:text-neutral-300 transition-colors border border-neutral-700/40"
                            title="View Details"
                          >
                            <Eye size={12} />
                          </button>
                          
                          <button
                            onClick={() => handleRemoveOrder(order.id)}
                            disabled={removingOrderId === order.id}
                            className="p-1.5 rounded hover:bg-red-900/40 text-red-400 hover:text-red-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-red-800/40"
                            title="Cancel Order"
                          >
                            {removingOrderId === order.id ? (
                              <div className="w-3 h-3 border-2 border-red-400 border-t-transparent rounded-full animate-spin" />
                            ) : (
                              <X size={12} />
                            )}
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {isDetailsModalOpen && selectedOrder && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-neutral-900 border border-neutral-700 rounded-2xl p-6 w-96 max-w-[90vw] shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  {selectedOrder.hasTP && <Target size={18} className="text-emerald-400" />}
                  {selectedOrder.hasSL && <Shield size={18} className="text-red-400" />}
                </div>
                <h3 className="text-lg font-semibold text-white">
                  {selectedOrder.hasTP && selectedOrder.hasSL ? 'TP/SL Order' : 
                   selectedOrder.hasTP ? 'Take Profit Order' : 'Stop Loss Order'}
                </h3>
              </div>
              <button 
                onClick={() => {
                  setIsDetailsModalOpen(false);
                  setSelectedOrder(null);
                }} 
                className="text-gray-400 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
                {selectedOrder.token_image && (
                  <img src={selectedOrder.token_image} alt={selectedOrder.token_symbol} className="w-10 h-10 rounded-full" />
                )}
                <div>
                  <div className="text-white font-medium">{selectedOrder.token_symbol}</div>
                  <div className="text-neutral-400 text-sm">{selectedOrder.token_name}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg col-span-2">
                  <div className="text-neutral-400 text-xs mb-2">Order Types</div>
                  <div className="flex gap-4">
                    {selectedOrder.hasTP && (
                      <div className="flex items-center gap-2">
                        <Target size={16} className="text-emerald-400" />
                        <span className="text-emerald-400 font-medium">Take Profit</span>
                      </div>
                    )}
                    {selectedOrder.hasSL && (
                      <div className="flex items-center gap-2">
                        <Shield size={16} className="text-red-400" />
                        <span className="text-red-400 font-medium">Stop Loss</span>
                      </div>
                    )}
                  </div>
                </div>
                
                {selectedOrder.hasTP && (
                  <>
                    <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
                      <div className="text-neutral-400 text-xs">TP Target Price</div>
                      <div className="text-emerald-400 font-medium">
                        {formatPrice(selectedOrder.tp_target_price || 0)}
                      </div>
                    </div>
                    <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
                      <div className="text-neutral-400 text-xs">TP Percentage</div>
                      <div className="text-white font-medium">
                        {(selectedOrder.tp_percentage || 0) > 0 ? '+' : ''}{(selectedOrder.tp_percentage || 0).toFixed(1)}%
                      </div>
                    </div>
                  </>
                )}
                
                {selectedOrder.hasSL && (
                  <>
                    <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
                      <div className="text-neutral-400 text-xs">SL Target Price</div>
                      <div className="text-red-400 font-medium">
                        {formatPrice(selectedOrder.sl_target_price || 0)}
                      </div>
                    </div>
                    <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
                      <div className="text-neutral-400 text-xs">SL Percentage</div>
                      <div className="text-white font-medium">
                        {(selectedOrder.sl_percentage || 0) > 0 ? '+' : ''}{(selectedOrder.sl_percentage || 0).toFixed(1)}%
                      </div>
                    </div>
                  </>
                )}
                
                <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
                  <div className="text-neutral-400 text-xs">Current Price</div>
                  <div className="text-white font-medium">
                    {formatPrice(getCurrentPriceForToken(selectedOrder.token_address, selectedOrder.current_price))}
                  </div>
                </div>
                
                <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
                  <div className="text-neutral-400 text-xs">Total Tokens</div>
                  <div className="text-white font-medium">
                    {selectedOrder.total_token ? `${Number(selectedOrder.total_token).toFixed(6)} ${selectedOrder.token_symbol}` : 'N/A'}
                  </div>
                </div>
                
                <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
                  <div className="text-neutral-400 text-xs">Status</div>
                  <div className="text-emerald-400 font-medium">
                    {selectedOrder.status.toUpperCase()}
                  </div>
                </div>
                
                <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
                  <div className="text-neutral-400 text-xs">Created</div>
                  <div className="text-white font-medium" title={new Date(selectedOrder.created_at).toLocaleString()}>
                    {formatDate(selectedOrder.created_at)}
                  </div>
                </div>
                
                {selectedOrder.txn && (
                  <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg col-span-2">
                    <div className="text-neutral-400 text-xs mb-2">Transaction</div>
                    <div className="flex items-center gap-2">
                      <div className="text-white font-medium text-xs">
                        {selectedOrder.txn.slice(0, 8)}...{selectedOrder.txn.slice(-6)}
                      </div>
                      <button 
                        onClick={() => copyToClipboard(selectedOrder.txn!)}
                        className="text-gray-400 hover:text-white transition-colors"
                        title="Copy transaction hash"
                      >
                        <Copy size={12} />
                      </button>
                      <button 
                        onClick={() => window.open(`https://solscan.io/tx/${selectedOrder.txn!}`, '_blank')}
                        className="text-gray-400 hover:text-blue-400 transition-colors"
                        title="View transaction on Solscan"
                      >
                        <ExternalLink size={12} />
                      </button>
                    </div>
                  </div>
                )}
                
                <div className="p-3 bg-neutral-800 border border-neutral-700 rounded-lg col-span-2">
                  <div className="text-neutral-400 text-xs mb-2">Token Address</div>
                  <div className="flex items-center gap-2">
                    <div className="text-white font-medium text-xs">
                      {selectedOrder.token_address.slice(0, 6)}...{selectedOrder.token_address.slice(-4)}
                    </div>
                    <button 
                      onClick={() => copyToClipboard(selectedOrder.token_address)}
                      className="text-gray-400 hover:text-white transition-colors"
                      title="Copy address"
                    >
                      <Copy size={12} />
                    </button>
                    <button 
                      onClick={() => openSolscan(selectedOrder.token_address)}
                      className="text-gray-400 hover:text-blue-400 transition-colors"
                      title="View on Solscan"
                    >
                      <ExternalLink size={12} />
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => {
                    setIsDetailsModalOpen(false);
                    setSelectedOrder(null);
                  }}
                  className="flex-1 bg-neutral-700 hover:bg-neutral-600 text-white py-2 px-4 rounded-lg transition-colors font-medium"
                >
                  Close
                </button>
                <button
                  onClick={() => handleRemoveOrder(selectedOrder.id)}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors font-medium"
                >
                  Cancel Order
                </button>
                <button
                  onClick={closeDetailsModal}
                  className="flex-1 bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg transition-colors font-medium text-sm"
                >
                  Close & Remove
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Professional Confirmation Modal */}
      {confirmModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-neutral-900 border border-neutral-700 rounded-2xl p-6 w-96 max-w-[90vw] shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                  <AlertCircle className="w-5 h-5 text-red-600" />
                </div>
                <h3 className="text-lg font-semibold text-white">
                  Confirm Action
                </h3>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-neutral-300 text-sm">
                {confirmAction?.type === 'cancel' 
                  ? 'Are you sure you want to cancel this entire TP/SL order? This action cannot be undone.'
                  : confirmAction?.type === 'remove_tp'
                  ? 'Are you sure you want to remove only the Take Profit order? The Stop Loss will remain active.'
                  : confirmAction?.type === 'remove_sl'
                  ? 'Are you sure you want to remove only the Stop Loss order? The Take Profit will remain active.'
                  : 'Closing this modal will permanently remove the order from the database. This action cannot be undone.'
                }
              </p>
            </div>

            <div className="flex gap-3">
              <button
                onClick={handleConfirmCancel}
                className="flex-1 bg-neutral-700 hover:bg-neutral-600 text-white py-3 px-4 rounded-lg transition-colors font-medium"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmAction}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg transition-colors font-medium"
              >
                {confirmAction?.type === 'cancel' ? 'Delete Order' 
                  : confirmAction?.type === 'remove_tp' ? 'Remove TP'
                  : confirmAction?.type === 'remove_sl' ? 'Remove SL'
                  : 'Remove & Close'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TpSlOrders;