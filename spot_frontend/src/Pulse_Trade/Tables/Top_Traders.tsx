import React from 'react';
import { ArrowUpRight, ArrowDownRight, ExternalLink } from 'lucide-react';

interface TopTradersProps {
  topTrades?: any[];
  isLoading?: boolean;
}

const Top_Traders: React.FC<TopTradersProps> = ({ topTrades, isLoading }) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500 mb-2"></div>
          <p className="text-neutral-400 text-sm">Loading top trades data...</p>
        </div>
      </div>
    );
  }

  if (!topTrades || topTrades.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-neutral-400 text-sm mb-1">No top trades data available</p>
          <p className="text-neutral-500 text-xs">Try refreshing or check back later</p>
        </div>
      </div>
    );
  }

  // Function to format time ago
  const formatTimeAgo = (timestamp: number) => {
    const now = new Date().getTime();
    const diff = now - timestamp;
    
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return `${seconds}s ago`;
  };

  return (
    <div className="rounded-lg bg-[#0F1011]/50 shadow-lg border border-neutral-800/50">
      <div className="overflow-x-auto">
        <table className="w-full table-fixed">
          <colgroup>
            <col className="w-[15%]" />
            <col className="w-[10%]" />
            <col className="w-[15%]" />
            <col className="w-[20%]" />
            <col className="w-[20%]" />
            <col className="w-[20%]" />
          </colgroup>
          <thead className="sticky top-0 bg-[#121518] z-10">
            <tr className="border-b border-neutral-700">
              <th className="py-3 px-4 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">Time</th>
              <th className="py-3 px-4 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">Type</th>
              <th className="py-3 px-4 text-right text-xs font-medium text-neutral-400 uppercase tracking-wider">Amount</th>
              <th className="py-3 px-4 text-right text-xs font-medium text-neutral-400 uppercase tracking-wider">Total (SOL)</th>
              <th className="py-3 px-4 text-right text-xs font-medium text-neutral-400 uppercase tracking-wider">Total (USD)</th>
              <th className="py-3 px-4 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">Sender</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-neutral-800/70">
            {topTrades.map((trade) => (
              <tr key={trade.hash} className="hover:bg-neutral-800/30 transition-colors">
                <td className="py-3 px-4 text-left whitespace-nowrap">
                  <div className="flex flex-col">
                    <span className="text-sm text-white">{new Date(trade.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                    <span className="text-xs text-neutral-500">{formatTimeAgo(trade.date)}</span>
                  </div>
                </td>
                <td className="py-3 px-4 text-left whitespace-nowrap">
                  <div className="flex items-center">
                    {trade.type === 'buy' ? (
                      <div className="flex items-center text-emerald-400">
                        <ArrowUpRight size={16} className="mr-1 flex-shrink-0" />
                        <span className="text-sm font-medium">Buy</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-rose-400">
                        <ArrowDownRight size={16} className="mr-1 flex-shrink-0" />
                        <span className="text-sm font-medium">Sell</span>
                      </div>
                    )}
                  </div>
                </td>
                <td className="py-3 px-4 text-right text-sm text-white font-medium whitespace-nowrap">{Number(trade.token_amount).toLocaleString(undefined, { maximumFractionDigits: 2 })}</td>
                <td className="py-3 px-4 text-right whitespace-nowrap">
                  <div className="flex items-center justify-end">
                    <div className="h-4 w-4 flex-shrink-0 rounded-full bg-gradient-to-r from-amber-500 to-orange-500 mr-2"></div>
                    <span className={`text-sm font-medium ${trade.type === 'buy' ? 'text-emerald-400' : 'text-rose-400'}`}>
                      {Number(trade.token_amount_vs).toLocaleString(undefined, { maximumFractionDigits: 4 })}
                    </span>
                  </div>
                </td>
                <td className="py-3 px-4 text-right text-sm text-white font-medium whitespace-nowrap">${Number(trade.token_amount_usd).toLocaleString(undefined, { maximumFractionDigits: 2 })}</td>
                <td className="py-3 px-4 text-left truncate">
                  <div className="flex items-center">
                    <span className="text-sm text-white font-mono truncate">{`${trade.sender.slice(0, 6)}...${trade.sender.slice(-4)}`}</span>
                    <a 
                      href={`https://explorer.solana.com/address/${trade.sender}`}
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="ml-1 text-blue-400 hover:text-blue-300 transition-colors flex-shrink-0"
                    >
                      <ExternalLink size={14} />
                    </a>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="py-2 px-4 border-t border-neutral-800/50 flex justify-between items-center">
        <span className="text-xs text-neutral-500">Showing {topTrades.length} trades</span>
        <a href="#" className="text-xs text-blue-400 hover:text-blue-300 transition-colors">View all trades</a>
      </div>
    </div>
  );
};

export default Top_Traders;