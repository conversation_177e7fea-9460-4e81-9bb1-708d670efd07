import { useState, useMemo, useEffect } from 'react';
import { ChevronUp, ChevronDown, ExternalLink, Filter } from 'lucide-react';
import { formatSmallNumber } from '@/utils/numberFormatting';
import { formatWithZeroCount } from '@/utils/formatWithZeroCount';
import { useUserTradeData } from '@/hooks/useUserTradeData';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { getDefaultSolanaWalletAddress } from '@/utils/walletUtils';
import { useActiveToken } from '@/Pulse_Trade/context/ActiveTokenContext';
import VirtualizedList from '../components/VirtualizedList';

interface TradersProps {
  view?: 'dev' | 'you';
  tradeData?: {
    trades: any[];
    isLoading: boolean;
    isConnected: boolean;
    error: string | null;
    lastUpdate: number | null;
    latestRawTrade: any;
  };
}

const Traders: React.FC<TradersProps> = ({ view, tradeData }) => {
  const [sortAsc, setSortAsc] = useState(false); // Default to newest first
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [filterByActiveToken, setFilterByActiveToken] = useState(false);
  const { user, authenticated } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  
  // Get active token for filtering
  const { activeToken } = useActiveToken();
  
  // Get Solana wallet address instead of Ethereum wallet address
  const solanaWalletAddress = useMemo(() => {
    // First try the default wallet from localStorage
    const defaultAddress = getDefaultSolanaWalletAddress(authenticated, solanaWallets);
    if (defaultAddress) return defaultAddress;
    
    // Fallback to the first Solana wallet if available
    if (solanaWallets && solanaWallets.length > 0 && solanaWallets[0].address) {
      return solanaWallets[0].address;
    }
    
    // If no specific Solana wallet found, check user's linked accounts
    if (user?.linkedAccounts) {
      const solanaAccount = user.linkedAccounts.find((account) => 
        account.type === 'wallet' && 
        'address' in account && 
        typeof account.address === 'string' && 
        !account.address.startsWith('0x')
      );
      
      if (solanaAccount && 'address' in solanaAccount && typeof solanaAccount.address === 'string') {
        return solanaAccount.address;
      }
    }
    
    return null;
  }, [user, authenticated, solanaWallets]);
  
  // Use user-specific trade data for 'you' view with Solana wallet address and optional token filter
  const userTradeData = useUserTradeData(
    view === 'you' ? solanaWalletAddress : null,
    view === 'you' && filterByActiveToken && activeToken ? activeToken.address : null
  );

  // Use trade data from props (passed from Tables component)
  const { trades = [], isLoading = false, isConnected = false, error = null } = tradeData || {};

  // Real-time age updates (similar to side panel implementation)
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, []);

  // Format age with real-time updates (matching side panel implementation)
  const formatRealTimeAge = (timestamp: number): string => {
    // Handle invalid timestamps - show "just now" instead of "unknown"
    if (!timestamp || isNaN(timestamp) || timestamp <= 0) {
      console.warn('⚠️ Invalid timestamp in formatRealTimeAge:', timestamp);
      return 'just now';
    }

    const diffSeconds = Math.floor((currentTime - timestamp) / 1000);

    // Handle negative or invalid time differences - show "just now" instead of "unknown"
    if (isNaN(diffSeconds) || diffSeconds < 0) {
      console.warn('⚠️ Invalid time difference:', { currentTime, timestamp, diffSeconds });
      return 'just now';
    }

    if (diffSeconds < 1) {
      return 'just now';
    } else if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else if (diffSeconds < 86400) {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffSeconds / 86400)}d ago`;
    }
  };

  // Format market cap from trade data (matching trade_example.md logic)
  const formatMarketCap = (trade: any): string => {
    let marketCap = trade.marketCap || 0;

    if (marketCap >= 1e12) {
      return (marketCap / 1e12).toFixed(2) + 'T';
    } else if (marketCap >= 1e9) {
      return (marketCap / 1e9).toFixed(2) + 'B';
    } else if (marketCap >= 1e6) {
      return (marketCap / 1e6).toFixed(2) + 'M';
    } else if (marketCap >= 1e3) {
      return (marketCap / 1e3).toFixed(2) + 'K';
    } else {
      return marketCap.toFixed(0);
    }
  };

  // Format token amount from trade data (using actualTokenAmount for actual token quantity)
  const formatTokenAmount = (trade: any): string => {
    // Use actualTokenAmount from FormattedTrade (actual token quantity being traded)
    let tokenAmount = trade.actualTokenAmount || 0;
    
    // Use the zero-count formatter
    return formatWithZeroCount(tokenAmount);
  };

  // Format SOL amount from trade data
  const formatSOLAmount = (trade: any): string => {
    // For user trades, we have a pre-formatted solAmount field
    if (trade.solAmount && typeof trade.solAmount === 'string') {
      return trade.solAmount;
    }
    
    // Fallback to tokenAmount for backward compatibility
    let solAmount = trade.tokenAmount || 0;
    
    // Use the zero-count formatter for consistent formatting
    return formatWithZeroCount(solAmount);
  };

  // Format trade USD value (keeping for reference, but will be replaced with SOL)
  const formatTradeValue = (trade: any): string => {
    let usdValue = trade.tokenAmountUsd || 0;
    return formatSmallNumber(usdValue);
  };

  // Format transaction hash (return just the shortened hash)
  const formatHash = (trade: any): string => {
    let hash = trade.txHash || '';

    if (!hash || hash === 'N/A') {
      return 'N/A';
    }

    if (hash.length > 8) {
      return `${hash.substring(0, 4)}...${hash.substring(hash.length - 4)}`;
    }

    return hash;
  };

  // Get full hash for click handler
  const getFullHash = (trade: any): string => {
    return trade.txHash || '';
  };

  // Handle hash click to open Solscan
  const handleHashClick = (hash: string) => {
    if (hash && hash !== 'N/A') {
      const explorerUrl = `https://solscan.io/tx/${hash}`;
      window.open(explorerUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const getCurrentData = () => {
    // If in 'you' view, use data from useUserTradeData hook
    if (view === 'you') {
      return [...userTradeData.trades].sort((a, b) => {
        return sortAsc ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
      });
    }
    
    // If in 'dev' view, would filter for dev wallets (not implemented yet)
    if (view === 'dev') {
      // TODO: Implement proper dev wallet identification
      return [...trades].sort((a, b) => {
        return sortAsc ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
      });
    }

    // Default view - all trades
    return [...trades].sort((a, b) => {
      return sortAsc ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
    });
  };

  // Determine loading and error states based on current view
  const isViewLoading = view === 'you' ? userTradeData.isLoading : isLoading;
  const viewError = view === 'you' ? userTradeData.error : error;
  const currentData = getCurrentData();
  const currentDataLength = currentData ? currentData.length : 0;

  // ---------- UI ----------
  return (
    <div className="rounded-lg overflow-hidden h-full flex flex-col">
      {/* Connection Status */}
      <div className="flex items-center justify-between px-6 py-2 bg-neutral-900/40 border-b border-neutral-700">
        <div className="flex items-center space-x-2 text-xs">
          <div className={`w-2 h-2 rounded-full ${(view === 'you' ? true : isConnected) ? 'bg-green-400' : 'bg-red-400'}`}></div>
          <span className="text-neutral-400">
            {isViewLoading 
              ? 'Loading trades...' 
              : view === 'you'
                ? `Your Trades • ${currentDataLength} trades` 
                : isConnected 
                  ? `Live • ${trades.length} trades` 
                  : 'Disconnected'
            }
          </span>
          {viewError && <span className="text-red-400">Error: {viewError}</span>}
        </div>
        <div className="flex items-center space-x-4">
          {/* Token filter toggle (only show when on YOU view and activeToken exists) */}
          {view === 'you' && activeToken && (
            <button
              onClick={() => setFilterByActiveToken(!filterByActiveToken)}
              className={`flex items-center text-xs font-medium px-3 py-1 rounded-full transition-colors ${
                filterByActiveToken 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              title={filterByActiveToken ? `Showing only ${activeToken.name || activeToken.symbol} trades` : `Show only ${activeToken.name || activeToken.symbol} trades`}
            >
              {filterByActiveToken && activeToken.imageUrl && (
                <img 
                  src={activeToken.imageUrl} 
                  alt={activeToken.symbol}
                  className="w-3 h-3 mr-1 rounded-full"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              )}
              <Filter size={12} className="mr-1" />
              {filterByActiveToken ? activeToken.symbol : 'All Tokens'}
            </button>
          )}
          <div className="text-xs text-neutral-500">
            {currentDataLength > 0 ? `${currentDataLength} trades` : 'No trades available'}
          </div>
        </div>
      </div>

      {/* Table Structure with Flexible Height */}
      <div className="flex flex-col flex-1"> {/* Use flex-1 to take remaining space */}
        {/* Fixed Header */}
        <div className="flex-shrink-0 border-b border-neutral-700 bg-[#141416]">
          <div className="flex text-neutral-400 text-xs">
            <div className="flex-1 px-4 py-3">
              <div
                className="flex items-center cursor-pointer hover:text-white transition-colors"
                onClick={() => setSortAsc(!sortAsc)}
              >
                <span>{view === 'you' ? 'Date' : 'Age'}</span>
                {sortAsc ? <ChevronUp size={12} className="ml-1" /> : <ChevronDown size={12} className="ml-1" />}
              </div>
            </div>
            <div className="flex-1 px-4 py-3">Type</div>
            {view !== 'you' && <div className="flex-1 px-4 py-3">Market Cap</div>}
            <div className="flex-1 px-4 py-3">Amount</div>
            <div className="flex-1 px-4 py-3">Total SOL</div>
            <div className="flex-1 px-4 py-3">Hash</div>
          </div>
        </div>

        {/* Scrollable Content with Virtual Scrolling */}
        <div className="flex-1">
          {isViewLoading && (
            <div className="text-center py-8">
              <div className="text-neutral-400">
                {view === 'you' ? 'Loading your trades...' : 'Loading trade data...'}
              </div>
            </div>
          )}

          {!isViewLoading && currentDataLength === 0 && (
            <div className="text-center py-8">
              <div className="text-neutral-400">
                {viewError 
                  ? 'Failed to load trade data' 
                  : view === 'you' && !solanaWalletAddress
                    ? 'Connect your Solana wallet to view your trades'
                    : view === 'you'
                      ? 'No trades found for your wallet' 
                      : 'No trades available'
                }
              </div>
              {view === 'you' && solanaWalletAddress && (
                <div className="text-xs text-blue-400 mt-2">
                  Checking trades for wallet: {solanaWalletAddress.slice(0, 6)}...{solanaWalletAddress.slice(-4)}
                </div>
              )}
            </div>
          )}

          {!isViewLoading && currentDataLength > 0 && (
            <VirtualizedList
              items={getCurrentData()}
              itemHeight={56} // Height of each trade row (py-3 + content)
              containerHeight="auto" // Auto-calculate based on available space
              className="divide-y divide-neutral-800"
              renderItem={(trade) => (
                <div className="flex hover:bg-neutral-800/50 transition-colors border-b border-neutral-800">
                  <div className="flex-1 px-4 py-3 text-neutral-500 text-sm">
                    {view === 'you' 
                      ? new Date(trade.timestamp).toLocaleDateString() 
                      : formatRealTimeAge(trade.timestamp)}
                  </div>
                  <div className={`flex-1 px-4 py-3 text-sm ${trade.type === 'buy' ? 'text-green-400' : 'text-red-400'}`}>
                    {trade.type === 'buy' ? 'BUY' : 'SELL'}
                  </div>
                  {view !== 'you' && (
                    <div className="flex-1 px-4 py-3 text-white text-sm">
                      {formatMarketCap(trade)}
                    </div>
                  )}
                  <div className="flex-1 px-4 py-3 text-white text-sm">
                    <div className="flex items-center space-x-2">
                      {trade.tokenImage && (
                        <img 
                          src={trade.tokenImage} 
                          alt={trade.tokenSymbol}
                          className="w-5 h-5 rounded-full"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      )}
                      <div className="flex flex-col">
                        <span className="font-medium">{formatTokenAmount(trade)}</span>
                        <span className="text-xs text-gray-400">{trade.tokenSymbol}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1 px-4 py-3 text-white text-sm">
                    <div className="flex items-center">
                      <img
                        src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
                        alt="SOL"
                        className="w-3 h-3 mr-1"
                      />
                      <span className={trade.type === 'buy' ? 'text-green-400' : 'text-red-400'}>
                        {formatSOLAmount(trade)}
                      </span>
                    </div>
                  </div>
                  <div className="flex-1 px-4 py-3 text-sm flex items-center">
                    <span 
                      className="text-blue-400 hover:text-blue-300 transition-colors cursor-pointer"
                      onClick={() => handleHashClick(getFullHash(trade))}
                    >
                      {formatHash(trade)}
                    </span>
                    <ExternalLink size={12} className="ml-1 text-neutral-500" />
                  </div>
                </div>
              )}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default Traders;
