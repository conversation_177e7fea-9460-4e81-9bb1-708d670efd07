import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Target, Shield, X, MoreHorizontal, Edit3, Eye, Trash2 } from 'lucide-react';
import { usePrivy } from '@privy-io/react-auth';
import { useActiveToken } from '../context/ActiveTokenContext';
import { useTradeData } from '../../hooks/useTradeData';
import tpslService, { Position, TPSLOrder } from '../../services/tpslService';

interface TPSLModalProps {
  isOpen: boolean;
  onClose: () => void;
  position: Position;
  orderType: 'take_profit' | 'stop_loss';
  onSubmit: (data: any) => void;
}

// TP/SL Modal Component
const TPSLModal: React.FC<TPSLModalProps> = ({ isOpen, onClose, position, orderType, onSubmit }) => {
  const [percentage, setPercentage] = useState('');
  const [amount, setAmount] = useState('');
  const [amountPercentage, setAmountPercentage] = useState('100');
  const [slippage, setSlippage] = useState('1');

  if (!isOpen) return null;

  const isTP = orderType === 'take_profit';
  const title = isTP ? 'Set Take Profit' : 'Set Stop Loss';
  const color = isTP ? 'emerald' : 'red';
  const icon = isTP ? Target : Shield;
  const IconComponent = icon;

  const handleSubmit = () => {
    const data = {
      position_id: position.id,
      order_type: orderType,
      trigger_percentage: parseFloat(percentage),
      amount: parseFloat(amount) || (position.remaining_amount * parseFloat(amountPercentage) / 100),
      amount_percentage: parseFloat(amountPercentage),
      slippage: parseFloat(slippage)
    };
    onSubmit(data);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-[#1A1D24] rounded-lg p-6 w-96 max-w-[90vw]">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <IconComponent size={20} className={`text-${color}-400`} />
            <h3 className="text-lg font-semibold text-white">{title}</h3>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-white">
            <X size={20} />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm text-gray-300 mb-2">
              Trigger Percentage
            </label>
            <div className="flex items-center gap-2">
              <input
                type="number"
                value={percentage}
                onChange={(e) => setPercentage(e.target.value)}
                placeholder={isTP ? "15" : "10"}
                min="0.1"
                step="0.1"
                className="w-full bg-[#2A2D35] border border-gray-600 rounded-lg px-3 py-2 text-white"
              />
              <span className="text-gray-400">%</span>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {isTP ? "Price will be above current" : "Price will be below current"}
            </div>
          </div>

          <div className="flex gap-3">
            <div className="flex-1">
              <label className="block text-sm text-gray-300 mb-2">
                Amount (%)
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  value={amountPercentage}
                  onChange={(e) => setAmountPercentage(e.target.value)}
                  placeholder="100"
                  min="1"
                  max="100"
                  className="w-full bg-[#2A2D35] border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
                <span className="text-gray-400">%</span>
              </div>
            </div>
            
            <div className="flex-1">
              <label className="block text-sm text-gray-300 mb-2">
                Amount (Exact)
              </label>
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.0"
                min="0"
                step="0.00001"
                className="w-full bg-[#2A2D35] border border-gray-600 rounded-lg px-3 py-2 text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm text-gray-300 mb-2">
              Slippage (%)
            </label>
            <input
              type="number"
              value={slippage}
              onChange={(e) => setSlippage(e.target.value)}
              placeholder="1"
              min="0.1"
              max="50"
              step="0.1"
              className="w-full bg-[#2A2D35] border border-gray-600 rounded-lg px-3 py-2 text-white"
            />
          </div>
        </div>

        <div className="flex gap-3 mt-6">
          <button
            onClick={onClose}
            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className={`flex-1 bg-${color}-600 hover:bg-${color}-700 text-white py-2 px-4 rounded-lg transition-colors`}
          >
            Create {isTP ? 'TP' : 'SL'}
          </button>
        </div>
      </div>
    </div>
  );
};

const Positions: React.FC = () => {
  const [positions, setPositions] = useState<Position[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [tpslModal, setTpslModal] = useState<{ isOpen: boolean; position?: Position; orderType?: 'take_profit' | 'stop_loss' }>({ isOpen: false });
  const [currentPrices, setCurrentPrices] = useState<{ [key: string]: number }>({});
  const [selectedPosition, setSelectedPosition] = useState<Position | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [removingOrderId, setRemovingOrderId] = useState<string | null>(null);
  const { user } = usePrivy();
  const { activeToken } = useActiveToken();
  const { latestRawTrade } = useTradeData(activeToken?.pool_address);

  // Fetch positions with TP/SL data
  const fetchPositions = async () => {
    if (!user?.id) return;
    
    setIsLoading(true);
    try {
      const response = await tpslService.getPositions(user.id);
      
      if (response.success) {
        setPositions(response.data || []);
      } else {
        console.error('Failed to fetch positions:', response.error);
        setPositions([]);
      }
    } catch (error) {
      console.error('Failed to fetch positions:', error);
      setPositions([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPositions();
  }, [user?.id]);

  // Listen for TP/SL order creation events
  useEffect(() => {
    const handleTPSLOrderCreated = () => {
      console.log('Positions: Received tpslOrderCreated event, refreshing positions');
      fetchPositions();
    };

    const handleTradeSuccess = () => {
      console.log('Positions: Received pulseTradeSuccess event, refreshing positions');
      fetchPositions();
    };

    window.addEventListener('tpslOrderCreated', handleTPSLOrderCreated);
    window.addEventListener('pulseTradeSuccess', handleTradeSuccess);

    return () => {
      window.removeEventListener('tpslOrderCreated', handleTPSLOrderCreated);
      window.removeEventListener('pulseTradeSuccess', handleTradeSuccess);
    };
  }, [user?.id]);

  // Update current prices when new trade data comes in
  useEffect(() => {
    if (latestRawTrade && latestRawTrade.token_price) {
      const tokenAddress = activeToken?.address;
      if (tokenAddress) {
        setCurrentPrices(prev => ({
          ...prev,
          [tokenAddress]: latestRawTrade.token_price || latestRawTrade.price || 0
        }));
        
        // Update positions with new current price
        setPositions(prev => prev.map(position => {
          if (position.token_address === tokenAddress) {
            return {
              ...position,
              current_price: latestRawTrade.token_price || latestRawTrade.price || position.current_price
            };
          }
          return position;
        }));
      }
    }
  }, [latestRawTrade, activeToken?.address]);

  const handleCreateTPSL = async (data: any) => {
    if (!user?.id) return;

    try {
      const orderData = {
        ...data,
        user_id: user.id,
        token_address: tpslModal.position?.token_address || '',
        token_name: tpslModal.position?.token_name || '',
        token_symbol: tpslModal.position?.token_symbol || '',
        token_image: tpslModal.position?.token_image || '',
        pool_address: tpslModal.position?.pool_address || '',
        dex_type: tpslModal.position?.dex_type || '',
        direction: (tpslModal.position?.direction as 'buy' | 'sell') || 'buy',
        entry_price: tpslModal.position?.entry_price || 0,
        current_price: tpslModal.position?.current_price || 0,
        wallet_address: tpslModal.position?.wallet_address || '',
        wallet_id: tpslModal.position?.wallet_id || '',
      };

      const result = await tpslService.createOrder(orderData);
      
      if (result.success) {
        // Refresh positions
        fetchPositions();
        
        // Dispatch event to notify other components
        window.dispatchEvent(new CustomEvent('tpslOrderCreated', {
          detail: {
            timestamp: Date.now(),
            orderCount: 1
          }
        }));
      } else {
        console.error('Failed to create TP/SL order:', result.error);
      }
    } catch (error) {
      console.error('Failed to create TP/SL order:', error);
    }
  };

  const handleRemoveTPSL = async (orderId: string) => {
    if (!user?.id) return;

    setRemovingOrderId(orderId);
    try {
      const result = await tpslService.cancelOrder(user.id, orderId);
      
      if (result.success) {
        // Refresh positions to update the UI
        await fetchPositions();
        
        // If we're in the details modal, update the selected position
        if (selectedPosition) {
          const updatedPositions = await tpslService.getPositions(user.id);
          if (updatedPositions.success) {
            const updatedPosition = updatedPositions.data?.find(p => p.id === selectedPosition.id);
            if (updatedPosition) {
              setSelectedPosition(updatedPosition);
            }
          }
        }
        
        console.log('TP/SL order removed successfully');
      } else {
        console.error('Failed to remove TP/SL order:', result.error);
        alert('Failed to remove order: ' + result.error);
      }
    } catch (error) {
      console.error('Failed to remove TP/SL order:', error);
      alert('Failed to remove order');
    } finally {
      setRemovingOrderId(null);
    }
  };

  const openTPSLModal = (position: Position, orderType: 'take_profit' | 'stop_loss') => {
    setTpslModal({ isOpen: true, position, orderType });
  };

  const closeTPSLModal = () => {
    setTpslModal({ isOpen: false });
  };

  const handleEditPosition = (position: Position) => {
    setSelectedPosition(position);
    setIsDetailsModalOpen(true);
  };

  const closeDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPosition(null);
  };

const formatCurrency = (value: number) =>
  new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);

const getPnLColor = (pnl: number) =>
  pnl >= 0 ? 'text-green-400' : 'text-red-400';

const getPnLIcon = (pnl: number) =>
  pnl >= 0 ? <TrendingUp size={14} /> : <TrendingDown size={14} />;

  return (
    <>
      <div className="rounded-2xl border border-neutral-800 overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-neutral-700 bg-neutral-900/40">
          <h3 className="text-white font-medium">Positions</h3>
        </div>
        
        {isLoading ? (
          <div className="text-center py-8 p-4">
            <div className="text-neutral-400">Loading positions...</div>
          </div>
        ) : positions.length === 0 ? (
          <div className="text-center py-8 p-4">
            <div className="text-neutral-400">No positions found</div>
          </div>
        ) : (
          <div className="overflow-x-auto p-4">
            <table className="w-full text-left">
              <thead>
                <tr className="border-b border-neutral-700 bg-neutral-900/40 backdrop-blur-md">
                  <th className="px-2 py-3 text-neutral-400 text-xs font-medium">Token</th>
                  <th className="px-2 py-3 text-neutral-400 text-xs font-medium text-right">Size & Type</th>
                  <th className="px-2 py-3 text-neutral-400 text-xs font-medium text-right">Entry</th>
                  <th className="px-2 py-3 text-neutral-400 text-xs font-medium text-right">Current</th>
                  <th className="px-2 py-3 text-neutral-400 text-xs font-medium text-right">P&L</th>
                  <th className="px-2 py-3 text-neutral-400 text-xs font-medium text-center">TP/SL</th>
                </tr>
              </thead>
              <tbody>
                {positions.map((position) => {
                  // Calculate real-time PnL
                  const currentPrice = currentPrices[position.token_address] || position.current_price;
                  const pnl = position.direction === 'buy' 
                    ? (currentPrice - position.entry_price) / position.entry_price * 100
                    : (position.entry_price - currentPrice) / position.entry_price * 100;
                  
                  return (
                    <tr key={position.id} className="border-b border-neutral-700 hover:bg-neutral-800/40 transition-colors">
                      <td className="py-3 px-2">
                        <div className="flex items-center gap-2">
                          {position.token_image && (
                            <img src={position.token_image} alt={position.token_symbol} className="w-6 h-6 rounded-full" />
                          )}
                          <div>
                            <div className="text-white font-medium">{position.token_symbol}</div>
                            <div className="text-gray-400 text-xs">{position.token_name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="text-right py-3 px-2">
                        <div className="text-white font-medium">{position.remaining_amount.toFixed(6)}</div>
                        <div className={`text-xs font-semibold ${
                          position.direction === "buy" ? "text-green-400" : "text-red-400"
                        }`}>
                          {position.direction === "buy" ? "🟢 LONG" : "🔴 SHORT"}
                        </div>
                      </td>
                      <td className="text-right py-3 px-2">
                        <div className="text-white font-medium">${position.entry_price.toFixed(6)}</div>
                      </td>
                      <td className="text-right py-3 px-2">
                        <div className="text-white font-medium">${currentPrice.toFixed(6)}</div>
                      </td>
                      <td className="text-right py-3 px-2">
                        <div className={`flex items-center justify-end ${getPnLColor(pnl)}`}>
                          {getPnLIcon(pnl)}
                          <span className="ml-1 font-medium">{Math.abs(pnl).toFixed(2)}%</span>
                        </div>
                      </td>
                      <td className="py-3 px-2 text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <button 
                            onClick={() => openTPSLModal(position, 'take_profit')}
                            className="p-1 rounded hover:bg-emerald-900/30 text-emerald-400"
                            title="Set Take Profit"
                          >
                            <Target size={16} />
                          </button>
                          <button 
                            onClick={() => openTPSLModal(position, 'stop_loss')}
                            className="p-1 rounded hover:bg-red-900/30 text-red-400"
                            title="Set Stop Loss"
                          >
                            <Shield size={16} />
                          </button>
                          <button 
                            onClick={() => handleEditPosition(position)}
                            className="p-1 rounded hover:bg-blue-900/30 text-blue-400"
                            title="Position Details"
                          >
                            <Eye size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* TP/SL Modal */}
      {tpslModal.isOpen && tpslModal.position && tpslModal.orderType && (
        <TPSLModal
          isOpen={tpslModal.isOpen}
          onClose={closeTPSLModal}
          position={tpslModal.position}
          orderType={tpslModal.orderType}
          onSubmit={handleCreateTPSL}
        />
      )}

      {/* Position Details Modal */}
      {isDetailsModalOpen && selectedPosition && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#1A1D24] rounded-lg p-6 w-96 max-w-[90vw]">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Eye size={20} className="text-blue-400" />
                <h3 className="text-lg font-semibold text-white">Position Details</h3>
              </div>
              <button onClick={closeDetailsModal} className="text-gray-400 hover:text-white">
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-[#2A2D35] rounded-lg">
                {selectedPosition.token_image && (
                  <img src={selectedPosition.token_image} alt={selectedPosition.token_symbol} className="w-10 h-10 rounded-full" />
                )}
                <div>
                  <div className="text-white font-medium">{selectedPosition.token_symbol}</div>
                  <div className="text-gray-400 text-sm">{selectedPosition.token_name}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div className="p-3 bg-[#2A2D35] rounded-lg">
                  <div className="text-gray-400 text-xs">Direction</div>
                  <div className="text-white font-medium">
                    {selectedPosition.direction === 'buy' ? 'Long' : 'Short'}
                  </div>
                </div>
                <div className="p-3 bg-[#2A2D35] rounded-lg">
                  <div className="text-gray-400 text-xs">Amount</div>
                  <div className="text-white font-medium">
                    {selectedPosition.remaining_amount.toFixed(6)}
                  </div>
                </div>
                <div className="p-3 bg-[#2A2D35] rounded-lg">
                  <div className="text-gray-400 text-xs">Entry Price</div>
                  <div className="text-white font-medium">
                    ${selectedPosition.entry_price.toFixed(6)}
                  </div>
                </div>
                <div className="p-3 bg-[#2A2D35] rounded-lg">
                  <div className="text-gray-400 text-xs">Current Price</div>
                  <div className="text-white font-medium">
                    ${(currentPrices[selectedPosition.token_address] || selectedPosition.current_price).toFixed(6)}
                  </div>
                </div>
              </div>

              <div className="p-3 bg-[#2A2D35] rounded-lg">
                <div className="text-gray-400 text-xs mb-2">Active Orders</div>
                <div className="text-sm">
                  {(selectedPosition.tp_orders?.length || 0) + (selectedPosition.sl_orders?.length || 0) === 0 ? (
                    <div className="text-gray-400">No active TP/SL orders</div>
                  ) : (
                    <div className="space-y-2">
                      {selectedPosition.tp_orders?.map(order => (
                        <div key={order.id} className="flex items-center justify-between bg-emerald-900/30 p-2 rounded">
                          <div className="flex items-center">
                            <Target size={14} className="text-emerald-400 mr-1" />
                            <span className="text-emerald-400">TP {order.trigger_percentage}%</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-white">{order.amount_percentage}%</span>
                            <button
                              onClick={() => handleRemoveTPSL(order.id)}
                              disabled={removingOrderId === order.id}
                              className="p-1 rounded hover:bg-red-900/50 text-red-400 hover:text-red-300 transition-colors disabled:opacity-50"
                              title="Remove TP Order"
                            >
                              {removingOrderId === order.id ? (
                                <div className="w-3 h-3 border border-red-400 border-t-transparent rounded-full animate-spin" />
                              ) : (
                                <X size={14} />
                              )}
                            </button>
                          </div>
                        </div>
                      ))}
                      {selectedPosition.sl_orders?.map(order => (
                        <div key={order.id} className="flex items-center justify-between bg-red-900/30 p-2 rounded">
                          <div className="flex items-center">
                            <Shield size={14} className="text-red-400 mr-1" />
                            <span className="text-red-400">SL {order.trigger_percentage}%</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-white">{order.amount_percentage}%</span>
                            <button
                              onClick={() => handleRemoveTPSL(order.id)}
                              disabled={removingOrderId === order.id}
                              className="p-1 rounded hover:bg-red-900/50 text-red-400 hover:text-red-300 transition-colors disabled:opacity-50"
                              title="Remove SL Order"
                            >
                              {removingOrderId === order.id ? (
                                <div className="w-3 h-3 border border-red-400 border-t-transparent rounded-full animate-spin" />
                              ) : (
                                <X size={14} />
                              )}
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={() => openTPSLModal(selectedPosition, 'take_profit')}
                  className="flex-1 bg-emerald-600/30 border border-emerald-700 text-emerald-400 py-2 px-4 rounded hover:bg-emerald-600/50 transition-colors"
                >
                  Add TP
                </button>
                <button
                  onClick={() => openTPSLModal(selectedPosition, 'stop_loss')}
                  className="flex-1 bg-red-600/30 border border-red-700 text-red-400 py-2 px-4 rounded hover:bg-red-600/50 transition-colors"
                >
                  Add SL
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Positions;
