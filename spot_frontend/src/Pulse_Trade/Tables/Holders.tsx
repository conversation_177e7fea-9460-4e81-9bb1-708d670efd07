import React from 'react';

interface HoldersProps {
  holders?: any[];
  isLoading?: boolean;
}

const Holders: React.FC<HoldersProps> = ({ holders, isLoading }) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500 mb-2"></div>
          <p className="text-neutral-400 text-sm">Loading holders data...</p>
        </div>
      </div>
    );
  }

  if (!holders || holders.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-neutral-400 text-sm mb-1">No holders data available</p>
          <p className="text-neutral-500 text-xs">Try refreshing or check back later</p>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg bg-[#0F1011]/50 shadow-lg border border-neutral-800/50">
      <div className="overflow-x-auto">
        <table className="w-full table-fixed">
          <colgroup>
            <col className="w-[35%]" />
            <col className="w-[20%]" />
            <col className="w-[25%]" />
            <col className="w-[20%]" />
          </colgroup>
          <thead className="sticky top-0 bg-[#121518] z-10">
            <tr className="border-b border-neutral-700">
              <th className="py-3 px-4 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">Address</th>
              <th className="py-3 px-4 text-right text-xs font-medium text-neutral-400 uppercase tracking-wider">Amount</th>
              <th className="py-3 px-4 text-right text-xs font-medium text-neutral-400 uppercase tracking-wider">Value (USD)</th>
              <th className="py-3 px-4 text-right text-xs font-medium text-neutral-400 uppercase tracking-wider">Share</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-neutral-800/70">
            {holders.map((holder) => (
              <tr key={holder.address} className="hover:bg-neutral-800/30 transition-colors">
                <td className="py-3 px-4 text-left truncate">
                  <div className="flex items-center">
                    <div className="h-6 w-6 flex-shrink-0 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 opacity-80 mr-2"></div>
                    <span className="text-sm text-white font-mono truncate">{`${holder.address.slice(0, 6)}...${holder.address.slice(-4)}`}</span>
                  </div>
                </td>
                <td className="py-3 px-4 text-right text-sm text-white font-medium whitespace-nowrap">{Number(holder.amount).toLocaleString(undefined, { maximumFractionDigits: 2 })}</td>
                <td className="py-3 px-4 text-right text-sm text-white font-medium whitespace-nowrap">${Number(holder.amountUSD).toLocaleString(undefined, { maximumFractionDigits: 2 })}</td>
                <td className="py-3 px-4 text-right whitespace-nowrap">
                  <div className="flex items-center justify-end">
                    <div className="w-16 bg-neutral-800 rounded-full h-1.5 mr-2">
                      <div 
                        className="bg-emerald-500 h-1.5 rounded-full" 
                        style={{ width: `${Math.min(100, holder.totalSupplyShare || 0)}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-emerald-400 font-medium">{holder.totalSupplyShare?.toFixed(2) || '0.00'}%</span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="py-2 px-4 border-t border-neutral-800/50 flex justify-between items-center">
        <span className="text-xs text-neutral-500">Showing {holders.length} holders</span>
        <a href="#" className="text-xs text-blue-400 hover:text-blue-300 transition-colors">View all holders</a>
      </div>
    </div>
  );
};

export default Holders;