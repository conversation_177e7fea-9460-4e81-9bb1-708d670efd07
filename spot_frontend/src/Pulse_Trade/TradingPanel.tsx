import React, { useState, useEffect, useCallback, useRef } from 'react';
import { DollarSign, Wallet, Coins, CircleDollarSign } from 'lucide-react';
import SlippageSettings from './TradingPanel/SlippageSettings';
// import AdvancedTradingStrategy from './TradingPanel/AdvancedTradingStrategy';
import NewTpSlSettings from './TradingPanel/NewTpSlSettings';
import Preset from './Preset/Preset';
import { getQuoteForExchange, QuoteRequest, QuoteResponse, getTokenBalance, BalanceRequest, BalanceResponse, getSwapForExchange, SwapRequest, SwapResponse, cancelBalanceRequest } from '../api/solana_api';
import { preloadWalletInfo } from '../api/privy_api';
import { showSwapSuccessToast, showSwapErrorToast, showSwapInfoToast } from '../utils/swapToast';
import { getDefaultSolanaWalletAddress, getDefaultSolanaWalletInfo, getDefaultSolanaWalletInfoWithDelegation, getDefaultSolanaWalletInfoEnhanced } from '../utils/walletUtils';
import { syncWalletSelection } from '../utils/walletSync';
import { useSessionSigners } from '../utils/sessionSigners';
import { useWalletDelegation } from '../hooks/useWalletDelegation';
import { DelegationToasts, showDedupedDelegationToast } from '../utils/delegationToast';
import { PulseTokenInfo } from '../hooks/usePulseData';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { usePreset } from './Preset/PresetContext';
import { useParams } from 'react-router-dom';
import TokenInfo from './TokenInfo/TokenInfo';
import { limitOrderService, CreateLimitOrderRequest } from '../services/limitOrderService';
import { useAutoFees } from '../hooks/useAutoFees';
interface BuySellToggleProps {
  isBuy: boolean;
  setIsBuy: (isBuy: boolean) => void;
}

interface TabNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  balance: string;
  isLoadingBalance: boolean;
  isBuy: boolean;
}

interface AmountInputProps {
  amount: string;
  setAmount: (amount: string) => void;
  onQuoteUpdate?: (quote: QuoteResponse | null) => void;
  isBuy: boolean;
  balanceData?: any; // Add balance data for percentage calculations
}

interface TabProps {
  amount: string;
  setAmount: (amount: string) => void;
  isBuy: boolean;
  onQuoteUpdate?: (quote: QuoteResponse | null) => void;
  balanceData?: any; // Add balance data for percentage calculations
  onLimitOrderDataUpdate?: (data: LimitOrderData) => void; // Add limit order data callback
  walletInfo?: { address: string; id: string } | null; // Add wallet info prop
  autoFeeState: any; // Auto fee state
  toggleAutoFees: () => void; // Auto fee toggle function
}

interface LimitOrderData {
  targetPrice: string;
  percentage: number;
  targetMarketCap: number;
  currentMarketCap: number;
}

interface BuyButtonProps {
  activeTab: string;
  isBuy: boolean;
  quoteData?: QuoteResponse | null;
  amount: string;
  balanceData: BalanceResponse | null; // Add balance data prop
  limitOrderData?: LimitOrderData; // Add limit order data prop
  onSwapComplete?: (result: SwapResponse) => void;
  onResetUI?: () => void; // New prop for resetting UI state
  // 🚀 BALANCE VALIDATION PROPS
  isLimitOrderBalanceValid?: boolean;
  limitOrderBalanceError?: string;
  autoFeeState?: any; // Add auto fee state prop
  walletInfo?: { address: string; id: string } | null; // Add wallet info prop
}

// Buy/Sell Toggle Component
const BuySellToggle: React.FC<BuySellToggleProps> = ({ isBuy, setIsBuy }) => (
  <div className="flex rounded-lg p-1 mx-4 my-4">
    <button 
      onClick={() => setIsBuy(true)}
      className={`flex-1 py-3 px-4 rounded-md font-medium transition-colors ${
        isBuy ? 'bg-[#214638] text-[#14FFA2]' : 'text-gray-400 hover:bg-[#222222] '
      }`}
    >
      Buy
    </button>
    <button 
      onClick={() => setIsBuy(false)}
      className={`flex-1 py-3 px-4 rounded-md font-medium transition-colors ${
        !isBuy ? 'bg-[#311D27] text-[#FF329B]' : 'text-gray-400 hover:bg-[#222222]'
      }`}
    >
      Sell
    </button>
  </div>
);

// Tab Navigation Component
const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  setActiveTab,
  balance,
  isLoadingBalance,
  isBuy,
}) => {
  const [activePulseToken, setActivePulseToken] = useState<any | null>(null);
  const params = useParams();
  const address = params?.address;

  useEffect(() => {
    try {
      const tokenStr = localStorage.getItem('activePulseToken');
      if (tokenStr) {
        const token = JSON.parse(tokenStr);
        setActivePulseToken(token);
      }
    } catch (error) {
      console.error('Failed to parse activePulseToken:', error);
    }
  }, [address]);

  return (
    <div className="flex items-center justify-between px-4 pt-4">
      <div className="flex space-x-6">
        {['Market', 'Limit'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`pb-2 font-medium transition-colors ${
              activeTab === tab
                ? 'text-white border-b-2 border-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>
      <div className="flex items-center space-x-2 text-sm">
        <Wallet size={16} className="text-gray-400" />
        <span className="text-white">1</span>
        <div className="flex items-center space-x-1">
          {isBuy ? (
            <CircleDollarSign size={12} className="text-cyan-400" />
          ) : (
            <Coins size={12} className="text-cyan-400" />
          )}
          <span className="text-white text-xs">
            {isLoadingBalance ? (
              <div className="animate-pulse">...</div>
            ) : (
              `${balance || '0'} ${isBuy ? 'SOL' : activePulseToken?.symbol || 'TKN'}`
            )}
          </span>
        </div>
      </div>
    </div>
  );
};
// Amount Input Component
const AmountInput: React.FC<AmountInputProps> = ({ amount, setAmount, onQuoteUpdate, isBuy, balanceData }) => {
  const [inputValue, setInputValue] = useState<string>(amount);
  const [isLoadingQuote, setIsLoadingQuote] = useState<boolean>(false);

  // Debounced quote fetching
  const fetchQuote = useCallback(async (amountValue: string) => {
    if (!amountValue || parseFloat(amountValue) <= 0) {
      onQuoteUpdate?.(null);
      return;
    }

    try {
      // Get active pulse token from localStorage
      const activePulseTokenStr = localStorage.getItem('activePulseToken');
      if (!activePulseTokenStr) {
        console.log('No active pulse token found');
        return;
      }

      const activePulseToken: PulseTokenInfo = JSON.parse(activePulseTokenStr);

      // Check if token is on Solana network
      if (!activePulseToken.network?.toLowerCase().includes('solana')) {
        console.log('Token is not on Solana network:', activePulseToken.network);
        onQuoteUpdate?.(null);
        return;
      }

      // Check if exchange is supported
      const exchangeName = activePulseToken.exchange_name;
      if (!exchangeName ||
          (!['PumpSwap', 'PumpFun', 'LaunchLab'].includes(exchangeName))) {
        console.log('Unsupported exchange:', exchangeName);
        onQuoteUpdate?.(null);
        return;
      }

      setIsLoadingQuote(true);

      const quoteRequest: QuoteRequest = {
        tokenAddress: activePulseToken.address,
        poolAddress: activePulseToken.pool_address || '',
        dexType: exchangeName.toLowerCase(),
        amount: parseFloat(amountValue),
        direction: isBuy ? 'buy' : 'sell',
        slippage: 0.10 // Default slippage
      };

      const response = await getQuoteForExchange(exchangeName, quoteRequest);
      onQuoteUpdate?.(response);

    } catch (error) {
      console.error('Error fetching quote:', error);
      onQuoteUpdate?.(null);
    } finally {
      setIsLoadingQuote(false);
    }
  }, [onQuoteUpdate, isBuy]);

  // Sync inputValue with amount prop when it changes externally
  useEffect(() => {
    setInputValue(amount);
  }, [amount]);

  // Debounce quote fetching
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchQuote(inputValue);
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [inputValue, fetchQuote]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Only allow numbers, decimal point, and empty string
    const numericRegex = /^[0-9]*\.?[0-9]*$/;

    if (value === '' || numericRegex.test(value)) {
      // Prevent multiple decimal points
      const decimalCount = (value.match(/\./g) || []).length;
      if (decimalCount <= 1) {
        setInputValue(value);
        if (setAmount) setAmount(value);
      }
    }
  };

  const handleQuickSelect = (value: string) => {
    let calculatedAmount = value;

    // Handle percentage-based amounts for sell transactions
    if (!isBuy && balanceData?.success) {
      const tokenBalance = parseFloat(balanceData.data.tokenBalance);

      if (value.includes('%')) {
        const percentage = parseFloat(value.replace('%', ''));
        calculatedAmount = (tokenBalance * (percentage / 100)).toFixed(6);
      } else if (value === 'MAX') {
        // For sell MAX, use 99% to leave some buffer for fees
        calculatedAmount = (tokenBalance * 0.99).toFixed(6);
      }
    } else if (isBuy && balanceData?.success) {
      if (value === 'MAX') {
        // For buy MAX, use available SOL minus dynamic fee reserve
        const solBalance = parseFloat(balanceData.data.solBalance);
        const reserveAmount = calculateDynamicFeeReserve(solBalance);
        calculatedAmount = Math.max(0, solBalance - reserveAmount).toFixed(4);
      }
      // For other buy values (0.01, 0.1, 1, 10), use the value as-is
    }

    setInputValue(calculatedAmount);
    if (setAmount) setAmount(calculatedAmount);
  };

  return (
    <div className="mb-6">
      {/* Top: Label + Input + Icon */}
      <div className="">
        <div className="relative">
          {/* Left-side 'Amount' label inside input */}
          <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-sm text-slate-400">
            Amount
          </span>
  
          {/* Right-side Solana icon */}
          <img
            src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
            alt="Solana"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
  
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={(e) => {
              // Prevent 'e', 'E', '+', '-' which are valid in number inputs but not desired here
              if (['e', 'E', '+', '-'].includes(e.key)) {
                e.preventDefault();
              }
            }}
            placeholder="0.0"
            className={`w-full bg-[#2A2D35] border border-gray-700 text-white rounded-md py-2 pl-20 pr-12 text-sm focus:outline-none focus:border-gray-500 ${
              isLoadingQuote ? 'opacity-75' : ''
            }`}
          />
          {/* Dynamic currency icon */}
          <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
            {isLoadingQuote ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              isBuy ? (
                <CircleDollarSign size={16} className="text-cyan-400" />
              ) : (
                <Coins size={16} className="text-cyan-400" />
              )
            )}
          </div>
        </div>
      </div>
  
      {/* 🚀 BALANCE DISPLAY */}
      {balanceData?.success && (
        <div className="mt-2 mb-1 flex justify-between items-center text-xs">
          <span className="text-gray-400">Available:</span>
          <span className="text-gray-300">
            {isBuy ? (
              <>
                {parseFloat(balanceData.data.solBalance).toFixed(4)} SOL
              </>
            ) : (
              <>
                {parseFloat(balanceData.data.tokenBalance) > 1
                  ? Math.round(parseFloat(balanceData.data.tokenBalance)).toString()
                  : parseFloat(balanceData.data.tokenBalance).toFixed(6)
                } tokens
              </>
            )}
          </span>
        </div>
      )}

      {/* Bottom: Quick Select Buttons */}
      <div className="grid grid-cols-5 gap-2 mt-3">
        {(isBuy
          ? ['0.01', '0.1', '1', '10', 'MAX'] // Buy: Fixed SOL amounts
          : ['10%', '25%', '50%', '75%', 'MAX'] // Sell: Percentage of token balance
        ).map((value) => (
          <button
            key={value}
            onClick={() => handleQuickSelect(value)}
            className={`border border-gray-700 hover:bg-gray-700 text-gray-300 py-2 px-3 text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 ${
              value === 'MAX' ? 'bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-500 hover:to-blue-500 text-white font-medium' : ''
            }`}
          >
            {value}
          </button>
        ))}
      </div>
    </div>
  );
  
}
  
  




// Market Tab Component
const MarketTab: React.FC<TabProps> = ({ amount, setAmount, isBuy, onQuoteUpdate, balanceData, walletInfo, autoFeeState, toggleAutoFees }) => {
  const [showTpSl, setShowTpSl] = useState<boolean>(false);

  return (
    <div className="px-4">
      <AmountInput amount={amount} setAmount={setAmount} isBuy={isBuy} onQuoteUpdate={onQuoteUpdate} balanceData={balanceData} />
      <SlippageSettings isBuy={isBuy} autoFeeState={autoFeeState} toggleAutoFees={toggleAutoFees} />

      {/* TP/SL Section - Only show for BUY mode */}
      {isBuy && (
        <div className="mb-6">
          <label className="flex items-center space-x-2 text-gray-300 w-full">
            <input 
              type="checkbox" 
              className="rounded" 
              checked={showTpSl}
              onChange={(e) => setShowTpSl(e.target.checked)}
            />
            <span>Take Profit / Stop Loss</span>
          </label>

          {showTpSl && (
            <div className="mt-4 w-full">
              <NewTpSlSettings 
                solBalance={balanceData?.success ? parseFloat(balanceData.data.solBalance) : 0}
                walletInfo={walletInfo}
                onOrderCreated={() => {
                  // Optionally refresh balance or do other actions
                  console.log('TP/SL orders created');
                }}
              />
            </div>
          )}
        </div>
      )}

      {/* Advanced Trading Strategy Toggle - Hidden */}
      {/* <div className="mb-6">
        <label className="flex items-center space-x-2 text-gray-300 w-full">
          <input 
            type="checkbox" 
            className="rounded" 
            checked={showAdvancedStrategy}
            onChange={(e) => setShowAdvancedStrategy(e.target.checked)}
          />
          <span>Advanced Trading Strategy</span>
        </label> */}

        {/* Advanced Trading Strategy - Hidden */}
        {/* {showAdvancedStrategy && (
          <div className="mt-4 w-full">
            <AdvancedTradingStrategy isBuy={isBuy} />
          </div>
        )} */}
      {/* </div> */}
    </div>
  );
};

// Limit Tab Component
const LimitTab: React.FC<TabProps> = ({ amount, setAmount, isBuy, onQuoteUpdate, balanceData, onLimitOrderDataUpdate, autoFeeState, toggleAutoFees }) => {
  const [sliderValue, setSliderValue] = useState<number>(0);
  const [percentage, setPercentage] = useState<number>(0);
  const [activePulseToken, setActivePulseToken] = useState<any | null>(null);
  const [currentMarketCap, setCurrentMarketCap] = useState<number>(0);
  const [targetMarketCap, setTargetMarketCap] = useState<number>(0);
  const [targetPrice, setTargetPrice] = useState<string>('0.00000000');

  const params = useParams();
  const address = params?.address;

  // Load active token data and calculate initial values
  useEffect(() => {
    try {
      const tokenStr = localStorage.getItem('activePulseToken');
      if (tokenStr) {
        const token = JSON.parse(tokenStr);
        setActivePulseToken(token);

        // Set current market cap from token data
        const marketCap = token.market_cap || 0;
        setCurrentMarketCap(marketCap);
        setTargetMarketCap(marketCap); // Initialize target to current

        // Calculate initial target price
        if (marketCap > 0 && token.supply > 0) {
          const price = marketCap / token.supply;
          setTargetPrice(price.toFixed(8));
        }
      }
    } catch (error) {
      console.error('Failed to parse activePulseToken:', error);
    }
  }, [address]);

  // Update target market cap and price when slider changes
  useEffect(() => {
    if (currentMarketCap > 0) {
      // Calculate target market cap: Current MC × (1 + percentage/100)
      const newTargetMarketCap = currentMarketCap * (1 + percentage / 100);
      setTargetMarketCap(newTargetMarketCap);

      // Calculate target price: Target MC / Token Supply
      if (activePulseToken?.supply > 0) {
        const newTargetPrice = newTargetMarketCap / activePulseToken.supply;
        setTargetPrice(newTargetPrice.toFixed(8));
      }
    }
  }, [percentage, currentMarketCap, activePulseToken?.supply]);

  // Update parent component with limit order data
  useEffect(() => {
    if (onLimitOrderDataUpdate) {
      onLimitOrderDataUpdate({
        targetPrice,
        percentage,
        targetMarketCap,
        currentMarketCap
      });
    }
  }, [targetPrice, percentage, targetMarketCap, currentMarketCap, onLimitOrderDataUpdate]);

  // Format market cap for display
  const formatMarketCap = (cap: number) => {
    if (cap >= 1_000_000_000) return `${(cap / 1_000_000_000).toFixed(2)}B`;
    if (cap >= 1_000_000) return `${(cap / 1_000_000).toFixed(2)}M`;
    if (cap >= 1_000) return `${(cap / 1_000).toFixed(2)}K`;
    return cap.toFixed(0);
  };

  // Handle slider change
  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    setSliderValue(value);
    setPercentage(value);
  };

  return (
    <div className="px-4">
      <AmountInput amount={amount} setAmount={setAmount} isBuy={isBuy} onQuoteUpdate={onQuoteUpdate} balanceData={balanceData} />
      <SlippageSettings isBuy={isBuy} autoFeeState={autoFeeState} toggleAutoFees={toggleAutoFees} />

      <div className="mb-6">
        {/* Current vs Target Market Cap Display */}
        <div className="flex items-center justify-between mb-2">
          <span className="text-gray-400 text-sm">MKT CAP</span>
          <div className="flex items-center space-x-2">
            <span className="text-gray-400 text-sm">${formatMarketCap(currentMarketCap)}</span>
            <span className="text-gray-500">→</span>
            <span className="text-white font-medium">${formatMarketCap(targetMarketCap)}</span>
          </div>
          <DollarSign size={16} className="text-gray-400" />
        </div>

        {/* Slider */}
        <div className="relative mb-4">
          <input
            type="range"
            min="-100"
            max="100"
            value={sliderValue}
            onChange={handleSliderChange}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>-100%</span>
            <span>-50%</span>
            <span>0%</span>
            <span>+50%</span>
            <span>+100%</span>
          </div>
        </div>

        <div className="flex justify-end mb-4">
          <div className="bg-gray-800 px-3 py-2 rounded flex items-center space-x-2">
            <span className={`text-white font-medium ${
              percentage > 0 ? 'text-green-400' : percentage < 0 ? 'text-red-400' : 'text-white'
            }`}>
              {percentage > 0 ? '+' : ''}{percentage.toFixed(1)}
            </span>
            <span className="text-gray-400">%</span>
          </div>
        </div>
      </div>
    </div>
  );
};


// Helper function to calculate dynamic fee reserve based on balance and transaction amount
const calculateDynamicFeeReserve = (solBalance: number, requestedAmount?: number): number => {
  if (solBalance < 0.02) {
    // For very small balances, use minimal reserve (0.002 SOL)
    return 0.002;
  } else if (requestedAmount && requestedAmount < 0.001) {
    // For micro transactions, use small reserve (0.003 SOL)
    return 0.003;
  } else if (requestedAmount && requestedAmount < 0.01) {
    // For small transactions, use moderate reserve (0.005 SOL)
    return 0.005;
  } else if (solBalance < 0.05) {
    // For small balances, use small reserve (0.003 SOL)
    return 0.003;
  } else if (solBalance < 0.1) {
    // For medium balances, use moderate reserve (0.005 SOL)
    return 0.005;
  } else {
    // For larger balances/transactions, use standard reserve (0.01 SOL)
    return 0.01;
  }
};

// Buy Button Component
const BuyButton: React.FC<BuyButtonProps> = ({
  activeTab,
  isBuy,
  quoteData,
  amount,
  balanceData,
  limitOrderData,
  onSwapComplete,
  onResetUI,
  isLimitOrderBalanceValid,
  limitOrderBalanceError,
  autoFeeState,
  walletInfo
}) => {
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionStage, setExecutionStage] = useState<string>('');
  const [executionProgress, setExecutionProgress] = useState<number>(0);
  const { authenticated, user } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  const preset = usePreset();
  const [activePulseToken, setActivePulseToken] = useState<any | null>(null);

  const params = useParams();
  const address = params?.address;

  useEffect(() => {
    try {
      const tokenStr = localStorage.getItem('activePulseToken');
      if (tokenStr) {
        const token = JSON.parse(tokenStr);
        setActivePulseToken(token);
      }
    } catch (error) {
      console.error('Failed to parse activePulseToken:', error);
    }
  }, [address]); // Updates when address param changes



  // Get Solana wallet address and ID (enhanced with delegation management)
  const getSolanaWalletInfo = async () => {
    // Use enhanced delegation-aware wallet info function
    return await getDefaultSolanaWalletInfoEnhanced(
      authenticated,
      user?.id,
      solanaWallets,
      user,
      async (address: string) => {
        console.log('[TradingPanel] Wallet delegation requested for address:', address);
        console.log('[TradingPanel] Current user ID:', user?.id);
        console.log('[TradingPanel] Available Solana wallets:', solanaWallets.map(w => ({
          address: w.address,
          type: w.walletClientType
        })));

        // Use specialized delegation toast system
        showDedupedDelegationToast('Enabling wallet for trading...', 'info');

        try {
          console.log('[TradingPanel] Calling delegateSolanaWallet...');
          const success = await delegateSolanaWallet(address);
          console.log('[TradingPanel] Delegation result:', success);

          if (success === true) {
            DelegationToasts.delegationSuccess(address);
            console.log('[TradingPanel] Wallet delegation successful for:', address);
            return true;
          } else if (success === 'imported') {
            DelegationToasts.importedWalletWarning(address);
            console.log('[TradingPanel] Wallet is imported, cannot delegate:', address);
            return false;
          } else {
            DelegationToasts.delegationFailed(address);
            console.error('[TradingPanel] Wallet delegation failed for:', address);
            return false;
          }
        } catch (error) {
          console.error('[TradingPanel] Error during wallet delegation:', error);
          DelegationToasts.apiError(error instanceof Error ? error.message : 'Unknown error');
          return false;
        }
      }
    );
  };

  // Execute transaction function - handles both market orders and limit orders
  const executeTransaction = async () => {
    if (!authenticated) {
      showSwapErrorToast('Please connect your wallet first');
      return;
    }

    // Use cached wallet info instead of calling API again
    let currentWalletInfo = walletInfo;

    // Only call API if we don't have cached wallet info
    if (!currentWalletInfo) {
      console.log('[TradingPanel] No cached wallet info, fetching...');
      currentWalletInfo = await getSolanaWalletInfo();
    }

    if (!currentWalletInfo) {
      // Enhanced error message with debugging info
      const errorMessage = `No Solana wallet found. Debug info:
      - Authenticated: ${authenticated}
      - Solana wallets count: ${solanaWallets.length}
      - User ID: ${user?.id || 'Not available'}
      - Cached wallet info: ${walletInfo ? 'Available' : 'Not available'}
      - Wallets: ${JSON.stringify(solanaWallets.map(w => ({ address: w.address, type: w.walletClientType })), null, 2)}`;

      console.error(errorMessage);
      showSwapErrorToast('No Solana wallet found. Please ensure you have a Solana wallet connected through Privy.');
      return;
    }

    // Get active token info
    const activePulseTokenStr = localStorage.getItem('activePulseToken');
    if (!activePulseTokenStr) {
      showSwapErrorToast('No token selected');
      return;
    }

    const activePulseToken: PulseTokenInfo = JSON.parse(activePulseTokenStr);

    if (!activePulseToken.exchange_name ||
        !['PumpSwap', 'PumpFun', 'LaunchLab'].includes(activePulseToken.exchange_name)) {
      showSwapErrorToast('Unsupported exchange');
      return;
    }

    if (parseFloat(amount) <= 0) {
      showSwapErrorToast('Please enter a valid amount');
      return;
    }

    // Branch execution based on active tab
    if (activeTab === 'Limit') {
      await executeLimitOrder(currentWalletInfo, activePulseToken);
    } else {
      await executeMarketOrder(currentWalletInfo, activePulseToken);
    }
  };

  // Execute limit order creation
  const executeLimitOrder = async (walletInfo: any, activePulseToken: PulseTokenInfo) => {
    // Validate limit order data
    if (!limitOrderData || !limitOrderData.targetPrice || parseFloat(limitOrderData.targetPrice) <= 0) {
      showSwapErrorToast('Please set a valid target price using the slider');
      return;
    }

    if (!user?.id) {
      showSwapErrorToast('User ID not available');
      return;
    }

    // 🚀 BALANCE VALIDATION FOR LIMIT ORDERS
    // Check if balance data is available
    if (!balanceData?.success) {
      showSwapErrorToast('Unable to verify balance. Please try again.');
      return;
    }

    const requestedAmount = parseFloat(amount);

    // Validate amount is positive
    if (requestedAmount <= 0) {
      showSwapErrorToast('Please enter a valid amount greater than 0.');
      return;
    }

    if (isBuy) {
      // Buy Order Validation: Check SOL balance
      const solBalance = parseFloat(balanceData.data.solBalance);

      if (solBalance < requestedAmount) {
        const shortfall = requestedAmount - solBalance;
        showSwapErrorToast(
          `Insufficient SOL balance. You have ${solBalance.toFixed(4)} SOL, but trying to spend ${requestedAmount} SOL. Shortfall: ${shortfall.toFixed(4)} SOL.`
        );
        return;
      }
    } else {
      // Sell Order Validation: Check token balance
      const tokenBalance = parseFloat(balanceData.data.tokenBalance);

      if (tokenBalance < requestedAmount) {
        const shortfall = requestedAmount - tokenBalance;
        showSwapErrorToast(
          `Insufficient token balance. You have ${tokenBalance > 1 ? Math.round(tokenBalance) : tokenBalance.toFixed(6)} tokens, but trying to sell ${requestedAmount}. Shortfall: ${shortfall > 1 ? Math.round(shortfall) : shortfall.toFixed(6)}.`
        );
        return;
      }
    }

    setIsExecuting(true);
    setExecutionStage('Creating limit order...');
    setExecutionProgress(20);

    try {
      // Get current preset settings for slippage
      const currentPresetSettings = preset.activePreset && preset.presetData[preset.activePreset]
        ? preset.presetData[preset.activePreset][isBuy ? 'buy' : 'sell']
        : {
            slippage: 10,
            priority: 0.0001,
            bribe: 0.00005,
            mevMode: 'Off'
          };

      // Use auto-calculated fees if auto mode is enabled, otherwise use preset fees
      const effectivePriority = autoFeeState.isEnabled && autoFeeState.dynamicFees 
        ? autoFeeState.dynamicFees.priorityFee 
        : currentPresetSettings.priority;
      
      const effectiveBribe = autoFeeState.isEnabled && autoFeeState.dynamicFees
        ? autoFeeState.dynamicFees.bribeAmount
        : currentPresetSettings.bribe;

      setExecutionStage('Preparing limit order data...');
      setExecutionProgress(40);

      // Prepare limit order request
      const limitOrderRequest: CreateLimitOrderRequest = {
        user_id: user.id,
        token_address: activePulseToken.address,
        token_name: activePulseToken.name || 'Unknown Token',
        token_symbol: activePulseToken.symbol || 'UNKNOWN',
        token_image: activePulseToken.imageUrl || '', // Include token image
        pool_address: activePulseToken.pool_address || '',
        dex_type: activePulseToken.exchange_name.toLowerCase(),
        direction: isBuy ? 'buy' : 'sell',
        amount: parseFloat(amount),
        target_price: parseFloat(limitOrderData.targetPrice),
        current_price: activePulseToken.price || 0,
        current_market_cap: limitOrderData.currentMarketCap || activePulseToken.market_cap,
        target_market_cap: limitOrderData.targetMarketCap,
        slippage: currentPresetSettings.slippage / 100, // Convert percentage to decimal
        wallet_address: walletInfo.address,
        wallet_id: walletInfo.id,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
      };


      setExecutionStage('Submitting limit order...');
      setExecutionProgress(70);

      const result = await limitOrderService.createLimitOrder(limitOrderRequest);


      if (result.success && result.data) {
        setExecutionStage('Limit order created!');
        setExecutionProgress(100);

        const tokenSymbol = activePulseToken.symbol || 'TOKEN';
        const targetPrice = parseFloat(limitOrderData.targetPrice);
        const formattedPrice = targetPrice >= 0.01
          ? `$${targetPrice.toFixed(4)}`
          : `$${targetPrice.toFixed(8)}`;

        // Use info toast for limit order creation since it doesn't have a transaction signature
        showSwapInfoToast(
          `${isBuy ? 'Buy' : 'Sell'} limit order created @ ${formattedPrice} for ${tokenSymbol}`
        );

        // Reset UI state
        onResetUI?.();

        // Dispatch custom event for limit order creation
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('limitOrderCreated', {
            detail: {
              tokenSymbol,
              direction: isBuy ? 'buy' : 'sell',
              amount: parseFloat(amount),
              targetPrice: parseFloat(limitOrderData.targetPrice),
              orderId: result.data?.id,
              timestamp: Date.now()
            }
          }));
        }, 1000);
      } else {
        setExecutionStage('Limit order failed');
        setExecutionProgress(0);
        const errorMessage = result.error || 'Failed to create limit order';
        showSwapErrorToast(errorMessage);
      }

    } catch (error) {
      console.error('Limit order creation error:', error);
      setExecutionStage('Error occurred');
      setExecutionProgress(0);
      showSwapErrorToast(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      // Reset execution states after a short delay to show final status
      setTimeout(() => {
        setIsExecuting(false);
        setExecutionStage('');
        setExecutionProgress(0);
      }, 2000);
    }
  };


  // Execute market order (original swap logic)
  const executeMarketOrder = async (walletInfo: any, activePulseToken: PulseTokenInfo) => {
    // Validate balance for both buy and sell transactions
    if (balanceData?.success) {
      const requestedAmount = parseFloat(amount);

      if (isBuy) {
        // Validate SOL balance for buy transactions
        const solBalance = parseFloat(balanceData.data.solBalance);

        if (solBalance < requestedAmount) {
          const shortfall = requestedAmount - solBalance;
          showSwapErrorToast(
            `Insufficient SOL balance. You have ${solBalance.toFixed(4)} SOL, ` +
            `but trying to spend ${requestedAmount} SOL. Shortfall: ${shortfall.toFixed(4)} SOL.`
          );
          return;
        }

        // Warn if spending more than 95% of SOL balance
        if (requestedAmount > solBalance * 0.95) {
          console.warn(`Spending ${((requestedAmount / solBalance) * 100).toFixed(1)}% of SOL balance`);
        }

        // Use dynamic fee reserve calculation
        const reserveAmount = calculateDynamicFeeReserve(solBalance, requestedAmount);
        const remainingAfterTransaction = solBalance - requestedAmount;

        console.log('[TradingPanel] Balance validation:', {
          solBalance,
          requestedAmount,
          reserveAmount,
          remainingAfterTransaction,
          isValid: remainingAfterTransaction >= reserveAmount
        });

        if (remainingAfterTransaction < reserveAmount) {
          showSwapErrorToast(
            `Insufficient SOL for transaction fees. Please reserve at least ${reserveAmount.toFixed(4)} SOL for fees. ` +
            `Maximum spendable: ${Math.max(0, solBalance - reserveAmount).toFixed(4)} SOL.`
          );
          return;
        }
      } else {
        // Validate token balance for sell transactions
        const tokenBalance = parseFloat(balanceData.data.tokenBalance);

        if (tokenBalance < requestedAmount) {
          const shortfall = requestedAmount - tokenBalance;
          const maxSellable = Math.floor(tokenBalance * 0.99 * 1000000) / 1000000; // 99% of balance, rounded to 6 decimals
          showSwapErrorToast(
            `Insufficient token balance. You have ${tokenBalance.toFixed(6)} tokens, ` +
            `but trying to sell ${requestedAmount} tokens. Shortfall: ${shortfall.toFixed(6)} tokens. ` +
            `Maximum sellable amount: ${maxSellable.toFixed(6)} tokens.`
          );
          return;
        }

        // Warn if selling more than 95% of balance
        if (requestedAmount > tokenBalance * 0.95) {
          console.warn(`Selling ${((requestedAmount / tokenBalance) * 100).toFixed(1)}% of token balance`);
        }
      }
    } else {
      // If balance data is not available, show a warning but allow transaction to proceed
      console.warn('Balance data not available, proceeding without balance validation');
      showSwapInfoToast('Balance data unavailable - proceeding with transaction');
    }

    setIsExecuting(true);
    setExecutionStage('Preparing transaction...');
    setExecutionProgress(10);

    try {
      // Get current preset settings
      setExecutionStage('Loading preset settings...');
      setExecutionProgress(20);

      const currentPresetSettings = preset.activePreset && preset.presetData[preset.activePreset]
        ? preset.presetData[preset.activePreset][isBuy ? 'buy' : 'sell']
        : {
            slippage: 10,
            priority: 0.0001,
            bribe: 0.00005,
            mevMode: 'Off'
          };

      // Use auto-calculated fees if auto mode is enabled, otherwise use preset fees
      const effectivePriority = autoFeeState.isEnabled && autoFeeState.dynamicFees 
        ? autoFeeState.dynamicFees.priorityFee 
        : currentPresetSettings.priority;
      
      const effectiveBribe = autoFeeState.isEnabled && autoFeeState.dynamicFees
        ? autoFeeState.dynamicFees.bribeAmount
        : currentPresetSettings.bribe;

      // Prepare swap request with all required parameters
      setExecutionStage('Building transaction...');
      setExecutionProgress(40);

      const swapRequest: SwapRequest = {
        tokenAddress: activePulseToken.address,
        poolAddress: activePulseToken.pool_address || '',
        dexType: activePulseToken.exchange_name.toLowerCase(),
        amount: parseFloat(amount),
        direction: isBuy ? 'buy' : 'sell',
        slippage: currentPresetSettings.slippage / 100, // Convert percentage to decimal
        walletAddress: walletInfo.address,
        walletId: walletInfo.id,

        // Token metadata from frontend (efficient - no backend API calls needed)
        tokenName: activePulseToken.name,
        tokenSymbol: activePulseToken.symbol,
        tokenImage: activePulseToken.imageUrl || activePulseToken.exchange_logo,

        // MEV Protection settings from preset - only enable for known MEV modes
        mevProtection: currentPresetSettings.mevMode !== 'Off',
        bribeAmount: effectiveBribe, // Use auto fee or preset fee
        // Enhanced priority level mapping to utilize all four levels: 'low' | 'medium' | 'high' | 'veryHigh'
        priorityLevel: currentPresetSettings.mevMode === 'Sec.' ? 'veryHigh' :   // Secure mode: maximum priority
                      currentPresetSettings.mevMode === 'Red.' ? 'high' :        // Reduced mode: high priority
                      currentPresetSettings.mevMode === 'Off' ? 'low' :       // MEV off: reliable medium priority
                      'low', // Fallback for any unknown modes
        priorityFee: effectivePriority // Use auto fee or preset fee
      };


      setExecutionStage('Submitting to blockchain...');
      setExecutionProgress(60);

      const result = await getSwapForExchange(activePulseToken.exchange_name, swapRequest);


      if (result.success && result.data) {
        setExecutionStage('Transaction confirmed!');
        setExecutionProgress(100);

        // Extract transaction signature from response
        const transactionSignature = result.data.signature || result.data.transactionHash || '';
        const tokenSymbol = activePulseToken.symbol || 'TOKEN';

        // Show success toast with Solscan link
        if (transactionSignature) {
          showSwapSuccessToast(
            isBuy ? 'buy' : 'sell',
            tokenSymbol,
            transactionSignature,
            result.data.solscanUrl // Use provided URL if available
          );
        } else {
          showSwapInfoToast(`Successfully ${isBuy ? 'bought' : 'sold'} ${tokenSymbol}`);
        }

        // Reset UI state
        onResetUI?.();

        // Call completion callback
        onSwapComplete?.(result);

        // Dispatch custom event for balance refresh
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('pulseTradeSuccess', {
            detail: {
              tokenSymbol,
              transactionSignature,
              direction: isBuy ? 'buy' : 'sell',
              amount: parseFloat(amount),
              timestamp: Date.now()
            }
          }));
        }, 1000); // 1 second delay for immediate feedback
      } else {
        setExecutionStage('Transaction failed');
        setExecutionProgress(0);
        // Enhanced error handling for MEV-specific errors
        const errorMessage = result.error || 'Swap failed for unknown reason';

        if (errorMessage.includes('Jito') || errorMessage.includes('bundle')) {
          showSwapErrorToast(`MEV Protection failed: ${errorMessage}. Try disabling MEV protection in settings.`);
        } else if (errorMessage.includes('bribe') || errorMessage.includes('tip')) {
          showSwapErrorToast(`MEV tip too low: ${errorMessage}. Try increasing bribe amount in settings.`);
        } else if (errorMessage.includes('timeout') && currentPresetSettings.mevMode !== 'Off') {
          showSwapErrorToast(`Transaction timeout (MEV protection may be causing delays): ${errorMessage}`);
        } else if (errorMessage.includes('slippage') || errorMessage.includes('TooLittleSolReceived')) {
          showSwapErrorToast(`Slippage protection triggered: ${errorMessage}. Try increasing slippage tolerance.`);
        } else {
          showSwapErrorToast(errorMessage);
        }
      }

    } catch (error) {
      console.error('Swap execution error:', error);
      setExecutionStage('Error occurred');
      setExecutionProgress(0);
      showSwapErrorToast(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      // Reset execution states after a short delay to show final status
      setTimeout(() => {
        setIsExecuting(false);
        setExecutionStage('');
        setExecutionProgress(0);
      }, 2000);
    }
  };

  const getButtonText = () => {

    if (isExecuting) {
      if (executionStage) {
        return `${executionStage} (${executionProgress}%)`;
      }
      return `${isBuy ? 'Buying' : 'Selling'}...`;
    }

    if (activeTab === 'Limit') {
      if (limitOrderData && limitOrderData.targetMarketCap && limitOrderData.targetMarketCap > 0) {
        // Format market cap for display
        const formatMarketCap = (cap: number) => {
          if (cap >= 1_000_000_000) return `$${(cap / 1_000_000_000).toFixed(2)}B`;
          if (cap >= 1_000_000) return `$${(cap / 1_000_000).toFixed(2)}M`;
          if (cap >= 1_000) return `$${(cap / 1_000).toFixed(2)}K`;
          return `$${cap.toFixed(0)}`;
        };

        const formattedMarketCap = formatMarketCap(limitOrderData.targetMarketCap);
        return `${isBuy ? 'Buy' : 'Sell'} @ ${formattedMarketCap} MC`;
      } else {
        // Fallback when no target market cap is set
        return `${isBuy ? 'Buy' : 'Sell'} Limit Order`;
      }
    }

    // Display minOut value if quote data is available
    if (quoteData?.success && quoteData.data?.minOut) {
      const minOut = quoteData.data.minOut;

      if (isBuy) {
        // Buy mode: Format as integer + token symbol
        const formattedAmount = Math.round(minOut).toString();

        return `Buy ${formattedAmount} ${activePulseToken?.symbol}`;
      } else {
        // Sell mode: Show raw minOut value without formatting
        return `Sell ${minOut}`;
      }
    }

    return `${isBuy ? 'Buy' : 'Sell'} ${activePulseToken?.symbol}`;
  };

  // 🚀 BALANCE VALIDATION FOR LIMIT ORDERS
  const isLimitOrderDisabled = activeTab === 'Limit' && !isLimitOrderBalanceValid;
  const isButtonDisabled = isExecuting || !authenticated || parseFloat(amount) <= 0 || isLimitOrderDisabled;

  return (
    <div className="relative">
      {/* 🚀 BALANCE ERROR MESSAGE FOR LIMIT ORDERS */}
      {activeTab === 'Limit' && limitOrderBalanceError && (
        <div className="mb-3 p-3 bg-red-900/20 border border-red-500/30 rounded-md">
          <div className="flex items-start space-x-2">
            <div className="flex-shrink-0 mt-0.5">
              <svg className="w-4 h-4 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="text-red-300 text-sm">{limitOrderBalanceError}</div>
          </div>
        </div>
      )}

      <button
        onClick={executeTransaction}
        disabled={isButtonDisabled}
        className={`w-full py-4 rounded-full font-medium text-lg transition-colors relative overflow-hidden ${
          isButtonDisabled
            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
            : isBuy
              ? 'bg-[#214638] hover:bg-[#14ffa2] hover:text-[#214638] text-[#14FFA2]'
              : 'bg-[#311D27] hover:bg-[#FF329B] hover:text-[#311d27] text-[#FF329B]'
        }`}
      >
        {/* Progress bar background */}
        {isExecuting && (
          <div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transition-all duration-300"
            style={{
              width: `${executionProgress}%`,
              background: isBuy
                ? 'linear-gradient(90deg, transparent, rgba(20, 255, 162, 0.2), transparent)'
                : 'linear-gradient(90deg, transparent, rgba(255, 50, 155, 0.2), transparent)'
            }}
          />
        )}
        <span className="relative z-10">{getButtonText()}</span>
      </button>
    </div>
  );
};

const PortfolioStats: React.FC = () => {
  const [currency, setCurrency] = useState<string>("sol");
  const formatValue = (valueSol: string, valueUsd: string) => {
    return currency === "usd" ? (
      `$${valueUsd}`
    ) : (
      <span className="flex items-center gap-1">
        <img src={"https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"} alt="Solana" className="w-4 h-4" />
        {valueSol}
      </span>
    );
  };

  return (
  
<div className="mt-6 text-sm bg-[#1A1D21]  overflow-hidden border border-gray-700">
      <div className="grid grid-cols-4 divide-x divide-gray-700 text-white">
        {/* Bought */}
        <div className="flex flex-col items-center justify-center px-4 py-3 hover:bg-[#22262A] transition-colors">
          <div className="text-gray-400 text-xs mb-1">Bought</div>
          <div className="flex items-center space-x-1">

            <span className="text-cyan-400">{formatValue("0", "0")}</span>
          </div>
        </div>

        {/* Sold */}
        <div className="flex flex-col items-center justify-center px-4 py-3 hover:bg-[#22262A] transition-colors">
          <div className="text-gray-400 text-xs mb-1">Sold</div>
          <div className="flex items-center space-x-1">
 
            <span className="text-pink-400">{formatValue("0", "0")}</span>
          </div>
        </div>

        {/* Holding */}
        <div className="flex flex-col items-center justify-center px-4 py-3 hover:bg-[#22262A] transition-colors">
          <div className="text-gray-400 text-xs mb-1">Holding</div>
          <div className="flex items-center space-x-1">
           
            <span className="text-cyan-400">{formatValue("0", "0.")}</span>
          </div>
        </div>

        {/* PnL with $ toggle */}
{/* PnL with $ toggle */}
<div className="flex flex-col items-center justify-center px-4 py-3 hover:bg-[#22262A] transition-colors relative">
  <div className="text-gray-400 text-xs mb-1 flex items-center gap-2">
    PnL 
    <button
      onClick={() => setCurrency(currency === "usd" ? "sol" : "usd")}
      className={`w-4 h-4 flex items-center justify-center rounded-full border text-xs transition-colors ${
        currency === "usd"
          ? "bg-green-600 border-green-400 text-white"
          : "border-gray-500 text-gray-400"
      }`}
      title="Toggle currency"
    >
      $
    </button>
  </div>

  <div className="flex items-center justify-center min-w-[100px] text-cyan-400">
    {currency === "sol" && (
      <img
        src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
        alt="Solana"
        className="w-4 h-4 mr-1"
      />
    )}
    <span>{currency === "usd" ? "+$0 (+0%)" : "+0 (+0%)"}</span>
  </div>
</div>

      </div>
    </div>
);
};

// Main Trading Interface Component
interface TradingPanelProps {
  exchangeType?: string;
}

const TradingPanel: React.FC<TradingPanelProps> = ({ exchangeType: propExchangeType }) => {
  const [activeTab, setActiveTab] = useState('Market');
  const [isBuy, setIsBuy] = useState(true);
  const [amount, setAmount] = useState('1');
  const [quoteData, setQuoteData] = useState<QuoteResponse | null>(null);
  const [balanceData, setBalanceData] = useState<BalanceResponse | null>(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState<boolean>(false);
  const [limitOrderData, setLimitOrderData] = useState<LimitOrderData | undefined>(undefined);
  const [activePulseToken, setActivePulseToken] = useState<any | null>(null);
  const [walletInfo, setWalletInfo] = useState<{ address: string; id: string } | null>(null);

  // 🚀 LIMIT ORDER BALANCE VALIDATION STATE
  const [limitOrderBalanceError, setLimitOrderBalanceError] = useState<string>('');
  const [isLimitOrderBalanceValid, setIsLimitOrderBalanceValid] = useState<boolean>(true);

  // Auto fees hook with exchange type from prop or active token
  const exchangeType = propExchangeType || activePulseToken?.exchange_name?.toLowerCase() || 'pumpfun';
  const { autoFeeState, toggleAutoFees, updateFeesForAmount, refreshFees } = useAutoFees(exchangeType);

  // Privy hooks for wallet access
  const { authenticated, user } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  
  // Session signers hook for wallet delegation
  const { delegateSolanaWallet } = useSessionSigners();
  const walletDelegation = useWalletDelegation();
  
  // Get preset context to sync buy/sell mode
  const { setActiveTab: setPresetActiveTab } = usePreset();

  // Debounced auto fee update when amount changes
  useEffect(() => {
    if (autoFeeState.isEnabled && amount && parseFloat(amount) > 0) {
      const debounceTimer = setTimeout(() => {
        console.log('[TradingPanel] Updating auto fees for amount:', amount, 'exchangeType:', exchangeType);
        updateFeesForAmount(parseFloat(amount), false, exchangeType);
      }, 500); // 500ms debounce instead of 1 second for better UX

      return () => clearTimeout(debounceTimer);
    }
  }, [amount, autoFeeState.isEnabled, exchangeType, updateFeesForAmount]);

  // Initial sync of preset tab on mount and check auto fees
  useEffect(() => {
    console.log('[TradingPanel] Initial mount - syncing preset tab and checking auto fees');
    setPresetActiveTab(isBuy ? 'buy' : 'sell');
    
    // If auto fees are enabled, trigger a refresh to ensure we have latest data
    if (autoFeeState.isEnabled) {
      console.log('[TradingPanel] Auto fees enabled on mount, triggering refresh');
      setTimeout(() => {
        refreshFees(undefined, undefined, exchangeType, true);
      }, 500); // Small delay to ensure component is fully mounted
    }
  }, []); // Run only on mount

  // State to prevent multiple simultaneous wallet info loads
  const [walletInfoLoading, setWalletInfoLoading] = useState(false);
  const [lastWalletInfoLoad, setLastWalletInfoLoad] = useState<string>('');

  // Toast deduplication to prevent multiple toasters
  const [lastToastMessage, setLastToastMessage] = useState<string>('');
  const [lastToastTime, setLastToastTime] = useState<number>(0);

  const showDedupedToast = (message: string, type: 'info' | 'error' | 'success' = 'info') => {
    const now = Date.now();
    // Only show toast if it's different message or more than 3 seconds since last toast
    if (lastToastMessage !== message || (now - lastToastTime) > 3000) {
      setLastToastMessage(message);
      setLastToastTime(now);

      switch (type) {
        case 'info':
          showSwapInfoToast(message);
          break;
        case 'error':
          showSwapErrorToast(message);
          break;
        case 'success':
          showSwapInfoToast(message);
          break;
      }
    }
  };

  // Load wallet info once for the entire component with caching
  useEffect(() => {
    const loadWalletInfo = async () => {
      if (!authenticated || !user?.id) {
        setWalletInfo(null);
        return;
      }

      // Create a cache key to prevent duplicate calls
      const cacheKey = `${user.id}-${solanaWallets.length}-${solanaWallets.map(w => w.address).join(',')}`;

      // Skip if we're already loading or if we just loaded the same configuration
      if (walletInfoLoading || lastWalletInfoLoad === cacheKey) {
        console.log('[TradingPanel] Skipping wallet info load - already loading or same config');
        return;
      }

      setWalletInfoLoading(true);
      setLastWalletInfoLoad(cacheKey);

      try {
        // Use the delegation-aware wallet info function
        const info = await getDefaultSolanaWalletInfoWithDelegation(
          authenticated,
          user.id,
          solanaWallets,
          user,
          async (address: string) => {
            console.log('[TradingPanel - LoadWallet] Wallet delegation requested for address:', address);

            // Use deduplicated toast to prevent multiple toasters
            showDedupedToast('Enabling wallet for trading...', 'info');

            try {
              console.log('[TradingPanel - LoadWallet] Calling delegateSolanaWallet...');
              const success = await delegateSolanaWallet(address);
              console.log('[TradingPanel - LoadWallet] Delegation result:', success);

              if (success) {
                showDedupedToast('Wallet enabled successfully!', 'success');
                console.log('[TradingPanel - LoadWallet] Wallet delegation successful for:', address);
              } else {
                showDedupedToast('Failed to enable wallet for trading', 'error');
                console.error('[TradingPanel - LoadWallet] Wallet delegation failed for:', address);
              }
              return success;
            } catch (error) {
              console.error('[TradingPanel - LoadWallet] Error during wallet delegation:', error);
              showDedupedToast('Error enabling wallet for trading', 'error');
              return false;
            }
          }
        );

        if (info) {
          console.log('[TradingPanel] Wallet info loaded successfully:', {
            address: info.address,
            id: info.id
          });
          setWalletInfo(info);
        } else {
          console.warn('[TradingPanel] Failed to load wallet info');
          setWalletInfo(null);
        }
      } catch (error) {
        console.error('[TradingPanel] Error loading wallet info:', error);
        setWalletInfo(null);
      } finally {
        setWalletInfoLoading(false);
      }
    };

    // Add a small delay to prevent rapid successive calls
    const timeoutId = setTimeout(loadWalletInfo, 200);
    return () => clearTimeout(timeoutId);
  }, [authenticated, user?.id, solanaWallets.length, delegateSolanaWallet]); // Use length instead of full array

  // Load active token data for TP/SL
  useEffect(() => {
    try {
      const tokenStr = localStorage.getItem('activePulseToken');
      if (tokenStr) {
        const token = JSON.parse(tokenStr);
        setActivePulseToken(token);
      }
    } catch (error) {
      console.error('Error loading active token:', error);
    }
  }, []);

  // Update dynamic fees when amount changes (auto fees)
  useEffect(() => {
    const amountNumber = parseFloat(amount);
    if (amountNumber > 0 && activePulseToken?.exchange_name && autoFeeState.isEnabled) {
      // Debounce the fee update to avoid too many API calls
      const timeoutId = setTimeout(() => {
        const exchangeType = activePulseToken.exchange_name.toLowerCase();
        updateFeesForAmount(amountNumber, false, exchangeType); // Pass exchange type
      }, 1000); // 1 second debounce for responsive updates

      return () => clearTimeout(timeoutId);
    } else if (amount === '0.0' && autoFeeState.isEnabled && activePulseToken?.exchange_name) {
      // If amount is 0, use default amount of 1 SOL for fee calculation
      const timeoutId = setTimeout(() => {
        const exchangeType = activePulseToken.exchange_name.toLowerCase();
        updateFeesForAmount(1, false, exchangeType); // Use 1 SOL as default
      }, 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [amount, activePulseToken?.exchange_name, autoFeeState.isEnabled]);

  // Create a ref to store the latest fetchBalance function to avoid stale closures
  const fetchBalanceRef = useRef<() => void>();

  // Get Solana wallet address from defaultWallets localStorage (same as Pulse table page)
  const getSolanaWalletAddress = useCallback(() => {
    return getDefaultSolanaWalletAddress(authenticated, solanaWallets);
  }, [authenticated, solanaWallets]);

  // 🚀 BALANCE VALIDATION FOR LIMIT ORDERS
  const validateLimitOrderBalance = useCallback((response: BalanceResponse | null, amount: number, isBuy: boolean): { isValid: boolean; errorMessage: string } => {
    if (!response?.success) {
      return {
        isValid: false,
        errorMessage: 'Unable to verify balance. Please try again.'
      };
    }

    const requestedAmount = amount;

    if (requestedAmount <= 0) {
      return {
        isValid: false,
        errorMessage: 'Please enter a valid amount greater than 0.'
      };
    }

    if (isBuy) {
      const solBalance = parseFloat(response.data.solBalance);
      if (solBalance < requestedAmount) {
        const shortfall = requestedAmount - solBalance;
        return {
          isValid: false,
          errorMessage: `Insufficient SOL balance. You have ${solBalance.toFixed(4)} SOL, but trying to spend ${requestedAmount} SOL. Shortfall: ${shortfall.toFixed(4)} SOL.`
        };
      }

      if (requestedAmount > solBalance * 0.95) {
        console.warn(`Spending ${((requestedAmount / solBalance) * 100).toFixed(1)}% of SOL balance`);
      }
    } else {
      const tokenBalance = parseFloat(response.data.tokenBalance);
      if (tokenBalance < requestedAmount) {
        const shortfall = requestedAmount - tokenBalance;
        return {
          isValid: false,
          errorMessage: `Insufficient token balance. You have ${tokenBalance > 1 ? Math.round(tokenBalance) : tokenBalance.toFixed(6)} tokens, but trying to sell ${requestedAmount}. Shortfall: ${shortfall > 1 ? Math.round(shortfall) : shortfall.toFixed(6)}.`
        };
      }

      if (requestedAmount > tokenBalance * 0.95) {
        console.warn(`Selling ${((requestedAmount / tokenBalance) * 100).toFixed(1)}% of token balance`);
      }
    }

    return {
      isValid: true,
      errorMessage: ''
    };
  }, []);

  // Fetch balance function (simplified, no complex debouncing)
  const fetchBalance = useCallback(async () => {
    if (!authenticated) {
      setBalanceData(null);
      setIsLoadingBalance(false);
      return;
    }

    try {
      setIsLoadingBalance(true);

      // Get active pulse token from localStorage
      const activePulseTokenStr = localStorage.getItem('activePulseToken');
      if (!activePulseTokenStr) {
        console.log('No active pulse token found');
        setBalanceData(null);
        return;
      }

      const activePulseToken: PulseTokenInfo = JSON.parse(activePulseTokenStr);

      // Get wallet address
      const walletAddress = getSolanaWalletAddress();
      if (!walletAddress) {
        console.log('No wallet address available');
        setBalanceData(null);
        return;
      }

      // Fetch balance data
      const balanceRequest: BalanceRequest = {
        tokenAddress: activePulseToken.address,
        walletAddress: walletAddress,
      };

      const response = await getTokenBalance(balanceRequest);
      setBalanceData(response);

      // 🚀 LIMIT ORDER BALANCE VALIDATION
      if (activeTab === 'Limit') {
        const validation = validateLimitOrderBalance(response, parseFloat(amount), isBuy);
        setIsLimitOrderBalanceValid(validation.isValid);
        setLimitOrderBalanceError(validation.errorMessage);
      }

    } catch (error) {
      console.error('Error fetching balance:', error);
      setBalanceData(null);
    } finally {
      setIsLoadingBalance(false);
    }
  }, [authenticated, getSolanaWalletAddress, activeTab, amount, isBuy, validateLimitOrderBalance]);

  const handleQuoteUpdate = useCallback((quote: QuoteResponse | null) => {
    setQuoteData(quote);
    if (quote?.success) {
      console.log('Quote received:', quote.data);
    } else if (quote?.error) {
      console.error('Quote error:', quote.error);
    }
  }, []);

  const handleLimitOrderDataUpdate = useCallback((data: LimitOrderData) => {
    setLimitOrderData(data);
  }, []);

  // Main effect: Handle authentication, wallet, and initial balance fetch
  useEffect(() => {

    // Preload wallet info if user is authenticated
    if (authenticated && user?.id) {
      preloadWalletInfo(user.id).catch(error => {
        console.error('Failed to preload wallet info:', error);
      });

      // Fetch balance when authenticated and has wallet
      const walletAddress = getSolanaWalletAddress();
      if (walletAddress) {
        fetchBalance();
      }
    }
  }, [authenticated, user?.id]); // Removed getSolanaWalletAddress to prevent excessive calls

  // Reset amount and clear quote data when buy/sell mode changes
  useEffect(() => {
    const newTab = isBuy ? 'buy' : 'sell';
    console.log('[TradingPanel] Buy/Sell mode changed:', {
      isBuy,
      newTab,
      exchangeType
    });
    
    // Sync preset activeTab with buy/sell mode
    setPresetActiveTab(newTab);
    
    // Reset amount when switching between buy/sell modes
    setAmount('0.0');

    // Clear existing quote data to prevent showing stale information
    setQuoteData(null);

    // Reset validation state
    setIsLimitOrderBalanceValid(true);
    setLimitOrderBalanceError('');

    if (balanceData) {
      // Balance data is already available, just update display
      console.log('Buy/Sell mode changed, updating balance display, resetting amount, and clearing quote data');
    }
  }, [isBuy, setPresetActiveTab]);

  // Validate balance whenever amount changes
  useEffect(() => {
    if (balanceData && amount && parseFloat(amount) > 0) {
      const validation = validateLimitOrderBalance(balanceData, parseFloat(amount), isBuy);
      setIsLimitOrderBalanceValid(validation.isValid);
      setLimitOrderBalanceError(validation.errorMessage);
    } else {
      setIsLimitOrderBalanceValid(true);
      setLimitOrderBalanceError('');
    }
  }, [amount, balanceData, isBuy, validateLimitOrderBalance]);

  // Listen for activePulseToken changes and trade completion events (setup once)
  useEffect(() => {
    const handleStorageChange = () => {
      console.log('Active pulse token changed, refetching balance');
      // Use ref to access the latest fetchBalance function and avoid stale closures
      if (fetchBalanceRef.current) {
        fetchBalanceRef.current();
      }
    };

    const handleTradeSuccess = (event: CustomEvent) => {
      console.log('PulseTrade: Received pulseTradeSuccess event, refreshing balance', event.detail);
      // Refresh balance after successful transaction
      setTimeout(() => {
        if (fetchBalanceRef.current) {
          fetchBalanceRef.current();
        }
      }, 2000); // Additional 2 second delay for blockchain confirmation
    };

    // Listen for custom events when activePulseToken changes
    window.addEventListener('pulseDataChanged', handleStorageChange);
    window.addEventListener('pulseTradeSuccess', handleTradeSuccess as EventListener);

    return () => {
      window.removeEventListener('pulseDataChanged', handleStorageChange);
      window.removeEventListener('pulseTradeSuccess', handleTradeSuccess as EventListener);
    };
  }, []); // Empty dependency array - setup once

  // Reset UI state after successful swap
  const handleResetUI = useCallback(() => {
    console.log('Resetting UI state after successful swap');

    // Clear amount input
    setAmount('');

    // Clear quote data
    setQuoteData(null);

    // Auto-refresh balance after a short delay to allow transaction to settle
    setTimeout(() => {
      console.log('Auto-refreshing balance after swap');
      fetchBalance();
    }, 2000); // 2 second delay
  }, [fetchBalance]);

  // Handle swap completion with balance refresh
  const handleSwapComplete = useCallback((result: SwapResponse) => {
    console.log('Swap completed:', result);

    if (result.success) {
      // Auto-refresh balance after successful swap
      setTimeout(() => {
        console.log('Refreshing balance after successful swap');
        fetchBalance();
      }, 3000); // 3 second delay to allow blockchain confirmation
    }
  }, [fetchBalance]);

  // Cleanup: Cancel pending balance requests when component unmounts
  useEffect(() => {
    return () => {
      // Cancel any pending balance requests for this wallet/token combination
      const walletAddress = getSolanaWalletAddress();
      if (walletAddress) {
        const activePulseTokenStr = localStorage.getItem('activePulseToken');
        if (activePulseTokenStr) {
          try {
            const activePulseToken = JSON.parse(activePulseTokenStr);
            cancelBalanceRequest(walletAddress, activePulseToken.address);
          } catch (error) {
            console.error('Error parsing activePulseToken for cleanup:', error);
          }
        }
      }
    };
  }, []); // Empty dependency array - cleanup on unmount only

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Market':
        return <MarketTab 
          amount={amount} 
          setAmount={setAmount} 
          isBuy={isBuy} 
          onQuoteUpdate={handleQuoteUpdate} 
          balanceData={balanceData}
          walletInfo={walletInfo}
          autoFeeState={autoFeeState}
          toggleAutoFees={toggleAutoFees}
        />;
      case 'Limit':
        return <LimitTab 
          amount={amount} 
          setAmount={setAmount} 
          isBuy={isBuy} 
          onQuoteUpdate={handleQuoteUpdate} 
          balanceData={balanceData} 
          onLimitOrderDataUpdate={handleLimitOrderDataUpdate}
          autoFeeState={autoFeeState}
          toggleAutoFees={toggleAutoFees}
        />;

      default:
        return <MarketTab 
          amount={amount} 
          setAmount={setAmount} 
          isBuy={isBuy} 
          onQuoteUpdate={handleQuoteUpdate} 
          balanceData={balanceData}
          autoFeeState={autoFeeState}
          toggleAutoFees={toggleAutoFees}
        />;
    }
  };

  return (
    <div className="text-white min-h-screen max-w-md mx-auto">
      <div className="">
        <div className='border-b border-gray-700'>

        <BuySellToggle isBuy={isBuy} setIsBuy={setIsBuy} />
        </div>
        <div className='border-b border-gray-700'>
        <TabNavigation
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          balance={balanceData?.success ? 
            (isBuy ? 
              parseFloat(balanceData.data.solBalance).toFixed(4) 
              : 
              (parseFloat(balanceData.data.tokenBalance) > 1 ? 
                Math.round(parseFloat(balanceData.data.tokenBalance)).toString() 
                : 
                parseFloat(balanceData.data.tokenBalance).toFixed(6))
            ) 
            : '0'
          }
          isLoadingBalance={isLoadingBalance}
          isBuy={isBuy}
        />
        </div>
        <div className="p-4">
        {/* Wallet Delegation Status Indicator */}
        {walletDelegation.needsDelegation && (
          <div className="mb-4 p-3 bg-yellow-900/30 border border-yellow-600/50 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-yellow-200">
                  Wallet delegation required for seamless trading
                </span>
              </div>
              <button
                onClick={() => walletDelegation.handleDelegationWorkflow()}
                disabled={walletDelegation.isChecking}
                className="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 disabled:opacity-50 text-xs rounded-md transition-colors"
              >
                {walletDelegation.isChecking ? 'Checking...' : 'Enable'}
              </button>
            </div>
            {walletDelegation.recommendedWallet && (
              <div className="mt-2 text-xs text-yellow-300">
                Alternative: Switch to delegated wallet ({walletDelegation.recommendedWallet.address.slice(0, 8)}...)
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {walletDelegation.error && (
          <div className="mb-4 p-3 bg-red-900/30 border border-red-600/50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm text-red-200">{walletDelegation.error}</span>
              <button
                onClick={walletDelegation.clearError}
                className="text-red-400 hover:text-red-300 text-xs"
              >
                ✕
              </button>
            </div>
          </div>
        )}

        {renderTabContent()}
        </div>
        <div className='px-4'>

        <BuyButton
          activeTab={activeTab}
          isBuy={isBuy}
          quoteData={quoteData}
          amount={amount}
          balanceData={balanceData}
          limitOrderData={limitOrderData}
          onSwapComplete={handleSwapComplete}
          onResetUI={handleResetUI}
          isLimitOrderBalanceValid={isLimitOrderBalanceValid}
          limitOrderBalanceError={limitOrderBalanceError}
          autoFeeState={autoFeeState}
          walletInfo={walletInfo}
        />
        </div>
        <PortfolioStats />
        <Preset/>
        <TokenInfo/>
      </div>


    </div>
  );
};

export default TradingPanel;