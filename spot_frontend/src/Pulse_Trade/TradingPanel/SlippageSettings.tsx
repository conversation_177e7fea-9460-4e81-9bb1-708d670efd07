import { TrendingUp, Fuel, Zap } from 'lucide-react';
import { usePreset } from '../Preset/PresetContext';
import { formatFeeAmount } from '../../services/dynamicFeeService';
import { AutoFeeState } from '../../hooks/useAutoFees';

interface SlippageSettingsProps {
  isBuy: boolean;
  autoFeeState: AutoFeeState;
  toggleAutoFees: () => void;
}

export default function Setting({ isBuy, autoFeeState, toggleAutoFees }: SlippageSettingsProps) {
  const { activePreset, presetData } = usePreset();
  const tabKey = isBuy ? 'buy' : 'sell';
  const current = activePreset !== null ? presetData[activePreset][tabKey] : null;
  
  // Log only essential state for debugging
  if (autoFeeState.isEnabled) {
    console.log('[SlippageSettings] Auto fees enabled, displaying dynamic fees');
  }
  const Oval = ({ 
    size = 16, 
    color = "#FFEB3B", 
    strokeWidth = 2,
    rx = 10,
    ry = 6 
  }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      {/* Horizontal oval */}
      <ellipse 
        cx="12" 
        cy="12" 
        rx={rx} 
        ry={ry} 
        stroke={color} 
        strokeWidth={strokeWidth}
      />
      
      {/* Curved line through the middle - curved in opposite direction */}
      <path 
        d="M 4 12 Q 12 16 20 12" 
        stroke={color} 
        strokeWidth={strokeWidth}
        fill="none"
      />
    </svg>
  )
  if (!current) return null;

  const { slippage, bribe, priority, mevMode } = current;
  
  // Display auto-calculated fees when auto is enabled, otherwise show preset fees
  const displayPriority = autoFeeState.isEnabled && autoFeeState.dynamicFees 
    ? autoFeeState.dynamicFees.priorityFee 
    : priority;
    
  const displayBribe = autoFeeState.isEnabled && autoFeeState.dynamicFees
    ? autoFeeState.dynamicFees.bribeAmount
    : bribe;

  return (
    <div className="mb-4">
      {/* Main settings row - single line layout */}
      <div className="flex items-center justify-between text-sm bg-gray-800/20 rounded-lg px-3 py-2 border border-gray-700/30">
        {/* Slippage */}
        <div className="flex items-center space-x-1.5">
          <TrendingUp size={14} className="text-gray-400" />
          <span className="text-white font-medium">{slippage}%</span>
        </div>

        {/* Priority Fee */}
        <div className="flex items-center space-x-1.5">
          <Fuel size={12} className={autoFeeState.isEnabled ? "text-green-400" : "text-yellow-500"} />
          <span className={`${autoFeeState.isEnabled ? "text-green-400" : "text-yellow-500"} font-mono text-xs`}>
            {formatFeeAmount(displayPriority, 6)}
          </span>
          {autoFeeState.isEnabled && autoFeeState.isLoading && (
            <div className="animate-spin w-2.5 h-2.5 border-2 border-green-400 border-t-transparent rounded-full"></div>
          )}
        </div>

        {/* Bribe Amount */}
        <div className="flex items-center space-x-1.5">
          <Oval size={14}/>
          <span className={`${autoFeeState.isEnabled ? "text-green-400" : "text-yellow-500"} font-mono text-xs`}>
            {formatFeeAmount(displayBribe, 6)}
          </span>
        </div>

        {/* MEV Mode */}
        <span className="text-gray-400 text-xs">{mevMode}</span>

        {/* Auto Fee Toggle */}
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('AUTO button clicked, current state:', autoFeeState.isEnabled);
            toggleAutoFees();
          }}
          className={`flex items-center space-x-1 px-2.5 py-1 rounded-md text-xs font-medium transition-all duration-200 select-none ${
            autoFeeState.isEnabled 
              ? 'bg-green-500/20 text-green-400 border border-green-500/50 hover:bg-green-500/30 shadow-sm shadow-green-500/20' 
              : 'bg-gray-700/50 text-gray-300 border border-gray-600 hover:bg-gray-700 hover:text-white'
          }`}
          title={autoFeeState.isEnabled ? 'Auto fees enabled - fees update based on network conditions' : 'Click to enable auto fees'}
        >
          <Zap size={12} className={autoFeeState.isEnabled ? 'animate-pulse' : ''} />
          <span>Auto</span>
        </button>
      </div>
    </div>
  );
}
