import React, { useState, useCallback, useEffect } from 'react';
import { ChevronDown, Trash2, ChevronUpIcon } from 'lucide-react';
import { ChevronsRight, ArrowUp, ArrowDown } from 'lucide-react';
import { showSwapSuccessToast, showSwapErrorToast, showSwapInfoToast } from '../../utils/swapToast';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { getDefaultSolanaWalletInfo, getDefaultSolanaWalletAddress } from '../../utils/walletUtils';
import { useActiveToken } from '../context/ActiveTokenContext';
import { getTokenBalance, BalanceRequest, BalanceResponse } from '../../api/solana_api';
import { tpSlService } from '../../services/newTpslService';
// TP/SL functionality removed - using new simplified component

interface TradingOption {
  id: string;
  label: string;
  icon: any; // Lucide icons
  color?: 'emerald' | 'red' | 'orange' | 'purple';
}

interface InputValues {
  [key: string]: {
    value?: string;
    primary?: string;
    amount?: string;
  };
}

interface AdvancedTradingStrategyProps {
  isBuy?: boolean;
}

const AdvancedTradingStrategy: React.FC<AdvancedTradingStrategyProps> = ({ isBuy = true }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selectedOptions, setSelectedOptions] = useState<TradingOption[]>([]);
  const [inputValues, setInputValues] = useState<InputValues>({});
  const [isCreating, setIsCreating] = useState(false);
  const [balanceData, setBalanceData] = useState<BalanceResponse | null>(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState<boolean>(false);
  const [balanceError, setBalanceError] = useState<string>('');
  const { user, authenticated } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  const { activeToken } = useActiveToken();

  // Get Solana wallet address
  const getSolanaWalletAddress = useCallback(() => {
    return getDefaultSolanaWalletAddress(authenticated, solanaWallets);
  }, [authenticated, solanaWallets]);

  // Balance validation function for TP/SL orders
  const validateTPSLBalance = useCallback((response: BalanceResponse | null, amount: number, isBuy: boolean): { isValid: boolean; errorMessage: string } => {
    if (!response?.success) {
      return {
        isValid: false,
        errorMessage: 'Unable to verify balance. Please try again.'
      };
    }

    const requestedAmount = amount;

    if (requestedAmount <= 0) {
      return {
        isValid: false,
        errorMessage: 'Please enter a valid amount greater than 0.'
      };
    }

    // For TP/SL: We check what balance we'll need AFTER the main trade
    if (isBuy) {
      // Buy tab: After buying tokens, TP/SL will sell tokens
      // So we need to estimate token balance we'll have after buying
      // For now, allow any reasonable amount (real validation happens after main trade)
      return { isValid: true, errorMessage: '' };
    } else {
      // Sell tab: After selling tokens, TP/SL will buy back with SOL
      // So we need to estimate SOL balance we'll have after selling
      // For now, allow any reasonable amount (real validation happens after main trade)
      return { isValid: true, errorMessage: '' };
    }
  }, []);

  // Fetch balance data
  const fetchBalance = useCallback(async () => {
    if (!authenticated || !activeToken) return;

    const walletAddress = getSolanaWalletAddress();
    if (!walletAddress) return;

    setIsLoadingBalance(true);
    try {
      const balanceRequest: BalanceRequest = {
        walletAddress,
        tokenAddress: activeToken.address
      };
      const response = await getTokenBalance(balanceRequest);
      setBalanceData(response);
    } catch (error) {
      console.error('Error fetching balance:', error);
      setBalanceData(null);
    } finally {
      setIsLoadingBalance(false);
    }
  }, [authenticated, activeToken, getSolanaWalletAddress]);

  // Fetch balance when component mounts or dependencies change
  useEffect(() => {
    fetchBalance();
  }, [fetchBalance]);

  const allOptions: TradingOption[] = [
    // TP/SL options removed - using separate simplified component
    // Hidden options: devSell and migration
    { id: 'devSell', label: 'Dev Sell', icon: ChevronUpIcon, color: 'orange' },
    { id: 'migration', label: 'Migration', icon: ChevronsRight, color: 'purple' }
  ];

  // Get available options (not yet selected)
  const availableOptions = allOptions.filter(option => 
    !selectedOptions.some(selected => selected.id === option.id)
  );

  const handleOptionSelect = (option: TradingOption) => {
    setSelectedOptions(prev => [...prev, option]);
    // Initialize input values for the new option
    if (option.id === 'takeProfit' || option.id === 'stopLoss') {
      setInputValues(prev => ({
        ...prev,
        [option.id]: { value: '', amount: '' }
      }));
    } else {
      setInputValues(prev => ({
        ...prev,
        [option.id]: { value: '' }
      }));
    }
    setIsOpen(false);
  };

  const handleDelete = (optionId: string) => {
    setSelectedOptions(prev => prev.filter(option => option.id !== optionId));
    setInputValues(prev => {
      const newValues = { ...prev };
      delete newValues[optionId];
      return newValues;
    });
  };

  const updateInputValue = (optionId: string, field: string, value: string) => {
    setInputValues(prev => ({
      ...prev,
      [optionId]: {
        ...prev[optionId],
        [field]: value
      }
    }));

    // Validate balance in real-time when amount changes
    if (field === 'amount' && balanceData) {
      setTimeout(() => {
        validateCurrentBalance();
      }, 100); // Small delay to allow state to update
    }
  };

  // Real-time balance validation
  const validateCurrentBalance = useCallback(() => {
    if (!balanceData || selectedOptions.length === 0) {
      setBalanceError('');
      return;
    }

    // Calculate total amount needed for all TP/SL orders
    let totalAmountNeeded = 0;
    selectedOptions.forEach(option => {
      const values = inputValues[option.id] || {};
      if (option.id === 'takeProfit' || option.id === 'stopLoss') {
        const amount = parseFloat(values.amount || '0');
        if (amount > 0) {
          totalAmountNeeded += amount;
        }
      }
    });

    if (totalAmountNeeded > 0) {
      const validation = validateTPSLBalance(balanceData, totalAmountNeeded, isBuy);
      setBalanceError(validation.isValid ? '' : validation.errorMessage);
    } else {
      setBalanceError('');
    }
  }, [balanceData, selectedOptions, inputValues, isBuy, validateTPSLBalance]);

  // Validate balance when dependencies change
  useEffect(() => {
    validateCurrentBalance();
  }, [validateCurrentBalance]);

  const handleSubmit = async () => {
    // Clear any previous balance errors
    setBalanceError('');

    if (!user?.id) {
      showSwapErrorToast('Please connect your wallet first');
      return;
    }

    if (!activeToken) {
      showSwapErrorToast('No active token selected');
      return;
    }

    // Validate that all required fields are filled
    const isValid = selectedOptions.every(option => {
      const values = inputValues[option.id] || {};
      if (option.id === 'takeProfit' || option.id === 'stopLoss') {
        return values.value && values.amount;
      }
      return values.value;
    });

    if (!isValid) {
      showSwapErrorToast('Please fill in all required fields');
      return;
    }

    // Calculate total amount needed for all TP/SL orders
    let totalAmountNeeded = 0;
    selectedOptions.forEach(option => {
      const values = inputValues[option.id] || {};
      if (option.id === 'takeProfit' || option.id === 'stopLoss') {
        const amount = parseFloat(values.amount || '0');
        if (amount > 0) {
          totalAmountNeeded += amount;
        }
      }
    });

    // Validate balance before creating orders
    const balanceValidation = validateTPSLBalance(balanceData, totalAmountNeeded, isBuy);
    if (!balanceValidation.isValid) {
      setBalanceError(balanceValidation.errorMessage);
      showSwapErrorToast(balanceValidation.errorMessage);
      return;
    }

    setIsCreating(true);
    
    try {
      // Get Solana wallet information
      const walletInfo = await getDefaultSolanaWalletInfo(authenticated, user.id, solanaWallets, user);
      if (!walletInfo) {
        showSwapErrorToast('No Solana wallet found. Please connect a Solana wallet.');
        setIsCreating(false);
        return;
      }

      // Create a position entry using actual token data
      const samplePosition = {
        id: crypto.randomUUID(),
        user_id: user.id,
        token_address: activeToken.address,
        token_name: activeToken.name || 'Unknown Token',
        token_symbol: activeToken.symbol || 'UNKNOWN',
        token_image: activeToken.imageUrl || '',
        pool_address: activeToken.pool_address || '',
        dex_type: activeToken.exchange_name?.toLowerCase() || 'pumpfun',
        direction: 'buy' as const,
        entry_price: activeToken.price || 0,
        current_price: activeToken.price || 0,
        total_amount: 1000,
        remaining_amount: 1000,
        wallet_address: walletInfo.address,
        wallet_id: walletInfo.id
      };

      // Create TP/SL orders for each selected option
      const orderPromises = selectedOptions.map(async (option) => {
        const values = inputValues[option.id] || {};
        const orderType = option.id === 'takeProfit' ? 'take_profit' as const : 'stop_loss' as const;
        
        // Parse trigger percentage and ensure it's positive for backend validation
        const triggerPercentage = Math.abs(parseFloat(values.value || '0'));
        const amount = parseFloat(values.amount || '0');
        
        // Calculate trigger price based on percentage
        const currentPrice = activeToken.price || 0;
        const calculateTriggerPrice = (percentage: number, isProfit: boolean) => {
          const pct = percentage / 100;
          
          if (isBuy) {
            // For buy orders: TP is higher, SL is lower
            return isProfit ? currentPrice * (1 + pct) : currentPrice * (1 - pct);
          } else {
            // For sell orders: TP is lower (buy back cheaper), SL is higher (buy back more expensive)
            return isProfit ? currentPrice * (1 - pct) : currentPrice * (1 + pct);
          }
        };
        
        const orderData = {
          user_id: user.id,
          token_address: activeToken.address,
          token_name: activeToken.name || 'Unknown Token',
          token_symbol: activeToken.symbol || 'UNKNOWN',
          token_image: activeToken.imageUrl || '',
          pool_address: activeToken.pool_address || '',
          dex_type: activeToken.exchange_name?.toLowerCase() || 'pumpfun',
          order_type: orderType,
          direction: isBuy ? 'sell' : 'buy', // Opposite direction of main trade
          trigger_price: calculateTriggerPrice(triggerPercentage, option.id === 'takeProfit'),
          trigger_percentage: triggerPercentage,
          amount: amount,
          slippage: 0.10, // Default slippage (10%)
          wallet_address: walletInfo.address,
          wallet_id: walletInfo.id,
          current_price: currentPrice
        };

        const result = await tpSlService.createOrder(orderData);
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to create TP/SL order');
        }
        
        return result;
      });

      await Promise.all(orderPromises);
      
      showSwapInfoToast('TP/SL Orders created successfully!');
      
      // Dispatch event to refresh positions table
      window.dispatchEvent(new CustomEvent('tpslOrderCreated', {
        detail: {
          timestamp: Date.now(),
          orderCount: orderPromises.length
        }
      }));
      
      // Reset the form
      setSelectedOptions([]);
      setInputValues({});
      
    } catch (error) {
      console.error('Failed to create TP/SL orders:', error);
      showSwapErrorToast(error instanceof Error ? error.message : 'Failed to create TP/SL orders');
    } finally {
      setIsCreating(false);
    }
  };

  const getColorClasses = (color?: 'emerald' | 'red' | 'orange' | 'purple') => {
    const colorMap = {
      emerald: 'text-emerald-400',
      red: 'text-red-400',
      orange: 'text-orange-400',
      purple: 'text-purple-400'
    };
    return (color && colorMap[color]) || 'text-slate-400';
  };

  const renderInputField = (option: TradingOption) => {
    const values = inputValues[option.id] || {};

    switch (option.id) {
      case 'takeProfit':
        return (
          <div key={option.id} className="flex items-center gap-3 mb-3 w-full ">
            <div className="flex items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1 w-1/3">
              <option.icon size={20} className={getColorClasses(option.color) + " mr-2"} />
              <span className="text-slate-300 text-sm font-medium mr-2">TP</span>
          
                <input
                  type="text"
                  placeholder="+0"
                  value={values.value || ''}
                  onChange={(e) => updateInputValue(option.id, 'value', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">%</span>
         
            </div>
            
            <div className="flex items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1 w-1/3">
              <span className="text-slate-300 text-sm font-medium mr-3">Amount</span>
           
                <input
                  type="text"
                  placeholder={isBuy ? "0 tokens" : "0 SOL"}
                  value={values.amount || ''}
                  onChange={(e) => updateInputValue(option.id, 'amount', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">{isBuy ? 'TKN' : 'SOL'}</span>
        
            </div>
            
            <button
              onClick={() => handleDelete(option.id)}
              className="p-2 text-slate-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
            >
              <Trash2 size={16} />
            </button>
          </div>
        );

      case 'stopLoss':
        return (
          <div key={option.id} className="flex items-center gap-3 mb-3 w-full">
            <div className="flex items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1 w-1/3">
              <option.icon size={20} className={getColorClasses(option.color) + " mr-2"} />
              <span className="text-slate-300 text-sm font-medium mr-2">SL</span>
           
                <input
                  type="text"
                  placeholder="-0"
                  value={values.value || ''}
                  onChange={(e) => updateInputValue(option.id, 'value', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">%</span>
            
            </div>
            
            <div className="flex items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1 w-1/3">
              <span className="text-slate-300 text-sm font-medium mr-3">Amount</span>
      
                <input
                  type="text"
                  placeholder={isBuy ? "0 tokens" : "0 SOL"}
                  value={values.amount || ''}
                  onChange={(e) => updateInputValue(option.id, 'amount', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">{isBuy ? 'TKN' : 'SOL'}</span>
      
            </div>
            
            <button
              onClick={() => handleDelete(option.id)}
              className="p-2 text-slate-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
            >
              <Trash2 size={16} />
            </button>
          </div>
        );

      case 'devSell':
        return (
          <div key={option.id} className="flex items-center gap-3 mb-3">
            <div className="flex justify-between items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1">
              {/* Left side: icon + label */}
              <div className="flex items-center whitespace-nowrap mr-3">
                <option.icon
                  size={16}
                  className={getColorClasses(option.color) + " mr-2"}
                />
                <span className="text-slate-300 text-sm font-medium">
                  Sell Amount on Dev Sell
                </span>
              </div>
        
              {/* Right side: input + % */}
              <div className="flex items-center min-w-[80px]">
                <input
                  type="text"
                  placeholder="0"
                  value={values.value || ''}
                  onChange={(e) => updateInputValue(option.id, 'value', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">%</span>
              </div>
            </div>
        
            <button
              onClick={() => handleDelete(option.id)}
              className="p-2 text-slate-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
            >
              <Trash2 size={16} />
            </button>
          </div>
        );
        

      case 'migration':
        return (
          <div key={option.id} className="flex items-center gap-3 mb-3">
            <div className="flex justify-between items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1">
              {/* Left side: icon + label */}
              <div className="flex items-center whitespace-nowrap mr-3">
                <option.icon
                  size={16}
                  className={getColorClasses(option.color) + " mr-2"}
                />
                <span className="text-slate-300 text-sm font-medium">
                  Sell Amount on Migration
                </span>
              </div>
        
              {/* Right side: input + % */}
              <div className="flex items-center min-w-[80px]">
                <input
                  type="text"
                  placeholder="0"
                  value={values.value || ''}
                  onChange={(e) => updateInputValue(option.id, 'value', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">%</span>
              </div>
            </div>
        
            <button
              onClick={() => handleDelete(option.id)}
              className="p-2 text-slate-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
            >
              <Trash2 size={16} />
            </button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full   rounded-xl">
      {/* Header */}
    

      {/* Custom Dropdown - Only show if there are available options */}
      {availableOptions.length > 0 && (
        <div className="relative mb-4">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="w-full bg-[#2A2D35] hover:bg-[#2F3339] border border-slate-600/40 hover:border-slate-500/60 rounded-lg px-4 py-3 flex items-center justify-between text-white transition-all duration-200"
          >
            <span className="text-slate-400 text-base">Add Strategy</span>
            <ChevronDown 
              size={18} 
              className={`text-slate-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
            />
          </button>

          {/* Dropdown Menu */}
          {isOpen && (
            <div className="absolute top-full left-0 right-0 mt-2 bg-[#2A2D35] border border-slate-600/40 rounded-lg shadow-xl shadow-black/20 z-50 overflow-hidden">
              {availableOptions.map((option) => (
                <button
                  key={option.id}
                  onClick={() => handleOptionSelect(option)}
                  className="w-full px-4 py-3 text-left hover:bg-[#3A3D45] border-b border-slate-700/50 last:border-b-0 flex items-center transition-colors duration-150"
                >
                  <option.icon size={18} className={getColorClasses(option.color) + " mr-3"} />
                  <span className="text-white text-base font-medium">{option.label}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Dynamic Input Fields */}
      <div className="space-y-3">
        {selectedOptions.map((option) => renderInputField(option))}
      </div>

      {/* Balance Error Message */}
      {balanceError && (
        <div className="mb-3 p-3 bg-red-900/20 border border-red-500/30 rounded-md">
          <div className="flex items-start space-x-2">
            <div className="flex-shrink-0 mt-0.5">
              <svg className="w-4 h-4 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="text-red-300 text-sm">{balanceError}</div>
          </div>
        </div>
      )}

      {/* Submit Button - Only show if there are selected options */}
      {selectedOptions.length > 0 && (
        <div className="mt-4">
          <button
            onClick={handleSubmit}
            disabled={isCreating || !!balanceError}
            className="w-full bg-gradient-to-r from-[#14FFA2] to-[#32E6A4] disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-black font-bold py-3 px-4 rounded-lg hover:opacity-90 transition-all duration-200"
          >
            {isCreating ? 'Creating...' : 'Create TP/SL Orders'}
          </button>
        </div>
      )}

    </div>
  );
};

export default AdvancedTradingStrategy;