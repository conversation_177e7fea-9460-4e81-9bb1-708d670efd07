import React, { useState, useEffect } from 'react';
import { Target, Shield, AlertCircle } from 'lucide-react';

interface TpSlSettingsProps {
  isBuy: boolean;
  onTpSlDataUpdate?: (data: TpSlData) => void;
  tokenPrice?: number;
  tokenSymbol?: string;
}

export interface TpSlData {
  takeProfitEnabled: boolean;
  takeProfitPrice: string;
  stopLossEnabled: boolean;
  stopLossPrice: string;
  amount: string; // Amount for TP/SL orders
}

const TpSlSettings: React.FC<TpSlSettingsProps> = ({ 
  isBuy, 
  onTpSlDataUpdate, 
  tokenPrice = 0,
  tokenSymbol = ''
}) => {
  const [takeProfitEnabled, setTakeProfitEnabled] = useState(false);
  const [takeProfitPrice, setTakeProfitPrice] = useState('');
  const [stopLossEnabled, setStopLossEnabled] = useState(false);
  const [stopLossPrice, setStopLossPrice] = useState('');
  const [amount, setAmount] = useState('');

  // Calculate suggested prices based on current token price
  const getSuggestedTpPrice = () => {
    if (!tokenPrice) return '';
    if (isBuy) {
      // For buy orders, TP should be higher than current price
      return (tokenPrice * 1.2).toFixed(8); // 20% profit
    } else {
      // For sell orders, TP should be higher than current price
      return (tokenPrice * 1.15).toFixed(8); // 15% profit
    }
  };

  const getSuggestedSlPrice = () => {
    if (!tokenPrice) return '';
    if (isBuy) {
      // For buy orders, SL should be lower than current price
      return (tokenPrice * 0.9).toFixed(8); // 10% loss
    } else {
      // For sell orders, SL should be lower than current price
      return (tokenPrice * 0.85).toFixed(8); // 15% loss
    }
  };

  // Update parent component when data changes
  useEffect(() => {
    const data: TpSlData = {
      takeProfitEnabled,
      takeProfitPrice,
      stopLossEnabled,
      stopLossPrice,
      amount
    };
    onTpSlDataUpdate?.(data);
  }, [takeProfitEnabled, takeProfitPrice, stopLossEnabled, stopLossPrice, amount, onTpSlDataUpdate]);

  // Auto-fill suggested prices when enabling
  const handleTpToggle = (enabled: boolean) => {
    setTakeProfitEnabled(enabled);
    if (enabled && !takeProfitPrice) {
      setTakeProfitPrice(getSuggestedTpPrice());
    }
  };

  const handleSlToggle = (enabled: boolean) => {
    setStopLossEnabled(enabled);
    if (enabled && !stopLossPrice) {
      setStopLossPrice(getSuggestedSlPrice());
    }
  };

  // Validation helpers
  const isValidTpPrice = () => {
    if (!takeProfitEnabled || !takeProfitPrice || !tokenPrice) return true;
    const tp = parseFloat(takeProfitPrice);
    if (isBuy) {
      return tp > tokenPrice; // TP should be higher for buy orders
    } else {
      return tp > tokenPrice; // TP should be higher for sell orders too
    }
  };

  const isValidSlPrice = () => {
    if (!stopLossEnabled || !stopLossPrice || !tokenPrice) return true;
    const sl = parseFloat(stopLossPrice);
    if (isBuy) {
      return sl < tokenPrice; // SL should be lower for buy orders
    } else {
      return sl < tokenPrice; // SL should be lower for sell orders too
    }
  };

  return (
    <div className="space-y-4">
      {/* Take Profit Section */}
      <div className="border border-emerald-800/30 rounded-lg p-4 bg-emerald-900/10">
        <div className="flex items-center justify-between mb-3">
          <label className="flex items-center space-x-2 text-emerald-300">
            <input
              type="checkbox"
              className="rounded text-emerald-500 focus:ring-emerald-500"
              checked={takeProfitEnabled}
              onChange={(e) => handleTpToggle(e.target.checked)}
            />
            <Target size={16} className="text-emerald-400" />
            <span className="font-medium">Take Profit</span>
          </label>
          {!isValidTpPrice() && (
            <AlertCircle size={16} className="text-red-400" />
          )}
        </div>

        {takeProfitEnabled && (
          <div className="space-y-3">
            <div>
              <label className="block text-xs text-gray-400 mb-1">
                Target Price (Execute when price ≥ ${takeProfitPrice})
              </label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  step="0.00000001"
                  placeholder="0.00000000"
                  value={takeProfitPrice}
                  onChange={(e) => setTakeProfitPrice(e.target.value)}
                  className={`flex-1 bg-[#1A1D24] border rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 ${
                    isValidTpPrice() 
                      ? 'border-emerald-600/50 focus:ring-emerald-500/50' 
                      : 'border-red-600/50 focus:ring-red-500/50'
                  }`}
                />
                <button
                  onClick={() => setTakeProfitPrice(getSuggestedTpPrice())}
                  className="px-3 py-2 bg-emerald-600/20 hover:bg-emerald-600/30 text-emerald-300 text-xs rounded-lg transition-colors"
                >
                  Auto
                </button>
              </div>
              {!isValidTpPrice() && (
                <p className="text-xs text-red-400 mt-1">
                  {isBuy ? 'Take profit price should be higher than current price' : 'Take profit price should be higher than current price'}
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Stop Loss Section */}
      <div className="border border-red-800/30 rounded-lg p-4 bg-red-900/10">
        <div className="flex items-center justify-between mb-3">
          <label className="flex items-center space-x-2 text-red-300">
            <input
              type="checkbox"
              className="rounded text-red-500 focus:ring-red-500"
              checked={stopLossEnabled}
              onChange={(e) => handleSlToggle(e.target.checked)}
            />
            <Shield size={16} className="text-red-400" />
            <span className="font-medium">Stop Loss</span>
          </label>
          {!isValidSlPrice() && (
            <AlertCircle size={16} className="text-red-400" />
          )}
        </div>

        {stopLossEnabled && (
          <div className="space-y-3">
            <div>
              <label className="block text-xs text-gray-400 mb-1">
                Stop Price (Execute when price ≤ ${stopLossPrice})
              </label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  step="0.00000001"
                  placeholder="0.00000000"
                  value={stopLossPrice}
                  onChange={(e) => setStopLossPrice(e.target.value)}
                  className={`flex-1 bg-[#1A1D24] border rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 ${
                    isValidSlPrice() 
                      ? 'border-red-600/50 focus:ring-red-500/50' 
                      : 'border-red-600/50 focus:ring-red-500/50'
                  }`}
                />
                <button
                  onClick={() => setStopLossPrice(getSuggestedSlPrice())}
                  className="px-3 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-300 text-xs rounded-lg transition-colors"
                >
                  Auto
                </button>
              </div>
              {!isValidSlPrice() && (
                <p className="text-xs text-red-400 mt-1">
                  {isBuy ? 'Stop loss price should be lower than current price' : 'Stop loss price should be lower than current price'}
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Amount Section - Only show if either TP or SL is enabled */}
      {(takeProfitEnabled || stopLossEnabled) && (
        <div className="border border-neutral-700/50 rounded-lg p-4 bg-neutral-900/20">
          <label className="block text-xs text-gray-400 mb-2">
            Amount for TP/SL Orders
          </label>
          <input
            type="number"
            step="0.000001"
            placeholder="Enter amount"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            className="w-full bg-[#1A1D24] border border-neutral-600/50 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-neutral-500/50"
          />
          <p className="text-xs text-gray-500 mt-1">
            Amount of {tokenSymbol} to trade when TP/SL triggers
          </p>
        </div>
      )}

      {/* Info Section */}
      {(takeProfitEnabled || stopLossEnabled) && tokenPrice > 0 && (
        <div className="text-xs text-gray-400 bg-neutral-900/30 rounded-lg p-3">
          <div className="flex items-center space-x-1 mb-1">
            <AlertCircle size={12} />
            <span className="font-medium">Current Price: ${tokenPrice.toFixed(8)}</span>
          </div>
          <p>
            TP/SL orders will be executed automatically when the target prices are reached.
          </p>
        </div>
      )}
    </div>
  );
};

export default TpSlSettings;