import React, { useState, useEffect } from 'react';
import { Target, Shield, Copy } from 'lucide-react';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
// import { getDefaultSolanaWalletInfo } from '../../utils/walletUtils'; // No longer needed, wallet info passed as prop
import { showSwapErrorToast, showSwapInfoToast } from '../../utils/swapToast';
import { useParams } from 'react-router-dom';
import { useActiveToken } from '../context/ActiveTokenContext';

interface NewTpSlSettingsProps {
  solBalance?: number;
  walletInfo?: { address: string; id: string } | null;
  onOrderCreated?: () => void;
}

export interface TpSlOrder {
  id: string;
  user_id: string;
  wallet_id: string;
  wallet_address: string;
  current_price: number;
  target_price: number;
  percentage: number;
  amount: number;
  token_address: string;
  pool_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  exchange_name?: string;
  action: 'take_profit' | 'stop_loss';
  status: 'active' | 'executed' | 'cancelled';
  created_at: string;
  
  // New combined order fields
  hasTP?: boolean;
  hasSL?: boolean;
  tp_target_price?: number;
  tp_percentage?: number;
  tp_id?: string;
  sl_target_price?: number;
  sl_percentage?: number;
  sl_id?: string;
  total_token?: number;
  txn?: string;
}

import { tpSlService, CreateTpSlOrderRequest } from '../../services/newTpslService';

const NewTpSlSettings: React.FC<NewTpSlSettingsProps> = ({ 
  solBalance = 0,
  walletInfo,
  onOrderCreated
}) => {
  const [solAmount, setSolAmount] = useState('');
  const [takeProfitPercentage, setTakeProfitPercentage] = useState(10);
  const [stopLossPercentage, setStopLossPercentage] = useState(5);
  const [takeProfitEnabled, setTakeProfitEnabled] = useState(true);
  const [stopLossEnabled, setStopLossEnabled] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const { user, authenticated } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  const params = useParams();
  const address = params?.address;
  
  // Use context for active token and live data
  const { activeToken, getCurrentPrice, getCurrentMarketCap } = useActiveToken();

  // Calculate target prices based on percentages using live or cached price
  const calculateTargetPrice = (percentage: number, isProfit: boolean) => {
    const currentPrice = getCurrentPrice();
    if (!currentPrice || currentPrice <= 0 || !percentage) return 0;
    
    const pct = percentage / 100;
    
    // For buy orders: TP is higher, SL is lower
    return isProfit 
      ? currentPrice * (1 + pct)  // Take profit: price goes up
      : currentPrice * (1 - pct); // Stop loss: price goes down
  };

  const takeProfitPrice = calculateTargetPrice(takeProfitPercentage, true);
  const stopLossPrice = calculateTargetPrice(stopLossPercentage, false);

  const handleCreateOrders = async () => {
    console.log('handleCreateOrders called');
    console.log('activeToken:', activeToken);
    console.log('user:', user);
    console.log('authenticated:', authenticated);
    console.log('solAmount:', solAmount);

    if (!authenticated || !user?.id) {
      showSwapErrorToast('Please connect your wallet first');
      return;
    }

    if (!activeToken) {
      showSwapErrorToast('No active token selected');
      console.error('activeToken is null/undefined:', activeToken);
      return;
    }

    if (!activeToken.address) {
      showSwapErrorToast('Token address is missing from active token');
      console.error('activeToken.address is missing:', activeToken);
      return;
    }

    if (!solAmount || parseFloat(solAmount) <= 0) {
      showSwapErrorToast('Please enter a valid SOL amount');
      return;
    }

    const amount = parseFloat(solAmount);
    if (amount > solBalance) {
      showSwapErrorToast(`Insufficient SOL balance. Available: ${solBalance.toFixed(4)} SOL`);
      return;
    }

    setIsCreating(true);

    try {
      // Use wallet info passed from parent (already loaded)
      if (!walletInfo) {
        showSwapErrorToast('No Solana wallet found');
        return;
      }

      const orderData: CreateTpSlOrderRequest = {
        user_id: user.id,
        wallet_id: walletInfo.id, // Correct wallet ID from Privy API
        wallet_address: walletInfo.address,
        current_price: getCurrentPrice() || 0, // Use live price from context
        amount: amount,
        token_address: activeToken.address,
        pool_address: activeToken.pool_address || '',
        token_name: activeToken.name || 'Unknown Token',
        token_symbol: activeToken.symbol || 'UNKNOWN',
        token_image: activeToken.imageUrl || '',
        exchange_name: activeToken.exchange_name || '',
        
        // Take Profit settings
        tp_enabled: takeProfitEnabled,
        tp_target_price: takeProfitEnabled ? takeProfitPrice : undefined,
        tp_percentage: takeProfitEnabled ? takeProfitPercentage : undefined,
        
        // Stop Loss settings
        sl_enabled: stopLossEnabled,
        sl_target_price: stopLossEnabled ? stopLossPrice : undefined,
        sl_percentage: stopLossEnabled ? stopLossPercentage : undefined
      };

      if (!takeProfitEnabled && !stopLossEnabled) {
        throw new Error('Please enable at least one order type (TP or SL)');
      }

      // Create single combined order
      const result = await tpSlService.createCombinedOrder(orderData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to create TP/SL order');
      }

      // Determine which orders were created
      const createdOrders = [];
      if (result.data?.takeProfitOrder) createdOrders.push('Take Profit');
      if (result.data?.stopLossOrder) createdOrders.push('Stop Loss');

      showSwapInfoToast(`${createdOrders.join(' & ')} order${createdOrders.length > 1 ? 's' : ''} created successfully`);
      
      // Reset form
      setSolAmount('');
      
      // Notify parent component
      onOrderCreated?.();

      // Dispatch event for positions table refresh
      window.dispatchEvent(new CustomEvent('tpslOrderCreated', {
        detail: {
          timestamp: Date.now(),
          orderCount: 2
        }
      }));

    } catch (error) {
      console.error('Error creating TP/SL orders:', error);
      showSwapErrorToast(error instanceof Error ? error.message : 'Failed to create TP/SL orders');
    } finally {
      setIsCreating(false);
    }
  };

  const copyToClipboard = (address: string) => {
    if (!address) {
      showSwapErrorToast('No address to copy');
      return;
    }
    navigator.clipboard.writeText(address);
    showSwapInfoToast('Address copied to clipboard');
  };

  return (
    <div className="space-y-4">
      {/* Debug info when no activeToken */}
      {!activeToken && (
        <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3 text-yellow-300 text-sm">
          ⚠️ No active token found. Please select a token first.
        </div>
      )}
      
      {/* SOL Amount Input */}
      <div className="space-y-2">
        <label className="block text-sm text-gray-400">
          SOL Amount (for both TP and SL)
        </label>
        <div className="relative">
          <input
            type="number"
            step="0.01"
            placeholder="0.00"
            value={solAmount}
            onChange={(e) => setSolAmount(e.target.value)}
            className="w-full bg-[#2A2D35] border border-gray-700 text-white rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-gray-500"
          />
          <span className="absolute right-3 top-2 text-gray-400 text-sm">SOL</span>
        </div>
        <div className="text-xs text-gray-500">
          Available: {solBalance.toFixed(4)} SOL
        </div>
      </div>

      {/* Take Profit Section */}
      <div className={`border rounded-lg p-4 transition-all ${takeProfitEnabled ? 'border-emerald-800/30 bg-emerald-900/10' : 'border-gray-700/30 bg-gray-900/10'}`}>
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Target size={16} className={takeProfitEnabled ? "text-emerald-400" : "text-gray-400"} />
            <span className={`font-medium ${takeProfitEnabled ? "text-emerald-300" : "text-gray-400"}`}>Take Profit</span>
          </div>
          <button
            onClick={() => setTakeProfitEnabled(!takeProfitEnabled)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              takeProfitEnabled ? 'bg-emerald-600' : 'bg-gray-600'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                takeProfitEnabled ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
        
        <div className={`space-y-3 ${!takeProfitEnabled ? 'opacity-50 pointer-events-none' : ''}`}>
          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="text-xs text-gray-400">Percentage</label>
              <span className={`text-sm ${takeProfitEnabled ? 'text-emerald-400' : 'text-gray-400'}`}>{takeProfitPercentage}%</span>
            </div>
            <input
              type="range"
              min="1"
              max="100"
              value={takeProfitPercentage}
              onChange={(e) => setTakeProfitPercentage(Number(e.target.value))}
              disabled={!takeProfitEnabled}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>
          
          {takeProfitPrice > 0 && takeProfitEnabled && (
            <div className="text-xs text-emerald-400">
              Target Price: ${takeProfitPrice.toFixed(8)}
            </div>
          )}
        </div>
      </div>

      {/* Stop Loss Section */}
      <div className={`border rounded-lg p-4 transition-all ${stopLossEnabled ? 'border-red-800/30 bg-red-900/10' : 'border-gray-700/30 bg-gray-900/10'}`}>
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Shield size={16} className={stopLossEnabled ? "text-red-400" : "text-gray-400"} />
            <span className={`font-medium ${stopLossEnabled ? "text-red-300" : "text-gray-400"}`}>Stop Loss</span>
          </div>
          <button
            onClick={() => setStopLossEnabled(!stopLossEnabled)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              stopLossEnabled ? 'bg-red-600' : 'bg-gray-600'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                stopLossEnabled ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
        
        <div className={`space-y-3 ${!stopLossEnabled ? 'opacity-50 pointer-events-none' : ''}`}>
          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="text-xs text-gray-400">Percentage</label>
              <span className={`text-sm ${stopLossEnabled ? 'text-red-400' : 'text-gray-400'}`}>{stopLossPercentage}%</span>
            </div>
            <input
              type="range"
              min="1"
              max="50"
              value={stopLossPercentage}
              onChange={(e) => setStopLossPercentage(Number(e.target.value))}
              disabled={!stopLossEnabled}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>
          
          {stopLossPrice > 0 && stopLossEnabled && (
            <div className="text-xs text-red-400">
              Target Price: ${stopLossPrice.toFixed(8)}
            </div>
          )}
        </div>
      </div>

      {/* Token Info */}
      {activeToken && (
        <div className="bg-neutral-900/30 rounded-lg p-3 text-xs">
          <div className="flex items-center space-x-2 mb-2">
            {activeToken.imageUrl && (
              <img 
                src={activeToken.imageUrl} 
                alt={activeToken.symbol} 
                className="w-4 h-4 rounded-full"
              />
            )}
            <span className="text-white font-medium">{activeToken.symbol}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-400">Current Price:</span>
            <span className="text-white">${(getCurrentPrice() || 0).toFixed(8)}</span>
          </div>
          {activeToken.address && (
            <div className="flex items-center space-x-1 mt-1">
              <span className="text-gray-400 text-xs truncate">
                {activeToken.address.slice(0, 6)}...{activeToken.address.slice(-4)}
              </span>
              <button 
                onClick={() => copyToClipboard(activeToken.address)}
                className="text-gray-400 hover:text-white"
              >
                <Copy size={12} />
              </button>
            </div>
          )}
        </div>
      )}

      {/* Create Orders Button */}
      <button
        onClick={handleCreateOrders}
        disabled={isCreating || !solAmount || parseFloat(solAmount) <= 0 || (!takeProfitEnabled && !stopLossEnabled)}
        className="w-full bg-gradient-to-r from-[#14FFA2] to-[#32E6A4] disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-black font-bold py-3 px-4 rounded-lg hover:opacity-90 transition-all duration-200"
      >
        {isCreating ? 'Creating Orders...' : 
         (!takeProfitEnabled && !stopLossEnabled) ? 'Enable TP or SL to create orders' :
         'Create TP/SL Orders'}
      </button>
    </div>
  );
};

export default NewTpSlSettings;
export { tpSlService };