// Utility to get default wallet addresses from localStorage
export function getDefaultSolanaWalletAddress(): string | null {
  try {
    const defaultWalletsStr = localStorage.getItem('defaultWallets');
    if (!defaultWalletsStr) {
      return null;
    }
    
    const defaultWallets = JSON.parse(defaultWalletsStr);
    return defaultWallets.solana || null;
  } catch (error) {
    console.error('Error reading default wallet from localStorage:', error);
    return null;
  }
}