// Simple in-memory cache for token balances to prevent duplicate API calls
interface CacheEntry {
  data: any;
  timestamp: number;
  walletAddress: string;
}

class TokenBalanceCache {
  private cache: Map<string, CacheEntry> = new Map();
  private readonly CACHE_DURATION = 30000; // 30 seconds cache duration
  private pendingRequests: Map<string, Promise<any>> = new Map();

  // Get cached data if it's still valid
  get(walletAddress: string): any | null {
    const entry = this.cache.get(walletAddress);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > this.CACHE_DURATION) {
      // Cache expired
      this.cache.delete(walletAddress);
      return null;
    }

    console.log('Returning cached token balance data for:', walletAddress);
    return entry.data;
  }

  // Set cache data
  set(walletAddress: string, data: any): void {
    console.log('Caching token balance data for:', walletAddress);
    this.cache.set(walletAddress, {
      data,
      timestamp: Date.now(),
      walletAddress
    });
  }

  // Check if there's a pending request for this wallet
  getPendingRequest(walletAddress: string): Promise<any> | null {
    return this.pendingRequests.get(walletAddress) || null;
  }

  // Set a pending request
  setPendingRequest(walletAddress: string, promise: Promise<any>): void {
    this.pendingRequests.set(walletAddress, promise);
  }

  // Clear pending request
  clearPendingRequest(walletAddress: string): void {
    this.pendingRequests.delete(walletAddress);
  }

  // Clear all cache
  clear(): void {
    this.cache.clear();
    this.pendingRequests.clear();
  }

  // Clear cache for specific wallet
  clearForWallet(walletAddress: string): void {
    this.cache.delete(walletAddress);
    this.pendingRequests.delete(walletAddress);
  }
}

// Export singleton instance
export const tokenBalanceCache = new TokenBalanceCache();