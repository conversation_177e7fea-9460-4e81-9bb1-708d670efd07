// Token image service utility
// Uses Mobula's metacore service for Solana token images

const MOBULA_BASE_URL = 'https://metacore.mobula.io';
const FALLBACK_IMAGE = '/placeholder-token.png';

// Cache token images to avoid repeated lookups
const tokenImageCache = new Map<string, string>();

/**
 * Get token image URL from token address
 * @param tokenAddress - The Solana token address
 * @param tokenSymbol - Optional token symbol for fallback
 * @returns Image URL
 */
export function getTokenImageUrl(tokenAddress: string, tokenSymbol?: string): string {
  // Check cache first
  const cached = tokenImageCache.get(tokenAddress);
  if (cached) {
    return cached;
  }

  // Special case for SOL
  if (tokenAddress === 'So11111111111111111111111111111111111111112') {
    const solImage = `${MOBULA_BASE_URL}/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png`;
    tokenImageCache.set(tokenAddress, solImage);
    return solImage;
  }

  // For other tokens, try to use <PERSON><PERSON>'s service
  // Note: This assumes the token address can be used directly with Mobula
  // In practice, you might need to map addresses to Mobula IDs
  const imageUrl = `${MOBULA_BASE_URL}/${tokenAddress}.png`;
  tokenImageCache.set(tokenAddress, imageUrl);
  
  return imageUrl;
}

/**
 * Clear the token image cache
 */
export function clearTokenImageCache(): void {
  tokenImageCache.clear();
}