import React from 'react';
import { toast } from 'react-toastify';
import { ExternalLink, CheckCircle, XCircle } from 'lucide-react';

interface SwapToastProps {
  type: 'buy' | 'sell';
  tokenSymbol: string;
  transactionSignature: string;
  solscanUrl?: string;
}

interface SwapErrorToastProps {
  error: string;
}

/**
 * Generate Solscan URL from transaction signature
 */
const generateSolscanUrl = (signature: string): string => {
  return `https://solscan.io/tx/${signature}`;
};

/**
 * Success toast component for swap transactions
 */
const SwapSuccessToast: React.FC<SwapToastProps> = ({ 
  type, 
  tokenSymbol, 
  transactionSignature, 
  solscanUrl 
}) => {
  const finalSolscanUrl = solscanUrl || generateSolscanUrl(transactionSignature);
  
  const handleSolscanClick = () => {
    window.open(finalSolscanUrl, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="flex flex-col gap-3 p-1">
      {/* Success message */}
      <div className="flex items-center gap-3">
        <CheckCircle className="text-green-400 w-5 h-5 flex-shrink-0" />
        <span className="font-medium">
          Successfully {type === 'buy' ? 'bought' : 'sold'} {tokenSymbol}
        </span>
      </div>
      
      {/* Transaction signature */}
      <div className="text-sm text-gray-300 ml-8">
        <span className="text-gray-400">Transaction:</span>{' '}
        <span className="font-mono text-xs">
          {transactionSignature.slice(0, 8)}...{transactionSignature.slice(-8)}
        </span>
      </div>
      
      {/* Solscan link */}
      <button
        onClick={handleSolscanClick}
        className="flex items-center gap-2 ml-8 text-sm text-blue-400 hover:text-blue-300 transition-colors group"
      >
        <ExternalLink className="w-4 h-4" />
        <span className="group-hover:underline">View on Solscan</span>
      </button>
    </div>
  );
};

/**
 * Error toast component for swap transactions
 */
const SwapErrorToast: React.FC<SwapErrorToastProps> = ({ error }) => {
  return (
    <div className="flex items-center gap-3 p-1">
      <XCircle className="text-red-400 w-5 h-5 flex-shrink-0" />
      <div className="flex flex-col gap-1">
        <span className="font-medium text-red-300">Swap Failed</span>
        <span className="text-sm text-gray-300">{error}</span>
      </div>
    </div>
  );
};

/**
 * Show success toast for completed swap
 */
export const showSwapSuccessToast = (
  type: 'buy' | 'sell',
  tokenSymbol: string,
  transactionSignature: string,
  solscanUrl?: string
) => {
  toast.success(
    <SwapSuccessToast
      type={type}
      tokenSymbol={tokenSymbol}
      transactionSignature={transactionSignature}
      solscanUrl={solscanUrl}
    />,
    {
      position: 'top-right',
      autoClose: 5000, // 5 seconds
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      className: 'bg-[#1f1f1f] text-white border border-green-500/20 rounded-lg shadow-lg',
      progressClassName: 'bg-green-400',
      icon: false, // We're using our own icon
    }
  );
};

/**
 * Show error toast for failed swap
 */
export const showSwapErrorToast = (error: string) => {
  toast.error(
    <SwapErrorToast error={error} />,
    {
      position: 'top-right',
      autoClose: 4000, // 4 seconds
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      className: 'bg-[#1f1f1f] text-white border border-red-500/20 rounded-lg shadow-lg',
      progressClassName: 'bg-red-400',
      icon: false, // We're using our own icon
    }
  );
};

/**
 * Show info toast for general messages
 */
export const showSwapInfoToast = (message: string) => {
  toast.info(message, {
    position: 'top-right',
    autoClose: 3000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    className: 'bg-[#1f1f1f] text-white border border-blue-500/20 rounded-lg shadow-lg',
    progressClassName: 'bg-blue-400',
  });
};
