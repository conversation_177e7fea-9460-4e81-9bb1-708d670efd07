/**
 * Wallet Delegation Management System for Privy Wallets
 * Handles delegation status checking, user prompts, and wallet selection logic
 */

import { getPrivySolanaWalletInfo } from '../api/privy_api';
import { useSessionSigners } from './sessionSigners';
import { DelegationToasts, handleDelegationError, showDelegationWorkflowResult } from './delegationToast';

export interface WalletDelegationStatus {
  walletId: string;
  address: string;
  delegated: boolean;
  imported: boolean;
  verifiedAt: number;
}

export interface DelegationCheckResult {
  success: boolean;
  currentWallet: WalletDelegationStatus | null;
  allWallets: WalletDelegationStatus[];
  needsDelegation: boolean;
  hasValidDelegatedWallet: boolean;
  recommendedWallet: WalletDelegationStatus | null;
  message?: string;
}

export interface DelegationPromptResult {
  action: 'enable' | 'switch' | 'create' | 'cancel';
  selectedWallet?: WalletDelegationStatus;
}

/**
 * Check current wallet delegation status and provide recommendations
 */
export const checkWalletDelegationStatus = async (
  userId: string,
  currentWalletAddress?: string
): Promise<DelegationCheckResult> => {
  try {
    console.log('🔍 Checking wallet delegation status for user:', userId);
    console.log('🔍 Current wallet address:', currentWalletAddress);

    // Get wallet information from API
    const response = await getPrivySolanaWalletInfo(userId, true);
    
    if (!response.success || !response.data) {
      return {
        success: false,
        currentWallet: null,
        allWallets: [],
        needsDelegation: false,
        hasValidDelegatedWallet: false,
        recommendedWallet: null,
        message: 'Failed to fetch wallet information from Privy'
      };
    }

    const { data } = response;
    const allWallets: WalletDelegationStatus[] = data.allSolanaWallets.map(wallet => ({
      walletId: wallet.id,
      address: wallet.address,
      delegated: wallet.delegated,
      imported: wallet.imported,
      verifiedAt: wallet.verifiedAt
    }));

    console.log('📊 All wallets:', allWallets);

    // Find current wallet in the list
    let currentWallet: WalletDelegationStatus | null = null;
    if (currentWalletAddress) {
      currentWallet = allWallets.find(w => w.address === currentWalletAddress) || null;
    }

    // If no current wallet specified, use the primary wallet from API
    if (!currentWallet) {
      currentWallet = {
        walletId: data.walletId,
        address: data.address,
        delegated: data.delegated,
        imported: data.imported,
        verifiedAt: data.verifiedAt
      };
    }

    console.log('🎯 Current wallet:', currentWallet);

    // Filter out temporary wallets (starting with "temp_") for recommendations
    const nonTempWallets = allWallets.filter(wallet => !wallet.walletId.startsWith('temp_'));
    const delegatedWallets = nonTempWallets.filter(wallet => wallet.delegated && !wallet.imported);
    
    console.log('✅ Non-temporary wallets:', nonTempWallets);
    console.log('🔐 Delegated wallets:', delegatedWallets);

    // Determine if current wallet needs delegation
    const needsDelegation = currentWallet && !currentWallet.delegated;
    const hasValidDelegatedWallet = delegatedWallets.length > 0;

    // Find recommended wallet (prioritize delegated, non-imported, non-temp wallets)
    let recommendedWallet: WalletDelegationStatus | null = null;
    if (needsDelegation && hasValidDelegatedWallet) {
      // Recommend the first delegated wallet that's not imported
      recommendedWallet = delegatedWallets[0];
    }

    const result: DelegationCheckResult = {
      success: true,
      currentWallet,
      allWallets,
      needsDelegation: !!needsDelegation,
      hasValidDelegatedWallet,
      recommendedWallet
    };

    console.log('📋 Delegation check result:', result);
    return result;

  } catch (error) {
    console.error('❌ Error checking wallet delegation status:', error);
    return {
      success: false,
      currentWallet: null,
      allWallets: [],
      needsDelegation: false,
      hasValidDelegatedWallet: false,
      recommendedWallet: null,
      message: `Error checking delegation status: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

/**
 * Get the default wallet ID from localStorage
 */
export const getStoredDefaultWalletId = (): string | null => {
  try {
    const storedDefaults = localStorage.getItem('defaultWallets');
    if (storedDefaults) {
      const defaultWallets = JSON.parse(storedDefaults);
      return defaultWallets.solana || null;
    }
  } catch (error) {
    console.error('Error reading defaultWallets from localStorage:', error);
  }
  return null;
};

/**
 * Update the default wallet in localStorage
 */
export const updateStoredDefaultWallet = (walletAddress: string): void => {
  try {
    const storedDefaults = localStorage.getItem('defaultWallets') || '{}';
    const defaultWallets = JSON.parse(storedDefaults);
    defaultWallets.solana = walletAddress;
    localStorage.setItem('defaultWallets', JSON.stringify(defaultWallets));
    console.log('✅ Updated defaultWallets in localStorage:', walletAddress);
  } catch (error) {
    console.error('❌ Error updating defaultWallets in localStorage:', error);
  }
};

/**
 * Show delegation prompt to user and handle their choice
 */
export const showDelegationPrompt = (
  currentWallet: WalletDelegationStatus,
  recommendedWallet: WalletDelegationStatus | null,
  onResult: (result: DelegationPromptResult) => void
): void => {
  let message: string;
  let detailMessage: string = '';

  if (currentWallet.imported) {
    message = `Your current wallet (${currentWallet.address.slice(0, 8)}...) is imported and cannot be delegated for automatic signing.`;
    detailMessage = 'Imported wallets require manual approval for each transaction due to security limitations. For seamless trading, consider switching to a delegated wallet or creating a new embedded wallet.';
  } else {
    message = `Your current wallet (${currentWallet.address.slice(0, 8)}...) is not enabled for automatic signing.`;
    detailMessage = 'Enable delegation to allow seamless trading without manual approval for each transaction.';
  }

  const options = [];

  if (!currentWallet.imported) {
    options.push({
      text: 'Enable Delegation',
      action: 'enable' as const,
      primary: true,
      description: 'Allow automatic signing for seamless trading'
    });
  }

  if (recommendedWallet) {
    options.push({
      text: `Switch to Delegated Wallet (${recommendedWallet.address.slice(0, 8)}...)`,
      action: 'switch' as const,
      wallet: recommendedWallet,
      primary: currentWallet.imported,
      description: 'Use an existing wallet that supports automatic signing'
    });
  }

  // Add option to create new embedded wallet for imported wallet users
  if (currentWallet.imported) {
    options.push({
      text: 'Create New Embedded Wallet',
      action: 'create' as const,
      primary: false,
      description: 'Create a new wallet that supports automatic signing'
    });
  }

  options.push({
    text: currentWallet.imported ? 'Continue with Manual Signing' : 'Cancel',
    action: 'cancel' as const,
    primary: false,
    description: currentWallet.imported ? 'Keep using imported wallet with manual approval' : ''
  });

  // Create and show custom modal
  const modal = document.createElement('div');
  modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
  modal.innerHTML = `
    <div class="bg-gray-800 rounded-lg p-6 max-w-lg mx-4 text-white">
      <h3 class="text-lg font-semibold mb-4">
        ${currentWallet.imported ? 'Imported Wallet Limitations' : 'Wallet Delegation Required'}
      </h3>
      <p class="text-gray-300 mb-4">${message}</p>
      ${detailMessage ? `<p class="text-gray-400 text-sm mb-6">${detailMessage}</p>` : ''}
      <div class="flex flex-col gap-3">
        ${options.map((option, index) => `
          <button
            data-action="${option.action}"
            data-wallet="${option.wallet ? JSON.stringify(option.wallet) : ''}"
            class="px-4 py-2 rounded-lg font-medium transition-colors text-left ${
              option.primary
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : option.action === 'cancel'
                  ? 'bg-gray-600 hover:bg-gray-700 text-gray-200'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
            }"
          >
            <div class="font-medium">${option.text}</div>
            ${option.description ? `<div class="text-xs opacity-80 mt-1">${option.description}</div>` : ''}
          </button>
        `).join('')}
      </div>
      ${currentWallet.imported ? `
        <div class="mt-4 p-3 bg-yellow-900/30 border border-yellow-600/50 rounded-lg">
          <div class="text-xs text-yellow-200">
            <strong>Why can't imported wallets be delegated?</strong><br>
            Imported wallets use external private keys that cannot generate session signers due to security limitations.
            This is a technical constraint, not a feature limitation.
          </div>
        </div>
      ` : ''}
    </div>
  `;

  // Add event listeners
  modal.addEventListener('click', (e) => {
    const target = e.target as HTMLElement;
    if (target.tagName === 'BUTTON') {
      const action = target.getAttribute('data-action') as DelegationPromptResult['action'];
      const walletData = target.getAttribute('data-wallet');

      const result: DelegationPromptResult = {
        action,
        selectedWallet: walletData ? JSON.parse(walletData) : undefined
      };

      document.body.removeChild(modal);
      onResult(result);
    } else if (target === modal) {
      // Click outside modal
      document.body.removeChild(modal);
      onResult({ action: 'cancel' });
    }
  });

  document.body.appendChild(modal);
};

/**
 * Handle delegation management workflow
 */
export const handleWalletDelegationWorkflow = async (
  userId: string,
  delegateSolanaWallet: (address: string) => Promise<boolean | 'imported'>,
  onSuccess?: (wallet: WalletDelegationStatus) => void,
  onError?: (error: string) => void,
  onCreateWallet?: () => Promise<WalletDelegationStatus | null>
): Promise<WalletDelegationStatus | null> => {
  try {
    console.log('🚀 Starting wallet delegation workflow for user:', userId);

    // Get current default wallet from localStorage
    const storedWalletAddress = getStoredDefaultWalletId();
    console.log('📱 Stored default wallet:', storedWalletAddress);

    // Check delegation status
    const delegationCheck = await checkWalletDelegationStatus(userId, storedWalletAddress || undefined);

    if (!delegationCheck.success) {
      const error = delegationCheck.message || 'Failed to check wallet delegation status';
      console.error('❌ Delegation check failed:', error);
      onError?.(error);
      return null;
    }

    const { currentWallet, needsDelegation, hasValidDelegatedWallet, recommendedWallet } = delegationCheck;

    if (!currentWallet) {
      const error = 'No current wallet found';
      console.error('❌', error);
      onError?.(error);
      return null;
    }

    // If current wallet is already delegated, return it
    if (currentWallet.delegated) {
      console.log('✅ Current wallet is already delegated:', currentWallet.address);
      onSuccess?.(currentWallet);
      return currentWallet;
    }

    // If wallet needs delegation, show prompt
    if (needsDelegation) {
      return new Promise((resolve) => {
        showDelegationPrompt(currentWallet, recommendedWallet, async (result) => {
          try {
            if (result.action === 'enable') {
              console.log('🔄 User chose to enable delegation for current wallet');
              DelegationToasts.enablingDelegation(currentWallet.address);

              try {
                const delegationResult = await delegateSolanaWallet(currentWallet.address);

                if (delegationResult === true) {
                  console.log('✅ Delegation successful for:', currentWallet.address);

                  // Re-fetch wallet info to confirm delegation
                  const updatedCheck = await checkWalletDelegationStatus(userId, currentWallet.address);
                  const updatedWallet = updatedCheck.currentWallet;

                  if (updatedWallet?.delegated) {
                    DelegationToasts.delegationSuccess(updatedWallet.address);
                    onSuccess?.(updatedWallet);
                    resolve(updatedWallet);
                  } else {
                    const error = 'Delegation appeared successful but wallet is still not delegated';
                    console.error('❌', error);
                    DelegationToasts.delegationFailed(currentWallet.address, 'Verification failed');
                    onError?.(error);
                    resolve(null);
                  }
                } else if (delegationResult === 'imported') {
                  const error = 'Wallet is imported and cannot be delegated';
                  console.error('❌', error);
                  DelegationToasts.importedWalletWarning(currentWallet.address);
                  onError?.(error);
                  resolve(null);
                } else {
                  const error = 'Failed to enable delegation';
                  console.error('❌', error);
                  DelegationToasts.delegationFailed(currentWallet.address);
                  onError?.(error);
                  resolve(null);
                }
              } catch (delegationError) {
                console.error('❌ Error during delegation:', delegationError);
                handleDelegationError(delegationError, 'wallet delegation');
                onError?.(delegationError instanceof Error ? delegationError.message : 'Unknown delegation error');
                resolve(null);
              }

            } else if (result.action === 'switch' && result.selectedWallet) {
              console.log('🔄 User chose to switch to delegated wallet:', result.selectedWallet.address);
              DelegationToasts.switchingWallet(currentWallet.address, result.selectedWallet.address);

              // Update localStorage with the selected wallet
              updateStoredDefaultWallet(result.selectedWallet.address);

              DelegationToasts.walletSwitched(result.selectedWallet.address);
              onSuccess?.(result.selectedWallet);
              resolve(result.selectedWallet);

            } else if (result.action === 'create') {
              console.log('🔄 User chose to create new embedded wallet');

              if (onCreateWallet) {
                try {
                  showDelegationToast('Creating new embedded wallet...', 'info');
                  const newWallet = await onCreateWallet();

                  if (newWallet) {
                    console.log('✅ New embedded wallet created:', newWallet.address);
                    updateStoredDefaultWallet(newWallet.address);
                    showDelegationToast(`New wallet created: ${newWallet.address.slice(0, 8)}...`, 'success');
                    onSuccess?.(newWallet);
                    resolve(newWallet);
                  } else {
                    const error = 'Failed to create new embedded wallet';
                    console.error('❌', error);
                    showDelegationToast(error, 'error');
                    onError?.(error);
                    resolve(null);
                  }
                } catch (createError) {
                  const errorMessage = createError instanceof Error ? createError.message : 'Unknown error creating wallet';
                  console.error('❌ Error creating new wallet:', createError);
                  handleDelegationError(createError, 'wallet creation');
                  onError?.(errorMessage);
                  resolve(null);
                }
              } else {
                const error = 'Wallet creation not supported in this context';
                console.error('❌', error);
                showDelegationToast(error, 'error');
                onError?.(error);
                resolve(null);
              }

            } else {
              console.log('❌ User cancelled delegation workflow');
              DelegationToasts.userCancelled();
              onError?.('User cancelled delegation');
              resolve(null);
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error during delegation';
            console.error('❌ Error in delegation workflow:', error);
            onError?.(errorMessage);
            resolve(null);
          }
        });
      });
    }

    // If no delegation needed but we have a valid delegated wallet, use it
    if (hasValidDelegatedWallet && recommendedWallet) {
      console.log('🔄 Switching to recommended delegated wallet:', recommendedWallet.address);
      updateStoredDefaultWallet(recommendedWallet.address);
      onSuccess?.(recommendedWallet);
      return recommendedWallet;
    }

    // Fallback: return current wallet even if not delegated
    console.log('⚠️ Using current wallet without delegation:', currentWallet.address);
    onSuccess?.(currentWallet);
    return currentWallet;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error in delegation workflow';
    console.error('❌ Error in wallet delegation workflow:', error);
    onError?.(errorMessage);
    return null;
  }
};
