/**
 * Specialized toast system for wallet delegation operations
 * Provides consistent user feedback during delegation processes
 */

import { toast } from 'react-toastify';

export type DelegationToastType = 'info' | 'success' | 'warning' | 'error';

interface DelegationToastOptions {
  autoClose?: number;
  hideProgressBar?: boolean;
  closeOnClick?: boolean;
  pauseOnHover?: boolean;
  draggable?: boolean;
}

/**
 * Show delegation-specific toast with consistent styling
 */
export const showDelegationToast = (
  message: string,
  type: DelegationToastType = 'info',
  options: DelegationToastOptions = {}
): void => {
  const defaultOptions = {
    autoClose: 5000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    ...options
  };

  const toastConfig = {
    position: 'top-right' as const,
    ...defaultOptions,
    className: 'delegation-toast',
    bodyClassName: 'delegation-toast-body',
    progressClassName: 'delegation-toast-progress'
  };

  switch (type) {
    case 'info':
      toast.info(`🔄 ${message}`, toastConfig);
      break;
    case 'success':
      toast.success(`✅ ${message}`, toastConfig);
      break;
    case 'warning':
      toast.warning(`⚠️ ${message}`, toastConfig);
      break;
    case 'error':
      toast.error(`❌ ${message}`, toastConfig);
      break;
  }
};

/**
 * Predefined delegation toast messages
 */
export const DelegationToasts = {
  // Checking status
  checkingStatus: () => showDelegationToast('Checking wallet delegation status...', 'info'),
  
  // Delegation process
  enablingDelegation: (address: string) => 
    showDelegationToast(`Enabling delegation for wallet ${address.slice(0, 8)}...`, 'info'),
  
  delegationSuccess: (address: string) => 
    showDelegationToast(`Wallet ${address.slice(0, 8)}... enabled for seamless trading!`, 'success'),
  
  delegationFailed: (address: string, reason?: string) => 
    showDelegationToast(
      `Failed to enable delegation for wallet ${address.slice(0, 8)}...${reason ? `: ${reason}` : ''}`, 
      'error'
    ),
  
  // Wallet switching
  switchingWallet: (fromAddress: string, toAddress: string) =>
    showDelegationToast(
      `Switching from ${fromAddress.slice(0, 8)}... to ${toAddress.slice(0, 8)}...`, 
      'info'
    ),
  
  walletSwitched: (address: string) =>
    showDelegationToast(`Now using delegated wallet ${address.slice(0, 8)}...`, 'success'),
  
  // Import wallet warnings
  importedWalletWarning: (address: string) =>
    showDelegationToast(
      `Wallet ${address.slice(0, 8)}... is imported and requires manual signing`, 
      'warning',
      { autoClose: 8000 }
    ),
  
  // Error states
  apiError: (error: string) =>
    showDelegationToast(`API Error: ${error}`, 'error', { autoClose: 8000 }),
  
  networkError: () =>
    showDelegationToast('Network error - please check your connection', 'error'),
  
  userCancelled: () =>
    showDelegationToast('Delegation setup cancelled by user', 'info'),
  
  // Success states
  allWalletsDelegated: () =>
    showDelegationToast('All wallets are properly configured for trading!', 'success'),
  
  noActionNeeded: () =>
    showDelegationToast('Your wallet is already enabled for seamless trading', 'success'),
  
  // Recommendations
  recommendSwitchWallet: (recommendedAddress: string) =>
    showDelegationToast(
      `Consider switching to delegated wallet ${recommendedAddress.slice(0, 8)}... for better experience`,
      'info',
      { autoClose: 10000 }
    ),

  // Imported wallet specific messages
  importedWalletLimitation: (address: string) =>
    showDelegationToast(
      `Imported wallet ${address.slice(0, 8)}... cannot be delegated due to security limitations`,
      'warning',
      { autoClose: 12000 }
    ),

  suggestEmbeddedWallet: () =>
    showDelegationToast(
      'Consider creating an embedded wallet for seamless trading experience',
      'info',
      { autoClose: 10000 }
    ),

  walletCreationStarted: () =>
    showDelegationToast('Creating new embedded wallet...', 'info'),

  walletCreationSuccess: (address: string) =>
    showDelegationToast(`New embedded wallet created: ${address.slice(0, 8)}... with automatic signing enabled!`, 'success', { autoClose: 8000 }),

  walletCreationFailed: (reason?: string) =>
    showDelegationToast(
      `Failed to create embedded wallet${reason ? `: ${reason}` : ''}`,
      'error'
    ),

  walletSetAsDefault: (address: string) =>
    showDelegationToast(`Wallet ${address.slice(0, 8)}... set as default for trading`, 'success'),

  manualSigningMode: (address: string) =>
    showDelegationToast(
      `Using manual signing mode for wallet ${address.slice(0, 8)}...`,
      'info',
      { autoClose: 8000 }
    )
};

/**
 * Handle delegation errors with appropriate toast messages
 */
export const handleDelegationError = (error: unknown, context: string = ''): void => {
  console.error(`Delegation error${context ? ` in ${context}` : ''}:`, error);
  
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      DelegationToasts.networkError();
    } else if (message.includes('imported') || message.includes('hierarchical deterministic')) {
      // Extract address if available
      const addressMatch = error.message.match(/([A-Za-z0-9]{32,})/);
      const address = addressMatch ? addressMatch[0] : 'unknown';
      DelegationToasts.importedWalletWarning(address);
    } else if (message.includes('cancelled') || message.includes('rejected')) {
      DelegationToasts.userCancelled();
    } else {
      DelegationToasts.apiError(error.message);
    }
  } else if (typeof error === 'string') {
    DelegationToasts.apiError(error);
  } else {
    DelegationToasts.apiError('Unknown error occurred during delegation');
  }
};

/**
 * Show delegation workflow completion toast
 */
export const showDelegationWorkflowResult = (
  success: boolean,
  walletAddress?: string,
  message?: string
): void => {
  if (success && walletAddress) {
    DelegationToasts.delegationSuccess(walletAddress);
  } else if (message) {
    showDelegationToast(message, success ? 'success' : 'error');
  } else {
    showDelegationToast(
      success ? 'Delegation workflow completed' : 'Delegation workflow failed',
      success ? 'success' : 'error'
    );
  }
};

/**
 * Deduplication system for delegation toasts
 */
const activeToasts = new Set<string>();

export const showDedupedDelegationToast = (
  message: string,
  type: DelegationToastType = 'info',
  options: DelegationToastOptions = {}
): void => {
  const toastKey = `${type}-${message}`;
  
  if (activeToasts.has(toastKey)) {
    console.log('Skipping duplicate delegation toast:', toastKey);
    return;
  }
  
  activeToasts.add(toastKey);
  
  // Remove from active set after toast duration
  const duration = options.autoClose || 5000;
  setTimeout(() => {
    activeToasts.delete(toastKey);
  }, duration + 1000); // Add buffer time
  
  showDelegationToast(message, type, options);
};
