// Utility for sharing auto fee state between components
export interface StoredAutoFeeState {
  isEnabled: boolean;
  priorityFee: number;
  bribeAmount: number;
  lastUpdated: string;
  isLoading?: boolean;
  networkStats?: {
    averageTps: number;
    congestionMultiplier: number;
  };
}

const AUTO_FEE_STATE_KEY = 'autoFeeState';
const AUTO_FEE_ENABLED_KEY = 'autoFeeEnabled'; // Separate key for enabled state
const AUTO_FEE_UPDATE_EVENT = 'autoFeeStateUpdate';

/**
 * Store auto fee state in localStorage and dispatch update event
 */
export function storeAutoFeeState(state: StoredAutoFeeState): void {
  localStorage.setItem(AUTO_FEE_STATE_KEY, JSON.stringify(state));
  
  // Dispatch custom event for other components to listen
  window.dispatchEvent(new CustomEvent(AUTO_FEE_UPDATE_EVENT, {
    detail: state
  }));
}

/**
 * Get stored auto fee state from localStorage
 */
export function getStoredAutoFeeState(): StoredAutoFeeState | null {
  try {
    const stored = localStorage.getItem(AUTO_FEE_STATE_KEY);
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.error('Error parsing stored auto fee state:', error);
    return null;
  }
}

/**
 * Subscribe to auto fee state updates
 */
export function subscribeToAutoFeeUpdates(callback: (state: StoredAutoFeeState) => void): () => void {
  const handler = (event: Event) => {
    const customEvent = event as CustomEvent<StoredAutoFeeState>;
    callback(customEvent.detail);
  };
  
  window.addEventListener(AUTO_FEE_UPDATE_EVENT, handler);
  
  // Return cleanup function
  return () => {
    window.removeEventListener(AUTO_FEE_UPDATE_EVENT, handler);
  };
}

/**
 * Clear stored auto fee state
 */
export function clearAutoFeeState(): void {
  localStorage.removeItem(AUTO_FEE_STATE_KEY);
  localStorage.removeItem(AUTO_FEE_ENABLED_KEY);
}

/**
 * Store auto fee enabled state separately for persistence
 */
export function storeAutoFeeEnabled(isEnabled: boolean): void {
  localStorage.setItem(AUTO_FEE_ENABLED_KEY, JSON.stringify(isEnabled));
  console.log('[autoFeeStorage] Stored auto fee enabled state:', isEnabled);
}

/**
 * Get stored auto fee enabled state
 */
export function getAutoFeeEnabled(): boolean {
  try {
    const stored = localStorage.getItem(AUTO_FEE_ENABLED_KEY);
    const isEnabled = stored ? JSON.parse(stored) : false;
    console.log('[autoFeeStorage] Retrieved auto fee enabled state:', isEnabled);
    return isEnabled;
  } catch (error) {
    console.error('Error parsing stored auto fee enabled state:', error);
    return false;
  }
}