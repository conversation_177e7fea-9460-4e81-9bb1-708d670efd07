/**
 * Utility to sync wallet selection between localStorage and Privy
 */

import { getPrivySolanaWalletInfo } from '../api/privy_api';

export interface WalletSyncResult {
  success: boolean;
  walletId?: string;
  address?: string;
  message?: string;
  updated?: boolean;
}

/**
 * Sync and validate wallet selection between localStorage and Privy
 * This ensures we always use a wallet that has a valid ID from Privy
 */
export async function syncWalletSelection(
  userId: string,
  preferredAddress?: string
): Promise<WalletSyncResult> {
  try {
    // Get all wallets from Privy API
    const privyResponse = await getPrivySolanaWalletInfo(userId, true);
    
    if (!privyResponse.success || !privyResponse.data) {
      return {
        success: false,
        message: 'Failed to fetch wallets from Privy'
      };
    }
    
    const availableWallets = privyResponse.data.allSolanaWallets;
    
    if (availableWallets.length === 0) {
      return {
        success: false,
        message: 'No Solana wallets found in Privy'
      };
    }
    
    // If a preferred address is provided, try to find it
    if (preferredAddress) {
      const matchingWallet = availableWallets.find(
        wallet => wallet.address === preferredAddress
      );
      
      if (matchingWallet) {
        return {
          success: true,
          walletId: matchingWallet.id,
          address: matchingWallet.address,
          message: 'Found matching wallet'
        };
      }
    }
    
    // Get current default from localStorage
    let currentDefault: string | null = null;
    try {
      const storedDefaults = localStorage.getItem('defaultWallets');
      if (storedDefaults) {
        const defaultWallets = JSON.parse(storedDefaults);
        currentDefault = defaultWallets.solana || null;
      }
    } catch (e) {
      console.error('Error reading defaultWallets:', e);
    }
    
    // Check if current default exists in Privy
    if (currentDefault) {
      const matchingWallet = availableWallets.find(
        wallet => wallet.address === currentDefault
      );
      
      if (matchingWallet) {
        return {
          success: true,
          walletId: matchingWallet.id,
          address: matchingWallet.address,
          message: 'Current default wallet is valid'
        };
      }
    }
    
    // If we get here, we need to update the default to the primary wallet from Privy
    const primaryWallet = availableWallets[0];
    
    // Update localStorage
    try {
      const storedDefaults = localStorage.getItem('defaultWallets') || '{}';
      const defaultWallets = JSON.parse(storedDefaults);
      defaultWallets.solana = primaryWallet.address;
      localStorage.setItem('defaultWallets', JSON.stringify(defaultWallets));
      
      console.log('Updated default wallet to:', primaryWallet.address);
    } catch (e) {
      console.error('Error updating defaultWallets:', e);
    }
    
    return {
      success: true,
      walletId: primaryWallet.id,
      address: primaryWallet.address,
      message: 'Updated to primary wallet from Privy',
      updated: true
    };
    
  } catch (error) {
    console.error('Error syncing wallet selection:', error);
    return {
      success: false,
      message: 'Error syncing wallet selection'
    };
  }
}

/**
 * Get all available wallets with their IDs
 */
export async function getAllWalletsWithIds(userId: string) {
  try {
    const privyResponse = await getPrivySolanaWalletInfo(userId, true);
    
    if (!privyResponse.success || !privyResponse.data) {
      return [];
    }
    
    return privyResponse.data.allSolanaWallets;
  } catch (error) {
    console.error('Error fetching all wallets:', error);
    return [];
  }
}