import { TokenBase, QuoteData } from '../types/trading';
import { walletAPI } from './api';
import { quotePancakeswap, quoteUniswap, quoteSushiSwap, quoteRaydium, quoteMeteora, quoteFourMeme } from './quote';
import axios from 'axios';
import { detectTokenChain, ChainType } from './chainUtils';

// Keep the API base URL logic if needed for other calls, or define it specifically for this call
function getBackendApiBaseUrl() {
    // Always use the proxy path for all environments
    return '/api';
  }

const BACKEND_API_BASE = getBackendApiBaseUrl();

// Define interface for DexScreener pair response (keep for type safety)
interface DexScreenerPair {
  chainId: string;
  dexId: string;
  url: string;
  pairAddress: string;
  baseToken: {
    address: string;
    name: string;
    symbol: string;
  };
  quoteToken: {
    address: string;
    name: string;
    symbol: string;
  };
  priceNative: string;
  priceUsd: string;
  txns: {
    h24: {
      buys: number;
      sells: number;
    }
  };
  volume: {
    h24: number;
  };
  priceChange: {
    h24: number;
  };
  liquidity: {
    usd: number;
  };
  fdv: number;
}

interface PairListResponse {
  success: boolean;
  message?: string;
  pairs?: DexScreenerPair[];
  matchedPair?: DexScreenerPair;
  availableTokens?: string[];
}

/**
 * Get a list of trading pairs by calling the backend API.
 * The backend now handles the DexScreener interaction.
 */
export const getPairList = async (
  selectedCoin: TokenBase,
  activeToken: TokenBase
): Promise<PairListResponse> => {
  try {
    // Get token addresses
    const selectedTokenAddress = selectedCoin.address || selectedCoin.tokenAddress || selectedCoin.contract;
    const activeTokenAddress = activeToken.address || activeToken.tokenAddress || activeToken.contract;

    if (!selectedTokenAddress || !activeTokenAddress) {
      console.error("Frontend getPairList: Missing token addresses");
      return {
        success: false,
        message: 'Missing token addresses',
      };
    }

    console.log(`Frontend: Calling backend get-pair-list for ${activeToken.symbol} (${activeTokenAddress}) and ${selectedCoin.symbol} (${selectedTokenAddress})`);

    // Construct the backend API URL with query parameters
    // Use the base URL determined by the function
    const backendApiUrl = `${BACKEND_API_BASE}/home/<USER>
    const params = {
        activeTokenAddress: activeTokenAddress,
        selectedTokenAddress: selectedTokenAddress
    };

    // Make the API call to the backend
    const response = await axios.get<PairListResponse>(backendApiUrl, { params });

    console.log("Frontend: Received response from backend get-pair-list:", response.data);

    // Return the data directly from the backend response
    return response.data;

  } catch (error) {
    console.error("Frontend: Error calling backend get-pair-list:", error);

    // Attempt to extract a meaningful error message from the backend response if available
    let errorMessage = 'Failed to fetch pair list from backend';
    if (axios.isAxiosError(error) && error.response?.data?.message) {
        errorMessage = error.response.data.message;
    } else if (error instanceof Error) {
        errorMessage = error.message;
    }

    return {
        success: false,
        message: errorMessage,
        // Ensure other optional fields are undefined or empty arrays on error
        pairs: undefined, 
        matchedPair: undefined,
        availableTokens: undefined
    };
  }
};

/**
 * Get quote data for a trading pair
 */
export const getQuoteDataForToken = async (baseToken: TokenBase, quoteToken: TokenBase): Promise<any> => {
  try {
    console.log(`Getting quote data for ${baseToken.symbol}/${quoteToken.symbol} pair`);
    
    if (!baseToken || !quoteToken) {
      return {
        success: false,
        message: 'Missing token information',
        data: null
      };
    }

    // This now calls the refactored getPairList which uses the backend
    const pairlistResponse = await getPairList(quoteToken, baseToken);
    
    // If no matching pair was found (check success flag from backend)
    if (!pairlistResponse.success || !pairlistResponse.matchedPair) {
       console.warn(`getQuoteDataForToken: Backend indicated no pair found or error occurred. Message: ${pairlistResponse.message}`);
      return {
        success: false,
        message: pairlistResponse.message || 'No trading pair available',
        data: null,
        availableOptions: pairlistResponse.availableTokens || [] // Still useful to return available tokens if backend provides them
      };
    }

    // Get token addresses - try all possible properties
    const baseTokenAddress = baseToken.address || baseToken.tokenAddress || baseToken.contract;
    const quoteTokenAddress = quoteToken.address || quoteToken.tokenAddress || quoteToken.contract;
    
    if (!baseTokenAddress || !quoteTokenAddress) {
       console.error("getQuoteDataForToken: Missing token addresses after successful pair check.");
      return {
        success: false,
        message: 'Missing token addresses',
        data: null
      };
    }
    
    // Get the matched pair's DEX ID and pair address from the backend response
    const dexId = pairlistResponse.matchedPair?.dexId;
    const pairAddress = pairlistResponse.matchedPair?.pairAddress;
    
    console.log(`Using dexId: ${dexId} with pair address: ${pairAddress} (from backend)`);
    
    // For now, we only support PancakeSwap
    // In the future, we can add more DEXes based on the dexId
    if (dexId && dexId.toLowerCase().includes('pancake')) {
    // Call the real PancakeSwap API through our quote utility
    const quoteAmount = "1"; // Default to 1 token
    
    console.log(`Calling quotePancakeswap for ${baseToken.symbol}/${quoteToken.symbol} with amount ${quoteAmount}`);
    console.log('Token details:', {
      tokenIn: {
        address: quoteTokenAddress, // The token you're selling (quote token)
        symbol: quoteToken.symbol,
        decimals: quoteToken.decimals
      },
      tokenOut: {
        address: baseTokenAddress, // The token you're buying (base token)
        symbol: baseToken.symbol,
        decimals: baseToken.decimals
      }
    });
    
      // Note: For PancakeSwap, tokenIn is the quoteToken (what you're selling)
      // and tokenOut is the baseToken (what you're buying)
      const response = await quotePancakeswap(quoteToken, baseToken, quoteAmount, pairAddress);
    
    if (response.success && response.data) {
      console.log('Quote data received successfully:', response.data);
      
      // Transform the data to match the expected QuoteData format
      return {
        success: true,
        data: {
          name: baseToken.name || baseToken.symbol,
          symbol: baseToken.symbol,
          price: response.data.price || response.data.executionPrice,
          executionPrice: response.data.executionPrice,
          priceImpact: response.data.priceImpact,
            pairAddress: pairAddress || response.data.pairAddress, // Prioritize pairAddress from backend
          routerAddress: response.data.routerAddress,
          path: response.data.path,
            dex: dexId || "pancakeswap",
          chain: "bsc", // Assume BSC for pancakeswap for now
          platformFee: response.data.platformFee || null,
          tokenIn: response.data.tokenIn || null,
          tokenOut: response.data.tokenOut || null,
          amountIn: response.data.amountIn || null,
            amountOut: response.data.amountOut || null,
            availableOptions: pairlistResponse.availableTokens || [] // Pass through available tokens
        }
      };
    } else {
      console.warn('Quote data fetch failed:', response.message);
      return {
        success: false,
        message: response.message || 'Failed to fetch quote data',
          data: null,
          availableOptions: pairlistResponse.availableTokens || [] // Pass through available tokens
        };
      }
    } else {
      // For other DEXes, we can add support here in the future
      return {
        success: false,
        message: `DEX ${dexId} is not supported yet`,
        data: null,
        availableOptions: pairlistResponse.availableTokens || [] // Pass through available tokens
      };
    }
  } catch (error) {
    console.error("Error fetching token data:", error);
    // Ensure consistent error response structure
    const errorMessage = axios.isAxiosError(error) && error.response?.data?.message 
                         ? error.response.data.message 
                         : (error instanceof Error ? error.message : 'Failed to fetch token data');
    return {
      success: false,
      message: errorMessage,
      data: null,
      availableOptions: [] // Default to empty array on error
    };
  }
};

/**
 * Formats a quote response into a standard format
 */
const formatQuoteResponse = (response: any, source: string): any => {
  const data = response.data;
  
  // Format based on source DEX
  if (source === 'uniswap') {
    return {
      dex: "uniswap",
      dexId: "uniswap",
      chain: "ethereum",
      price: data.executionPrice,
      executionPrice: data.executionPrice,
      priceImpact: data.priceImpact,
      path: data.route?.map((r: any) => r.path) || [],
      routerAddress: "******************************************", // Uniswap Universal Router
      platformFee: data.platformFee || null,
      tokenIn: data.tokenIn || null,
      tokenOut: data.tokenOut || null,
      amountIn: data.amountIn || null,
      amountOut: data.amountOut || null,
      quote: {
        amountOut: data.amountOut,
        outputAmount: data.amountOut,
        priceImpact: data.priceImpact
      }
    };
  } else if (source === 'sushiswap') {
    return {
      dex: "sushiswap",
      dexId: "sushiswap",
      chain: "ethereum",
      price: data.executionPrice,
      executionPrice: data.executionPrice,
      priceImpact: data.priceImpact,
      path: data.route?.path || [],
      routerAddress: data.routerAddress || "******************************************", // SushiSwap Router
      platformFee: data.platformFee || null,
      tokenIn: data.tokenIn || null,
      tokenOut: data.tokenOut || null,
      amountIn: data.amountIn || null,
      amountOut: data.amountOut || null,
      quote: {
        amountOut: data.amountOut,
        outputAmount: data.amountOut,
        priceImpact: data.priceImpact
      }
    };
  } else { // pancakeswap and others
    return {
      dex: source || "pancakeswap",
      dexId: source || "pancakeswap",
      chain: "bsc",
      price: data.price || data.executionPrice,
      executionPrice: data.executionPrice,
      priceImpact: data.priceImpact,
      path: data.path || [],
      routerAddress: data.routerAddress || "******************************************", // PancakeSwap Router
      pairAddress: data.pairAddress,
      platformFee: data.platformFee || null,
      tokenIn: data.tokenIn || null,
      tokenOut: data.tokenOut || null,
      amountIn: data.amountIn || null,
      amountOut: data.amountOut || null,
      quote: {
        amountOut: data.amountOut || data.outputAmount,
        outputAmount: data.outputAmount,
        priceImpact: data.priceImpact
      }
    };
  }
};

/**
 * Find the best quote from multiple DEX quotes based on amountOut
 */
const findBestQuote = (quotes: any[]): any => {
  if (!quotes || quotes.length === 0) {
    return null;
  }
  
  if (quotes.length === 1) {
    return quotes[0];
  }
  
  // Compare quotes based on amountOut value (higher is better)
  return quotes.reduce((best, current) => {
    const bestAmountOut = parseFloat(best.amountOut || best.quote?.amountOut || '0');
    const currentAmountOut = parseFloat(current.amountOut || current.quote?.amountOut || '0');
    
    return currentAmountOut > bestAmountOut ? current : best;
  });
};

/**
 * Helper function to get available DEXes for a chain
 */
export const getAvailableDEXesForChain = (chainType: ChainType): string[] => {
  switch (chainType) {
    case 'solana':
      return ['raydium', 'meteora', 'jupiter']; // Add other Solana DEXes as needed
    case 'bsc':
      return ['pancakeswap', 'fourmeme', 'biswap']; // Add other BSC DEXes as needed
    case 'ethereum':
      return ['uniswap', 'sushiswap']; // Add other Ethereum DEXes as needed
    case 'polygon':
      return ['quickswap', 'uniswap', 'sushiswap']; // Add other Polygon DEXes as needed
    default:
      return ['pancakeswap', 'uniswap', 'sushiswap']; // Default DEXes for fallback
  }
};

/**
 * Gets quotes from multiple DEXes asynchronously
 * @param onQuoteReceived - Callback function that gets called whenever a new quote is received
 */
export const getBestDEXQuote = async (
  baseToken: TokenBase, 
  quoteToken: TokenBase, 
  amount: string,
  onQuoteReceived?: (quote: any) => void // New callback parameter
): Promise<any> => {
  try {
    console.log(`Getting DEX quotes for ${baseToken.symbol}/${quoteToken.symbol} with amount ${amount}`);
    
    if (!baseToken || !quoteToken || !amount) {
      return {
        success: false,
        message: 'Missing parameters for quote',
        data: null
      };
    }
    
    // Detect which chain these tokens are on
    const baseTokenChain = detectTokenChain(baseToken);
    const quoteTokenChain = detectTokenChain(quoteToken);
    
    // If chains don't match, log a warning but proceed with base token's chain
    if (baseTokenChain !== quoteTokenChain && baseTokenChain !== 'unknown' && quoteTokenChain !== 'unknown') {
      console.warn(`Token chains don't match: ${baseToken.symbol} on ${baseTokenChain}, ${quoteToken.symbol} on ${quoteTokenChain}. Using ${baseTokenChain}.`);
    }
    
    // Use the base token's chain, or quote token's chain if base is unknown
    let chain = baseTokenChain !== 'unknown' ? baseTokenChain : quoteTokenChain;
    console.log(`Initially detected chain: ${chain} for pair ${baseToken.symbol}/${quoteToken.symbol}`);
    
    // Get a list of pairs from the pair list API
    const pairListResponse = await getPairList(baseToken, quoteToken); 
    console.log('Got pair list response from backend:', pairListResponse.success);
    
    // Map of DEX ID to pair info, for direct DEX-specific routing
    const pairsByDex = new Map<string, DexScreenerPair>();
    
    // Set of available DEXes (those with liquidity for this pair)
    const availableDEXes = new Set<string>();
    
    // First, check which DEXes are available for the chain
    if (chain === 'bsc') {
      // BSC Defaults
      availableDEXes.add('pancakeswap');
      availableDEXes.add('fourmeme');
    } else if (chain === 'ethereum') {
      // Ethereum Defaults
      availableDEXes.add('uniswap');
      availableDEXes.add('sushiswap');
    } else if (chain === 'solana') {
      // Solana Defaults
      availableDEXes.add('raydium');
      availableDEXes.add('meteora');
      availableDEXes.add('jupiter');
    }
    
    // Extract unique DEXes that have liquidity for this pair
    if (pairListResponse.pairs && pairListResponse.pairs.length > 0) {
      console.log('Pairs found in pair list response:');
      pairListResponse.pairs.forEach(pair => {
        if (pair.dexId) {
          const dexId = pair.dexId.toLowerCase();
          console.log(`- ${dexId} (${pair.baseToken?.symbol}/${pair.quoteToken?.symbol})`);
          availableDEXes.add(dexId);
          pairsByDex.set(dexId, pair);
        }
      });
    } else {
      console.log('No pairs found in pairListResponse, using default DEXes for chain');
    }
    
    console.log(`Available DEXes with liquidity: ${Array.from(availableDEXes).join(', ')}`);
    
    const quotes: any[] = [];
    let currentBestQuote: any = null;
    const quoteErrors: string[] = [];
    
    // Helper function to add a quote and notify via callback
    const addQuote = (quote: any, dexId: string) => {
      const formattedQuote = formatQuoteResponse(quote, dexId);
      quotes.push(formattedQuote);
      
      // Find the best quote so far
      if (!currentBestQuote) {
        currentBestQuote = formattedQuote;
      } else {
        const bestAmountOut = parseFloat(currentBestQuote.amountOut || currentBestQuote.quote?.amountOut || '0');
        const newAmountOut = parseFloat(formattedQuote.amountOut || formattedQuote.quote?.amountOut || '0');
        
        if (newAmountOut > bestAmountOut) {
          currentBestQuote = formattedQuote;
        }
      }
      
      // Notify via callback if provided
      if (onQuoteReceived) {
        onQuoteReceived({
          success: true,
          data: quotes,
          bestQuote: currentBestQuote,
          allQuotes: quotes,
          platformFee: currentBestQuote.platformFee || null
        });
      }
    };
    
    // Create an array of quote fetching functions
    const quoteFetchers = [];
    
    // Check for BSC chain and add supported BSC DEXes
    if (availableDEXes.has('pancakeswap')) {
      const pancakePair = pairsByDex.get('pancakeswap');
      console.log(`Fetching PancakeSwap quote using pair: ${pancakePair?.pairAddress || 'default routing'}`);
      
      quoteFetchers.push(async () => {
        try {
          const response = await quotePancakeswap(baseToken, quoteToken, amount, pancakePair?.pairAddress);
          if (response.success && response.data) {
            addQuote(response, 'pancakeswap');
          } else if (response.message) {
            quoteErrors.push(`PancakeSwap: ${response.message}`);
          }
        } catch (error) {
          console.error('Error fetching PancakeSwap quote:', error);
          quoteErrors.push(`PancakeSwap: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });
    }
    
    // Try other BSC DEXes if chain is BSC
    if (chain === 'bsc') {
      // Try FourMeme for BSC
      if (availableDEXes.has('fourmeme')) {
        console.log('Fetching FourMeme quote...');
        
        quoteFetchers.push(async () => {
          try {
            const response = await quoteFourMeme(quoteToken, baseToken, amount);
            if (response.success && response.data) {
              addQuote(response, 'fourmeme');
            } else if (response.message) {
              quoteErrors.push(`FourMeme: ${response.message}`);
            }
          } catch (error) {
            console.error('Error fetching FourMeme quote:', error);
            quoteErrors.push(`FourMeme: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        });
      }
    }
    
    // Uniswap (supports Ethereum chain)
    // Check for Ethereum chain and add Ethereum DEXes
    if (chain === 'ethereum') {
      // Try Uniswap for Ethereum
      if (availableDEXes.has('uniswap') || availableDEXes.has('uniswapv3')) {
        const uniswapPair = pairsByDex.get('uniswap') || pairsByDex.get('uniswapv3');
        console.log(`Fetching Uniswap quote using pair: ${uniswapPair?.pairAddress || 'default routing'}`);
        
        quoteFetchers.push(async () => {
          try {
            const response = await quoteUniswap(baseToken, quoteToken, amount);
            if (response.success && response.data) {
              addQuote(response, 'uniswap');
            } else if (response.message) {
              quoteErrors.push(`Uniswap: ${response.message}`);
            }
          } catch (error) {
            console.error('Error fetching Uniswap quote:', error);
            quoteErrors.push(`Uniswap: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        });
      }
      
      // Try SushiSwap for Ethereum
      if (availableDEXes.has('sushiswap')) {
        const sushiPair = pairsByDex.get('sushiswap') || pairsByDex.get('sushi');
        console.log(`Fetching SushiSwap quote using pair: ${sushiPair?.pairAddress || 'default routing'}`);
        
        quoteFetchers.push(async () => {
          try {
            const response = await quoteSushiSwap(baseToken, quoteToken, amount);
            if (response.success && response.data) {
              addQuote(response, 'sushiswap');
            } else if (response.message) {
              quoteErrors.push(`SushiSwap: ${response.message}`);
            }
          } catch (error) {
            console.error('Error fetching SushiSwap quote:', error);
            quoteErrors.push(`SushiSwap: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        });
      }
    }
    
    // Check for Solana chain and add Solana DEXes
    if (chain === 'solana') {
      // Try Raydium for Solana
      if (availableDEXes.has('raydium')) {
        console.log('Fetching Raydium quote...');
        
        quoteFetchers.push(async () => {
          try {
            const response = await quoteRaydium(quoteToken, baseToken, amount);
            if (response.success && response.data) {
              addQuote(response, 'raydium');
            } else if (response.message) {
              quoteErrors.push(`Raydium: ${response.message}`);
            }
          } catch (error) {
            console.error('Error fetching Raydium quote:', error);
            quoteErrors.push(`Raydium: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        });
      }
      
      // Try Meteora for Solana
      if (availableDEXes.has('meteora')) {
        console.log('Fetching Meteora quote...');
        
        quoteFetchers.push(async () => {
          try {
            const response = await quoteMeteora(quoteToken, baseToken, amount);
            if (response.success && response.data) {
              addQuote(response, 'meteora');
            } else if (response.message) {
              quoteErrors.push(`Meteora: ${response.message}`);
            }
          } catch (error) {
            console.error('Error fetching Meteora quote:', error);
            quoteErrors.push(`Meteora: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        });
      }
      
      // Try Jupiter for Solana (optional, as it's a DEX aggregator)
      if (availableDEXes.has('jupiter')) {
        console.log('Jupiter DEX support is not yet implemented');
      }
    }
    
    // Execute all quote fetchers in parallel, but don't wait for them all to complete
    // instead, let each one call the callback as it completes
    quoteFetchers.forEach(fetcher => fetcher());
    
    // Wait for all quote fetchers to complete to return the final result
    await Promise.all(quoteFetchers.map(fetcher => fetcher()));
    
    // If no quotes are available, return error
    if (quotes.length === 0) {
      console.warn('No quotes available from any DEX');
      return {
        success: false,
        message: quoteErrors.length > 0 
          ? `Failed to get quotes: ${quoteErrors.join(', ')}` 
          : 'Failed to get quotes from any DEX',
        data: null,
        availableTokens: pairListResponse.availableTokens || []
      };
    }
    
    // Find the best quote from all available quotes
    const bestQuote = findBestQuote(quotes);
    console.log(`Best quote from ${bestQuote.dex} with amount out ${bestQuote.amountOut || bestQuote.quote?.amountOut}`);
    
    // Return all quotes and the best one
    return {
      success: true,
      data: quotes,
      bestQuote: bestQuote,
      allQuotes: quotes,  // Include all quotes for UI display
      platformFee: bestQuote.platformFee || null
    };
  } catch (error) {
    console.error("Error fetching quotes:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to fetch quotes',
      data: null
    };
  }
};