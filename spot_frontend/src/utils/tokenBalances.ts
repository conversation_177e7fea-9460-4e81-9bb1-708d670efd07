/**
 * Token balance utility functions
 */

export interface TokenBalance {
  symbol: string;
  name: string;
  balance: string;
  decimals: number;
  mintAddress?: string;     // For Solana tokens
  logo?: string;
}


/**
 * Get real token balances for a given Solana address
 * Note: This is now handled directly in the Navbar component using getSolanaTokenBalances API
 * 
 * @param address - Solana wallet address
 * @returns Promise<TokenBalance[]>
 */
export const getTokenBalances = async (
  address: string
): Promise<TokenBalance[]> => {
  if (!address) {
    console.error('No address provided to getTokenBalances');
    return [];
  }
  
  console.log(`For Solana wallets, use the getSolanaTokenBalances API directly in Navbar component`);
  // The Solana token balances are handled directly in the Navbar component
  // using the getSolanaTokenBalances function from getSolToken.ts
  return [];
};

/**
 * Format token balance with appropriate decimals
 * 
 * @param balance - Raw balance string
 * @param decimals - Token decimals
 * @returns Formatted balance string
 */
export const formatTokenBalance = (balance: string, _decimals: number): string => {
  if (!balance) return '0';
  
  try {
    // For mock data, we're assuming the balance is already in human-readable format
    // so we don't need to apply decimals conversion
    const value = parseFloat(balance);
    
    // Format based on size
    if (value < 0.0001 && value > 0) {
      return '< 0.0001';
    } else if (value > 1000000) {
      return `${(value / 1000000).toFixed(2)}M`;
    } else if (value > 1000) {
      return `${(value / 1000).toFixed(2)}K`;
    } else {
      return value.toFixed(4);
    }
  } catch (error) {
    console.error('Error formatting token balance:', error);
    return '0';
  }
};


