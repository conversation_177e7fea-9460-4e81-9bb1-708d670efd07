import axios from 'axios';
import { TokenBase, SwapResponse } from '../types/trading';
import { detectTokenChain } from './chainUtils';
import { execSolanaSwap } from './swap';
import { quoteRaydium } from './quote';

// Make sure we use the same TokenBase and SwapResponse types everywhere
type LocalTokenBase = TokenBase;
type LocalSwapResponse = SwapResponse;

// Always use the proxy path for all environments
function getLiquidityApiBaseUrl() {
  return '/liquidity-api';
}

// Define API base URL
const API_BASE_URL = getLiquidityApiBaseUrl();

/**
 * Function to prepare a swap transaction on Raydium (Solana)
 */
export const execRaydium = async (
  tokenIn: LocalTokenBase, 
  tokenOut: LocalTokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5
): Promise<LocalSwapResponse> => {
  try {
    console.log(`Preparing Raydium swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}`);
    
    // Validate requirements
    if (!tokenIn.address || !tokenOut.address) {
      console.error('Missing token address(es) for Raydium swap');
      return {
        success: false,
        message: 'Missing token address(es)',
        data: null
      };
    }
    
    // Verify this is for Solana chain
    const tokenChain = detectTokenChain(tokenIn);
    if (tokenChain !== 'solana') {
      console.error('Raydium is only available for Solana tokens');
      return {
        success: false,
        message: 'Raydium is only available for Solana tokens',
        data: null
      };
    }
    
    // Normalize amount to a decimal string
    const normalizedAmount = amount.replace(/,/g, '');
    
    // Prepare API request
    const requestBody = {
      tokenIn: {
        address: tokenIn.address,
        decimals: tokenIn.decimals || 9,
        symbol: tokenIn.symbol || 'Unknown',
        name: tokenIn.name || tokenIn.symbol || 'Unknown Token'
      },
      tokenOut: {
        address: tokenOut.address,
        decimals: tokenOut.decimals || 9,
        symbol: tokenOut.symbol || 'Unknown',
        name: tokenOut.name || tokenOut.symbol || 'Unknown Token'
      },
      amount: normalizedAmount,
      recipient: recipient, // Recipient Solana address
      slippageTolerance: slippageTolerance
    };
    
    // Make API call to prepare the swap
    const response = await axios.post(`${API_BASE_URL}/raydium/prepare-swap`, requestBody);
    
    if (response.data && response.data.success) {
      console.log('Raydium swap prepared successfully:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: 'Swap prepared successfully',
        source: 'raydium'
      };
    } else {
      console.warn('Failed to prepare Raydium swap:', response.data?.message || 'Unknown error');
      return {
        success: false,
        message: response.data?.message || 'Failed to prepare swap',
        data: null
      };
    }
  } catch (error) {
    console.error('Error preparing Raydium swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to prepare swap',
      data: null
    };
  }
};

/**
 * Complete a trade on Raydium (Solana)
 */
export const completeTradeRaydium = async (
  baseToken: LocalTokenBase, 
  quoteToken: LocalTokenBase, 
  amount: string, 
  recipient: string, 
  privySendTransaction: (tx: any) => Promise<{ hash: string }>,
  options: Record<string, any> = {}
): Promise<LocalSwapResponse> => {
  console.log(`Completing Raydium trade: ${amount} ${quoteToken.symbol} for ${baseToken.symbol}`);
  try {
    // First, get a quote to validate the swap is possible
    const quoteResponse = await quoteRaydium(quoteToken, baseToken, amount);
    
    if (!quoteResponse.success) {
      return {
        success: false,
        message: `Failed to get Raydium quote: ${quoteResponse.message}`,
        data: null
      };
    }
    
    // Prepare the swap transaction
    const preparedSwapResponse = await execRaydium(
      quoteToken, 
      baseToken, 
      amount,
      recipient,
      options.slippageTolerance || 0.5
    );
    
    if (!preparedSwapResponse.success || !preparedSwapResponse.data) {
      return {
        success: false,
        message: `Failed to prepare Raydium swap: ${preparedSwapResponse.message}`,
        data: null
      };
    }
    
    // Add metadata for transaction recording
    const preparedSwapData = {
      ...preparedSwapResponse.data,
      tokenIn: quoteToken,
      tokenOut: baseToken,
      amountIn: amount,
      dex: 'raydium'
    };
    
    // Execute the Solana swap (different from EVM swaps)
    return await execSolanaSwap(preparedSwapData, privySendTransaction);
  } catch (error) {
    console.error('Error completing Raydium trade:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to complete Raydium trade',
      data: null
    };
  }
};
