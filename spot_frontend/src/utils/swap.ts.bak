import axios from 'axios';
import { quotePancakeswap, quoteRaydium, quoteMeteora, quoteFourMeme } from './quote';
import { ethers } from 'ethers';
import { useSendTransaction } from "@privy-io/react-auth";
import { detectToken<PERSON>hain } from './chainUtils';

// Import Solana dependencies
import { PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';

// Add window.privy type declaration
declare global {
  interface Window {
    ethereum?: any;
    privy?: {
      recordTransaction?: (txData: {
        hash: string;
        description: string;
        metadata?: any;
      }) => Promise<void>;
    };
  }
}

// Define token interfaces
interface TokenBase {
  address?: string;
  tokenAddress?: string;
  contract?: string;
  decimals?: number;
  symbol?: string;
  name?: string;
  chainId?: number | string;
  network?: string;
}

// Export the interface so it can be imported
export interface SwapResponse {
  success: boolean;
  data?: any;
  message?: string;
  source?: string;
  error?: any;
  needsAllowance?: boolean;
}

interface WalletBase {
  address?: string;
  connected?: boolean;
  getAddress?: () => string;
  sendTransaction?: (tx: any) => Promise<any>;
  provider?: any;
  ethersProvider?: any;
  embeddedWallet?: any;
  walletClient?: any;
  walletClientType?: string;
  send?: (method: string, params: any[]) => Promise<any>;
  // Add Privy transaction recording capability
  recordTransaction?: (txData: {
    hash: string;
    description: string;
    metadata?: any;
  }) => Promise<void>;
}

interface PreparedSwap {
  to: string;
  data: string;
  value?: string;
  gasLimit?: string;
  // Add fields for Privy transaction tracking
  tokenIn?: {
    symbol?: string;
    address?: string;
  };
  tokenOut?: {
    symbol?: string;
    address?: string;
  };
  amountIn?: string;
  amountOut?: string;
  dex?: string;
}

// Token contracts mapping for common tokens on different networks
const TOKEN_CONTRACTS: Record<string, Record<string, string>> = {
  ethereum: {
    'WETH': '******************************************',
    'USDC': '******************************************',
    'USDT': '******************************************'
  },
  bsc: {
    'WBNB': '******************************************',
    'BUSD': '******************************************',
    'USDT': '******************************************'
  },
  // Add other networks as needed
};

// Function to get network name from chain ID
const getNetworkName = (chainId: number | string): string => {
  if (!chainId) return 'bsc'; // Default to BSC
  
  const chainIdNum = typeof chainId === 'string' ? parseInt(chainId, 10) : chainId;
  
  switch (chainIdNum) {
    case 1:
      return 'ethereum';
    case 56:
      return 'bsc';
    case 137:
      return 'polygon';
    case 43114:
      return 'avalanche';
    default:
      return 'bsc'; // Default to BSC
  }
};

// Function to normalize token addresses across different formats
export const normalizeTokenAddress = (token: TokenBase | string, chainId?: number | string): string | null => {
  // Handle the case when token is a string (just an address)
  if (typeof token === 'string') {
    return token;
  }
  
  // If token is null or undefined, return null
  if (!token) {
    return null;
  }
  
  // Check for address in different property names
  let address = token.address || token.tokenAddress || token.contract || null;
  
  // Handle native token case
  if (!address && token.symbol) {
    const symbol = token.symbol.toUpperCase();
    const network = getNetworkName(chainId || 0);
    
    // Native token detection based on symbol and network
    if ((symbol === 'ETH' && network === 'ethereum') ||
        (symbol === 'BNB' && network === 'bsc') ||
        (symbol === 'MATIC' && network === 'polygon') ||
        (symbol === 'SOL' && network === 'solana')) {
      return 'native';
    }
    
    // Get address from our token contracts mapping
    if (TOKEN_CONTRACTS[network] && TOKEN_CONTRACTS[network][symbol]) {
      return TOKEN_CONTRACTS[network][symbol];
    }
  }
  
  // If we found an address, return it
  return address;
};

// Dynamic API base URL based on the current hostname
function getLiquidityApiBaseUrl() {
  // Try to use environment variable first
  if (import.meta.env.VITE_API_LIQUIDITY_URL) {
    const apiUrl = import.meta.env.VITE_API_LIQUIDITY_URL;
    console.log("Using liquid API URL from environment:", apiUrl);
    return apiUrl;
  }
  
  // Fallback to configured endpoints based on hostname
  const hostname = window.location.hostname;
  
  // Localhost should use the local dev instance
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'http://localhost:3047/api';
  }
  
  if (hostname.includes('crypfi.io')) {
    return 'https://redfyn.crypfi.io/liquidity-api/api';
  }
  
  // Default production URL
  return 'https://dev.crypfi.io/liquidity-api/api';
}

// Define API base URL for the liquidity pool
const API_BASE_URL = getLiquidityApiBaseUrl();

// Update execPancakeswap to properly include platform fees
export const execPancakeswap = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5,
  platformFee?: {
    recipient: string;
    amount: string;
    token: string;
    percentage: number;
  } | null
): Promise<SwapResponse> => {
  try {
    console.log(`Preparing PancakeSwap swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}, slippage: ${slippageTolerance}%, with platform fee: ${platformFee ? 'yes' : 'no'}`);
    
    // Validate requirements
    if (!tokenIn.address && !tokenIn.tokenAddress) {
      console.error('Missing input token address for PancakeSwap swap');
      return {
        success: false,
        message: 'Missing input token address',
        data: null
      };
    }
    
    if (!tokenOut.address && !tokenOut.tokenAddress) {
      console.error('Missing output token address for PancakeSwap swap');
      return {
        success: false,
        message: 'Missing output token address',
        data: null
      };
    }
    
    // Format token addresses and create token objects
    const tokenInAddress = tokenIn.address || tokenIn.tokenAddress || '';
    const tokenOutAddress = tokenOut.address || tokenOut.tokenAddress || '';
    
    // Format request body for our API using the API's expected format
    const requestBody: any = {
      tokenIn: {
        address: tokenInAddress,
        decimals: tokenIn.decimals || 18,
        symbol: tokenIn.symbol || "Unknown",
        name: tokenIn.name || tokenIn.symbol || "Unknown Token"
      },
      tokenOut: {
        address: tokenOutAddress,
        decimals: tokenOut.decimals || 18,
        symbol: tokenOut.symbol || "Unknown",
        name: tokenOut.name || tokenOut.symbol || "Unknown Token"
      },
      amount: amount,
      recipient: recipient,
      slippageTolerance: slippageTolerance
    };
    
    // Only include platform fee if it's valid
    if (platformFee && 
        platformFee.recipient && 
        platformFee.amount && 
        platformFee.token && 
        typeof platformFee.percentage === 'number') {
      
      // Format the platform fee object
      requestBody.platformFee = {
        recipient: platformFee.recipient,
        amount: platformFee.amount,
        token: platformFee.token,
        percentage: platformFee.percentage
      };
      
      console.log('Including platform fee in swap request:', requestBody.platformFee);
    }
    
    console.log('PancakeSwap swap request:', requestBody);
    
    // Get API URL
    const apiUrl = getLiquidityApiBaseUrl();
    console.log('Using API URL:', apiUrl);
    
    try {
      // Make API call to prepare the swap
      const response = await axios.post(`${apiUrl}/pancakeswap/prepare-swap`, requestBody);
      
      if (response.data && response.data.success) {
        console.log('PancakeSwap swap prepared successfully:', response.data);
        
        // Add platform fee to response data if provided
        if (platformFee && response.data.data) {
          // Attach platform fee to the response data
          response.data.data.platformFee = platformFee;
          
          console.log('Added platform fee to swap data:', platformFee);
        }
        
        return {
          success: true,
          data: response.data.data,
          message: 'Swap prepared successfully',
          source: 'pancakeswap'
        };
      } else {
        console.error('Failed to prepare PancakeSwap swap:', response.data);
        return {
          success: false,
          message: response.data.message || 'Failed to prepare swap',
          source: 'pancakeswap',
          data: null
        };
      }
    } catch (apiError: any) {
      // Special handling for allowance errors
      const errorMessage = apiError instanceof Error ? apiError.message : String(apiError);
      const responseData = apiError?.response?.data;
      
      console.error(`API error (${apiError?.response?.status || 'unknown status'})`, 
        responseData || errorMessage);
      
      if (responseData?.code === 'INSUFFICIENT_ALLOWANCE' || 
          errorMessage.includes('allowance') || 
          errorMessage.includes('approve')) {
        console.log('Detected allowance error:', errorMessage);
        
        return {
          success: false,
          message: responseData?.message || 'Insufficient token allowance',
          needsAllowance: true,
          data: {
            tokenAddress: tokenInAddress,
            spenderAddress: responseData?.data?.spender || '',
            requiredAllowance: responseData?.data?.requiredAllowance || amount
          }
        };
      }
      
      // Check for validation errors
      if (apiError?.response?.status === 400 && responseData?.message) {
        return {
          success: false,
          message: responseData.message,
          source: 'pancakeswap',
          data: null
        };
      }
      
      // General API error
      return {
        success: false,
        message: responseData?.message || `API error preparing swap: ${errorMessage}`,
        source: 'pancakeswap',
        data: null,
        error: apiError
      };
    }
  } catch (error) {
    console.error('Error executing PancakeSwap swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error executing swap',
      source: 'pancakeswap',
      data: null
    };
  }
};

// Function to prepare a swap transaction on Uniswap
export const execUniswap = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5,
  deadline: number = 1800 // Default 30 minutes
): Promise<SwapResponse> => {
  try {
    console.log(`Preparing Uniswap swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}`);
    
    // Get chain ID from tokenIn or tokenOut - Uniswap is on Ethereum
    const chainId = tokenIn.chainId || tokenIn.network || tokenOut.chainId || tokenOut.network || 1; // Default to Ethereum
    
    // Normalize token addresses using our helper function
    const tokenInAddress = normalizeTokenAddress(tokenIn, chainId);
    const tokenOutAddress = normalizeTokenAddress(tokenOut, chainId);
    
    // Validate required parameters
    if (!tokenInAddress) {
      console.error("Missing or invalid tokenIn address:", tokenIn);
      return {
        success: false,
        message: "TokenIn must have a valid address",
        data: null
      };
    }
    
    if (!tokenOutAddress) {
      console.error("Missing or invalid tokenOut address:", tokenOut);
      return {
        success: false,
        message: "TokenOut must have a valid address",
        data: null
      };
    }
    
    if (!amount || parseFloat(amount) <= 0) {
      console.error("Invalid amount:", amount);
      return {
        success: false,
        message: "Amount must be a positive number",
        data: null
      };
    }
    
    if (!recipient) {
      console.error("Missing recipient address");
      return {
        success: false,
        message: "Recipient address is required",
        data: null
      };
    }
    
    // Prepare the request body
    const requestBody = {
      tokenIn: {
        address: tokenInAddress,
        decimals: tokenIn.decimals || 18,
        symbol: tokenIn.symbol || "Unknown",
        name: tokenIn.name || tokenIn.symbol || "Unknown Token"
      },
      tokenOut: {
        address: tokenOutAddress,
        decimals: tokenOut.decimals || 18,
        symbol: tokenOut.symbol || "Unknown",
        name: tokenOut.name || tokenOut.symbol || "Unknown Token"
      },
      amount: amount,
      recipient: recipient,
      slippageTolerance: slippageTolerance,
      deadline: deadline,
      protocol: "auto" // Let Uniswap choose the best protocol
    };
    
    console.log('Uniswap prepare-swap request:', requestBody);
    
    // Make the request to the Uniswap API
    const response = await axios.post(`${API_BASE_URL}/uniswap/prepare-swap`, requestBody);
    
    // Check if we got a valid response
    if (response.data && response.data.success && response.data.data) {
      console.log('Uniswap prepare-swap response:', response.data);
      
      // Extract the transaction data from the response
      const swapData = response.data.data.transactions || response.data.data.swapData;
      
      // Return the data needed for transaction signing
      return {
        success: true,
        data: {
          swapData: swapData,
          quote: response.data.data.quote,
          feeData: response.data.data.feeData || response.data.data.platformFee,
          source: 'uniswap'
        }
      };
    } else {
      console.warn('Invalid Uniswap prepare-swap response:', response.data);
      return {
        success: false,
        message: response.data?.message || 'Failed to prepare swap on Uniswap',
        data: null
      };
    }
  } catch (error: unknown) {
    console.error('Error preparing Uniswap swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to prepare swap',
      error: error instanceof Error && 'response' in error ? (error as any).response?.data : error,
      data: null
    };
  }
};

// Function to prepare a swap transaction on SushiSwap
export const execSushiSwap = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5
): Promise<SwapResponse> => {
  try {
    console.log(`Preparing SushiSwap swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}`);
    
    // Get chain ID from tokenIn or tokenOut - SushiSwap is on Ethereum primarily
    const chainId = tokenIn.chainId || tokenIn.network || tokenOut.chainId || tokenOut.network || 1; // Default to Ethereum
    
    // Normalize token addresses using our helper function
    const tokenInAddress = normalizeTokenAddress(tokenIn, chainId);
    const tokenOutAddress = normalizeTokenAddress(tokenOut, chainId);
    
    // Validate required parameters
    if (!tokenInAddress) {
      console.error("Missing or invalid tokenIn address:", tokenIn);
      return {
        success: false,
        message: "TokenIn must have a valid address",
        data: null
      };
    }
    
    if (!tokenOutAddress) {
      console.error("Missing or invalid tokenOut address:", tokenOut);
      return {
        success: false,
        message: "TokenOut must have a valid address",
        data: null
      };
    }
    
    if (!amount || parseFloat(amount) <= 0) {
      console.error("Invalid amount:", amount);
      return {
        success: false,
        message: "Amount must be a positive number",
        data: null
      };
    }
    
    if (!recipient) {
      console.error("Missing recipient address");
      return {
        success: false,
        message: "Recipient address is required",
        data: null
      };
    }
    
    // Prepare the request body
    const requestBody = {
      tokenIn: {
        address: tokenInAddress,
        decimals: tokenIn.decimals || 18,
        symbol: tokenIn.symbol || "Unknown",
        name: tokenIn.name || tokenIn.symbol || "Unknown Token"
      },
      tokenOut: {
        address: tokenOutAddress,
        decimals: tokenOut.decimals || 18,
        symbol: tokenOut.symbol || "Unknown",
        name: tokenOut.name || tokenOut.symbol || "Unknown Token"
      },
      amount: amount,
      recipient: recipient,
      slippageTolerance: slippageTolerance
    };
    
    console.log('SushiSwap prepare-swap request:', requestBody);
    
    // Make the request to the SushiSwap API
    const response = await axios.post(`${API_BASE_URL}/sushiswap/prepare-swap`, requestBody);
    
    // Check if we got a valid response
    if (response.data && response.data.success && response.data.data) {
      console.log('SushiSwap prepare-swap response:', response.data);
      
      // Extract the transaction data from the response
      const swapData = response.data.data.swapData;
      
      // Return the data needed for transaction signing
      return {
        success: true,
        data: {
          swapData: swapData,
          quote: response.data.data.quote,
          approvalData: response.data.data.approvalData,
          feeData: response.data.data.feeData || response.data.data.fee,
          source: 'sushiswap'
        }
      };
    } else {
      console.warn('Invalid SushiSwap prepare-swap response:', response.data);
      return {
        success: false,
        message: response.data?.message || 'Failed to prepare swap on SushiSwap',
        data: null
      };
    }
  } catch (error: unknown) {
    console.error('Error preparing SushiSwap swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to prepare swap',
      error: error instanceof Error && 'response' in error ? (error as any).response?.data : error,
      data: null
    };
  }
};

// Modify execSwap to accept the sendTransaction function from the hook
export const execSwap = async (
    preparedSwap: PreparedSwap, 
    privySendTransaction: (tx: any) => Promise<{ hash: string }>, // Accept the hook function
    // Remove wallet parameter: wallet: WalletBase 
    options: { chainId?: number | string } = {} // Add options for chainId if needed
): Promise<SwapResponse> => {
  console.log("Executing swap with prepared data:", preparedSwap);

  if (!preparedSwap || !preparedSwap.to || !preparedSwap.data) {
    console.error("Invalid preparedSwap data");
    return { success: false, message: "Invalid swap data provided." };
  }

  // Ensure privySendTransaction is a function
  if (typeof privySendTransaction !== 'function') {
      console.error("privySendTransaction function is not valid.");
      return { success: false, message: "Wallet transaction function unavailable." };
  }

  try {
    // Construct the transaction object needed by Privy's sendTransaction hook
    const tx = {
        to: preparedSwap.to,
        data: preparedSwap.data,
        value: preparedSwap.value || '0x0', // Ensure value is hex string or 0x0
        gasLimit: preparedSwap.gasLimit, // Optional: Use if provided
        chainId: options.chainId ? 
                 (typeof options.chainId === 'string' ? parseInt(options.chainId, 10) : options.chainId) 
                 : undefined
    };

    // Remove undefined fields
    if (tx.gasLimit === undefined) delete tx.gasLimit;
    if (tx.chainId === undefined) {
        console.warn("ChainId missing in execSwap options, sending without it.");
        delete tx.chainId; 
        // Note: Privy's hook might require chainId. Consider adding it based on preparedSwap if possible.
    }

    console.log("Sending swap transaction with params:", tx);

    // Use the passed-in sendTransaction function from the hook
    const txResponse = await privySendTransaction(tx as any);
    
    // Handle both formats: either txResponse is the hash string itself or an object with hash property
    let txHash: string | undefined;
    
    // Type guards for transaction response handling
    if (typeof txResponse === 'string') {
        txHash = txResponse; // Direct string hash
        console.log("Received direct hash from transaction:", txHash);
    } else if (txResponse && typeof txResponse === 'object') {
        const hash = (txResponse as any).hash;
        if (typeof hash === 'string') {
            txHash = hash;
            console.log("Received object with hash from transaction:", hash);
        } else {
            console.log("Received object without valid hash property");
        }
    } else {
        console.log("Received unexpected response type:", typeof txResponse);
    }
    
    if (!txHash) {
        console.error("Transaction sent but no valid hash received:", txResponse);
        throw new Error("Transaction sent, but hash was missing.");
    }
    
    console.log("Transaction sent, hash:", txHash);
    
    // Record the transaction for tracking if window.privy is available
    try {
      if (window.privy?.recordTransaction) {
        await window.privy.recordTransaction({
          hash: txHash,
          description: `Swap ${preparedSwap.amountIn} ${preparedSwap.tokenIn?.symbol} for ${preparedSwap.tokenOut?.symbol} on ${preparedSwap.dex}`,
          metadata: {
            tokenIn: preparedSwap.tokenIn,
            tokenOut: preparedSwap.tokenOut,
            amountIn: preparedSwap.amountIn,
            dex: preparedSwap.dex
          }
        });
      }
    } catch (recordError) {
      console.warn('Failed to record transaction:', recordError);
      // Continue as the swap was successful
    }
    
    // Return success with transaction details
    return {
      success: true,
      message: "Transaction submitted successfully",
      data: { transactionHash: txHash },
      source: preparedSwap.dex || 'unknown'
    };

  } catch (error: unknown) {
    console.error("Error sending swap transaction via Privy:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown transaction error";
    
    // Check for user rejection
    if (errorMessage.includes('rejected') || errorMessage.includes('denied')) {
      return { success: false, message: "Transaction rejected by user." };
    }
    
    // Return error details
    return {
      success: false,
      message: errorMessage,
      error: error,
      source: preparedSwap.dex || 'unknown'
    };
  }
};

// Add the TradeResponse interface at the top of the file with other interfaces 
export interface TradeResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
  needsAllowance?: boolean;
}

// Update the completeTradePancakeswap function to properly handle the smartWalletClient
export async function completeTradePancakeswap(
  tokenIn: any,
  tokenOut: any,
  amount: string | number,
  recipient: string,
  sendTransaction: (tx: any) => Promise<string | { hash: string }>,
  options: {
    slippageTolerance?: number,
    pairAddress?: string,
    gasLimit?: number,
    useSmartWalletClient?: boolean,
    smartWalletClient?: any,
    platformFee?: {
      recipient: string;
      amount: string;
      token: string;
      percentage: number;
    } | null;
  } = {}
): Promise<TradeResponse> {
  try {
    // Default options
    const slippageTolerance = options.slippageTolerance || 0.5;
    console.log(`completeTradePancakeswap: Using smart wallet client: ${options.useSmartWalletClient}`);
    
    // Check if we should use the smart wallet client for this transaction
    const actualSendTransaction = options.useSmartWalletClient && options.smartWalletClient && 
                                 typeof options.smartWalletClient.sendTransaction === 'function' 
                                    ? options.smartWalletClient.sendTransaction 
                                    : sendTransaction;
    
    console.log('Transaction will be executed using:', 
      options.useSmartWalletClient ? 'Smart Wallet Client' : 'Regular Wallet');
      
    // Prepare the swap transaction first
    const swapResponse = await execPancakeswap(
      tokenIn, 
      tokenOut, 
      amount.toString(), 
      recipient,
      slippageTolerance,
      options.platformFee
    );
    
    if (!swapResponse.success) {
      return {
        success: false,
        message: swapResponse.message || 'Failed to prepare swap transaction',
        needsAllowance: swapResponse.needsAllowance
      };
    }
    
    console.log('Swap prepared successfully, transaction:', swapResponse.data);
    
    // The swap transaction object that we'll send to the wallet
    const transaction = swapResponse.data?.transaction;
    
    if (!transaction) {
      return {
        success: false,
        message: 'No transaction data returned from swap preparation',
        needsAllowance: swapResponse.needsAllowance
      };
    }
    
    // Change to
    console.log('Swap prepared successfully, transaction:', swapResponse.data);
    
    // The swap transaction object from the response
    // Use the entire swapResponse.data as the transaction since it should already contain to, data, value fields
    const transaction = {
      to: swapResponse.data.to,
      data: swapResponse.data.data,
      value: swapResponse.data.value || '0',
      gasLimit: options.gasLimit || undefined
    };
    
    if (!transaction.to || !transaction.data) {
      console.error('Missing required transaction fields:', transaction);
      return {
        success: false,
        message: 'Missing required transaction data (to/data fields)',
        needsAllowance: swapResponse.needsAllowance
      };
    }
    
    // Send the transaction using the appropriate function based on whether we're using smart wallet
    const txResponse = await actualSendTransaction(transaction);
    console.log('Transaction sent, response:', txResponse);
    
    if (!txResponse || (!txResponse.hash && !txResponse.txHash && !txResponse.transactionHash)) {
      return {
        success: false,
        message: 'Transaction was sent but no transaction hash was returned',
        needsAllowance: false
      };
    }
    
    // Extract the transaction hash from various possible formats
    const transactionHash = txResponse.hash || txResponse.txHash || txResponse.transactionHash;
    
    return {
      success: true,
      message: 'Transaction sent successfully',
      data: {
        transaction: txResponse,
        transactionHash: transactionHash,
        tokenIn,
        tokenOut,
        amountIn: amount
      },
      needsAllowance: false
    };
  } catch (error) {
    console.error('Error completing PancakeSwap trade:', error);
    
    // Check for allowance-related errors
    const errorString = error instanceof Error ? error.message : String(error);
    const isAllowanceError = /allowance|approve|insufficient allowance/i.test(errorString);
    
    return {
      success: false,
      message: `PancakeSwap trade error: ${error instanceof Error ? error.message : String(error)}`,
      needsAllowance: isAllowanceError
    };
  }
}

/**
 * Complete a trade on Uniswap
 */
export const completeTradeUniswap = async (
  baseToken: TokenBase, 
  quoteToken: TokenBase, 
  amount: string, 
  recipient: string, 
  privySendTransaction: (tx: any) => Promise<{ hash: string }>, // Accept hook function
  // Remove wallet: WalletBase, 
  options: Record<string, any> = {}
): Promise<SwapResponse> => {
   console.log(`Completing Uniswap trade: ${amount} ${quoteToken.symbol} for ${baseToken.symbol}`);
   try {
    // Prepare the swap using execUniswap
    const preparedSwapResponse = await execUniswap(
      quoteToken, 
      baseToken,  
      amount,
      recipient,
      options.slippageTolerance || 0.5,
      options.deadline
    );
    if (!preparedSwapResponse.success || !preparedSwapResponse.data) {
      return { success: false, message: `Failed to prepare Uniswap swap: ${preparedSwapResponse.message}` };
    }

    const preparedSwapData: PreparedSwap = { 
        ...preparedSwapResponse.data, 
        tokenIn: quoteToken,
        tokenOut: baseToken,
        amountIn: amount, 
        dex: 'uniswap' 
    };
    if (options.gasLimit) preparedSwapData.gasLimit = options.gasLimit.toString();
    const chainId = baseToken.chainId || quoteToken.chainId || 1; // Default to Ethereum

    // Execute the swap using privySendTransaction
    const tx = {
        to: preparedSwapData.to,
        data: preparedSwapData.data,
        value: preparedSwapData.value || '0x0',
        gasLimit: preparedSwapData.gasLimit,
        chainId: chainId
    };

    const txResponse = await privySendTransaction(tx);

    // Handle both formats: either txResponse is the hash string itself or an object with hash property
    let txHash: string | undefined;
    
    // Type guards for transaction response handling
    if (typeof txResponse === 'string') {
        txHash = txResponse; // Direct string hash
        console.log("Received direct hash from transaction:", txHash);
    } else if (txResponse && typeof txResponse === 'object') {
        const hash = (txResponse as any).hash;
        if (typeof hash === 'string') {
            txHash = hash;
            console.log("Received object with hash from transaction:", hash);
        } else {
            console.log("Received object without valid hash property");
        }
    } else {
        console.log("Received unexpected response type:", typeof txResponse);
    }
    
    if (!txHash) {
        console.error("Transaction sent but no valid hash received:", txResponse);
        throw new Error("Transaction sent, but hash was missing.");
    }
    
    console.log("Transaction sent, hash:", txHash);
    
    // Record the transaction for tracking if window.privy is available
    try {
      if (window.privy?.recordTransaction) {
        await window.privy.recordTransaction({
          hash: txHash,
          description: `Swap ${amount} ${quoteToken.symbol} for ${baseToken.symbol} on Uniswap`,
          metadata: {
            tokenIn: quoteToken,
            tokenOut: baseToken,
            amountIn: amount,
            dex: 'uniswap'
          }
        });
      }
    } catch (recordError) {
      console.warn('Failed to record transaction:', recordError);
      // Continue as the swap was successful
    }
    
    // Return success with transaction details
    return {
      success: true,
      data: {
        transactionHash: txHash,
        tokenIn: preparedSwapData.tokenIn,
        tokenOut: preparedSwapData.tokenOut,
        amountIn: preparedSwapData.amountIn,
        amountOut: preparedSwapData.amountOut,
        dex: preparedSwapData.dex
      },
      message: "Transaction submitted successfully"
    };

  } catch (error: unknown) {
    console.error("Error completing Uniswap trade:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to complete Uniswap trade",
      data: null
    };
  }
};

/**
 * Complete a trade on SushiSwap
 */
export const completeTradeSushiSwap = async (
  baseToken: TokenBase, 
  quoteToken: TokenBase, 
  amount: string, 
  recipient: string, 
  privySendTransaction: (tx: any) => Promise<{ hash: string }>, // Accept hook function
  // Remove wallet: WalletBase, 
  options: Record<string, any> = {}
): Promise<SwapResponse> => {
  console.log(`Completing SushiSwap trade: ${amount} ${quoteToken.symbol} for ${baseToken.symbol}`);
   try {
    // Prepare swap using execSushiSwap
    const preparedSwapResponse = await execSushiSwap(
      quoteToken, 
      baseToken, 
      amount,
      recipient,
      options.slippageTolerance || 0.5
    );
    if (!preparedSwapResponse.success || !preparedSwapResponse.data) {
       return { success: false, message: `Failed to prepare SushiSwap swap: ${preparedSwapResponse.message}` };
    }

     const preparedSwapData: PreparedSwap = { 
        ...preparedSwapResponse.data, 
        tokenIn: quoteToken,
        tokenOut: baseToken,
        amountIn: amount,
        dex: 'sushiswap' 
    };
     if (options.gasLimit) preparedSwapData.gasLimit = options.gasLimit.toString();
     const chainId = baseToken.chainId || quoteToken.chainId || 1; // Default to Ethereum

    // Execute swap using privySendTransaction
    const tx = {
        to: preparedSwapData.to,
        data: preparedSwapData.data,
        value: preparedSwapData.value || '0x0',
        gasLimit: preparedSwapData.gasLimit,
        chainId: chainId
    };

    const txResponse = await privySendTransaction(tx);

    // Handle both formats: either txResponse is the hash string itself or an object with hash property
    let txHash: string | undefined;
    
    // Type guards for transaction response handling
    if (typeof txResponse === 'string') {
        txHash = txResponse; // Direct string hash
        console.log("Received direct hash from transaction:", txHash);
    } else if (txResponse && typeof txResponse === 'object') {
        const hash = (txResponse as any).hash;
        if (typeof hash === 'string') {
            txHash = hash;
            console.log("Received object with hash from transaction:", hash);
        } else {
            console.log("Received object without valid hash property");
        }
    } else {
        console.log("Received unexpected response type:", typeof txResponse);
    }
    
    if (!txHash) {
        console.error("Transaction sent but no valid hash received:", txResponse);
        throw new Error("Transaction sent, but hash was missing.");
    }
    
    console.log("Transaction sent, hash:", txHash);
    
    // Record the transaction for tracking if window.privy is available
    try {
      if (window.privy?.recordTransaction) {
        await window.privy.recordTransaction({
          hash: txHash,
          description: `Swap ${amount} ${quoteToken.symbol} for ${baseToken.symbol} on SushiSwap`,
          metadata: {
            tokenIn: quoteToken,
            tokenOut: baseToken,
            amountIn: amount,
            dex: 'sushiswap'
          }
        });
      }
    } catch (recordError) {
      console.warn('Failed to record transaction:', recordError);
      // Continue as the swap was successful
    }
    
    return {
      success: true,
      data: {
        transactionHash: txHash,
        tokenIn: preparedSwapData.tokenIn,
        tokenOut: preparedSwapData.tokenOut,
        amountIn: preparedSwapData.amountIn,
        amountOut: preparedSwapData.amountOut,
        dex: preparedSwapData.dex
      },
      message: "Transaction submitted successfully"
    };

  } catch (error: unknown) {
    console.error("Error completing SushiSwap trade:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to complete SushiSwap trade",
      data: null
    };
  }
}; 

/**
 * Function to prepare a swap transaction on Raydium (Solana)
 */
export const execRaydium = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5
): Promise<SwapResponse> => {
  try {
    console.log(`Preparing Raydium swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}`);
    
    // Validate requirements
    if (!tokenIn.address || !tokenOut.address) {
      console.error('Missing token address(es) for Raydium swap');
      return {
        success: false,
        message: 'Missing token address(es)',
        data: null
      };
    }
    
    // Verify this is for Solana chain
    const tokenChain = detectTokenChain(tokenIn);
    if (tokenChain !== 'solana') {
      console.error('Raydium is only available for Solana tokens');
      return {
        success: false,
        message: 'Raydium is only available for Solana tokens',
        data: null
      };
    }
    
    // Normalize amount to a decimal string
    const normalizedAmount = amount.replace(/,/g, '');
    
    // Prepare API request
    const requestBody = {
      tokenIn: {
        address: tokenIn.address,
        decimals: tokenIn.decimals || 9,
        symbol: tokenIn.symbol || 'Unknown',
        name: tokenIn.name || tokenIn.symbol || 'Unknown Token'
      },
      tokenOut: {
        address: tokenOut.address,
        decimals: tokenOut.decimals || 9,
        symbol: tokenOut.symbol || 'Unknown',
        name: tokenOut.name || tokenOut.symbol || 'Unknown Token'
      },
      amount: normalizedAmount,
      recipient: recipient, // Recipient Solana address
      slippageTolerance: slippageTolerance
    };
    
    // Make API call to prepare the swap
    const response = await axios.post(`${API_BASE_URL}/raydium/prepare-swap`, requestBody);
    
    if (response.data && response.data.success) {
      console.log('Raydium swap prepared successfully:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: 'Swap prepared successfully',
        source: 'raydium'
      };
    } else {
      console.warn('Failed to prepare Raydium swap:', response.data?.message || 'Unknown error');
      return {
        success: false,
        message: response.data?.message || 'Failed to prepare swap',
        data: null
      };
    }
  } catch (error) {
    console.error('Error preparing Raydium swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to prepare swap',
      data: null
    };
  }
};

/**
 * Execute a Solana transaction for Raydium/Meteora with Privy
 */
export const execSolanaSwap = async (
  preparedSwap: any,
  privySendTransaction: (tx: any) => Promise<{ hash: string }>,
  options: { chainId?: number | string } = {}
): Promise<SwapResponse> => {
  try {
    console.log('Executing Solana swap transaction...');
    
    // Extract transaction from the prepared swap
    const { transaction, signers = [] } = preparedSwap;
    
    // Check if we have a transaction
    if (!transaction) {
      return {
        success: false,
        message: 'Invalid transaction data',
        data: null
      };
    }
    
    // For Solana, privySendTransaction works a bit differently
    // It expects either a base64 encoded transaction or a Transaction object
    let txToSend;
    
    // We could receive different formats from backend
    if (typeof transaction === 'string') {
      // If it's a base64 string, we can send it directly
      txToSend = { transaction };
    } else if (transaction instanceof Transaction || transaction instanceof VersionedTransaction) {
      // If it's already a Transaction object
      txToSend = { transaction };
    } else if (transaction.data) {
      // If it's a transaction data object
      txToSend = { transaction: transaction.data };
    } else {
      return {
        success: false,
        message: 'Invalid transaction format',
        data: null
      };
    }
    
    // Send the transaction using Privy's sendTransaction
    const { hash } = await privySendTransaction(txToSend);
    
    // Record the transaction for tracking if available
    try {
      if (window.privy?.recordTransaction) {
        await window.privy.recordTransaction({
          hash,
          description: `Swap ${preparedSwap.amountIn} ${preparedSwap.tokenIn?.symbol} for ${preparedSwap.tokenOut?.symbol} on ${preparedSwap.dex}`,
          metadata: {
            tokenIn: preparedSwap.tokenIn,
            tokenOut: preparedSwap.tokenOut,
            amountIn: preparedSwap.amountIn,
            dex: preparedSwap.dex
          }
        });
      }
    } catch (recordError) {
      console.warn('Failed to record transaction:', recordError);
      // Continue anyway as the swap is completed
    }
    
    return {
      success: true,
      data: { hash },
      message: 'Transaction submitted successfully'
    };
  } catch (error) {
    console.error('Error executing Solana swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to execute swap',
      data: null
    };
  }
};

/**
 * Complete a trade on Raydium (Solana)
 */
export const completeTradeRaydium = async (
  baseToken: TokenBase, 
  quoteToken: TokenBase, 
  amount: string, 
  recipient: string, 
  privySendTransaction: (tx: any) => Promise<{ hash: string }>,
  options: Record<string, any> = {}
): Promise<SwapResponse> => {
  console.log(`Completing Raydium trade: ${amount} ${quoteToken.symbol} for ${baseToken.symbol}`);
  try {
    // First, get a quote to validate the swap is possible
    const quoteResponse = await quoteRaydium(quoteToken, baseToken, amount);
    
    if (!quoteResponse.success) {
      return {
        success: false,
        message: `Failed to get Raydium quote: ${quoteResponse.message}`,
        data: null
      };
    }
    
    // Prepare the swap transaction
    const preparedSwapResponse = await execRaydium(
      quoteToken, 
      baseToken, 
      amount,
      recipient,
      options.slippageTolerance || 0.5
    );
    
    if (!preparedSwapResponse.success || !preparedSwapResponse.data) {
      return {
        success: false,
        message: `Failed to prepare Raydium swap: ${preparedSwapResponse.message}`,
        data: null
      };
    }
    
    // Add metadata for transaction recording
    const preparedSwapData = {
      ...preparedSwapResponse.data,
      tokenIn: quoteToken,
      tokenOut: baseToken,
      amountIn: amount,
      dex: 'raydium'
    };
    
    // Execute the Solana swap (different from EVM swaps)
    return await execSolanaSwap(preparedSwapData, privySendTransaction);
  } catch (error) {
    console.error('Error completing Raydium trade:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to complete Raydium trade',
      data: null
    };
  }
};