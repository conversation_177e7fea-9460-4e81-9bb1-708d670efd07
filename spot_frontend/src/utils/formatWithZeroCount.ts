/**
 * Format very small numbers with zero-count notation
 * Examples:
 * 0.00008900 → 0.0₃89
 * 0.000000123 → 0.0₆123
 */
export function formatWithZeroCount(value: number): string {
  if (value === 0) return '0';
  
  const absValue = Math.abs(value);
  
  // For large numbers, use K, M, B, T suffixes
  if (absValue >= 1e12) return `${(value / 1e12).toFixed(3)}T`;
  if (absValue >= 1e9) return `${(value / 1e9).toFixed(3)}B`;
  if (absValue >= 1e6) return `${(value / 1e6).toFixed(3)}M`;
  if (absValue >= 1e3) return `${(value / 1e3).toFixed(3)}K`;
  
  // For very small values, use zero-count notation
  if (absValue < 0.01 && absValue > 0) {
    // Convert to string to count zeros after decimal
    const str = absValue.toFixed(20); // Use high precision
    const match = str.match(/^0\.0+/);
    if (match) {
      const zeroCount = match[0].length - 2; // Subtract '0.' 
      if (zeroCount >= 3) {
        // Get the significant digits after the zeros
        const significantPart = str.substring(match[0].length);
        // Take up to 4 significant digits
        let digits = significantPart.substring(0, 4).replace(/0+$/, '');
        
        // If we have more than 2 decimal places, round to 2
        if (digits.length > 2) {
          const num = parseFloat('0.' + digits);
          digits = num.toFixed(2).substring(2);
        }
        
        // Create subscript number
        const subscript = String(zeroCount).split('').map(d => 
          String.fromCharCode(0x2080 + parseInt(d))
        ).join('');
        
        return `0.0${subscript}${digits}`;
      }
    }
  }
  
  // For small values that don't need zero-count notation
  if (absValue < 0.0001) return value.toFixed(8);
  if (absValue < 0.01) return value.toFixed(6);
  if (absValue < 1) return value.toFixed(4);
  
  return value.toFixed(2);
}