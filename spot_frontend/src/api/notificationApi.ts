import axios from 'axios';

// Define notification types
export interface Notification {
  id: string;
  user_id: string;
  activity_type: string;
  token_address?: string;
  token_symbol?: string;
  amount?: number;
  price?: number;
  tx_hash?: string;
  exchange_name?: string;
  status: string;
  message: string;
  metadata?: any;
  read: boolean;
  created_at: string;
  updated_at?: string;
}

export interface NotificationResponse {
  success: boolean;
  data: {
    notifications: Notification[];
    unread_count: number;
    total_count: number;
  };
  error?: string;
}

export interface UnreadCountResponse {
  success: boolean;
  data: {
    unread_count: number;
  };
  error?: string;
}

const API_BASE_URL = '/api/notifications';

class NotificationAPI {
  /**
   * Get notifications for a user
   */
  async getNotifications(
    userId: string, 
    limit: number = 50, 
    offset: number = 0, 
    unreadOnly: boolean = false
  ): Promise<NotificationResponse> {
    try {
      const params = new URLSearchParams({
        user_id: userId,
        limit: limit.toString(),
        offset: offset.toString()
      });

      if (unreadOnly) {
        params.append('unread_only', 'true');
      }

      const response = await axios.get(`${API_BASE_URL}?${params.toString()}`, {
        timeout: 10000
      });

      return response.data;
    } catch (error: any) {
      console.error('Error fetching notifications:', error);
      return {
        success: false,
        data: {
          notifications: [],
          unread_count: 0,
          total_count: 0
        },
        error: error.response?.data?.error || error.message || 'Failed to fetch notifications'
      };
    }
  }

  /**
   * Mark a specific notification as read
   */
  async markAsRead(notificationId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await axios.put(`${API_BASE_URL}/${notificationId}/read`, {
        user_id: userId
      }, {
        timeout: 5000
      });

      return { success: response.data.success };
    } catch (error: any) {
      console.error('Error marking notification as read:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to mark notification as read'
      };
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await axios.put(`${API_BASE_URL}/mark-all-read`, {
        user_id: userId
      }, {
        timeout: 5000
      });

      return { success: response.data.success };
    } catch (error: any) {
      console.error('Error marking all notifications as read:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to mark all notifications as read'
      };
    }
  }

  /**
   * Get unread notification count for a user
   */
  async getUnreadCount(userId: string): Promise<UnreadCountResponse> {
    try {
      const response = await axios.get(`${API_BASE_URL}/unread-count?user_id=${encodeURIComponent(userId)}`, {
        timeout: 5000
      });

      return response.data;
    } catch (error: any) {
      console.error('Error fetching unread count:', error);
      return {
        success: false,
        data: {
          unread_count: 0
        },
        error: error.response?.data?.error || error.message || 'Failed to fetch unread count'
      };
    }
  }
}

export const notificationAPI = new NotificationAPI();
export default notificationAPI;