import axios from 'axios';
import { SwapResponse } from '../api/solana_api';

// Define the API URL for the trade history
const API_URL = '/api/user-trades';

export interface TradeHistoryRecord {
  user_id: string;
  wallet_address: string;
  token_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  pool_address?: string;
  trade_type: 'buy' | 'sell';
  dex_type: 'pumpfun' | 'pumpswap';
  token_amount: number;
  sol_amount: number;
  price: number;
  tx_hash: string;
  execution_status?: 'pending' | 'completed' | 'failed';
  slippage?: number;
  gas_fee?: number;
}

/**
 * Save a trade to the history database
 * @param tradeData Trade history data to save
 * @returns Success status and any error message
 */
export const saveTradeHistory = async (tradeData: TradeHistoryRecord): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log('Saving trade history:', tradeData);
    
    const response = await axios.post(API_URL, tradeData, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response.data;
  } catch (error: any) {
    console.error('Error saving trade history:', error);
    
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to save trade history'
    };
  }
};

/**
 * Format and save a trade from a swap response
 * @param swapResponse The response from a swap operation
 * @param swapDetails Additional details about the swap
 * @returns Success status and any error
 */
export const saveTradeFromSwapResponse = async (
  swapResponse: SwapResponse,
  swapDetails: {
    userId: string;
    walletAddress: string;
    direction: 'buy' | 'sell';
    amount: number;
    tokenName: string;
    tokenSymbol: string;
    tokenAddress: string;
    poolAddress: string;
    dexType: 'pumpfun' | 'pumpswap';
    slippage: number;
    tokenImage?: string;
    gasFee?: number;
  }
): Promise<{ success: boolean; error?: string }> => {
  try {
    if (!swapResponse.success || !swapResponse.data?.signature) {
      return {
        success: false,
        error: 'Invalid swap response, missing signature or success status'
      };
    }

    const { userId, direction, amount, walletAddress, tokenName, tokenSymbol, tokenAddress, 
      poolAddress, dexType, slippage, tokenImage, gasFee } = swapDetails;

    const isBuy = direction === 'buy';
    const transaction = swapResponse.data;

    const tradeRecord: TradeHistoryRecord = {
      user_id: userId,
      wallet_address: walletAddress,
      token_address: tokenAddress,
      token_name: tokenName,
      token_symbol: tokenSymbol,
      token_image: tokenImage,
      pool_address: poolAddress,
      trade_type: direction,
      dex_type: dexType,
      token_amount: isBuy ? (transaction.outAmount || 0) : amount,
      sol_amount: isBuy ? amount : (transaction.outAmount || 0),
      price: transaction.price || 0,
      tx_hash: transaction.signature || transaction.transactionHash || '',
      execution_status: 'completed',
      slippage: slippage,
      gas_fee: gasFee
    };

    return await saveTradeHistory(tradeRecord);
  } catch (error: any) {
    console.error('Error formatting and saving trade:', error);
    
    return {
      success: false,
      error: error.message || 'Failed to format and save trade history'
    };
  }
};

/**
 * Get trade history for a wallet or user
 * @param params Query parameters
 * @returns Trade history data
 */
export const getTradeHistory = async (
  params: {
    walletAddress?: string;
    userId?: string;
    tokenAddress?: string;
    tradeType?: 'buy' | 'sell';
    limit?: number;
    offset?: number;
  }
): Promise<{ success: boolean; data?: any[]; error?: string; count?: number }> => {
  try {
    const response = await axios.get(API_URL, {
      params: {
        wallet_address: params.walletAddress,
        user_id: params.userId,
        token_address: params.tokenAddress,
        trade_type: params.tradeType,
        limit: params.limit || 100,
        offset: params.offset || 0
      }
    });

    return response.data;
  } catch (error: any) {
    console.error('Error getting trade history:', error);
    
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to get trade history'
    };
  }
};

/**
 * Get trade statistics for a user or wallet
 * @param params Query parameters
 * @returns Trade statistics
 */
export const getTradeStats = async (
  params: {
    walletAddress?: string;
    userId?: string;
  }
): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const response = await axios.get(`${API_URL}/stats`, {
      params: {
        wallet_address: params.walletAddress,
        user_id: params.userId
      }
    });

    return response.data;
  } catch (error: any) {
    console.error('Error getting trade stats:', error);
    
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to get trade stats'
    };
  }
};

export default {
  saveTradeHistory,
  saveTradeFromSwapResponse,
  getTradeHistory,
  getTradeStats
};
