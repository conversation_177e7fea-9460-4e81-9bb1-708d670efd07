import { TpSlOrder } from '../Pulse_Trade/TradingPanel/NewTpSlSettings';

// New unified order creation interface
export interface CreateTpSlOrderRequest {
  user_id: string;
  wallet_id: string;
  wallet_address: string;
  current_price: number;
  amount: number;
  token_address: string;
  pool_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  exchange_name?: string;
  
  // Take Profit settings (optional)
  tp_enabled?: boolean;
  tp_target_price?: number;
  tp_percentage?: number;
  
  // Stop Loss settings (optional)
  sl_enabled?: boolean;
  sl_target_price?: number;
  sl_percentage?: number;
}

// Response interface
export interface TpSlOrderResponse {
  mainOrder: any;
  takeProfitOrder?: any;
  stopLossOrder?: any;
}

// Direct Supabase integration using backend API via proxy
class TpSlService {
  private baseUrl = '/api/simple-tpsl';

  // New unified create order method
  async createCombinedOrder(orderData: CreateTpSlOrderRequest): Promise<{ success: boolean; data?: TpSlOrderResponse; error?: string }> {
    try {
      console.log('Creating combined TP/SL order via backend API:', orderData);

      const response = await fetch(`${this.baseUrl}/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Created combined TP/SL order via backend:', result.data);
      
      return {
        success: true,
        data: result.data
      };
    } catch (error) {
      console.error('Error creating combined TP/SL order:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  // Legacy method for backward compatibility
  async createOrder(orderData: Omit<TpSlOrder, 'id' | 'created_at' | 'status'>): Promise<{ success: boolean; data?: TpSlOrder; error?: string }> {
    try {
      console.log('Creating TP/SL order via backend API:', orderData);

      const response = await fetch(`${this.baseUrl}/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...orderData,
          status: 'active'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Created TP/SL order via Supabase:', result.data);
      
      return {
        success: true,
        data: result.data
      };
    } catch (error) {
      console.error('Error creating TP/SL order:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async getUserOrders(userId: string): Promise<{ success: boolean; data?: TpSlOrder[]; error?: string }> {
    try {
      console.log('Fetching TP/SL orders for user:', userId);

      // Use POST method with user_id in payload instead of GET with URL parameter
      const response = await fetch(`${this.baseUrl}/orders/list`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
          status: 'active'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      let orders = result.data || [];

      // Check if we got the new format (TpSlOrderResponse[]) and transform it
      if (orders.length > 0 && orders[0].mainOrder) {
        console.log('Detected new TPSL format, transforming to combined format...');
        const transformedOrders: any[] = [];

        orders.forEach((orderResponse: TpSlOrderResponse) => {
          const { mainOrder, takeProfitOrder, stopLossOrder } = orderResponse;

          // Create a single combined order that includes both TP and SL info
          transformedOrders.push({
            id: mainOrder.id, // Use main order ID
            user_id: mainOrder.user_id,
            wallet_id: mainOrder.wallet_id,
            wallet_address: mainOrder.wallet_address,
            current_price: mainOrder.current_price,
            amount: mainOrder.amount,
            token_address: mainOrder.token_address,
            pool_address: mainOrder.pool_address,
            token_name: mainOrder.token_name,
            token_symbol: mainOrder.token_symbol,
            token_image: mainOrder.token_image,
            exchange_name: mainOrder.exchange_name,
            total_token: mainOrder.total_token,
            txn: mainOrder.txn,
            status: 'active',
            created_at: mainOrder.created_at || new Date().toISOString(),
            
            // Combined TP/SL information
            hasTP: !!takeProfitOrder,
            hasSL: !!stopLossOrder,
            tp_target_price: takeProfitOrder?.target_price,
            tp_percentage: takeProfitOrder?.percentage,
            tp_id: takeProfitOrder?.id,
            sl_target_price: stopLossOrder?.target_price,
            sl_percentage: stopLossOrder?.percentage,
            sl_id: stopLossOrder?.id,
            
            // For backward compatibility, use TP as primary if exists, otherwise SL
            action: takeProfitOrder ? 'take_profit' : 'stop_loss',
            target_price: takeProfitOrder?.target_price || stopLossOrder?.target_price,
            percentage: takeProfitOrder?.percentage || stopLossOrder?.percentage
          });
        });

        console.log(`Transformed ${orders.length} order responses to ${transformedOrders.length} combined orders`);
        orders = transformedOrders;
      }
      
      return {
        success: true,
        data: orders
      };
    } catch (error) {
      console.error('Error fetching TP/SL orders:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async cancelOrder(orderId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Cancelling TP/SL order:', { orderId, userId });

      const response = await fetch(`${this.baseUrl}/orders/${encodeURIComponent(orderId)}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ user_id: userId })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      console.log('TP/SL order cancelled successfully');
      return {
        success: true
      };
    } catch (error) {
      console.error('Error cancelling TP/SL order:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  // Remove individual Take Profit order
  async removeTakeProfit(orderId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Removing Take Profit order:', { orderId, userId });

      const response = await fetch(`${this.baseUrl}/orders/${encodeURIComponent(orderId)}/remove-tp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ user_id: userId })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      console.log('Take Profit order removed successfully');
      return {
        success: true
      };
    } catch (error) {
      console.error('Error removing Take Profit order:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  // Remove individual Stop Loss order
  async removeStopLoss(orderId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Removing Stop Loss order:', { orderId, userId });

      const response = await fetch(`${this.baseUrl}/orders/${encodeURIComponent(orderId)}/remove-sl`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ user_id: userId })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      console.log('Stop Loss order removed successfully');
      return {
        success: true
      };
    } catch (error) {
      console.error('Error removing Stop Loss order:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

export const tpSlService = new TpSlService();
export default tpSlService;