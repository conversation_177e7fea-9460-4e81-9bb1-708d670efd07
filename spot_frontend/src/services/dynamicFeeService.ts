// Frontend service for dynamic fee calculation API
// Use proxy route instead of direct port access
const FEE_API_BASE = '/api/fees';

export interface NetworkStats {
  averageTps: number;
  congestionMultiplier: number;
  lastUpdated: string;
}

export interface DynamicFeesResponse {
  tradeAmount: number;
  isUrgent: boolean;
  priorityFee: number; // SOL amount
  bribeAmount: number; // SOL amount
  priorityLevel: 'low' | 'medium' | 'high' | 'veryHigh';
  priorityFeeFormatted: {
    sol: number;
    lamports: number;
    microLamports: number;
  };
  bribeAmountFormatted: {
    sol: number;
    lamports: number;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Fetch current network statistics from the limit orders service
 */
export async function fetchNetworkStats(): Promise<NetworkStats | null> {
  const url = `${FEE_API_BASE}/network-stats`;
  console.log('🔌 [FEE API] Attempting to fetch network stats from:', url);
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('🔌 [FEE API] Network stats response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('🔌 [FEE API] Network stats error response:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
    }

    const result: ApiResponse<NetworkStats> = await response.json();
    console.log('🔌 [FEE API] Network stats result:', result);
    
    if (result.success && result.data) {
      return result.data;
    } else {
      console.error('🔌 [FEE API] Failed to fetch network stats:', result.error);
      return null;
    }
  } catch (error) {
    console.error('🔌 [FEE API] Error fetching network stats:', error);
    return null;
  }
}

/**
 * Calculate dynamic fees for a given trade amount and exchange type
 */
export async function calculateDynamicFees(
  amount: number, 
  isUrgent: boolean = false,
  exchangeType: string = 'default'
): Promise<DynamicFeesResponse | null> {
  const url = `${FEE_API_BASE}/calculate`;
  const requestBody = { amount, isUrgent, exchangeType };
  console.log('🔌 [FEE API] Attempting to calculate fees from:', url, 'with body:', requestBody);
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('🔌 [FEE API] Calculate fees response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('🔌 [FEE API] Calculate fees error response:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
    }

    const result: ApiResponse<DynamicFeesResponse> = await response.json();
    console.log('🔌 [FEE API] Calculate fees result:', result);
    
    if (result.success && result.data) {
      return result.data;
    } else {
      console.error('🔌 [FEE API] Failed to calculate dynamic fees:', result.error);
      return null;
    }
  } catch (error) {
    console.error('🔌 [FEE API] Error calculating dynamic fees:', error);
    return null;
  }
}

/**
 * Get fee tier description based on priority level
 */
export function getFeeDescription(priorityLevel: string): string {
  switch (priorityLevel) {
    case 'low':
      return 'Low priority - slower execution, lower cost';
    case 'medium':
      return 'Medium priority - balanced execution speed and cost';
    case 'high':
      return 'High priority - faster execution, higher cost';
    case 'veryHigh':
      return 'Very high priority - fastest execution, highest cost';
    default:
      return 'Unknown priority level';
  }
}

/**
 * Format fee amount for display
 */
export function formatFeeAmount(amount: number, decimals: number = 6): string {
  // Handle very small amounts by showing in scientific notation
  if (amount > 0 && amount < 0.000001) {
    return amount.toExponential(2);
  }
  
  // For larger amounts, reduce decimals
  if (amount >= 1) {
    return amount.toFixed(2);
  } else if (amount >= 0.01) {
    return amount.toFixed(4);
  }
  
  // Default formatting for small amounts
  return amount.toFixed(decimals);
}

/**
 * Convert SOL amount to USD (requires current SOL price)
 */
export function convertSolToUsd(solAmount: number, solPrice: number): number {
  return solAmount * solPrice;
}

/**
 * Get congestion level description
 */
export function getCongestionDescription(multiplier: number): string {
  if (multiplier >= 3) {
    return 'Very High Congestion';
  } else if (multiplier >= 2) {
    return 'High Congestion';
  } else if (multiplier >= 1.5) {
    return 'Medium Congestion';
  } else {
    return 'Low Congestion';
  }
}

/**
 * Get congestion color for UI
 */
export function getCongestionColor(multiplier: number): string {
  if (multiplier >= 3) {
    return '#ff4444'; // Red
  } else if (multiplier >= 2) {
    return '#ff8800'; // Orange
  } else if (multiplier >= 1.5) {
    return '#ffaa00'; // Yellow
  } else {
    return '#44ff44'; // Green
  }
}