import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';

interface PausedCategory {
  category: 'new' | 'bonding' | 'bonded';
  pausedAt: number;
  queuedData?: any[];
}

interface PulsePauseContextType {
  pausedCategories: Set<string>;
  queuedUpdates: Map<string, any[]>;
  pauseCategory: (category: string) => void;
  resumeCategory: (category: string) => void;
  isPaused: (category: string) => boolean;
  getQueuedUpdates: (category: string) => any[] | undefined;
  clearQueuedUpdates: (category: string) => void;
}

const PulsePauseContext = createContext<PulsePauseContextType | undefined>(undefined);

export const PulsePauseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [pausedCategories, setPausedCategories] = useState<Set<string>>(new Set());
  const queuedUpdatesRef = useRef<Map<string, any[]>>(new Map());
  const pauseTimersRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Auto-resume after 10 seconds to prevent permanent pause
  const AUTO_RESUME_DELAY = 10000;

  const pauseCategory = useCallback((category: string) => {
    console.log(`⏸️ Pausing category: ${category}`);
    setPausedCategories(prev => new Set(prev).add(category));

    // Clear any existing timer
    const existingTimer = pauseTimersRef.current.get(category);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set auto-resume timer
    const timer = setTimeout(() => {
      console.log(`⏯️ Auto-resuming category after timeout: ${category}`);
      resumeCategory(category);
    }, AUTO_RESUME_DELAY);

    pauseTimersRef.current.set(category, timer);
  }, []);

  const resumeCategory = useCallback((category: string) => {
    console.log(`▶️ Resuming category: ${category}`);
    setPausedCategories(prev => {
      const newSet = new Set(prev);
      newSet.delete(category);
      return newSet;
    });

    // Clear timer
    const timer = pauseTimersRef.current.get(category);
    if (timer) {
      clearTimeout(timer);
      pauseTimersRef.current.delete(category);
    }

    // Emit event to notify components about queued updates
    window.dispatchEvent(new CustomEvent('pulseCategoryResumed', { 
      detail: { category, queuedUpdates: queuedUpdatesRef.current.get(category) || [] } 
    }));
  }, []);

  const isPaused = useCallback((category: string) => {
    return pausedCategories.has(category);
  }, [pausedCategories]);

  const getQueuedUpdates = useCallback((category: string) => {
    return queuedUpdatesRef.current.get(category);
  }, []);

  const clearQueuedUpdates = useCallback((category: string) => {
    queuedUpdatesRef.current.delete(category);
  }, []);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      pauseTimersRef.current.forEach(timer => clearTimeout(timer));
    };
  }, []);

  const contextValue: PulsePauseContextType = {
    pausedCategories,
    queuedUpdates: queuedUpdatesRef.current,
    pauseCategory,
    resumeCategory,
    isPaused,
    getQueuedUpdates,
    clearQueuedUpdates
  };

  return (
    <PulsePauseContext.Provider value={contextValue}>
      {children}
    </PulsePauseContext.Provider>
  );
};

export const usePulsePause = () => {
  const context = useContext(PulsePauseContext);
  if (!context) {
    throw new Error('usePulsePause must be used within a PulsePauseProvider');
  }
  return context;
};

// Custom events for global communication
export const PULSE_PAUSE_EVENTS = {
  PAUSE_CATEGORY: 'pulsePauseCategory',
  RESUME_CATEGORY: 'pulseResumeCategory',
  CATEGORY_RESUMED: 'pulseCategoryResumed'
};