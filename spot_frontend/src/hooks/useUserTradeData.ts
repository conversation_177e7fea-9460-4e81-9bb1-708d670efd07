import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { getTokenImageUrl } from '@/utils/tokenImageService';

// Types (matching backend)
interface UserTrade {
  chain_id: string;
  swap_type: string;
  raw_amount0: string;
  raw_amount1: string;
  amount0: number;
  amount1: number;
  ratio: number;
  price_usd_token0: number;
  price_usd_token1: number;
  date: string;
  amount_usd: number;
  pool_address: string;
  token0_address: string;
  token1_address: string;
  transaction_sender_address: string;
  transaction_hash: string;
  base: string;
  quote: string;
  side: 'buy' | 'sell';
  amount_quote: number;
  amount_base: number;
}

interface FormattedUserTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  amount: string;
  usdAmount: string;
  price: string;
  trader: string;
  txHash: string;
  poolAddress: string;
  token0Address: string;
  token1Address: string;
  marketCap: number;
  tokenAmount: number;
  actualTokenAmount: number;
  [key: string]: any;
}

// Helper function to convert trade history format to UserTrade format
const convertTradeHistoryToUserTrades = (tradeHistoryData: any[]): UserTrade[] => {
  return tradeHistoryData.map((trade: any) => {
    // Convert the trade_history table format to the expected UserTrade format
    const isBuy = trade.trade_type === 'buy';
    
    // For Solana trades, SOL is always one of the tokens
    const solAddress = 'So11111111111111111111111111111111111111112';
    
    // IMPORTANT: Backend has a bug where SELL trades have sol_amount and token_amount swapped
    // We need to fix this in the frontend until the backend is fixed
    let solAmount = trade.sol_amount;
    let tokenAmount = trade.token_amount;
    
    if (!isBuy) {
      // For SELL trades, swap the values back to correct positions
      solAmount = trade.token_amount;  // This contains the actual SOL received
      tokenAmount = trade.sol_amount;  // This contains the actual tokens sold
    }
    
    return {
      chain_id: 'solana',
      swap_type: trade.trade_type,
      // For proper display:
      // - token0 should always be SOL
      // - token1 should always be the token
      // This ensures consistent handling in formatUserTrades
      raw_amount0: solAmount.toString(),
      raw_amount1: tokenAmount.toString(),
      amount0: solAmount,
      amount1: tokenAmount,
      ratio: trade.price,
      price_usd_token0: 0, // SOL price in USD (not used)
      price_usd_token1: trade.price, // Token price in USD
      date: trade.created_at,
      amount_usd: solAmount * trade.price, // Use corrected solAmount for USD calculation
      pool_address: trade.pool_address || '',
      token0_address: solAddress, // Always SOL
      token1_address: trade.token_address, // Always the token
      transaction_sender_address: trade.wallet_address,
      transaction_hash: trade.tx_hash,
      base: trade.token_symbol,
      quote: 'SOL',
      side: trade.trade_type as 'buy' | 'sell',
      amount_quote: solAmount,
      amount_base: tokenAmount
    };
  });
};

export const useUserTradeData = (walletAddress?: string | null, tokenAddress?: string | null) => {
  const [trades, setTrades] = useState<FormattedUserTrade[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Format a number with K, M, B, T suffix
  const formatAmountWithSuffix = (value: number): string => {
    if (value === 0) return '0';
    
    const absValue = Math.abs(value);
    
    if (absValue >= 1e12) return `${(value / 1e12).toFixed(3)}T`;
    if (absValue >= 1e9) return `${(value / 1e9).toFixed(3)}B`;
    if (absValue >= 1e6) return `${(value / 1e6).toFixed(3)}M`;
    if (absValue >= 1e3) return `${(value / 1e3).toFixed(3)}K`;
    
    // For very small values, use zero-count notation
    if (absValue < 0.01 && absValue > 0) {
      // Convert to string to count zeros after decimal
      const str = absValue.toFixed(20); // Use high precision
      const match = str.match(/^0\.0+/);
      if (match) {
        const zeroCount = match[0].length - 2; // Subtract '0.' 
        if (zeroCount >= 3) {
          // Get the significant digits after the zeros
          const significantPart = str.substring(match[0].length);
          // Take up to 4 significant digits
          const digits = significantPart.substring(0, 4).replace(/0+$/, '');
          
          // Create subscript number
          const subscript = String(zeroCount).split('').map(d => 
            String.fromCharCode(0x2080 + parseInt(d))
          ).join('');
          
          return `0.0${subscript}${digits}`;
        }
      }
    }
    
    // For small values that don't need zero-count notation
    if (absValue < 0.0001) return value.toFixed(8);
    if (absValue < 0.01) return value.toFixed(6);
    if (absValue < 1) return value.toFixed(4);
    
    return value.toFixed(2);
  };

  // Format user trades from API response
  const formatUserTrades = (rawTrades: UserTrade[]): FormattedUserTrade[] => {
    return rawTrades.map((trade) => {
      // Parse timestamp from date string (using UTC to avoid timezone issues)
      const timestamp = trade.date 
        ? new Date(trade.date).getTime() 
        : Date.now();
      
      // Now that we've standardized the format:
      // - amount0 is always SOL amount
      // - amount1 is always token amount
      // For buy trades: user spends SOL to get tokens
      // For sell trades: user sells tokens to get SOL
      
      const solAmount = Math.abs(trade.amount0 || 0);
      const tokenAmount = Math.abs(trade.amount1 || 0);
      
      // Format amounts with fallbacks for missing data
      // For display, we want to show the token amount (non-SOL) that the user bought/sold
      const amount = formatAmountWithSuffix(tokenAmount);
      
      // USD amount of the trade
      const usdAmount = trade.amount_usd !== undefined 
        ? formatAmountWithSuffix(trade.amount_usd) 
        : '-';
      
      // Price per token (in USD)
      const price = trade.price_usd_token1 !== undefined 
        ? formatAmountWithSuffix(trade.price_usd_token1) 
        : '-';
      
      // Don't calculate market cap since we don't have reliable data
      const marketCap = 0;
      
      // SOL amount formatted for display
      const solAmountFormatted = formatAmountWithSuffix(solAmount);
      
      // Get token address (always token1_address now)
      const tokenAddress = trade.token1_address;
      
      return {
        id: `${trade.transaction_hash || 'unknown'}-${trade.token0_address || 'unknown'}-${trade.token1_address || 'unknown'}`,
        timestamp,
        type: trade.side || 'buy',
        amount,
        usdAmount,
        price,
        solAmount: solAmountFormatted, // Add formatted SOL amount for display
        trader: trade.transaction_sender_address || '-',
        txHash: trade.transaction_hash || '-',
        poolAddress: trade.pool_address || '-',
        token0Address: trade.token0_address || '-', // Always SOL address
        token1Address: trade.token1_address || '-', // Always token address
        // Don't show market cap
        marketCap: 0,
        // For compatibility with trade table:
        // tokenAmount is the SOL amount (for side panel)
        tokenAmount: solAmount,
        // actualTokenAmount is the token quantity (for bottom table)
        actualTokenAmount: tokenAmount,
        // Include original data for reference
        originalTrade: trade,
        // Add age calculation
        age: formatTimeAgo(timestamp),
        // Add token metadata
        tokenSymbol: trade.base || 'Unknown',
        tokenAddress: tokenAddress || '-',
        tokenImage: tokenAddress ? getTokenImageUrl(tokenAddress, trade.base) : null
      };
    });
  };
  
  // Helper function to format time ago
  const formatTimeAgo = (timestamp: number): string => {
    const now = Date.now();
    const diffSeconds = Math.floor((now - timestamp) / 1000);
    
    if (diffSeconds < 0) {
      return 'just now'; // Handle future dates or clock skew
    } else if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else if (diffSeconds < 86400) {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
         } else {
       return `${Math.floor(diffSeconds / 86400)}d ago`;
     }
   };

  const fetchUserTrades = useCallback(async () => {
    // Skip if no wallet address
    if (!walletAddress) {
      setTrades([]);
      setIsLoading(false);
      setError(null);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      console.log(`Fetching user trades for Solana wallet: ${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)}`);
      
      // Call the user-trades API from trading-panel
      const params: any = {
        limit: 100
      };
      
      // Add token filtering if provided
      if (tokenAddress) {
        params.token_address = tokenAddress;
      }
      
      const response = await axios.get(`/api/trading-panel/user-trades/${walletAddress}`, { params });
      
      // Check if we have valid data from new API format
      if (response.data && response.data.success && response.data.data && Array.isArray(response.data.data.data)) {
        console.log(`Received ${response.data.data.data.length} user trades from trade history API`);
        // Convert the new trade format to the expected format
        const convertedTrades = convertTradeHistoryToUserTrades(response.data.data.data);
        const formattedTrades = formatUserTrades(convertedTrades);
        setTrades(formattedTrades);
      } else {
        console.warn('Invalid response format from trade history API:', response.data);
        setTrades([]);
      }
    } catch (err: any) {
      console.error('Error fetching user trades:', err);
      setError(err.message || 'Failed to fetch user trades');
      setTrades([]);
    } finally {
      setIsLoading(false);
    }
  }, [walletAddress, tokenAddress]);

  useEffect(() => {
    fetchUserTrades();
  }, [fetchUserTrades]);

  // Listen for trade success events to refresh user trades
  useEffect(() => {
    const handleTradeSuccess = (event: CustomEvent) => {
      console.log('Trade success event received, refreshing user trades:', event.detail);
      // Refresh user trades after a successful trade
      setTimeout(() => {
        fetchUserTrades();
      }, 2000); // 2 second delay to allow backend to process the trade
    };

    const handleLimitOrderCreated = (event: CustomEvent) => {
      console.log('Limit order created event received, refreshing user trades:', event.detail);
      // Refresh user trades after a limit order is created
      setTimeout(() => {
        fetchUserTrades();
      }, 2000); // 2 second delay to allow backend to process the order
    };

    // Add event listeners
    window.addEventListener('pulseTradeSuccess', handleTradeSuccess as EventListener);
    window.addEventListener('limitOrderCreated', handleLimitOrderCreated as EventListener);

    // Cleanup event listeners
    return () => {
      window.removeEventListener('pulseTradeSuccess', handleTradeSuccess as EventListener);
      window.removeEventListener('limitOrderCreated', handleLimitOrderCreated as EventListener);
    };
  }, [fetchUserTrades]);
  
  return {
    trades,
    isLoading,
    error
  };
}; 