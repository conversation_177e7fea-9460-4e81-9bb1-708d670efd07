import { useState, useEffect, useCallback, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { websocketService } from '@/services/websocketService';
import { processPulseData, applyDeltas } from '@/utils/pulseDeltaHandler';

// Debounce utility function
const debounce = <T extends (...args: any[]) => void>(func: T, wait: number): T => {
  let timeout: NodeJS.Timeout | null = null;
  return ((...args: any[]) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }) as T;
};

// Simple hash function for data comparison
const hashData = (data: any): string => {
  const str = JSON.stringify(data);
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString(36);
};

console.log('🚀 [DEBUG] useWebSocketPulseData module loaded');

interface PulseDataUpdate {
  data: any;
  timestamp: number;
  source: 'websocket' | 'api';
}

interface UseWebSocketPulseDataReturn {
  pulseData: any | null;
  isConnected: boolean;
  isConnecting: boolean;
  lastUpdate: number | null;
  dataSource: 'websocket' | 'api' | null;
  error: string | null;
  reconnect: () => Promise<void>;
  forceRefresh: () => Promise<void>;
}

/**
 * Check if current route requires pulse data
 * ONLY the /pulse page should fetch pulse data
 */
const isPulseRoute = (pathname: string): boolean => {
  return pathname === '/pulse';
};


// Global state to track if WebSocket is already connected for pulse data
let globalPulseConnection: {
  isConnected: boolean;
  subscriberCount: number;
  data: any | null;
  lastUpdate: number | null;
  error: string | null;
  callbacks: Set<(data: any) => void>;
  dataHash: string | null;
  lastBroadcastTime: number;
  pausedCategories: Set<string>;
  queuedUpdates: Map<string, any[]>;
} = {
  isConnected: false,
  subscriberCount: 0,
  data: null,
  lastUpdate: null,
  error: null,
  callbacks: new Set(),
  dataHash: null,
  lastBroadcastTime: 0,
  pausedCategories: new Set(),
  queuedUpdates: new Map()
};

// Global WebSocket management functions
const connectGlobalPulseWebSocket = async (): Promise<void> => {
  if (globalPulseConnection.isConnected || !websocketService) {
    return;
  }

  try {
    console.log('🔌 Connecting to pulse WebSocket (global singleton)...');
    console.log('🚀 [DEBUG] websocketService.connected:', websocketService.connected);
    console.log('🚀 [DEBUG] websocketService.inPulseRoom:', websocketService.inPulseRoom);

    if (!websocketService.connected) {
      console.log('🚀 [DEBUG] WebSocket not connected, calling connect()...');
      await websocketService.connect();
      console.log('🚀 [DEBUG] WebSocket connect() completed');
    }

    if (!websocketService.inPulseRoom) {
      console.log('🚀 [DEBUG] Not in pulse room, calling joinPulseRoom()...');
      await websocketService.joinPulseRoom();
      console.log('🚀 [DEBUG] joinPulseRoom() completed');
    }

    globalPulseConnection.isConnected = true;
    console.log('✅ Global pulse WebSocket connected');

    // Create debounced function for background processing to prevent excessive re-renders
    const debouncedBackgroundProcess = debounce((completeData: any) => {
      // Check if data has actually changed using hash
      const newHash = hashData(completeData);
      const now = Date.now();
      
      // Skip if same data and less than 50ms since last broadcast
      if (globalPulseConnection.dataHash === newHash && 
          (now - globalPulseConnection.lastBroadcastTime) < 50) {
        console.log('📊 Skipping duplicate data broadcast');
        return;
      }
      
      console.log('💾 Notifying with complete data:', {
        new: completeData?.new?.length || 0,
        bonding: completeData?.bonding?.length || 0,
        bonded: completeData?.bonded?.length || 0,
        hash: newHash.substring(0, 8)
      });

      // Update hash and broadcast time
      globalPulseConnection.dataHash = newHash;
      globalPulseConnection.lastBroadcastTime = now;

      // ALWAYS notify subscribers with complete processed data
      // This ensures bonding/bonded data is properly updated
      globalPulseConnection.callbacks.forEach(callback => {
        callback(completeData);
      });
    }, 100); // 100ms debounce for background processing

    // Set up pause/resume event listeners
    const handlePauseCategory = (event: CustomEvent) => {
      const category = event.detail.category;
      console.log(`⏸️ WebSocket: Pausing category ${category}`);
      globalPulseConnection.pausedCategories.add(category);
    };

    const handleResumeCategory = (event: CustomEvent) => {
      const category = event.detail.category;
      console.log(`▶️ WebSocket: Resuming category ${category}`);
      globalPulseConnection.pausedCategories.delete(category);
      
      // Apply queued updates for this category
      const queuedUpdates = globalPulseConnection.queuedUpdates.get(category);
      if (queuedUpdates && queuedUpdates.length > 0) {
        console.log(`📦 Applying ${queuedUpdates.length} queued updates for ${category}`);
        
        // Merge queued updates with current data
        const currentData = globalPulseConnection.data || { new: [], bonding: [], bonded: [] };
        const updatedData = { ...currentData };
        
        // Replace the category data with queued updates
        updatedData[category] = queuedUpdates;
        
        // Update global data and notify subscribers
        globalPulseConnection.data = updatedData;
        globalPulseConnection.callbacks.forEach(callback => {
          callback(updatedData);
        });
        
        // Clear the queue
        globalPulseConnection.queuedUpdates.delete(category);
      }
    };

    window.addEventListener('pulsePauseCategory', handlePauseCategory as EventListener);
    window.addEventListener('pulseResumeCategory', handleResumeCategory as EventListener);

    // Set up data listener with priority handling for all token categories
    const unsubscribe = websocketService.onPulseData((update: PulseDataUpdate) => {
      // Check what categories are included in this update
      const hasNewTokens = update.data?.new && update.data.new.length > 0;
      const hasBondingTokens = update.data?.bonding && update.data.bonding.length > 0;
      const hasBondedTokens = update.data?.bonded && update.data.bonded.length > 0;
      
      // Check if this is a complete data update (has all arrays defined) or priority-only
      const isCompleteUpdate = update.data.bonding !== undefined && update.data.bonded !== undefined && update.data.new !== undefined;
      
      // SPEED OPTIMIZATION: Priority path for single-category updates (immediate, no debouncing)
      if (!isCompleteUpdate && (hasNewTokens || hasBondingTokens || hasBondedTokens)) {
        let priorityType = '';
        if (hasNewTokens) priorityType += 'new ';
        if (hasBondingTokens) priorityType += 'bonding ';
        if (hasBondedTokens) priorityType += 'bonded ';
        
        console.log(`🚀 Priority: ${priorityType.trim()} tokens update:`, {
          new: update.data.new?.length || 0,
          bonding: update.data.bonding?.length || 0,
          bonded: update.data.bonded?.length || 0
        });
        
        // Check which categories are paused and queue updates accordingly
        const currentData = globalPulseConnection.data || { new: [], bonding: [], bonded: [] };
        const immediateData = { ...currentData };
        
        // Process each category
        ['new', 'bonding', 'bonded'].forEach(category => {
          if (update.data[category] !== undefined) {
            if (globalPulseConnection.pausedCategories.has(category)) {
              // Queue the update for this category
              console.log(`📦 Queueing update for paused category: ${category} (${update.data[category].length} tokens)`);
              globalPulseConnection.queuedUpdates.set(category, update.data[category]);
            } else {
              // Apply the update immediately
              immediateData[category] = update.data[category];
            }
          }
        });
        
        // Check for duplicates even in priority path
        const immediateHash = hashData(immediateData);
        const now = Date.now();
        
        if (globalPulseConnection.dataHash === immediateHash && 
            (now - globalPulseConnection.lastBroadcastTime) < 50) {
          console.log('🚀 Priority update is duplicate, skipping');
          return;
        }
        
        console.log('🚀 Priority update data structure:', {
          new: immediateData.new?.length || 0,
          bonding: immediateData.bonding?.length || 0,
          bonded: immediateData.bonded?.length || 0,
          hash: immediateHash.substring(0, 8)
        });
        
        // Update global data with the new immediate data
        globalPulseConnection.data = immediateData;
        globalPulseConnection.dataHash = immediateHash;
        globalPulseConnection.lastBroadcastTime = now;
        
        // Notify subscribers immediately with updated data
        globalPulseConnection.callbacks.forEach(callback => {
          callback(immediateData);
        });
        
        globalPulseConnection.lastUpdate = update.timestamp;
        globalPulseConnection.error = null;
        
        // Skip background processing for priority-only updates
        return;
      }
      
      // Process complete data updates (including those with new tokens)
      setTimeout(() => {
        const processedData = processPulseData(update.data);
        
        console.log('📡 Processing complete data:', {
          isNew: update.data?.new?.length > 0,
          isBonding: update.data?.bonding?.length > 0,
          isBonded: update.data?.bonded?.length > 0,
          isDelta: processedData._isDelta,
          isCompleteUpdate
        });
        
        // Update global data with complete processed data, respecting paused categories
        const currentData = globalPulseConnection.data || { new: [], bonding: [], bonded: [] };
        let finalData = currentData;
        
        if (processedData._isDelta) {
          const mergedData = applyDeltas(currentData, processedData);
          finalData = { ...currentData };
          
          // Apply merged data only for non-paused categories
          ['new', 'bonding', 'bonded'].forEach(category => {
            if (globalPulseConnection.pausedCategories.has(category)) {
              console.log(`📦 Queueing delta update for paused category: ${category}`);
              globalPulseConnection.queuedUpdates.set(category, mergedData[category] || []);
            } else {
              finalData[category] = mergedData[category] || [];
            }
          });
          
          globalPulseConnection.data = finalData;
          console.log('📡 Applied delta update (respecting pauses), current counts:', {
            new: finalData.new?.length || 0,
            bonding: finalData.bonding?.length || 0,
            bonded: finalData.bonded?.length || 0
          });
        } else {
          finalData = { ...currentData };
          
          // Apply complete data only for non-paused categories
          ['new', 'bonding', 'bonded'].forEach(category => {
            if (globalPulseConnection.pausedCategories.has(category)) {
              console.log(`📦 Queueing complete update for paused category: ${category}`);
              globalPulseConnection.queuedUpdates.set(category, processedData[category] || []);
            } else {
              finalData[category] = processedData[category] || [];
            }
          });
          
          globalPulseConnection.data = finalData;
          console.log('📡 Applied complete update (respecting pauses), current counts:', {
            new: finalData.new?.length || 0,
            bonding: finalData.bonding?.length || 0,
            bonded: finalData.bonded?.length || 0
          });
        }
        
        // Notify subscribers with processed data
        debouncedBackgroundProcess(globalPulseConnection.data);
      }, 0);
    });

    // Store unsubscribe function and event listeners for cleanup
    (globalPulseConnection as any).unsubscribe = unsubscribe;
    (globalPulseConnection as any).handlePauseCategory = handlePauseCategory;
    (globalPulseConnection as any).handleResumeCategory = handleResumeCategory;

  } catch (error) {
    console.error('❌ Failed to connect global pulse WebSocket:', error);
    globalPulseConnection.error = error instanceof Error ? error.message : 'Connection failed';
  }
};

const disconnectGlobalPulseWebSocket = (): void => {
  if (!globalPulseConnection.isConnected) {
    return;
  }

  console.log('🔌 Disconnecting global pulse WebSocket...');

  // Clean up WebSocket connection
  if (websocketService.inPulseRoom) {
    websocketService.leavePulseRoom();
  }

  if (websocketService.connected) {
    websocketService.disconnect();
  }

  // Clean up unsubscribe function and event listeners
  if ((globalPulseConnection as any).unsubscribe) {
    (globalPulseConnection as any).unsubscribe();
    delete (globalPulseConnection as any).unsubscribe;
  }
  
  // Remove event listeners
  if ((globalPulseConnection as any).handlePauseCategory) {
    window.removeEventListener('pulsePauseCategory', (globalPulseConnection as any).handlePauseCategory as EventListener);
    delete (globalPulseConnection as any).handlePauseCategory;
  }
  
  if ((globalPulseConnection as any).handleResumeCategory) {
    window.removeEventListener('pulseResumeCategory', (globalPulseConnection as any).handleResumeCategory as EventListener);
    delete (globalPulseConnection as any).handleResumeCategory;
  }

  // Reset global state but keep data for restoration
  globalPulseConnection.isConnected = false;
  // DON'T clear data here - keep it for restoration when returning to pulse page
  // globalPulseConnection.data = null;
  globalPulseConnection.lastUpdate = null;
  globalPulseConnection.error = null;
  globalPulseConnection.callbacks.clear();

  console.log('✅ Global pulse WebSocket disconnected (data preserved)');
};

export const useWebSocketPulseData = (enableWebSocket: boolean = true): UseWebSocketPulseDataReturn => {
  const location = useLocation();
  const shouldConnectToPulse = isPulseRoute(location.pathname);

  // Initialize with data from global state
  const [pulseData, setPulseData] = useState<any | null>(() => {
    // If we have global data, use it
    if (globalPulseConnection.data) {
      console.log('🔄 Using existing global data:', {
        new: globalPulseConnection.data?.new?.length || 0,
        bonding: globalPulseConnection.data?.bonding?.length || 0,
        bonded: globalPulseConnection.data?.bonded?.length || 0
      });
      return globalPulseConnection.data;
    }
    
    console.log('❌ No initial data available');
    return null;
  });

  const [isConnected, setIsConnected] = useState(globalPulseConnection.isConnected);
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<number | null>(globalPulseConnection.lastUpdate);
  const [dataSource, setDataSource] = useState<'websocket' | 'api' | null>(null);
  const [error, setError] = useState<string | null>(globalPulseConnection.error);

  const componentId = useRef<string>(`component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const fallbackTimer = useRef<NodeJS.Timeout | null>(null);

  // Data update callback for this component with deduplication
  const handleDataUpdate = useCallback((data: any) => {
    // Use request animation frame for smooth updates
    requestAnimationFrame(() => {
      setPulseData(data);
      setLastUpdate(Date.now());
      setDataSource('websocket');
      setError(null);
      setIsConnecting(false);
      setIsConnected(true);
    });
  }, []);

  // Main effect to manage WebSocket connection based on route
  useEffect(() => {
    console.log('🚀 [DEBUG] useEffect triggered');
    console.log('🚀 [DEBUG] - enableWebSocket:', enableWebSocket);
    console.log('🚀 [DEBUG] - shouldConnectToPulse:', shouldConnectToPulse);
    console.log('🚀 [DEBUG] - location.pathname:', location.pathname);

    if (!enableWebSocket) {
      console.log('❌ WebSocket disabled');
      setError('WebSocket disabled');
      return;
    }

    if (!shouldConnectToPulse) {
      // Not on pulse route - but keep data available for when we return
      console.log(`📍 Not on pulse route (${location.pathname}) - disconnecting but preserving data`);

      // Remove this component's callback
      globalPulseConnection.callbacks.delete(handleDataUpdate);
      globalPulseConnection.subscriberCount--;

      // If no more subscribers, disconnect but keep data
      if (globalPulseConnection.subscriberCount <= 0) {
        disconnectGlobalPulseWebSocket();
      }

      // Clear local state but don't clear localStorage data
      setDataSource(null);
      setError('Not on pulse route');
      setIsConnected(false);
      setIsConnecting(false);
      // Keep pulseData available for quick restoration

      return;
    }

    // On pulse route - ensure connection and restore data if needed
    console.log(`📍 On pulse route (${location.pathname}) - ensuring WebSocket connection`);

    // Add this component as a subscriber
    globalPulseConnection.callbacks.add(handleDataUpdate);
    globalPulseConnection.subscriberCount++;

    // Set initial state from global connection
    setPulseData(globalPulseConnection.data);
    setLastUpdate(globalPulseConnection.lastUpdate);
    setError(globalPulseConnection.error);
    setIsConnected(globalPulseConnection.isConnected);

    // Connect if not already connected
    if (!globalPulseConnection.isConnected) {
      console.log('🚀 [DEBUG] Global connection not established, connecting...');
      setIsConnecting(true);
      connectGlobalPulseWebSocket().then(() => {
        console.log('🚀 [DEBUG] Global connection established successfully');
        setIsConnecting(false);
      }).catch((error) => {
        console.log('🚀 [DEBUG] Global connection failed:', error);
        setIsConnecting(false);
        setError(error.message || 'Connection failed');
      });
    } else {
      console.log('🚀 [DEBUG] Global connection already established');
    }

    // Cleanup function
    return () => {
      // Remove this component's callback
      globalPulseConnection.callbacks.delete(handleDataUpdate);
      globalPulseConnection.subscriberCount--;

      // If no more subscribers, disconnect after a delay but preserve data
      if (globalPulseConnection.subscriberCount <= 0) {
        setTimeout(() => {
          if (globalPulseConnection.subscriberCount <= 0) {
            disconnectGlobalPulseWebSocket();
          }
        }, 1000); // 1 second delay to handle rapid navigation
      }
    };
  }, [enableWebSocket, shouldConnectToPulse, location.pathname, handleDataUpdate, pulseData]);

  // Simple reconnect function
  const reconnect = useCallback(async () => {
    console.log('🔄 Force reconnecting to pulse WebSocket...');
    disconnectGlobalPulseWebSocket();
    if (shouldConnectToPulse) {
      await connectGlobalPulseWebSocket();
    }
  }, [shouldConnectToPulse]);

  // Simple refresh function
  const forceRefresh = useCallback(async () => {
    console.log('🔄 Force refreshing pulse data...');
    if (globalPulseConnection.isConnected) {
      // WebSocket should automatically provide fresh data
      console.log('📡 WebSocket connected - fresh data should arrive automatically');
    } else {
      console.log('❌ WebSocket not connected - cannot refresh');
    }
  }, []);

  return {
    pulseData,
    isConnected,
    isConnecting,
    lastUpdate,
    dataSource,
    error,
    reconnect,
    forceRefresh
  };
};
