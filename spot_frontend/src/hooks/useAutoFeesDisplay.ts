// Hook for displaying auto fee state in components that don't have PresetProvider
import { useState, useEffect } from 'react';
import { getStoredAutoFeeState, subscribeToAutoFeeUpdates, StoredAutoFeeState } from '../utils/autoFeeStorage';
import { fetchNetworkStats, calculateDynamicFees } from '../services/dynamicFeeService';
import { storeAutoFeeState } from '../utils/autoFeeStorage';

// Cache for API calls
interface ApiCache {
  networkStats: {
    data: any;
    timestamp: number;
  } | null;
  fees: {
    data: any;
    timestamp: number;
    amount: number;
    exchangeType: string;
  } | null;
}

const CACHE_DURATION = 10000; // 10 seconds
const apiCache: ApiCache = {
  networkStats: null,
  fees: null
};

// Track ongoing requests to prevent duplicates
let ongoingNetworkRequest: Promise<any> | null = null;
let ongoingFeesRequest: Promise<any> | null = null;

export function useAutoFeesDisplay() {
  const [autoFeeState, setAutoFeeState] = useState<StoredAutoFeeState | null>(() => {
    return getStoredAutoFeeState();
  });
  
  const [isCalculating, setIsCalculating] = useState(false);

  // Subscribe to auto fee updates from other components
  useEffect(() => {
    const unsubscribe = subscribeToAutoFeeUpdates((state) => {
      setAutoFeeState(state);
    });
    
    return unsubscribe;
  }, []);

  // Function to manually calculate fees (for components without PresetProvider)
  const calculateFeesForAmount = async (amount: number = 1, exchangeType: string = 'pumpfun') => {
    const now = Date.now();
    
    // Check if we have valid cached data for the same parameters
    if (apiCache.fees && 
        apiCache.fees.amount === amount && 
        apiCache.fees.exchangeType === exchangeType &&
        (now - apiCache.fees.timestamp) < CACHE_DURATION) {
      console.log('Using cached fees data, skipping API call');
      return;
    }
    
    // Check if a request is already in progress
    if (ongoingFeesRequest) {
      console.log('Fees calculation already in progress, skipping duplicate request');
      return;
    }
    
    setIsCalculating(true);
    
    // Store loading state
    storeAutoFeeState({
      isEnabled: true,
      priorityFee: autoFeeState?.priorityFee || 0,
      bribeAmount: autoFeeState?.bribeAmount || 0,
      lastUpdated: new Date().toISOString(),
      isLoading: true,
      networkStats: autoFeeState?.networkStats
    });
    
    try {
      // Fetch network stats with caching
      let networkStats = null;
      
      if (apiCache.networkStats && (now - apiCache.networkStats.timestamp) < CACHE_DURATION) {
        console.log('Using cached network stats');
        networkStats = apiCache.networkStats.data;
      } else {
        // Prevent duplicate network stats requests
        if (!ongoingNetworkRequest) {
          console.log('Fetching fresh network stats');
          ongoingNetworkRequest = fetchNetworkStats();
          try {
            networkStats = await ongoingNetworkRequest;
            apiCache.networkStats = {
              data: networkStats,
              timestamp: now
            };
          } finally {
            ongoingNetworkRequest = null;
          }
        } else {
          console.log('Network stats request already in progress, waiting...');
          networkStats = await ongoingNetworkRequest;
        }
      }
      
      // Calculate fees with deduplication
      console.log('Calculating fees for amount:', amount, 'exchange:', exchangeType);
      ongoingFeesRequest = calculateDynamicFees(amount, false, exchangeType);
      
      const fees = await ongoingFeesRequest;
      
      if (fees) {
        // Cache the fees result
        apiCache.fees = {
          data: fees,
          timestamp: now,
          amount,
          exchangeType
        };
        
        const newState: StoredAutoFeeState = {
          isEnabled: true,
          priorityFee: fees.priorityFee,
          bribeAmount: fees.bribeAmount,
          lastUpdated: new Date().toISOString(),
          networkStats: networkStats ? {
            averageTps: networkStats.averageTps,
            congestionMultiplier: networkStats.congestionMultiplier
          } : undefined,
          isLoading: false
        };
        
        // Store and update state
        storeAutoFeeState(newState);
        setAutoFeeState(newState);
      }
    } catch (error) {
      console.error('Error calculating auto fees:', error);
      // Update state to show error
      const errorState: StoredAutoFeeState = {
        isEnabled: true,
        priorityFee: autoFeeState?.priorityFee || 0,
        bribeAmount: autoFeeState?.bribeAmount || 0,
        lastUpdated: new Date().toISOString(),
        isLoading: false,
        networkStats: autoFeeState?.networkStats
      };
      storeAutoFeeState(errorState);
    } finally {
      setIsCalculating(false);
      ongoingFeesRequest = null;
    }
  };

  return {
    autoFeeState,
    isCalculating,
    calculateFeesForAmount
  };
}