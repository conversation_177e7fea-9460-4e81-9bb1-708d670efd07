import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  fetchNetworkStats, 
  calculateDynamicFees, 
  NetworkStats, 
  DynamicFeesResponse 
} from '../services/dynamicFeeService';
import { usePreset } from '../Pulse_Trade/Preset/PresetContext';
import { storeAutoFeeState, storeAutoFeeEnabled, getAutoFeeEnabled } from '../utils/autoFeeStorage';

export interface AutoFeeState {
  isEnabled: boolean;
  networkStats: NetworkStats | null;
  dynamicFees: DynamicFeesResponse | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface UseAutoFeesResult {
  autoFeeState: AutoFeeState;
  toggleAutoFees: () => void;
  refreshFees: (amount?: number, isUrgent?: boolean, exchangeType?: string) => Promise<void>;
  updateFeesForAmount: (amount: number, isUrgent?: boolean, exchangeType?: string) => Promise<void>;
}

/**
 * Hook to manage automatic fee calculation based on network conditions
 */
export function useAutoFees(defaultExchangeType: string = 'pumpfun'): UseAutoFeesResult {
  const { activePreset, activeTab, presetData } = usePreset();
  
  // Initialize with persisted auto fee enabled state
  const [autoFeeState, setAutoFeeState] = useState<AutoFeeState>(() => {
    const persistedEnabled = getAutoFeeEnabled();
    console.log('[useAutoFees] Initializing state with persisted enabled state:', persistedEnabled);
    return {
      isEnabled: persistedEnabled, // Use persisted state
      networkStats: null,
      dynamicFees: null,
      isLoading: false,
      error: null,
      lastUpdated: null,
    };
  });

  const refreshInterval = useRef<NodeJS.Timeout | null>(null);
  const lastAmount = useRef<number>(1); // Default trade amount
  const currentExchangeType = useRef<string>(defaultExchangeType); // Current exchange type
  const lastNetworkStatsTime = useRef<number>(0); // Cache timestamp for network stats
  const lastFeesCalculationTime = useRef<number>(0); // Cache timestamp for fees
  const CACHE_DURATION = 10000; // 10 seconds cache as requested
  const pendingNetworkRequest = useRef<Promise<any> | null>(null); // Prevent concurrent requests
  const pendingFeesRequest = useRef<Promise<any> | null>(null); // Prevent concurrent requests
  const refreshFeesRef = useRef<Function>(); // Ref for stable access to refreshFees

  // Helper function to create consistent event detail
  const createEventDetail = (state: Partial<AutoFeeState>) => ({
    ...state,
    exchangeType: currentExchangeType.current,
    activeTab: activeTab,
    activePreset: activePreset
  });

  // Initialize auto state based on preset but don't sync after that
  useEffect(() => {
    console.log('[useAutoFees] Initial mount - auto fees state initialized:', {
      isEnabled: autoFeeState.isEnabled,
      activePreset,
      activeTab
    });
    
    // Emit initial state for components that need it
    window.dispatchEvent(new CustomEvent('autoFeeStateUpdate', { 
      detail: createEventDetail(autoFeeState)
    }));
    
    // If auto fees are enabled on mount, trigger initial fetch
    if (autoFeeState.isEnabled) {
      console.log('[useAutoFees] Auto fees enabled on mount, triggering initial fetch');
      setTimeout(() => {
        refreshFeesRef.current?.(undefined, undefined, undefined, true);
      }, 100);
    }
  }, []); // Only run on mount

  /**
   * Fetch network stats and update state with caching and deduplication
   */
  const fetchNetworkStatsData = useCallback(async (forceRefresh: boolean = false) => {
    const now = Date.now();
    
    // Skip if data is fresh and not forcing refresh
    if (!forceRefresh && (now - lastNetworkStatsTime.current) < CACHE_DURATION && autoFeeState.networkStats) {
      console.log('Using cached network stats, skipping API call');
      return autoFeeState.networkStats;
    }

    // Prevent concurrent requests
    if (pendingNetworkRequest.current) {
      console.log('Network stats request already in progress, waiting...');
      return await pendingNetworkRequest.current;
    }

    try {
      console.log('Fetching fresh network stats');
      pendingNetworkRequest.current = fetchNetworkStats();
      const stats = await pendingNetworkRequest.current;
      lastNetworkStatsTime.current = now;
      const newState = {
        ...autoFeeState,
        networkStats: stats,
        error: stats ? null : 'Failed to fetch network stats',
      };
      setAutoFeeState(newState);
      
      // Emit state update event for Footer
      window.dispatchEvent(new CustomEvent('autoFeeStateUpdate', { 
        detail: createEventDetail(newState)
      }));
      
      // Store updated state with network stats
      if (stats && autoFeeState.dynamicFees) {
        storeAutoFeeState({
          isEnabled: newState.isEnabled,
          priorityFee: autoFeeState.dynamicFees.priorityFee,
          bribeAmount: autoFeeState.dynamicFees.bribeAmount,
          lastUpdated: new Date().toISOString(),
          networkStats: {
            averageTps: stats.averageTps,
            congestionMultiplier: stats.congestionMultiplier
          }
        });
      }
      
      return stats;
    } catch (error) {
      console.error('Error fetching network stats:', error);
      setAutoFeeState(prev => {
        const newState = {
          ...prev,
          error: 'Error fetching network stats',
        };
        // Emit state update event for Footer
        window.dispatchEvent(new CustomEvent('autoFeeStateUpdate', { detail: newState }));
        return newState;
      });
      return null;
    } finally {
      pendingNetworkRequest.current = null;
    }
  }, [autoFeeState.networkStats]);

  /**
   * Calculate dynamic fees and update preset if auto mode is enabled with caching and deduplication
   */
  const updateFeesForAmount = useCallback(async (amount: number, isUrgent: boolean = false, exchangeType?: string, forceRefresh: boolean = false) => {
    if (!autoFeeState.isEnabled || activePreset === null) {
      return;
    }

    // Update current exchange type if provided
    const finalExchangeType = exchangeType || currentExchangeType.current;
    if (exchangeType) {
      currentExchangeType.current = exchangeType;
    }

    const now = Date.now();
    
    // Skip if same parameters and data is fresh and not forcing refresh
    if (!forceRefresh && 
        (now - lastFeesCalculationTime.current) < CACHE_DURATION && 
        lastAmount.current === amount) {
      console.log('Using cached fee calculation, skipping API call');
      return;
    }

    // Prevent concurrent requests
    if (pendingFeesRequest.current) {
      console.log('Fees calculation request already in progress, waiting...');
      return await pendingFeesRequest.current;
    }

    setAutoFeeState(prev => {
      const newState = { ...prev, isLoading: true, error: null };
      // Emit state update event for Footer
      window.dispatchEvent(new CustomEvent('autoFeeStateUpdate', { 
        detail: createEventDetail(newState)
      }));
      return newState;
    });
    
    try {
      console.log('Calculating fresh dynamic fees for:', { amount, isUrgent, finalExchangeType });
      pendingFeesRequest.current = calculateDynamicFees(amount, isUrgent, finalExchangeType);
      const fees = await pendingFeesRequest.current;
      lastFeesCalculationTime.current = now;
      
      if (fees) {
        // DO NOT modify preset values - store auto-calculated fees separately
        const newState = {
          ...autoFeeState,
          dynamicFees: fees,
          lastUpdated: new Date(),
          isLoading: false,
        };
        
        setAutoFeeState(newState);
        
        // Emit state update event for Footer
        window.dispatchEvent(new CustomEvent('autoFeeStateUpdate', { detail: newState }));
        
        // Store fee state for other components
        storeAutoFeeState({
          isEnabled: newState.isEnabled,
          priorityFee: fees.priorityFee,
          bribeAmount: fees.bribeAmount,
          lastUpdated: new Date().toISOString(),
          networkStats: newState.networkStats ? {
            averageTps: newState.networkStats.averageTps,
            congestionMultiplier: newState.networkStats.congestionMultiplier
          } : undefined
        });
        
        // Also ensure enabled state is persisted
        storeAutoFeeEnabled(newState.isEnabled);
        
        lastAmount.current = amount;
      } else {
        setAutoFeeState(prev => {
          const newState = {
            ...prev,
            error: 'Failed to calculate dynamic fees',
            isLoading: false,
          };
          
          // Emit state update event for Footer
          window.dispatchEvent(new CustomEvent('autoFeeStateUpdate', { detail: newState }));
          
          return newState;
        });
      }
    } catch (error) {
      console.error('Error calculating dynamic fees:', error);
      setAutoFeeState(prev => {
        const newState = {
          ...prev,
          error: 'Error calculating dynamic fees',
          isLoading: false,
        };
        
        // Emit state update event for Footer
        window.dispatchEvent(new CustomEvent('autoFeeStateUpdate', { detail: newState }));
        
        return newState;
      });
    } finally {
      pendingFeesRequest.current = null;
    }
  }, [autoFeeState.isEnabled, activePreset]);

  /**
   * Refresh fees using the last known amount with smart caching
   */
  const refreshFees = useCallback(async (amount?: number, isUrgent?: boolean, exchangeType?: string, forceRefresh?: boolean) => {
    const tradeAmount = amount || lastAmount.current;
    const finalExchangeType = exchangeType || currentExchangeType.current;
    
    // Only call network stats if we don't have recent data or forcing refresh
    const now = Date.now();
    const networkStatsPromise = (forceRefresh || (now - lastNetworkStatsTime.current) > CACHE_DURATION) 
      ? fetchNetworkStatsData(forceRefresh) 
      : Promise.resolve(autoFeeState.networkStats);
    
    // Only call fees calculation if we don't have recent data or forcing refresh or amount changed
    const feesPromise = (forceRefresh || (now - lastFeesCalculationTime.current) > CACHE_DURATION || lastAmount.current !== tradeAmount)
      ? updateFeesForAmount(tradeAmount, isUrgent, finalExchangeType, forceRefresh)
      : Promise.resolve();
    
    await Promise.all([networkStatsPromise, feesPromise]);
  }, [fetchNetworkStatsData, updateFeesForAmount]);

  /**
   * Toggle auto fee mode
   */
  const toggleAutoFees = useCallback(() => {
    const newValue = !autoFeeState.isEnabled;
    
    console.log('[useAutoFees] Toggling auto fees:', { 
      from: autoFeeState.isEnabled, 
      to: newValue,
      activeTab,
      activePreset,
      exchangeType: currentExchangeType.current
    });
    
    // Store the enabled state immediately for persistence
    storeAutoFeeEnabled(newValue);
    
    // NOTE: We no longer update preset values - auto fees are separate from presets
    
    // Immediately update local state
    setAutoFeeState(prev => {
      const newState = {
        ...prev,
        isEnabled: newValue,
      };
      
      console.log('[useAutoFees] Dispatching state update event after toggle');
      // Emit state update event for Footer
      window.dispatchEvent(new CustomEvent('autoFeeStateUpdate', { 
        detail: createEventDetail(newState)
      }));
      
      return newState;
    });
    
    // Store the full state including enabled state change
    const currentFees = autoFeeState.dynamicFees;
    if (currentFees) {
      storeAutoFeeState({
        isEnabled: newValue,
        priorityFee: currentFees.priorityFee,
        bribeAmount: currentFees.bribeAmount,
        lastUpdated: new Date().toISOString(),
        networkStats: autoFeeState.networkStats ? {
          averageTps: autoFeeState.networkStats.averageTps,
          congestionMultiplier: autoFeeState.networkStats.congestionMultiplier
        } : undefined
      });
    } else {
      // Even if no fees yet, store the enabled state
      storeAutoFeeState({
        isEnabled: newValue,
        priorityFee: 0,
        bribeAmount: 0,
        lastUpdated: new Date().toISOString()
      });
    }

    if (newValue) {
      console.log('[useAutoFees] Starting auto refresh with force refresh');
      // Start auto refresh when enabled with force refresh
      refreshFeesRef.current?.(undefined, undefined, undefined, true);
    } else {
      console.log('[useAutoFees] Auto fees disabled, stopping refresh');
    }
  }, [autoFeeState.isEnabled, autoFeeState.dynamicFees, autoFeeState.networkStats, activeTab, activePreset]);

  // Keep ref updated with latest refreshFees function
  refreshFeesRef.current = refreshFees;

  // Broadcast initial state on mount if auto fees are enabled
  useEffect(() => {
    console.log('[useAutoFees] Mount effect - checking initial state', {
      isEnabled: autoFeeState.isEnabled,
      activeTab,
      activePreset,
      exchangeType: currentExchangeType.current
    });

    let footerReadyTimeout: NodeJS.Timeout;
    let hasReceivedFooterReady = false;

    const broadcastInitialState = () => {
      if (autoFeeState.isEnabled && !hasReceivedFooterReady) {
        console.log('[useAutoFees] Broadcasting initial auto fee state');
        const initialState: AutoFeeState = {
          isEnabled: autoFeeState.isEnabled,
          networkStats: null,
          dynamicFees: null,
          isLoading: true,
          error: null,
          lastUpdated: null
        };
        
        // Broadcast initial enabled state
        window.dispatchEvent(new CustomEvent('autoFeeStateUpdate', { 
          detail: createEventDetail(initialState)
        }));
        
        // Trigger initial data fetch after a short delay
        setTimeout(() => {
          console.log('[useAutoFees] Triggering initial data fetch');
          refreshFeesRef.current?.(undefined, undefined, undefined, true);
        }, 200);
        
        hasReceivedFooterReady = true;
      }
    };

    // Listen for footer ready event
    const handleFooterReady = () => {
      console.log('[useAutoFees] Footer ready event received');
      clearTimeout(footerReadyTimeout);
      broadcastInitialState();
    };

    window.addEventListener('footerReady', handleFooterReady);

    // Fallback timeout in case footer ready event is missed
    footerReadyTimeout = setTimeout(() => {
      console.log('[useAutoFees] Footer ready timeout - broadcasting anyway');
      broadcastInitialState();
    }, 1000);

    return () => {
      clearTimeout(footerReadyTimeout);
      window.removeEventListener('footerReady', handleFooterReady);
    };
  }, []); // Run only on mount

  /**
   * Set up auto refresh interval when auto mode is enabled
   */
  useEffect(() => {
    if (autoFeeState.isEnabled) {
      // Initial fetch with force refresh
      refreshFeesRef.current?.(undefined, undefined, undefined, true);
      
      // Set up interval for every 10 seconds with smart caching
      refreshInterval.current = setInterval(() => {
        refreshFeesRef.current?.(); // No force refresh for intervals - use cache when possible
      }, 10000); // 10 seconds to match cache duration
    } else {
      // Clear interval when disabled
      if (refreshInterval.current) {
        clearInterval(refreshInterval.current);
        refreshInterval.current = null;
      }
    }

    // Cleanup on unmount
    return () => {
      if (refreshInterval.current) {
        clearInterval(refreshInterval.current);
      }
    };
  }, [autoFeeState.isEnabled]); // Removed refreshFees dependency to prevent circular updates

  /**
   * Update fees when active tab changes (buy/sell) with smart refresh
   */
  useEffect(() => {
    if (autoFeeState.isEnabled) {
      // Only force refresh fees calculation for tab changes, keep network stats cached
      refreshFeesRef.current?.(undefined, undefined, undefined, false);
    }
  }, [activeTab, autoFeeState.isEnabled]); // Removed refreshFees dependency to prevent circular updates

  return {
    autoFeeState,
    toggleAutoFees,
    refreshFees,
    updateFeesForAmount,
  };
}