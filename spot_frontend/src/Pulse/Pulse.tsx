import { useState, useEffect } from 'react';
import PulseDash from './Pulse/Pulse';
import Navbar from '@/Home/Navbar/Navbar';
import { activityService } from '@/services/activityService';
import Display from './Pulse/Options/Display';
import QuickBuy from './Pulse/Options/QuickBuy';
import TradingSettingsModal from './Pulse/TradingSettiingsModal';
const tabs = ["Pulse"];
type SettingsType = {
  greyButtons: boolean;
  circleImages: boolean;
  progressBar: boolean;
  compactTables: boolean;
  // add any other keys that you have here too
};
type CustomRows ={
  marketCap: boolean;
  volume: boolean;
  tx: boolean;
  socials: boolean;
  holders: boolean;
  proTraders: boolean;
  devMigrations: boolean;
}
const Pulse = () => {
  const [activeTab, setActiveTab] = useState("Pulse");
  useEffect(() => {
    const session = activityService.getOrCreateSession();
    console.log('Main Pulse component mounted with session:', session);
    activityService.registerPulseActivity();
    return () => {
      activityService.unregisterPulseActivity();
    };
  }, []);
// Display + QuickBuy settings
const [showDropdown, setShowDropdown] = useState(false);
// const [metricsSize, setMetricsSize] = useState('small');
// const [quickBuySize, setQuickBuySize] = useState('small');
const handlePresetClick = () => setModalOpen(true);
const handleAmountChange = (value: string) => {
  setAmount(value);
  localStorage.setItem('quickbuy_amount', value);
};

const [settings, setSettings] = useState<SettingsType>(() => {
  const saved = localStorage.getItem('display_settings');
  return saved ? JSON.parse(saved) : {
    greyButtons: false,
    circleImages: false,
    progressBar: false,
    compactTables: false,
  };
});

const [customRows, setCustomRows] = useState<CustomRows>(() => {
  const saved = localStorage.getItem('custom_rows');
  return saved ? JSON.parse(saved) : {
    marketCap: true,
    volume: true,
    tx: true,
    socials: true,
    holders: true,
    proTraders: true,
    devMigrations: true,
  };
});

const [metricsSize, setMetricsSize] = useState(() => {
  return localStorage.getItem('metrics_size') || 'large';
});

const [quickBuySize, setQuickBuySize] = useState(() => {
  return localStorage.getItem('quick_buy_size') || 'small';
});


const [amount, setAmount] = useState(() => {
  const saved = localStorage.getItem('quickbuy_amount');
  return saved || "";
}); // Entered amount
const [modalOpen, setModalOpen] = useState(false);
const [preset, setPreset] = useState(3);

  return (
    <div className="h-screen flex flex-col relative bg-[#141416]">
      {/* App Theme Consistent Background */}
      <div className="absolute inset-0 bg-[#141416]"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-[#181C20]/30 via-transparent to-[#181C20]/20"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-[#00FFBD]/5 via-transparent to-[#025FDA]/5"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-[#00FFBD]/8 via-transparent to-transparent"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-[#025FDA]/8 via-transparent to-transparent"></div>

      {/* Subtle Pattern Overlay */}
      <div className="absolute inset-0 opacity-[0.015]" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.2) 1px, transparent 0)`,
        backgroundSize: '32px 32px'
      }}></div>

      {/* Content Container */}
      <div className="relative z-10 flex flex-col h-full min-h-0">
        <div className="flex-shrink-0">
          <Navbar />
        </div>

        {/* Tabs + Conditional Search UI */}
        <div className="flex-shrink-0 flex justify-between items-center text-white font-medium text-lg py-6 px-4 mb-4 relative">
          {/* App Theme Consistent Header Background */}
          <div className="absolute inset-0 bg-gradient-to-r from-[#181C20]/95 via-[#141416]/90 to-[#181C20]/95 backdrop-blur-xl border-b border-[#181C20]/60 shadow-xl"></div>
          <div className="flex gap-6 relative z-10">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`relative pb-1 hover:text-white transition-all duration-300 font-semibold ${
                  activeTab === tab ? "text-white" : "text-gray-400"
                }`}
              >
                {tab}
                {activeTab === tab && (
                  <span className="absolute left-0 bottom-0 w-full h-[2px] bg-gradient-to-r from-emerald-400 to-cyan-400 rounded shadow-lg shadow-emerald-400/50" />
                )}
              </button>
            ))}
          </div>

          <div className="flex items-center justify-end gap-4 relative">
            <div className="relative">
              <QuickBuy
                onAmountChange={handleAmountChange}
                selectedPreset={preset}
                onPresetClick={handlePresetClick}
              />
              {modalOpen && (
                <TradingSettingsModal
                  visible={modalOpen}
                  onClose={() => setModalOpen(false)}
                  selectedPreset={preset}
                  onPresetChange={setPreset}
                />
              )}
            </div>

            <Display
              metricsSize={metricsSize}
              setMetricsSize={setMetricsSize}
              showDropdown={showDropdown}
              setShowDropdown={setShowDropdown}
              quickBuySize={quickBuySize}
              setQuickBuySize={setQuickBuySize}
              settings={settings}
              setSettings={setSettings}
              customRows={customRows}
              setCustomRows={setCustomRows}
            />
          </div>
        </div>

        <div className="flex-1 min-h-0 overflow-hidden">
          <PulseDash
            settings={settings}
            customRows={customRows}
            metricsSize={metricsSize}
            quickBuySize={quickBuySize}
            amount={amount}
            preset={preset}
            setPreset={setPreset}
            onPresetClick={handlePresetClick}
          />
        </div>
      </div>
    </div>
  );
};

export default Pulse;
