import { useState, useEffect } from 'react';
import { getStoredAutoFeeState, subscribeToAutoFeeUpdates, StoredAutoFeeState } from '../../utils/autoFeeStorage';
import { formatFeeAmount, getCongestionDescription } from '../../services/dynamicFeeService';
import { Tooltip } from '../../components/Tooltip';
import { useAutoFeesDisplay } from '../../hooks/useAutoFeesDisplay';

type FooterProps = {
  preset: number;
  setPreset: (preset: number) => void;
  onPresetClick: () => void;
};

export default function Footer({ preset, setPreset: _setPreset, onPresetClick }: FooterProps) {
  const [isAutoTrading, setIsAutoTrading] = useState<boolean>(() => {
    const saved = localStorage.getItem('auto_trading_enabled');
    return saved ? JSON.parse(saved) : false;
  });
  
  // Use auto fees display hook
  const { autoFeeState: hookAutoFeeState, calculateFeesForAmount } = useAutoFeesDisplay();
  
  const [autoFeeState, setAutoFeeState] = useState<StoredAutoFeeState | null>(() => {
    const stored = getStoredAutoFeeState();
    console.log('Footer: Initial auto fee state from storage:', stored);
    return stored || hookAutoFeeState;
  });

  // Listen for auto trading state changes
  useEffect(() => {
    const handleStorageChange = () => {
      const saved = localStorage.getItem('auto_trading_enabled');
      const newState = saved ? JSON.parse(saved) : false;
      setIsAutoTrading(newState);
    };

    // Check periodically since storage event doesn't fire on same page
    const checkInterval = setInterval(() => {
      const saved = localStorage.getItem('auto_trading_enabled');
      const newState = saved ? JSON.parse(saved) : false;
      if (newState !== isAutoTrading) {
        setIsAutoTrading(newState);
        
        // If auto trading was just enabled and we don't have fee data, calculate it
        if (newState && (!autoFeeState || !autoFeeState.priorityFee)) {
          // Get current amount from QuickBuy or use default
          const quickBuyAmount = localStorage.getItem('quickBuyAmount');
          const amount = quickBuyAmount ? parseFloat(quickBuyAmount) : 1;
          
          // Get active token exchange type
          const activePulseTokenStr = localStorage.getItem('activePulseToken');
          let exchangeType = 'pumpfun';
          if (activePulseTokenStr) {
            try {
              const activePulseToken = JSON.parse(activePulseTokenStr);
              exchangeType = activePulseToken.exchange_name?.toLowerCase() || 'pumpfun';
            } catch (error) {
              console.error('Error parsing active token:', error);
            }
          }
          
          console.log('Auto trading enabled, calculating initial fees...');
          calculateFeesForAmount(amount, exchangeType);
        }
      }
    }, 1000); // Check every second

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(checkInterval);
    };
  }, [isAutoTrading, autoFeeState, calculateFeesForAmount]);
  
  // Subscribe to auto fee updates
  useEffect(() => {
    const unsubscribe = subscribeToAutoFeeUpdates((state) => {
      console.log('Footer: Received auto fee update via subscription:', state);
      setAutoFeeState(state);
    });
    
    return unsubscribe;
  }, []);
  
  // Update state from hook
  useEffect(() => {
    if (hookAutoFeeState) {
      console.log('Footer: Updating auto fee state from hook:', hookAutoFeeState);
      setAutoFeeState(hookAutoFeeState);
    }
  }, [hookAutoFeeState]);
  
  // Calculate fees on mount if auto trading is enabled but no fees exist
  // Also recalculate if we detect preset values (0.0003, 0.0001)
  useEffect(() => {
    const shouldRecalculate = isAutoTrading && (
      !autoFeeState || 
      !autoFeeState.priorityFee ||
      // Check if we have the exact preset values which indicate stale data
      (autoFeeState.priorityFee === 0.0003 && autoFeeState.bribeAmount === 0.0001) ||
      (autoFeeState.priorityFee === 0.0002 && autoFeeState.bribeAmount === 0.00005) ||
      (autoFeeState.priorityFee === 0.001 && autoFeeState.bribeAmount === 0.0002)
    );
    
    if (shouldRecalculate) {
      // Get current amount from QuickBuy or use default
      const quickBuyAmount = localStorage.getItem('quickBuyAmount');
      const amount = quickBuyAmount ? parseFloat(quickBuyAmount) : 1;
      
      // Get active token exchange type
      const activePulseTokenStr = localStorage.getItem('activePulseToken');
      let exchangeType = 'pumpfun';
      if (activePulseTokenStr) {
        try {
          const activePulseToken = JSON.parse(activePulseTokenStr);
          exchangeType = activePulseToken.exchange_name?.toLowerCase() || 'pumpfun';
        } catch (error) {
          console.error('Error parsing active token:', error);
        }
      }
      
      console.log('Footer: Detected stale/missing fees, calculating fresh dynamic fees...');
      calculateFeesForAmount(amount, exchangeType);
    }
  }, [isAutoTrading, autoFeeState?.priorityFee, autoFeeState?.bribeAmount]); // Re-run when these change
  
  return (
    <div className="bg-[#0d0e10] text-gray-300 w-full h-10 px-4 flex items-center justify-between border-t border-gray-800">
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        {/* Preset */}
        <div className="flex items-center space-x-1">
          <button  onClick={onPresetClick} className="bg-indigo-600/80 hover:bg-indigo-600 text-xs font-medium py-1 px-2 rounded flex items-center">
            <svg viewBox="0 0 24 24" width="14" height="14" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
              <path d="M12 20h9"></path>
              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
            </svg>
            <span>PRESET {preset}</span>
          </button>
        </div>
        
        {/* Auto Trading Status Indicator */}
        {isAutoTrading && (
          <div className="flex items-center space-x-3 border-l border-gray-700 pl-4">
            {/* Auto Trading Badge */}
            <div className="flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-gradient-to-r from-emerald-500/10 to-green-500/10 border border-emerald-500/20 shadow-emerald-500/10 shadow-sm">
              <div className="relative flex items-center justify-center">
                <div className="absolute w-2 h-2 rounded-full bg-emerald-400 animate-ping opacity-75"></div>
                <div className="w-1.5 h-1.5 rounded-full bg-emerald-400"></div>
              </div>
              <span className="text-xs font-medium text-emerald-400">Auto Trading</span>
            </div>
            
            {/* Fee Details */}
            {autoFeeState && (
              <div className="flex items-center gap-3">
                {/* Loading State */}
                {autoFeeState.isLoading && !autoFeeState.priorityFee && (
                  <div className="flex items-center gap-2 text-xs text-gray-400">
                    <div className="w-3 h-3 border-2 border-gray-600 border-t-emerald-400 rounded-full animate-spin"></div>
                    <span>Calculating optimal fees...</span>
                  </div>
                )}
                
                {/* Show fees when available */}
                {autoFeeState.priorityFee > 0 && (
                  <>
                    {(() => {
                      console.log('Footer: Displaying fees:', {
                        priority: autoFeeState.priorityFee,
                        bribe: autoFeeState.bribeAmount,
                        formattedPriority: formatFeeAmount(autoFeeState.priorityFee, 4),
                        formattedBribe: formatFeeAmount(autoFeeState.bribeAmount, 4)
                      });
                      return null;
                    })()}
                {/* Priority Fee */}
                <Tooltip content="Transaction priority fee for faster execution" position="top">
                  <div className="flex items-center gap-1.5 group cursor-help hover:bg-blue-500/5 px-2 py-1 rounded-md transition-colors">
                    <svg className="w-3.5 h-3.5 text-blue-400 group-hover:text-blue-300 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <div className="flex flex-col">
                      <span className="text-[10px] text-gray-500 leading-none group-hover:text-gray-400">Priority</span>
                      <span className="text-xs font-medium text-gray-300 group-hover:text-white">{formatFeeAmount(autoFeeState.priorityFee, 4)} SOL</span>
                    </div>
                  </div>
                </Tooltip>
                
                {/* Separator */}
                <div className="w-px h-5 bg-gray-700"></div>
                
                {/* Bribe Fee */}
                <Tooltip content="Additional fee for pump.fun transactions" position="top">
                  <div className="flex items-center gap-1.5 group cursor-help hover:bg-purple-500/5 px-2 py-1 rounded-md transition-colors">
                    <svg className="w-3.5 h-3.5 text-purple-400 group-hover:text-purple-300 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div className="flex flex-col">
                      <span className="text-[10px] text-gray-500 leading-none group-hover:text-gray-400">Bribe</span>
                      <span className="text-xs font-medium text-gray-300 group-hover:text-white">{formatFeeAmount(autoFeeState.bribeAmount, 4)} SOL</span>
                    </div>
                  </div>
                </Tooltip>
                
                {/* Separator */}
                <div className="w-px h-5 bg-gray-700"></div>
                
                {/* Network Status */}
                {autoFeeState.networkStats && (
                  <Tooltip 
                    content={`TPS: ${autoFeeState.networkStats.averageTps.toFixed(0)} | Multiplier: ${autoFeeState.networkStats.congestionMultiplier.toFixed(1)}x`} 
                    position="top"
                  >
                    <div className={`flex items-center gap-1.5 px-3 py-1.5 rounded-full border cursor-help transition-all ${
                      autoFeeState.networkStats.congestionMultiplier >= 3 
                        ? 'bg-gradient-to-r from-red-500/10 to-red-600/10 border-red-500/30 hover:border-red-400/40 shadow-red-500/10' 
                        : autoFeeState.networkStats.congestionMultiplier >= 2 
                        ? 'bg-gradient-to-r from-orange-500/10 to-orange-600/10 border-orange-500/30 hover:border-orange-400/40 shadow-orange-500/10'
                        : autoFeeState.networkStats.congestionMultiplier >= 1.5
                        ? 'bg-gradient-to-r from-yellow-500/10 to-yellow-600/10 border-yellow-500/30 hover:border-yellow-400/40 shadow-yellow-500/10'
                        : 'bg-gradient-to-r from-green-500/10 to-green-600/10 border-green-500/30 hover:border-green-400/40 shadow-green-500/10'
                    } hover:shadow-lg`}>
                      <svg className={`w-3.5 h-3.5 ${
                        autoFeeState.networkStats.congestionMultiplier >= 3 
                          ? 'text-red-400' 
                          : autoFeeState.networkStats.congestionMultiplier >= 2 
                          ? 'text-orange-400'
                          : autoFeeState.networkStats.congestionMultiplier >= 1.5
                          ? 'text-yellow-400'
                          : 'text-green-400'
                      }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <div className={`w-1.5 h-1.5 rounded-full ${
                        autoFeeState.networkStats.congestionMultiplier >= 3 
                          ? 'bg-red-400' 
                          : autoFeeState.networkStats.congestionMultiplier >= 2 
                          ? 'bg-orange-400'
                          : autoFeeState.networkStats.congestionMultiplier >= 1.5
                          ? 'bg-yellow-400'
                          : 'bg-green-400'
                      } ${autoFeeState.networkStats.congestionMultiplier >= 2 ? 'animate-pulse' : ''}`}></div>
                      <span className={`text-xs font-medium ${
                        autoFeeState.networkStats.congestionMultiplier >= 3 
                          ? 'text-red-400' 
                          : autoFeeState.networkStats.congestionMultiplier >= 2 
                          ? 'text-orange-400'
                          : autoFeeState.networkStats.congestionMultiplier >= 1.5
                          ? 'text-yellow-400'
                          : 'text-green-400'
                      }`}>
                        {getCongestionDescription(autoFeeState.networkStats.congestionMultiplier)}
                      </span>
                    </div>
                  </Tooltip>
                )}
                </>)}
              </div>
            )}
          </div>
        )}

        {/* PnL Tracker - Only show when auto trading is NOT enabled */}
        {!isAutoTrading && (
          <div className="flex items-center space-x-1 border-l border-gray-700 pl-4">
            <svg viewBox="0 0 24 24" width="14" height="14" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="20" x2="12" y2="10"></line>
              <line x1="18" y1="20" x2="18" y2="4"></line>
              <line x1="6" y1="20" x2="6" y2="16"></line>
            </svg>
            <span className="text-xs font-medium">PnL Tracker</span>
          </div>
        )}
      </div>
      
      {/* Right Section */}
      <div className="flex items-center space-x-4">
        {/* Twitter */}
        <button className="text-gray-400 hover:text-gray-200 transition-colors">
          <svg viewBox="0 0 24 24" width="18" height="18" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
            <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
          </svg>
        </button>
        
        {/* Discord */}
        <button className="text-gray-400 hover:text-gray-200 transition-colors">
          <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path d="M18.9327 4.93652C17.6012 4.32861 16.1645 3.8784 14.6525 3.60607C14.4361 4.01117 14.1803 4.56384 14.0033 5.00034C12.4018 4.74679 10.81 4.74679 9.23726 5.00034C9.06023 4.56384 8.79967 4.01117 8.58182 3.60607C7.06833 3.8784 5.63015 4.32957 4.29871 4.93749C1.84336 8.84131 1.12766 12.6423 1.48732 16.3841C3.25335 17.7144 4.96877 18.5571 6.65731 19.1037C7.07316 18.5239 7.44268 17.9057 7.7607 17.2508C7.1909 17.0305 6.65007 16.7618 6.14373 16.4428C6.32065 16.3132 6.49272 16.1779 6.65875 16.0378C9.89021 17.5565 13.3739 17.5565 16.5638 16.0378C16.7308 16.1779 16.9029 16.3132 17.0788 16.4428C16.5715 16.7628 16.0297 17.0315 15.4589 17.2518C15.7769 17.9057 16.1454 18.5248 16.5623 19.1046C18.2518 18.5581 19.9682 17.7154 21.7342 16.3841C22.1583 12.0534 21.0021 8.28622 18.9327 4.93652ZM8.20167 14.0009C7.16252 14.0009 6.31379 13.0534 6.31379 11.9015C6.31379 10.7495 7.14287 9.80101 8.20167 9.80101C9.26047 9.80101 10.1092 10.7485 10.0896 11.9015C10.0906 13.0534 9.26047 14.0009 8.20167 14.0009ZM15.0199 14.0009C13.9807 14.0009 13.132 13.0534 13.132 11.9015C13.132 10.7495 13.9611 9.80101 15.0199 9.80101C16.0787 9.80101 16.9274 10.7485 16.9078 11.9015C16.9078 13.0534 16.0787 14.0009 15.0199 14.0009Z" />
          </svg>
        </button>
      </div>
    </div>
  );
}