import React, { useState, useEffect, useRef, MouseEvent, useMemo } from 'react';
import {usePulseData} from '@/hooks/usePulseData';
import { useWebSocketPulseData } from '@/hooks/useWebSocketPulseData';
import Display from './Options/Display';
import QuickBuy from './Options/QuickBuy';
import { activityService } from '@/services/activityService';
import {
  Users,
  User,
  Settings,
  TrendingUp,
  Activity,
  Zap,
  SlidersHorizontal,
  Twitter,
  Globe,
  Send,
  Shield,
  Target,
  Package,
  Copy,
  Clipboard,
  AlertTriangle,
  ChevronRight,
  Wallet,
  History,
  BarChart,
  ArrowUp,
  ArrowDown,
  Loader,
  ZapIcon
} from 'lucide-react';
import clsx from "clsx";
import TradingSettingsModal from './TradingSettiingsModal';
import Footer from '../Footer/Footer';
import { homeAPI } from '@/utils/api';
import { useNavigate } from 'react-router-dom';
import TokenAge from '@/components/TokenAge';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import {
  getTokenBalance,
  getSwapForExchange,
  SwapRequest,
  BalanceRequest,
  cancelBalanceRequest
} from '../../api/solana_api';
import { getPrivySolanaWalletInfo, getCachedWalletId } from '../../api/privy_api';
import { showSwapSuccessToast, showSwapErrorToast, showSwapInfoToast } from '../../utils/swapToast';
import { getDefaultSolanaWalletAddress, getDefaultSolanaWalletInfo } from '../../utils/walletUtils';
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import localizedFormat from 'dayjs/plugin/localizedFormat';
import { formatDistanceToNow } from 'date-fns';

import {toast} from 'react-toastify'
import './Pulse.css';

// Use dayjs plugins
dayjs.extend(relativeTime);
dayjs.extend(localizedFormat);

// Enhanced custom CSS for professional animations and effects with theme colors
const customStyles = `
  @keyframes pulse-premium {
    0%, 100% {
      box-shadow: 0 8px 32px rgba(16, 185, 129, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.08);
      transform: translateY(0px);
    }
    50% {
      box-shadow: 0 16px 64px rgba(16, 185, 129, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.12);
      transform: translateY(-2px);
    }
  }

  @keyframes slide-in-elegant {
    0% {
      transform: translateY(-24px) scale(0.96);
      opacity: 0;
      filter: blur(4px);
    }
    60% {
      transform: translateY(2px) scale(1.01);
      opacity: 0.9;
      filter: blur(1px);
    }
    100% {
      transform: translateY(0) scale(1);
      opacity: 1;
      filter: blur(0px);
    }
  }

  @keyframes shimmer-premium {
    0% {
      transform: translateX(-100%) skewX(-15deg);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateX(200%) skewX(-15deg);
      opacity: 0;
    }
  }

  @keyframes glow-pulse {
    0%, 100% {
      filter: drop-shadow(0 0 12px rgba(16, 185, 129, 0.4));
    }
    50% {
      filter: drop-shadow(0 0 24px rgba(16, 185, 129, 0.7));
    }
  }

  @keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-pulse-premium {
    animation: pulse-premium 3.5s ease-in-out infinite;
  }

  .animate-slide-in-elegant {
    animation: slide-in-elegant 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-shimmer-premium {
    animation: shimmer-premium 3s infinite;
  }

  .animate-glow-pulse {
    animation: glow-pulse 2.5s ease-in-out infinite;
  }

  .animate-gradient-shift {
    animation: gradient-shift 8s ease infinite;
    background-size: 200% 200%;
  }

  .glass-morphism-premium {
    background: rgba(20, 20, 22, 0.85);
    backdrop-filter: blur(32px) saturate(180%);
    -webkit-backdrop-filter: blur(32px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  .premium-gradient-bg {
    background: linear-gradient(135deg,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(30, 41, 59, 0.85) 15%,
      rgba(51, 65, 85, 0.75) 35%,
      rgba(30, 41, 59, 0.85) 65%,
      rgba(15, 23, 42, 0.95) 100%);
  }

  .theme-gradient-primary {
    background: linear-gradient(135deg,
      rgba(16, 185, 129, 0.1) 0%,
      rgba(5, 150, 105, 0.08) 25%,
      rgba(4, 120, 87, 0.06) 50%,
      rgba(5, 150, 105, 0.08) 75%,
      rgba(16, 185, 129, 0.1) 100%);
  }

  .theme-gradient-secondary {
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.08) 0%,
      rgba(37, 99, 235, 0.06) 25%,
      rgba(29, 78, 216, 0.04) 50%,
      rgba(37, 99, 235, 0.06) 75%,
      rgba(59, 130, 246, 0.08) 100%);
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = customStyles;
  document.head.appendChild(styleSheet);
}

interface TokenType {
  id?: string;
  address: string;
  name: string;
  symbol: string;
  network?: string;
  [key: string]: any;
}

interface StatsType {
  holders: string;
  buys: number;
  sells: number;
  proTraders: string;
  devMigrations: string;
  top10Holders: string;
  devHolding: string;
  snipers: string;
  insiders: string;
  bundlers: string;
  dexPaid: string;
}

interface SocialsType {
  telegram: string;
  website: string;
}

interface CustomRowsType {
  marketCap: boolean;
  volume: boolean;
  tx: boolean;
  socials: boolean;
  holders: boolean;
  proTraders: boolean;
  devMigrations: boolean;
}

interface SettingsType {
  circleImages: boolean;
  progressBar: boolean;
  greyButtons: boolean;
 
}

interface CryptoCardProps {
  imageUrl: string;
  exchange_logo: string;
  name: string;
  symbol: string;
  age: string;
  createdAt: string | null; // Add createdAt field
  marketCap: number;
  volume: number;
  txCount: string;
  price: number;
  bonding: number;
  address: string;
  network: string; // ✅ Added network field to CryptoCardProps
  socials: SocialsType;
  stats: StatsType;
  amount?: string;
  quickBuySize: string;
  supply: string;
  liquidity: string;
  customRows: CustomRowsType;
  settings: SettingsType;
  token: TokenType;
  metricsSize: string;
  holders_count: string | number; // Add holders_count field
  trades_24h: number; // Add trades_24h field
  bondingPercentage?: number; // Add bondingPercentage field (optional for bonded)
}

// Define a custom event for amount updates
const AMOUNT_UPDATE_EVENT = 'quickBuyAmountUpdate';

// Global state to store the current amount and ensure consistency across components
let globalCurrentAmount = '';

// Initialize global amount as empty (no localStorage loading)
// Users must enter amount manually each session

// Create a custom hook for sharing the QuickBuy amount
const useQuickBuyAmount = () => {
  const [amount, setAmount] = useState('');
  const [validAmount, setValidAmount] = useState('');

  useEffect(() => {
    // Initialize with current global amount (start empty)
    const initializeAmount = () => {
      if (globalCurrentAmount) {
        setAmount(globalCurrentAmount);
        if (globalCurrentAmount !== '' && !isNaN(parseFloat(globalCurrentAmount)) && parseFloat(globalCurrentAmount) > 0) {
          setValidAmount(globalCurrentAmount);
        }
      }
    };

    // Define handler for amount updates
    const handleAmountUpdate = (event: CustomEvent) => {
      const newAmount = event.detail.amount;
      const isValid = event.detail.isValid;
      
      // Update global state
      globalCurrentAmount = newAmount;
      
      // Always update the input amount for display
      setAmount(newAmount);
      
      // Update valid amount based on validation and content
      if (newAmount === '') {
        setValidAmount('');
      } else if (isValid && newAmount !== '' && !isNaN(parseFloat(newAmount)) && parseFloat(newAmount) > 0) {
        setValidAmount(newAmount);
      }
      // If invalid but not empty, keep the previous valid amount
    };
    
    // Initialize amount on mount
    initializeAmount();
    
    // Listen for amount updates from QuickBuy component
    window.addEventListener(AMOUNT_UPDATE_EVENT, handleAmountUpdate as EventListener);
    
    return () => {
      window.removeEventListener(AMOUNT_UPDATE_EVENT, handleAmountUpdate as EventListener);
    };
  }, []);

  return { amount: validAmount };
 };


const CryptoCard: React.FC<CryptoCardProps> = ({
  imageUrl,
  name: _name,
  token,
  symbol: _symbol,
  age,
  createdAt, // Add createdAt prop
  marketCap,
  volume,
  bonding,
  txCount,
  address,
  stats,
  quickBuySize,
  amount: initialAmount,
  exchange_logo,
  customRows,
  settings,
  metricsSize,
  holders_count,
  trades_24h,
  bondingPercentage
}) => {
  const navigate = useNavigate();
  const [isExecutingBuy, setIsExecutingBuy] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const [buttonHovered, setButtonHovered] = useState(false);
  
  // Use the shared QuickBuy amount
  const { amount } = useQuickBuyAmount();
  
  // Display a friendly "0" instead of "0.00"
  const formattedAmount = parseFloat(amount) > 0 
    ? parseFloat(amount).toFixed(2) 
    : '0';
  
  // Privy hooks for wallet access
  const { authenticated, user } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();

  const {setActiveToken}= usePulseData();

  // Ensure all data is properly formatted to prevent rendering objects directly
  const safeName = typeof _name === 'string' ? _name : '';
  const safeSymbol = typeof _symbol === 'string' ? _symbol : '';
  const safeAddress = typeof address === 'string' ? address : '';
  const safeBonding = typeof bonding === 'number' ? bonding : 0;
  const safeMarketCap = typeof marketCap === 'number' ? marketCap : 0;
  const safeVolume = typeof volume === 'number' ? volume : 0;
  const safeTxCount = typeof txCount === 'string' ? txCount : '';
  const safeHoldersCount = typeof holders_count === 'string' || typeof holders_count === 'number' ? holders_count : 0;
  const safeTrades24h = typeof trades_24h === 'number' ? trades_24h : 0;
  const safeBondingPercentage = typeof bondingPercentage === 'number' ? bondingPercentage : 0;
  
  // Ensure stats object is valid
  const safeStats = {
    holders: typeof stats?.holders === 'string' ? stats.holders : '',
    buys: typeof stats?.buys === 'number' ? stats.buys : 0,
    sells: typeof stats?.sells === 'number' ? stats.sells : 0,
    proTraders: typeof stats?.proTraders === 'string' ? stats.proTraders : '',
    devMigrations: typeof stats?.devMigrations === 'string' ? stats.devMigrations : '',
    top10Holders: typeof stats?.top10Holders === 'string' ? stats.top10Holders : '',
    devHolding: typeof stats?.devHolding === 'string' ? stats.devHolding : '',
    snipers: typeof stats?.snipers === 'string' ? stats.snipers : '',
    insiders: typeof stats?.insiders === 'string' ? stats.insiders : '',
    bundlers: typeof stats?.bundlers === 'string' ? stats.bundlers : '',
    dexPaid: typeof stats?.dexPaid === 'string' ? stats.dexPaid : '',
  };

  // Get first letter of token name or symbol for fallback
  const getTokenInitial = () => {
    if (safeName && safeName.length > 0) {
      return safeName.charAt(0).toUpperCase();
    } else if (safeSymbol && safeSymbol.length > 0) {
      return safeSymbol.charAt(0).toUpperCase();
    }
    return "T"; // Default fallback
  };

  // Generate a consistent background color based on token address
  const getBackgroundColor = () => {
    if (!safeAddress) return "hsl(210, 70%, 50%)"; // Default blue
    
    // Simple hash function to generate a consistent color
    const hash = safeAddress.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);
    
    // Generate hue between 0-360 degrees
    const hue = Math.abs(hash) % 360;
    
    return `hsl(${hue}, 70%, 50%)`;
  };

  const handleImageLoad = () => {
    setIsImageLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setIsImageLoading(false);
  };

  const handleRowClick = (e: MouseEvent<HTMLDivElement>, token: TokenType) => {
    setActiveToken(e, token);
    navigate(`/trade/${token.address}`);
  };

  // Get Solana wallet address from defaultWallets localStorage (same as Pulse trade page)
  const getSolanaWalletAddress = () => {
    if (!authenticated) {
      return null;
    }

    // First try to get from defaultWallets localStorage
    try {
      const storedDefaults = localStorage.getItem('defaultWallets');
      if (storedDefaults) {
        const defaultWallets = JSON.parse(storedDefaults);
        if (defaultWallets.solana) {
          console.log('Using default Solana wallet from localStorage:', defaultWallets.solana);
          return defaultWallets.solana;
        }
      }
    } catch (error) {
      console.error('Error reading defaultWallets from localStorage:', error);
    }

    // Fallback to Privy hooks if no default wallet set
    if (solanaWallets.length === 0) {
      return null;
    }

    const solanaWallet = solanaWallets[0];
    console.log('Using fallback Solana wallet from Privy:', solanaWallet?.address);
    return solanaWallet?.address || null;
  };

  // Get Solana wallet info for swap using defaultWallets localStorage (same as Pulse trade page)
  const getSolanaWalletInfo = async () => {
    if (!authenticated || !user?.id) {
      return null;
    }

    // First try to get wallet address from defaultWallets localStorage
    let walletAddress: string | null = null;
    try {
      const storedDefaults = localStorage.getItem('defaultWallets');
      if (storedDefaults) {
        const defaultWallets = JSON.parse(storedDefaults);
        if (defaultWallets.solana) {
          walletAddress = defaultWallets.solana;
          console.log('Using default Solana wallet from localStorage for swap:', walletAddress);
        }
      }
    } catch (error) {
      console.error('Error reading defaultWallets from localStorage:', error);
    }

    // Fallback to Privy hooks if no default wallet set
    if (!walletAddress) {
      if (solanaWallets.length === 0) {
        return null;
      }

      const solanaWallet = solanaWallets[0];
      if (!solanaWallet.address) {
        return null;
      }

      walletAddress = solanaWallet.address;
      console.log('Using fallback Solana wallet from Privy for swap:', walletAddress);
    }

    // Try to get cached wallet ID first
    const cachedWalletId = getCachedWalletId(user.id, walletAddress);
    if (cachedWalletId) {
      return {
        address: walletAddress,
        id: cachedWalletId
      };
    }

    // Fallback to Privy API
    try {
      const privyResponse = await getPrivySolanaWalletInfo(user.id);
      if (privyResponse.success && privyResponse.data) {
        if (privyResponse.data.address === walletAddress) {
          return {
            address: walletAddress,
            id: privyResponse.data.walletId
          };
        }
      }
    } catch (error) {
      console.error('Error fetching wallet ID:', error);
    }

    // Fallback ID
    const fallbackId = `solana_${walletAddress.slice(0, 8)}_${walletAddress.slice(-8)}`;
    return {
      address: walletAddress,
      id: fallbackId
    };
  };

  // Execute quick buy function
  const executeQuickBuy = async () => {
    if (!authenticated) {
      showSwapErrorToast('Please connect your wallet first');
      return;
    }

    // Enhanced validation with better error messages
    if (!amount) {
      showSwapErrorToast('Please enter an amount in QuickBuy');
      return;
    }
    
    const amountValue = parseFloat(amount);
    if (isNaN(amountValue) || amountValue <= 0) {
      showSwapErrorToast('Please enter a valid positive amount');
      return;
    }

    if (!token?.exchange_name || !['PumpSwap', 'PumpFun', 'LaunchLab'].includes(token.exchange_name)) {
      showSwapErrorToast('Unsupported exchange');
      return;
    }

    const walletInfo = await getSolanaWalletInfo();
    if (!walletInfo) {
      showSwapErrorToast('No Solana wallet found. Please ensure you have a Solana wallet connected.');
      return;
    }

    // Validate SOL balance
    try {
      const balanceRequest: BalanceRequest = {
        walletAddress: walletInfo.address
      };

      const balanceResponse = await getTokenBalance(balanceRequest);
      if (balanceResponse?.success) {
        const solBalance = parseFloat(balanceResponse.data.solBalance);
        const requestedAmount = amountValue;

        if (solBalance < requestedAmount) {
          const shortfall = requestedAmount - solBalance;
          showSwapErrorToast(
            `Insufficient SOL balance. You have ${solBalance.toFixed(4)} SOL, ` +
            `but trying to spend ${requestedAmount} SOL. Shortfall: ${shortfall.toFixed(4)} SOL.`
          );
          return;
        }
      }
    } catch (error) {
      console.error('Error checking balance:', error);
      showSwapErrorToast('Failed to check wallet balance');
      return;
    }

    setIsExecutingBuy(true);

    try {
      // Use default preset settings for quick buy
      const defaultPresetSettings = {
        slippage: 10,
        priority: 0.0001,
        bribe: 0.00005,
        mevMode: 'Off'
      };

      const swapRequest: SwapRequest = {
        tokenAddress: (token.address || token.id) as string,
        poolAddress: token.pool_address || '',
        dexType: token.exchange_name.toLowerCase(),
        amount: amountValue,
        direction: 'buy',
        slippage: defaultPresetSettings.slippage / 100,
        walletAddress: walletInfo.address,
        walletId: walletInfo.id,
        mevProtection: defaultPresetSettings.mevMode !== 'Off',
        bribeAmount: defaultPresetSettings.bribe,
        priorityLevel: 'high',
        priorityFee: defaultPresetSettings.priority
      };

      const result = await getSwapForExchange(token.exchange_name, swapRequest);

      if (result.success) {
        const transactionSignature = result.data.signature || result.data.transactionHash || '';
        const tokenSymbol = token.symbol || 'TOKEN';

        if (transactionSignature) {
          showSwapSuccessToast(
            'buy',
            tokenSymbol,
            transactionSignature,
            result.data.solscanUrl
          );
        } else {
          showSwapInfoToast(`Successfully bought ${tokenSymbol}`);
        }

        // Trigger balance refresh after successful transaction
        setTimeout(() => {
          console.log('Triggering balance refresh after successful quick buy');
          // Dispatch custom event to notify QuickBuy component to refresh balance
          window.dispatchEvent(new CustomEvent('quickBuySuccess', {
            detail: {
              tokenSymbol,
              transactionSignature,
              timestamp: Date.now()
            }
          }));
        }, 2000); // 2 second delay to allow blockchain confirmation

      } else {
        const errorMessage = result.error || 'Swap failed for unknown reason';
        showSwapErrorToast(errorMessage);
      }

    } catch (error) {
      console.error('Quick buy execution error:', error);
      showSwapErrorToast(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsExecutingBuy(false);
    }
  };

  const handleZapClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent row click
    executeQuickBuy();
  };
  const formatNumber = (num: number | null | undefined): string => {
    if (num === null || num === undefined || isNaN(num)) return "--";
    if (num >= 1e12) return "$" + (num / 1e12).toFixed(2) + "T"; 
    if (num >= 1e9) return "$" + (num / 1e9).toFixed(2) + "B";   
    if (num >= 1e6) return "$" + (num / 1e6).toFixed(2) + "M";   
    if (num >= 1e3) return "$" + (num / 1e3).toFixed(2) + "K";   
    return num.toLocaleString(); 
  };
  const formatAddress = (address: string | undefined | null) => {
    if (!address || typeof address !== 'string') return 'N/A';
    return `${address.slice(0, 3)}...${address.slice(-4)}`;
  };
  const buyPercent = Math.round((safeStats.buys / (safeStats.buys + safeStats.sells)) * 100) || 0;
  
  // Function to toggle between circle and rectangle mode
  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(safeAddress);
  
    toast(
      <div className="flex items-center gap-3">
        <Clipboard className="text-indigo-400 w-5 h-5" />
        <span>Address copied to clipboard</span>
      </div>,
      {
        autoClose: 2000, // disappears after 2 seconds
        closeButton: true,
        hideProgressBar: true,
        className: 'bg-[#1f1f1f] text-white font-medium border border-gray-700 rounded-lg shadow-md',
        icon: false,
      }
    );
  };
  
  const StatPill = ({ icon: Icon, value, color }: {
    icon?: any,
    value: string,
    color: 'green' | 'yellow' | 'red' | 'blue' | 'purple'
  }) => {
    const colorClasses = {
      green: "bg-emerald-500/10 text-emerald-400 border-emerald-500/20",
      yellow: "bg-yellow-500/10 text-yellow-400 border-yellow-500/20",
      red: "bg-red-500/10 text-red-400 border-red-500/20",
      blue: "bg-blue-500/10 text-blue-400 border-blue-500/20",
      purple: "bg-purple-500/10 text-purple-400 border-purple-500/20"
    };

    return (
      <div className={`px-2 py-1.5 rounded-lg border backdrop-blur-sm ${colorClasses[color]} flex items-center justify-center gap-1`}>
        {Icon && <Icon size={12} />}
        <span className="font-medium text-xs">{value}</span>
      </div>
    );
  };

  const zapBoxStyles = clsx(
    "absolute bg-[#1F1F1F] border border-gray-600 flex items-center gap-1 px-2 py-1 rounded-full text-lg",
    {
      "right-2 bottom-2": quickBuySize === "small",
      "right-3 bottom-3 scale-110": quickBuySize === "large",
      "left-auto right-4 bottom-4 rounded-md px-4 py-2 text-lg": quickBuySize === "mega",
    }
  );
  
  // Enhanced animation for new tokens with introduction effect
  const isNewToken = token.category === 'new';
  const isBondingToken = token.category === 'bonding';
  const isBondedToken = token.category === 'bonded';
  const animationClass = isNewToken ? 'animate-slide-in-elegant' : '';
  
  // App theme-based token card styling with professional glass morphism
  const getCategoryStyles = () => {
    if (isNewToken) {
      return {
        background: 'linear-gradient(135deg, rgba(0, 255, 189, 0.03), rgba(24, 28, 32, 0.85), rgba(20, 20, 22, 0.95))',
        borderColor: 'rgba(0, 255, 189, 0.15)',
        shadowColor: 'rgba(0, 255, 189, 0.1)'
      };
    } else if (isBondingToken) {
      return {
        background: 'linear-gradient(135deg, rgba(2, 95, 218, 0.03), rgba(24, 28, 32, 0.85), rgba(20, 20, 22, 0.95))',
        borderColor: 'rgba(2, 95, 218, 0.15)',
        shadowColor: 'rgba(2, 95, 218, 0.1)'
      };
    } else if (isBondedToken) {
      return {
        background: 'linear-gradient(135deg, rgba(0, 255, 0, 0.03), rgba(24, 28, 32, 0.85), rgba(20, 20, 22, 0.95))',
        borderColor: 'rgba(0, 255, 0, 0.15)',
        shadowColor: 'rgba(0, 255, 0, 0.1)'
      };
    }
    return {
      background: 'linear-gradient(135deg, rgba(24, 28, 32, 0.9), rgba(20, 20, 22, 0.8), rgba(24, 28, 32, 0.7))',
      borderColor: 'rgba(255, 255, 255, 0.08)',
      shadowColor: 'rgba(0, 0, 0, 0.3)'
    };
  };
  
  const categoryStyles = getCategoryStyles();
  
  return (
    <div
      onClick={(e) => handleRowClick(e, token)}
      className={`group relative pulse-card glass-morphism-premium rounded-2xl p-5 m-2 cursor-pointer overflow-hidden ${animationClass}`}
      style={{
        background: categoryStyles.background,
        backdropFilter: 'blur(40px) saturate(180%)',
        WebkitBackdropFilter: 'blur(40px) saturate(180%)',
        border: `1px solid ${categoryStyles.borderColor}`,
        boxShadow: `0 16px 32px ${categoryStyles.shadowColor}, inset 0 1px 0 rgba(255, 255, 255, 0.08)`,
        transform: 'translateZ(0)',
        willChange: 'transform'
      }}
    >
      {/* Simplified hover effect for better performance */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-[#00FFBD]/[0.03] via-transparent to-[#025FDA]/[0.03] opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      
      {/* Improved Buy Button */}
      <div
        onClick={!isExecutingBuy && amount && parseFloat(amount) > 0 ? handleZapClick : undefined}
        onMouseEnter={() => setButtonHovered(true)}
        onMouseLeave={() => setButtonHovered(false)}
        className={`absolute right-4 bottom-4 buy-button-hover-active ${
          isExecutingBuy ? 'cursor-wait' : !amount || parseFloat(amount) <= 0 ? 'cursor-not-allowed opacity-70' : 'cursor-pointer'
        }`}
        title={isExecutingBuy ? "Executing Buy..." : !amount || parseFloat(amount) <= 0 ? "Enter an amount first" : "Quick Buy"}
        style={{ zIndex: 50 }}
      >
        <div
          className={`relative bg-gradient-to-r border flex items-center gap-2 px-3 py-2 rounded-md text-xs font-semibold transition-all duration-300 transform-gpu backdrop-blur-sm overflow-hidden shadow-lg ${
            isExecutingBuy
              ? "from-amber-500 to-orange-500 text-white border-amber-400/60 shadow-lg shadow-amber-500/40"
              : !amount || parseFloat(amount) <= 0
              ? "from-slate-700 to-slate-800 text-slate-300 border-slate-600/40"
              : settings.greyButtons
              ? "from-slate-600 to-slate-700 text-slate-100 border-slate-500/60 hover:from-slate-500 hover:to-slate-600 hover:shadow-xl hover:shadow-slate-500/30 hover:scale-105"
              : "from-emerald-500 to-teal-600 text-white border-emerald-400/60 hover:from-emerald-400 hover:to-teal-500 hover:shadow-xl hover:shadow-emerald-500/30 hover:scale-105"
          }`}
          style={{
            background: isExecutingBuy
              ? 'linear-gradient(to right, rgb(245 158 11), rgb(249 115 22))'
              : !amount || parseFloat(amount) <= 0
              ? 'linear-gradient(to right, rgb(51 65 85), rgb(30 41 59))'
              : settings.greyButtons
              ? 'linear-gradient(to right, rgb(71 85 105), rgb(51 65 85))'
              : buttonHovered
              ? 'linear-gradient(to right, rgb(5, 150, 105), rgb(13, 148, 136))'
              : 'linear-gradient(to right, rgb(16 185 129), rgb(13 148 136))',
            transform: buttonHovered && amount && parseFloat(amount) > 0 && !isExecutingBuy ? 'scale(1.05)' : 'scale(1)',
            boxShadow: buttonHovered && amount && parseFloat(amount) > 0 && !isExecutingBuy 
              ? '0 10px 25px -5px rgba(16, 185, 129, 0.25), 0 8px 10px -6px rgba(16, 185, 129, 0.1)'
              : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
          }}
        >

          {/* Enhanced shimmer effect on hover */}
          {amount && parseFloat(amount) > 0 && !isExecutingBuy && buttonHovered && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_1s_ease-in-out]"></div>
          )}

          {/* Icon with enhanced animation */}
          <div className="relative z-10">
            {isExecutingBuy ? (
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white/90 border-t-transparent"></div>
            ) : (
              <Zap
                size={18}
                fill="currentColor"
                className={`transition-all duration-300 ${amount && parseFloat(amount) > 0 ? (buttonHovered ? 'scale-125 text-white/95' : '') : ''}`}
                strokeWidth={2}
                style={{
                  filter: buttonHovered && amount && parseFloat(amount) > 0 ? 'drop-shadow(0 0 3px rgba(255,255,255,0.5))' : 'none'
                }}
              />
            )}
          </div>
          
          {/* Text with improved formatting - display amount with max 4 decimal places */}
          <span className="relative z-10 font-medium tracking-wide">
            {isExecutingBuy ? 'Processing...' : (
              <>
                <span className={`${amount && parseFloat(amount) > 0 ? 'text-white' : ''} transition-all duration-300`} style={{
                  textShadow: buttonHovered && amount && parseFloat(amount) > 0 ? '0 0 10px rgba(255,255,255,0.5)' : 'none'
                }}>
                  {amount && parseFloat(amount) > 0 ? (
                    // Smart decimal formatting based on amount size
                    (() => {
                      const num = parseFloat(amount);
                      if (num < 0.000001) {
                        // For very small amounts, show up to 12 significant digits
                        return num.toFixed(18).replace(/0+$/, '').replace(/\.$/, '');
                      } else if (num < 0.001) {
                        // For small amounts, show up to 8 decimal places
                        return num.toFixed(8).replace(/0+$/, '').replace(/\.$/, '');
                      } else if (num < 1) {
                        // For amounts less than 1, show up to 6 decimal places
                        return num.toFixed(6).replace(/0+$/, '').replace(/\.$/, '');
                      } else {
                        // For amounts >= 1, show up to 4 decimal places
                        return num.toFixed(4).replace(/0+$/, '').replace(/\.$/, '');
                      }
                    })()
                  ) : '0'}
                </span>
                <span className="text-xs opacity-90 ml-1 font-medium">SOL</span>
              </>
            )}
          </span>
        </div>
      </div>

      <div className="flex items-start gap-3">
        {/* Optimized Avatar Section */}
        <div className="relative">
          <div className="relative w-20 h-20">
            {/* Enhanced Bonding Visualization */}
            {!settings.progressBar && (
              settings.circleImages ? (
                // Circular bonding ring with enhanced glow effect
                <div className="absolute inset-0 rounded-full pointer-events-none z-10">
                  {/* Background ring */}
                  <div
                    className="absolute inset-0 rounded-full opacity-60"
                    style={{
                      background: 'conic-gradient(from 0deg, #1e293b 0deg, #334155 360deg)',
                      maskImage: 'radial-gradient(circle, transparent 65%, black 67%, black 96%, transparent 100%)',
                      WebkitMaskImage: 'radial-gradient(circle, transparent 65%, black 67%, black 96%, transparent 100%)',
                    }}
                  />
                  {/* Progress ring with enhanced glow */}
                  <div
                    className="absolute inset-0 rounded-full"
                    style={{
                      background: `conic-gradient(
                        transparent 0deg 135deg,
                        #10b981 135deg ${135 + (safeBonding / 100) * 360}deg,
                        transparent ${135 + (safeBonding / 100) * 360}deg 360deg
                      )`,
                      maskImage: 'radial-gradient(circle, transparent 65%, black 67%, black 96%, transparent 100%)',
                      WebkitMaskImage: 'radial-gradient(circle, transparent 65%, black 67%, black 96%, transparent 100%)',
                      filter: 'drop-shadow(0 0 8px rgba(16, 185, 129, 0.6))'
                    }}
                  />
                </div>
              ) : (
                // Enhanced rectangular bonding border with improved animations
                <>
                  {/* Static background borders with glow */}
                  <div className="absolute bottom-0 right-0 h-1 bg-slate-600/60 rounded-lg w-full z-10 shadow-inner" />
                  <div className="absolute bottom-0 left-0 w-1 bg-slate-600/60 rounded-lg h-full z-10 shadow-inner" />
                  <div className="absolute top-0 left-0 h-1 bg-slate-600/60 rounded-lg w-full z-10 shadow-inner" />
                  <div className="absolute top-0 right-0 w-1 bg-slate-600/60 rounded-lg h-full z-10 shadow-inner" />

                  {/* Progressive green border with enhanced glow and animation */}
                  <div
                    className="absolute bottom-0 right-0 h-1 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-lg z-10 shadow-[0_0_8px_rgba(16,185,129,0.8)] transition-all duration-700"
                    style={{ width: `${Math.min(safeBonding, 25) * 4}%` }}
                  />
                  {safeBonding > 25 && (
                    <div
                      className="absolute bottom-0 left-0 w-1 bg-gradient-to-t from-emerald-400 to-emerald-500 rounded-lg z-10 shadow-[0_0_8px_rgba(16,185,129,0.8)] transition-all duration-700"
                      style={{ height: `${Math.min(safeBonding - 25, 25) * 4}%` }}
                    />
                  )}
                  {safeBonding > 50 && (
                    <div
                      className="absolute top-0 left-0 h-1 bg-gradient-to-l from-emerald-400 to-emerald-500 rounded-lg z-10 shadow-[0_0_8px_rgba(16,185,129,0.8)] transition-all duration-700"
                      style={{ width: `${Math.min(safeBonding - 50, 25) * 4}%` }}
                    />
                  )}
                  {safeBonding > 75 && (
                    <div
                      className="absolute top-0 right-0 w-1 bg-gradient-to-b from-emerald-400 to-emerald-500 rounded-lg z-10 shadow-[0_0_8px_rgba(16,185,129,0.8)] transition-all duration-700"
                      style={{ height: `${Math.min(safeBonding - 75, 25) * 4}%` }}
                    />
                  )}
                </>
              )
            )}

            {/* Elegant Token Image Container */}
            <div className={`relative w-full h-full ${settings.circleImages ? 'rounded-full' : 'rounded-2xl'} overflow-hidden transition-all duration-500 group-hover:scale-[1.02] group-hover:shadow-2xl`} style={{
              background: 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',
              border: '1px solid rgba(255,255,255,0.2)',
              boxShadow: token.category === 'bonding'
                ? '0 8px 32px rgba(148, 163, 184, 0.3), inset 0 1px 0 rgba(255,255,255,0.2)'
                : token.category === 'bonded'
                ? '0 8px 32px rgba(251, 191, 36, 0.4), inset 0 1px 0 rgba(255,255,255,0.2)'
                : token.category === 'new'
                ? '0 8px 32px rgba(34, 211, 238, 0.4), inset 0 1px 0 rgba(255,255,255,0.2)'
                : '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)',
              backdropFilter: 'blur(20px)',
              WebkitBackdropFilter: 'blur(20px)'
            }}>
              {/* Category Border Accent */}
              <div className={`absolute inset-0 ${settings.circleImages ? 'rounded-full' : 'rounded-2xl'} pointer-events-none`} style={{
                background: token.category === 'bonding'
                  ? 'linear-gradient(45deg, transparent 30%, rgba(148, 163, 184, 0.3) 50%, transparent 70%)'
                  : token.category === 'bonded'
                  ? 'linear-gradient(45deg, transparent 30%, rgba(251, 191, 36, 0.4) 50%, transparent 70%)'
                  : token.category === 'new'
                  ? 'linear-gradient(45deg, transparent 30%, rgba(34, 211, 238, 0.4) 50%, transparent 70%)'
                  : 'none',
                opacity: 0.6
              }} />
              {/* Token Image Content */}
              <div className={`relative w-full h-full ${settings.circleImages ? 'rounded-full' : 'rounded-2xl'} overflow-hidden`}>
                {imageError || !imageUrl ? (
                  <div 
                    className={`w-full h-full flex items-center justify-center text-white text-2xl font-semibold ${settings.circleImages ? 'rounded-full' : 'rounded-2xl'}`}
                    style={{ 
                      background: `linear-gradient(135deg, ${getBackgroundColor()}, ${getBackgroundColor()}cc)`,
                      backdropFilter: 'blur(10px)'
                    }}
                  >
                    {getTokenInitial()}
                  </div>
                ) : (
                  <img
                    src={imageUrl}
                    alt="Token"
                    className={`object-cover w-full h-full transition-all duration-300 group-hover:scale-105 ${settings.circleImages ? 'rounded-full' : 'rounded-2xl'}`}
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                  />
                )}
                
                {/* Exchange Logo Overlay */}
                <div className="absolute bottom-1 right-1 z-10">
                  {exchange_logo ? (
                    <div className="relative">
                      <img
                        src={exchange_logo}
                        alt="Exchange"
                        className="w-4 h-4 rounded-full border border-white/90 bg-white shadow-md"
                      />
                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/20 to-transparent"></div>
                    </div>
                  ) : (
                    <div className="w-4 h-4 rounded-full border border-white/90 bg-gradient-to-br from-slate-500 to-slate-700 shadow-md flex items-center justify-center">
                      <span className="text-[8px] text-white font-medium">E</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Elegant Progress Indicator */}
            {settings.progressBar && (
              <div className="absolute -bottom-2 left-0 right-0 mx-auto w-[90%] h-[3px] bg-gray-800/60 rounded-full overflow-hidden z-10 backdrop-blur-sm">
                <div
                  className="h-full rounded-full transition-all duration-500 ease-out relative overflow-hidden"
                  style={{ 
                    width: `${safeBonding}%`,
                    background: token.category === 'bonding'
                      ? 'linear-gradient(90deg, rgba(148, 163, 184, 0.7), rgba(203, 213, 225, 0.9))'
                      : token.category === 'bonded'
                      ? 'linear-gradient(90deg, rgba(251, 191, 36, 0.7), rgba(253, 224, 71, 0.9))'
                      : token.category === 'new'
                      ? 'linear-gradient(90deg, rgba(6, 182, 212, 0.7), rgba(103, 232, 249, 0.9))'
                      : 'linear-gradient(90deg, rgba(16, 185, 129, 0.7), rgba(5, 150, 105, 0.9))'
                  }}
                >
                  {/* Subtle shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-[pulse_2s_ease-in-out_infinite]" />
                </div>
              </div>
            )}
          </div>

          {/* Premium Address Section */}
          <div className="mt-2 flex items-center gap-3">
            <div
              className="relative font-mono py-2 px-4 rounded-2xl text-xs text-slate-300 cursor-pointer hover:text-emerald-300 hover:bg-white/[0.05] transition-all duration-500 border border-white/[0.08] hover:border-emerald-400/40 bg-black/30 backdrop-blur-lg group/address"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              onClick={handleCopy}
            >
              <div className="truncate max-w-[100px] font-medium">{formatAddress(safeAddress)}</div>

              {/* Enhanced Tooltip */}
              {isHovered && (
                <div className="absolute left-full ml-4 top-1/2 -translate-y-1/2 bg-black/95 text-white text-xs px-4 py-3 rounded-2xl shadow-2xl z-30 whitespace-nowrap border border-white/15 backdrop-blur-2xl">
                  <div className="flex items-center gap-2">
                    <Copy size={12} className="text-emerald-400" />
                    <span className="font-medium">Copy Address</span>
                  </div>
                  {/* Enhanced Tooltip arrow */}
                  <div className="absolute right-full top-1/2 -translate-y-1/2 border-[6px] border-transparent border-r-black/95"></div>
                </div>
              )}
              
              {/* Hover shimmer effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/[0.05] to-transparent -translate-x-full group-hover/address:translate-x-full transition-transform duration-700 ease-out rounded-2xl" />
            </div>
          </div>
        </div>

        {/* Premium Project Info Section */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-2">
            <h2 className="text-lg font-bold text-white/95 truncate group-hover:text-white transition-colors tracking-tight leading-tight font-['Inter',_'system-ui',_sans-serif]">{token.name}</h2>
            <span className="text-slate-300 text-xs font-semibold px-2 py-1 rounded-lg bg-white/[0.05] border border-white/[0.08] backdrop-blur-sm">
              {token.symbol}
            </span>
          </div>
          
          {/* Optimized Age and Socials Section */}
          <div className="flex items-center text-sm mb-2 gap-3">
            <div className="flex items-center gap-1.5 bg-[#00FFBD]/[0.12] px-2 py-1 rounded-lg border border-[#00FFBD]/25 backdrop-blur-sm">
              <div className="w-1.5 h-1.5 rounded-full bg-[#00FFBD] animate-pulse" />
              {/* Ensure createdAt is always a string or null */}
              <TokenAge 
                createdAt={typeof createdAt === 'string' ? createdAt : null} 
                className="font-medium text-[#00FFBD] text-xs" 
              />
            </div>
            {customRows.socials && (
              <div className="flex items-center gap-2 text-slate-400">
                <Twitter size={12} className="hover:text-[#025FDA] hover:scale-110 transition-all duration-300 cursor-pointer hover:drop-shadow-lg" />
                <Globe size={12} className="hover:text-[#00FFBD] hover:scale-110 transition-all duration-300 cursor-pointer hover:drop-shadow-lg" />
                <Send size={12} className="hover:text-[#00ff00] hover:scale-110 transition-all duration-300 cursor-pointer hover:drop-shadow-lg" />
              </div>
            )}
          </div>

          {/* Optimized Stats Row */}
          <div className="flex text-sm gap-1.5 flex-wrap">
            {customRows.proTraders && (
              <div className="flex items-center gap-1.5 bg-blue-500/[0.12] px-2 py-1 rounded-lg border border-blue-500/25 backdrop-blur-sm hover:bg-blue-500/[0.15] transition-all duration-300 group/stat">
                <TrendingUp size={10} className="text-blue-400 group-hover/stat:scale-110 transition-transform" />
                <span className="text-slate-300 text-xs font-medium">PT</span>
                <span className="text-white font-bold text-xs">{safeStats.proTraders}</span>
              </div>
            )}
            {customRows.holders && (
              <div className="flex items-center gap-1.5 bg-purple-500/[0.12] px-2 py-1 rounded-lg border border-purple-500/25 backdrop-blur-sm hover:bg-purple-500/[0.15] transition-all duration-300 group/stat">
                <Users size={10} className="text-purple-400 group-hover/stat:scale-110 transition-transform" />
                <span className="text-slate-300 text-xs font-medium">H</span>
                <span className="text-white font-bold text-xs">{safeHoldersCount || 0}</span>
              </div>
            )}
            {customRows.devMigrations && (
              <div className="flex items-center gap-1.5 bg-orange-500/[0.12] px-2 py-1 rounded-lg border border-orange-500/25 backdrop-blur-sm hover:bg-orange-500/[0.15] transition-all duration-300 group/stat">
                <Settings size={10} className="text-orange-400 group-hover/stat:scale-110 transition-transform" />
                <span className="text-slate-300 text-xs font-medium">DM</span>
                <span className="text-white font-bold text-xs">{safeBonding}</span>
              </div>
            )}
          </div>

          {/* Enhanced Progress Bar Below Stats with Bonding Percentage - Full Width */}
          <div className="flex flex-col items-center w-full mt-4 mb-2">
            <div className="relative w-full h-4 bg-slate-800/30 rounded-full overflow-hidden shadow-inner">
              <div
                className={`h-full rounded-full transition-all duration-1000 ease-out relative ${
                  token.category === 'new'
                    ? 'bg-gradient-to-r from-emerald-400 to-emerald-500'
                    : token.category === 'bonding'
                    ? 'bg-gradient-to-r from-blue-400 to-blue-500'
                    : token.category === 'bonded'
                    ? 'bg-gradient-to-r from-green-400 to-green-500'
                    : 'bg-gradient-to-r from-slate-400 to-slate-500'
                }`}
                style={{
                  width: `${
                    token.category === 'new' ? Math.max(2, safeBondingPercentage) :
                    token.category === 'bonding' ? safeBondingPercentage :
                    token.category === 'bonded' ? 100 : 0
                  }%`
                }}
              >
                {/* Subtle shimmer effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
              </div>
              
              {/* Bonding percentage text overlay */}
              {(token.category === 'new' || token.category === 'bonding') && safeBondingPercentage !== undefined && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs font-bold text-white" style={{ textShadow: '0px 0px 2px rgba(0,0,0,0.8), 0px 0px 4px rgba(0,0,0,0.5)' }}>{safeBondingPercentage}%</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Optimized Market Info Section */}
        <div className="text-right text-sm space-y-2 min-w-fit pr-3 pb-12 relative z-10">
          {customRows.marketCap && (
            <div className={`flex items-center justify-end gap-2 bg-yellow-500/[0.12] px-3 py-1.5 rounded-lg border border-yellow-500/25 backdrop-blur-sm hover:bg-yellow-500/[0.15] transition-all duration-300 group/metric ${metricsSize === "large" ? "text-sm" : "text-xs"}`}>
              <span className="text-slate-300 font-medium text-xs">MC</span>
              <span className="text-yellow-300 font-bold text-xs group-hover/metric:scale-105 transition-transform">{formatNumber(safeMarketCap)}</span>
            </div>
          )}
          {customRows.volume && (
            <div className="flex items-center justify-end gap-2 bg-slate-500/[0.12] px-3 py-1.5 rounded-lg border border-slate-500/25 backdrop-blur-sm hover:bg-slate-500/[0.15] transition-all duration-300 group/metric">
              <span className="text-slate-300 font-medium text-xs">V</span>
              <span className="text-slate-200 font-bold text-xs group-hover/metric:scale-105 transition-transform">{formatNumber(safeVolume)}</span>
            </div>
          )}

          {/* Optimized trades 24h */}
          <div className="flex items-center justify-end gap-2 bg-green-500/[0.12] px-3 py-1.5 rounded-lg border border-green-500/25 backdrop-blur-sm hover:bg-green-500/[0.15] transition-all duration-300 group/metric">
            <span className="text-slate-300 font-medium text-xs">Txn</span>
            <span className="text-green-300 font-bold text-xs group-hover/metric:scale-105 transition-transform">{safeTrades24h || 0}</span>
          </div>

          {/* Bonding percentage badge removed as requested */}
        </div>
      </div>
    </div>
  );
};

const SkeletonCard = () => (
  <div className="bg-gradient-to-br from-slate-900/80 via-slate-800/60 to-slate-700/40 backdrop-blur-md border border-slate-600/30 rounded-3xl p-6 m-3 animate-pulse-premium shadow-2xl">
    <div className="flex items-start gap-5">
      {/* Premium Avatar skeleton */}
      <div className="relative w-20 h-20 flex-shrink-0">
        <div className="w-full h-full bg-gradient-to-br from-slate-600/60 to-slate-700/80 rounded-3xl shadow-xl animate-shimmer"></div>
        <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-br from-slate-500/60 to-slate-600/80 rounded-full shadow-lg animate-shimmer"></div>
      </div>
      
      {/* Enhanced Content skeleton */}
      <div className="flex-1 space-y-3">
        <div className="flex items-center gap-3">
          <div className="h-5 bg-gradient-to-r from-slate-600/60 to-slate-700/80 rounded-xl w-32 animate-shimmer"></div>
          <div className="h-4 bg-gradient-to-r from-slate-500/60 to-slate-600/80 rounded-lg w-16 animate-shimmer"></div>
        </div>
        <div className="flex items-center gap-4">
          <div className="h-4 bg-gradient-to-r from-emerald-500/30 to-emerald-600/50 rounded-xl w-20 animate-shimmer"></div>
          <div className="flex gap-2">
            <div className="w-4 h-4 bg-gradient-to-br from-slate-500/60 to-slate-600/80 rounded-lg animate-shimmer"></div>
            <div className="w-4 h-4 bg-gradient-to-br from-slate-500/60 to-slate-600/80 rounded-lg animate-shimmer"></div>
            <div className="w-4 h-4 bg-gradient-to-br from-slate-500/60 to-slate-600/80 rounded-lg animate-shimmer"></div>
          </div>
        </div>
        <div className="flex gap-3">
          <div className="h-6 bg-gradient-to-r from-blue-500/30 to-blue-600/50 rounded-2xl w-16 animate-shimmer"></div>
          <div className="h-6 bg-gradient-to-r from-purple-500/30 to-purple-600/50 rounded-2xl w-14 animate-shimmer"></div>
          <div className="h-6 bg-gradient-to-r from-orange-500/30 to-orange-600/50 rounded-2xl w-12 animate-shimmer"></div>
        </div>
      </div>
      
      {/* Premium Stats skeleton */}
      <div className="space-y-3 min-w-fit">
        <div className="h-7 bg-gradient-to-r from-yellow-500/30 to-yellow-600/50 rounded-2xl w-20 animate-shimmer"></div>
        <div className="h-6 bg-gradient-to-r from-slate-500/30 to-slate-600/50 rounded-xl w-18 animate-shimmer"></div>
        <div className="h-6 bg-gradient-to-r from-green-500/30 to-green-600/50 rounded-xl w-16 animate-shimmer"></div>
        <div className="h-6 bg-gradient-to-r from-purple-500/30 to-purple-600/50 rounded-xl w-14 animate-shimmer"></div>
      </div>
    </div>
  </div>
);

const CategoryColumn = ({
  title,
  cards,
  customRows,
  settings,
  metricsSize,
  quickBuySize,
  amount,
  loading,
}: any) => {
  const [listHeight, setListHeight] = useState(window.innerHeight - 150);
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  // Update list height on resize
  useEffect(() => {
    const handleResize = () => {
      setListHeight(window.innerHeight - 150);
    };

    window.addEventListener('resize', handleResize, { passive: true });
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  const getCategoryColor = (title: string) => {
    switch (title.toLowerCase()) {
      case 'new': return 'from-emerald-500 to-teal-600';
      case 'bonding': return 'from-blue-500 to-indigo-600';
      case 'bonded': return 'from-purple-500 to-pink-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const getCategoryIcon = (title: string) => {
    switch (title.toLowerCase()) {
      case 'new': return '🚀';
      case 'bonding': return '⚡';
      case 'bonded': return '💎';
      default: return '📊';
    }
  };

  return (
    <div className="w-1/3 min-w-0 border-r border-[#181C20]/60 glass-morphism-premium flex flex-col max-h-screen relative overflow-hidden">
      {/* App Theme Token Listing Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#141416] via-[#181C20]/80 to-[#141416]"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-[#181C20]/50 via-[#141416]/70 to-[#181C20]/50"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-[#00FFBD]/4 via-transparent to-[#025FDA]/4"></div>

      {/* App Theme Token Listing Header */}
      <div className="sticky top-0 glass-morphism-premium z-20 border-b border-[#181C20]/70 shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-[#141416]/98 via-[#181C20]/95 to-[#141416]/98"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-transparent to-blue-500/5"></div>
        <div className="relative px-4 py-3 z-10">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              <div className={`w-8 h-8 rounded-xl bg-gradient-to-br ${getCategoryColor(title)} flex items-center justify-center text-white font-medium text-sm shadow-lg border border-white/10`}>
                <span>{getCategoryIcon(title)}</span>
              </div>
              <div>
                <h2 className="font-semibold text-base text-white font-['Inter',_'system-ui',_sans-serif] tracking-tight">{title}</h2>
                <p className="text-xs text-slate-400 font-medium">
                  {cards.length} tokens • Live updates
                </p>
              </div>
            </div>
            <button className="text-slate-400 hover:text-emerald-400 p-2 rounded-xl transition-all duration-300 hover:bg-emerald-500/10 hover:scale-105 group">
              <SlidersHorizontal size={16} className="transition-transform duration-300 group-hover:rotate-90" />
            </button>
          </div>

          {/* Enhanced Category Stats Bar */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 text-xs">
              <div className="flex items-center gap-1.5 px-3 py-1.5 rounded-lg bg-emerald-500/10 border border-emerald-500/20 backdrop-blur-sm">
                <div className="w-2 h-2 rounded-full bg-emerald-400 animate-pulse"></div>
                <span className="text-emerald-400 font-medium text-xs">Live</span>
              </div>
              <div className="flex items-center gap-1.5 px-3 py-1.5 rounded-lg bg-blue-500/10 border border-blue-500/20 backdrop-blur-sm">
                <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                <span className="text-blue-400 font-medium text-xs">Real-time</span>
              </div>
            </div>
            <div className="text-xs text-slate-500 font-medium">
              Updated now
            </div>
          </div>
        </div>
      </div>

      {/* Premium Scrollable Content with Enhanced Background - Performance Optimized */}
      <div
        ref={scrollContainerRef}
        className="flex-1 min-h-0 overflow-y-auto pulse-scrollable pulse-container relative"
      >
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-900/20 to-transparent pointer-events-none"></div>
        {loading ? (
          <div className="p-5 space-y-5">
            {Array.from({ length: 4 }).map((_, i) => <SkeletonCard key={i} />)}
          </div>
        ) : cards.length > 0 ? (
          <div className="p-3">
              {title.toLowerCase() === 'new' ? (
                // For "New" category, render with animation effect for each card
                <>
                  {cards.map((card: CryptoCardProps, index: number) => {
                    // Add category information to the token
                    const tokenWithCategory = {
                      ...card.token,
                      category: title.toLowerCase()
                    };

                    // Make sure to pass quickBuy props with key to force refresh when amount changes
                    return (
                      <div
                        key={card.address || index}
                        className="animate-slide-in-elegant pulse-card"
                        style={{animationDelay: `${index * 0.02}s`}}
                      >
                        <CryptoCard
                          {...card}
                          token={tokenWithCategory}
                          amount={amount}
                          quickBuySize={quickBuySize}
                          customRows={customRows}
                          settings={settings}
                          metricsSize={metricsSize}
                        />
                      </div>
                    );
                  })}
                </>
              ) : (
                // For other categories, use standard rendering for better performance
                <>
                  {cards.map((card: CryptoCardProps, index: number) => {
                    // Add category information to the token
                    const tokenWithCategory = {
                      ...card.token,
                      category: title.toLowerCase()
                    };

                    return (
                      <div
                        key={card.address || index}
                        className="pulse-card"
                      >
                        <CryptoCard
                          {...card}
                          token={tokenWithCategory}
                          amount={amount}
                          quickBuySize={quickBuySize}
                          customRows={customRows}
                          settings={settings}
                          metricsSize={metricsSize}
                        />
                      </div>
                    );
                  })}
                </>
              )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center p-8">
            <div className="w-24 h-24 rounded-3xl bg-gradient-to-br from-slate-700/60 to-slate-800/80 flex items-center justify-center mb-6 shadow-2xl ring-2 ring-slate-600/20 backdrop-blur-sm">
              <span className="text-4xl animate-pulse-premium">📊</span>
            </div>
            <h3 className="text-xl font-bold text-slate-200 mb-3 tracking-tight">No {title.toLowerCase()} tokens</h3>
            <p className="text-sm text-slate-400 max-w-sm mb-6 leading-relaxed">
              {title === 'New' && 'New tokens will appear here as they are discovered and added to the platform'}
              {title === 'Bonding' && 'Tokens currently in the bonding phase will be shown here with real-time progress'}
              {title === 'Bonded' && 'Successfully bonded tokens will be displayed here with full trading capabilities'}
            </p>
            <div className="flex items-center gap-3 text-sm text-slate-500 mb-6 bg-slate-800/40 px-4 py-2 rounded-2xl border border-slate-700/30">
              <div className="w-3 h-3 rounded-full bg-emerald-400 animate-pulse-premium"></div>
              <span className="font-medium">Waiting for updates...</span>
            </div>
            <button
              onClick={() => window.location.reload()}
              className="flex items-center justify-center px-6 py-3 text-sm font-semibold text-white bg-gradient-to-r from-slate-700 to-slate-600 rounded-2xl hover:from-slate-600 hover:to-slate-500 transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-105 border border-slate-600/50 hover:border-slate-500/70 backdrop-blur-sm group"
            >
              <SlidersHorizontal size={16} className="mr-2 group-hover:rotate-180 transition-transform duration-500" />
              Refresh Page
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

interface PulseDashProps {
  settings: any;
  customRows: any;
  metricsSize: string;
  quickBuySize: string;
  amount: string;
  preset: number;
  setPreset: (value: number) => void;
  onPresetClick: () => void;
}

const PulseDash: React.FC<PulseDashProps> = ({
  settings,
  customRows,
  metricsSize,
  quickBuySize,
  amount,
  preset,
  setPreset,
  onPresetClick
}) => {
  const [newProjects, setNewProjects] = useState<CryptoCardProps[]>([]);
  const [bondingProjects, setBondingProjects] = useState<CryptoCardProps[]>([]);
  const [bondedProjects, setBondedProjects] = useState<CryptoCardProps[]>([]);

  // Use WebSocket hook for real-time pulse data



  const {
     pulseData,
    error,
    isConnecting
  } = useWebSocketPulseData(true); // Enable WebSocket
  const loading = isConnecting && !pulseData;

  // Revert transformProjectToCard to original state
  const transformProjectToCard = (project: any) => ({
    imageUrl: project.image || '',
    name: project.name || '',
    symbol: project.symbol || '',
    age: project.age || '', // Keep for backward compatibility
    createdAt: typeof project.createdAt === 'string' ? project.createdAt : null, // Ensure createdAt is string or null
    marketCap: project.market_cap || 0,
    volume: project.total_volume || 0,
    txCount: project.txCount || '',
    price_change_24h: project.price_change_24h || 0,
    pool_address: project.pool_address || '',
    price: project.current_price || 0,
    address: project.id || '',
    bonding: project.bonding || project.bonding_percent || 0,
    supply: project.supply || 0,
    liquidity: project.liquidity || 0,
    exchange_name: project.exchange_name || '',
    exchange_logo: project.exchange_logo || '',
    network: project.network || '', // ✅ Added missing network field
    holders_count: project.holders_count || 0, // Add holders_count field
    trades_24h: project.trades_24h || 0, // Add trades_24h field
    bondingPercentage: project.bonding || project.bonding_percent || 0, // Add bondingPercentage field
    stats: {
      holders: project.stats?.holders || project.holders_count || '',
      buys: project.stats?.buys || 0,
      sells: project.stats?.sells || 0,
      proTraders: project.stats?.proTraders || '',
      devMigrations: project.stats?.devMigrations || '',
      top10Holders: project.stats?.top10Holders || '',
      devHolding: project.stats?.devHolding || '',
      snipers: project.stats?.snipers || '',
      insiders: project.stats?.insiders || '',
      bundlers: project.stats?.bundlers || '',
      dexPaid: project.stats?.dexPaid || '',
    },
    socials: {
      telegram: project.socials?.telegram || '',
      website: project.socials?.website || ''
    },
    quickBuySize,
    customRows,
    settings,
    metricsSize,
    token: project // Add the token property
  });

  // Store previous pulse data for comparison
  const prevPulseDataRef = useRef<any>(null);

  // Update projects when pulse data changes
  useEffect(() => {
    if (pulseData) {
      // Check if data has actually changed by comparing with previous data
      const currentDataString = JSON.stringify(pulseData);
      const prevDataString = JSON.stringify(prevPulseDataRef.current);

      if (currentDataString !== prevDataString) {
        console.log('📡 Processing new pulse data (data changed):', {
          hasData: !!pulseData,
          newCount: pulseData.new?.length || 0,
          bondingCount: pulseData.bonding?.length || 0,
          bondedCount: pulseData.bonded?.length || 0
        });

        // Transform and deduplicate the data to prevent duplicate tokens
        const deduplicateTokens = (tokens: any[]) => {
          const seen = new Map();
          return tokens.filter(token => {
            const key = token.address || token.id || token.symbol;
            if (seen.has(key)) {
              return false;
            }
            seen.set(key, true);
            return true;
          });
        };

        const newTokens = deduplicateTokens(pulseData.new || []).map(transformProjectToCard);
        const bondingTokens = deduplicateTokens(pulseData.bonding || []).map(transformProjectToCard);
        const bondedTokens = deduplicateTokens(pulseData.bonded || []).map(transformProjectToCard);

        setNewProjects(newTokens);
        setBondingProjects(bondingTokens);
        setBondedProjects(bondedTokens);

        console.log('📊 Deduplicated tokens:', {
          newCount: newTokens.length,
          bondingCount: bondingTokens.length,
          bondedCount: bondedTokens.length
        });

        // Re-broadcast the current amount to ensure new tokens pick it up
        setTimeout(() => {
          if (globalCurrentAmount) {
            const isValid = globalCurrentAmount !== '' && !isNaN(parseFloat(globalCurrentAmount)) && parseFloat(globalCurrentAmount) > 0;
            window.dispatchEvent(new CustomEvent('quickBuyAmountUpdate', { 
              detail: { amount: globalCurrentAmount, isValid } 
            }));
            console.log('📡 Re-broadcasted amount for new tokens:', globalCurrentAmount);
          }
        }, 200); // Small delay to ensure components are ready

        // Update the reference
        prevPulseDataRef.current = pulseData;
      } else {
        console.log('📊 Received identical pulse data, skipping update');
      }
    }
  }, [pulseData]);
  
  const [showDropdown, setShowDropdown] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);

  const handlePresetClick = () => setModalOpen(true);
  const handleAmountChange = (value: string) => {
    // This function is called by QuickBuy but the actual amount handling
    // is done through the AMOUNT_UPDATE_EVENT system
    // No need to do anything here as useQuickBuyAmount handles the state
  };

  return (
    <div className="flex flex-col h-full relative overflow-hidden bg-[#141416]">
      {/* App Theme Consistent Background */}
      <div className="absolute inset-0 bg-[#141416]"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-[#181C20]/40 via-transparent to-[#181C20]/30"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-[#00FFBD]/6 via-transparent to-[#025FDA]/6"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-[#00FFBD]/8 via-transparent to-transparent"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-[#025FDA]/8 via-transparent to-transparent"></div>

      {/* Subtle Pattern Overlay */}
      <div className="absolute inset-0 opacity-[0.02]" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)`,
        backgroundSize: '24px 24px'
      }}></div>

      {/* Content Container */}
      <div className="relative z-10 flex flex-col h-full text-gray-100">
        <div className="flex-1 min-h-0 px-4 pb-4">

        {/* Top section with Display and QuickBuy */}
        {/* <div className="flex justify-end gap-4">
          <div className="">
            <Display
              metricsSize={metricsSize}
              setMetricsSize={setMetricsSize}
              showDropdown={showDropdown}
              setShowDropdown={setShowDropdown}
              quickBuySize={quickBuySize}
              setQuickBuySize={setQuickBuySize}
              settings={settings}
              setSettings={setSettings}
              customRows={customRows}
              setCustomRows={setCustomRows}
            />
          </div>

          <div className="">
            <QuickBuy
              onAmountChange={handleAmountChange}
              selectedPreset={preset}
              onPresetClick={handlePresetClick}
            />
            {modalOpen && (
              <TradingSettingsModal
                visible={modalOpen}
                onClose={() => setModalOpen(false)}
                selectedPreset={preset}
                onPresetChange={setPreset}
              />
            )}
          </div>
        </div> */}

        {/* Middle section with CategoryColumns */}
        <div className={`flex overflow-hidden w-full h-full ${settings.compactTables ? 'gap-0' : 'gap-4'} mt-4`}>
          
        <CategoryColumn
  title="New"
  cards={newProjects}
  customRows={customRows}
  settings={settings}
  loading={loading}
  metricsSize={metricsSize}
  quickBuySize={quickBuySize}
  amount={amount}
/>

<CategoryColumn
  title="Bonding"
  cards={bondingProjects}
  customRows={customRows}
  settings={settings}
  loading={loading}
  metricsSize={metricsSize}
  quickBuySize={quickBuySize}
  amount={amount}
/>

<CategoryColumn
  title="Bonded"
  cards={bondedProjects}
  customRows={customRows}
  settings={settings}
  loading={loading}
  metricsSize={metricsSize}
  quickBuySize={quickBuySize}
  amount={amount}
/>

</div>





        </div>

        {/* Footer positioned at the bottom */}
        <Footer preset={preset} setPreset={setPreset} onPresetClick={handlePresetClick} />
      </div>
    </div>
  );

};

export default PulseDash;
