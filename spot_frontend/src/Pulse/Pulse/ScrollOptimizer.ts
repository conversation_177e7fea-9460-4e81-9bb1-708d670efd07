/**
 * ScrollOptimizer - Native scroll optimization for React components
 * 
 * This utility provides functions to optimize scrolling performance in the Pulse component
 */

// Flag to track if optimization has been applied
let isOptimized = false;

/**
 * Apply scroll optimizations to the document
 * This function should be called only once when the Pulse component mounts
 */
export function optimizeScrolling(): void {
  if (isOptimized) return;
  
  // Apply CSS optimizations
  const style = document.createElement('style');
  style.innerHTML = `
    /* Optimize all scrollable elements */
    .pulse-scrollable {
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: none;
      scroll-behavior: auto !important;
    }
    
    /* Force hardware acceleration on all relevant elements */
    .pulse-scrollable, 
    .pulse-card,
    .glass-morphism-premium {
      transform: translate3d(0, 0, 0);
      backface-visibility: hidden;
      perspective: 1000px;
      will-change: transform;
    }
    
    /* Prevent layout shifts */
    .pulse-card {
      contain: layout style;
      scroll-snap-align: start;
    }
  `;
  document.head.appendChild(style);
  
  // Optimize scroll event handling for the entire document
  const supportsPassive = (() => {
    let passiveSupported = false;
    
    try {
      const options = Object.defineProperty({}, "passive", {
        get: function() {
          passiveSupported = true;
          return true;
        }
      });
      
      window.addEventListener("test", null as any, options);
      window.removeEventListener("test", null as any, options);
    } catch (err) {
      passiveSupported = false;
    }
    
    return passiveSupported;
  })();
  
  // Add scroll event optimizations
  document.addEventListener('scroll', (e) => {
    if (!e.target || !(e.target as Element).classList?.contains('pulse-scrollable')) return;
    
    // Use requestAnimationFrame to optimize rendering
    if (window.requestAnimationFrame) {
      window.requestAnimationFrame(() => {
        // Do nothing here - just ensure scroll events are handled in animation frames
      });
    }
  }, supportsPassive ? { passive: true } : false);
  
  isOptimized = true;
}

/**
 * Disable scroll momentum for an element when needed
 * @param element The element to disable momentum scrolling for
 */
export function disableMomentumScroll(element: HTMLElement): void {
  if (!element) return;
  
  element.style.cssText += `
    -webkit-overflow-scrolling: auto !important; 
    scroll-behavior: auto !important;
    overscroll-behavior: none !important;
  `;
}

/**
 * Enable scroll momentum for an element
 * @param element The element to enable momentum scrolling for
 */
export function enableMomentumScroll(element: HTMLElement): void {
  if (!element) return;
  
  element.style.cssText += `
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
    overscroll-behavior: auto !important;
  `;
}

export default {
  optimizeScrolling,
  disableMomentumScroll,
  enableMomentumScroll
}; 