
import NewCoin from '../Tabs/NewCoin';
import TrendingCoin from '../Tabs/TrendingCoin';
import PreLaunch from '../Tabs/Pre-Launch';
import PulseDash from './Pulse';
import { useWebSocketPulseData } from '@/hooks/useWebSocketPulseData';
import { useState, useRef, useEffect } from 'react';
// Mock data for the cryptocurrency table
interface TokenType {
  id?: string;
  address: string;
  name: string;
  symbol: string;
  network?: string;
  [key: string]: any;
}

interface StatsType {
  holders: string;
  buys: number;
  sells: number;
  proTraders: string;
  devMigrations: string;
  top10Holders: string;
  devHolding: string;
  snipers: string;
  insiders: string;
  bundlers: string;
  dexPaid: string;
}

interface SocialsType {
  telegram: string;
  website: string;
}
interface CryptoCardProps {
  baseimage: string;
  bonding: number;
  createdAt: string;
  current_price: number;
  exchange_logo: string;
  exchange_name: string;
  holders_count: number;
  id: string;
  image: string;
  liquidity: number;
  market_cap: number;
  name: string;
  network: string;
  pool_address: string;
  price_change_percentage_24h: number;
  supply: number;
  symbol: string;
  total_volume: number;
  trades_24h: number;
}







// Main component
interface CryptoDashboardProps {
  activeTab: string;
  searchTerm: string;
  amount: string;
}

const CryptoDashboard = ({ activeTab, searchTerm, amount }: CryptoDashboardProps) => {
  const [newProjects, setNewProjects] = useState<CryptoCardProps[]>([]);
  const [bondingProjects, setBondingProjects] = useState<CryptoCardProps[]>([]);
  const [bondedProjects, setBondedProjects] = useState<CryptoCardProps[]>([]);
  const {
    pulseData,
    isConnecting
  } = useWebSocketPulseData(true); 
   const prevPulseDataRef = useRef<any>(null);
  useEffect(() => {
    if (pulseData) {
      const currentDataString = JSON.stringify(pulseData);
      const prevDataString = JSON.stringify(prevPulseDataRef.current);
  
      if (currentDataString !== prevDataString) {
        console.log('📡 Processing new pulse data (data changed):', {
          hasData: !!pulseData,
          newCount: pulseData.new?.length || 0,
          bondingCount: pulseData.bonding?.length || 0,
          bondedCount: pulseData.bonded?.length || 0
        });
  
        // Deduplicate tokens using address or id or symbol
        const deduplicateTokens = (tokens: any []) => {
          const seen = new Map();
          return tokens.filter(token => {
            const key = token.address || token.id || token.symbol;
            if (seen.has(key)) return false;
            seen.set(key, true);
            return true;
          });
        };
  
        setNewProjects(deduplicateTokens(pulseData.new || []));
        setBondingProjects(deduplicateTokens(pulseData.bonding || []));
        setBondedProjects(deduplicateTokens(pulseData.bonded || []));
        
        // Update reference
        prevPulseDataRef.current = pulseData;
      }
    }
  }, [pulseData]);
  
  const renderTabContent = () => {
    switch (activeTab) {
      case "New":
        return <NewCoin tokens={newProjects} searchTerm={searchTerm} amount={amount} />;
      case "Trending":
        return <TrendingCoin tokens={bondedProjects} />;
      case "Pre-Launch":
        return <PreLaunch tokens={bondingProjects} searchTerm={searchTerm} amount={amount} />;
      default:
        return null;
    }
  };
  
  
    return <div>{renderTabContent()}</div>;
  };
  
  export default CryptoDashboard;
  