import { useCallback, useRef, useEffect } from 'react';

interface ScrollLockHook {
  scrollContainerRef: React.RefObject<HTMLDivElement>;
  lockScroll: () => void;
  unlockScroll: () => void;
}

/**
 * React hook to lock scrolling on element hover
 * 
 * @returns ScrollLockHook object with methods and refs
 */
export default function useScrollLock(): ScrollLockHook {
  // Store a reference to the locked scroll container
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  
  // Store the scroll position to restore it when locked
  const savedScrollPosition = useRef<number>(0);
  
  // Track if currently locked
  const isLocked = useRef<boolean>(false);
  
  // Wheel event handler to prevent scrolling
  const handleWheel = useCallback((e: WheelEvent) => {
    if (isLocked.current && scrollContainerRef.current) {
      e.preventDefault();
      e.stopPropagation();
      
      // Force container back to saved position
      scrollContainerRef.current.scrollTop = savedScrollPosition.current;
      return false;
    }
  }, []);
  
  // Touch event handler
  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (isLocked.current && scrollContainerRef.current) {
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
  }, []);

  // Function to lock scrolling
  const lockScroll = useCallback(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer || isLocked.current) return;
    
    // Save current position
    savedScrollPosition.current = scrollContainer.scrollTop;
    
    // Mark as locked
    isLocked.current = true;
    
    // Add event listeners at document level to capture all events
    document.addEventListener('wheel', handleWheel as EventListener, { passive: false });
    document.addEventListener('touchmove', handleTouchMove as EventListener, { passive: false });
    
    // Stop smooth scrolling temporarily
    const originalScrollBehavior = scrollContainer.style.scrollBehavior;
    scrollContainer.style.scrollBehavior = 'auto';
    
    // Return cleanup function
    return () => {
      document.removeEventListener('wheel', handleWheel as EventListener);
      document.removeEventListener('touchmove', handleTouchMove as EventListener);
      
      // Restore scroll behavior
      scrollContainer.style.scrollBehavior = originalScrollBehavior;
      
      // Mark as unlocked
      isLocked.current = false;
    };
  }, [handleWheel, handleTouchMove]);

  // Function to unlock scrolling
  const unlockScroll = useCallback(() => {
    if (!isLocked.current) return;
    
    document.removeEventListener('wheel', handleWheel as EventListener);
    document.removeEventListener('touchmove', handleTouchMove as EventListener);
    
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.style.scrollBehavior = 'smooth';
    }
    
    isLocked.current = false;
  }, [handleWheel, handleTouchMove]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isLocked.current) {
        unlockScroll();
      }
    };
  }, [unlockScroll]);
  
  return {
    scrollContainerRef,
    lockScroll,
    unlockScroll
  };
} 