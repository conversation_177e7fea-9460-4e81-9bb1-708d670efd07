import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { SlidersHorizontal } from 'lucide-react';
import { CryptoCardProps } from '../Pulse/Pulse';

interface VirtualizedCategoryColumnProps {
  title: string;
  cards: CryptoCardProps[];
  customRows: any;
  settings: any;
  metricsSize: string;
  quickBuySize: string;
  amount: string;
  loading: boolean;
  CryptoCard: React.ComponentType<CryptoCardProps>;
}

const ITEM_HEIGHT = 180; // Approximate height of each crypto card
const BUFFER_SIZE = 3; // Reduced buffer size for better performance (was 5)
const SCROLL_THROTTLE = 16; // 60fps throttling for scroll events

const VirtualizedCategoryColumn: React.FC<VirtualizedCategoryColumnProps> = ({
  title,
  cards,
  customRows,
  settings,
  metricsSize,
  quickBuySize,
  amount,
  loading,
  CryptoCard,
}) => {
  const [listHeight, setListHeight] = useState(window.innerHeight - 150);
  const [scrollTop, setScrollTop] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const [isPaused, setIsPaused] = useState(false);

  // Update list height on resize
  useEffect(() => {
    const handleResize = () => {
      setListHeight(window.innerHeight - 150);
    };

    window.addEventListener('resize', handleResize, { passive: true });
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Listen for pause/resume events for this category
  useEffect(() => {
    const handlePauseCategory = (event: CustomEvent) => {
      if (event.detail.category === title.toLowerCase()) {
        setIsPaused(true);
      }
    };

    const handleResumeCategory = (event: CustomEvent) => {
      if (event.detail.category === title.toLowerCase()) {
        setIsPaused(false);
      }
    };

    window.addEventListener('pulsePauseCategory', handlePauseCategory as EventListener);
    window.addEventListener('pulseResumeCategory', handleResumeCategory as EventListener);

    return () => {
      window.removeEventListener('pulsePauseCategory', handlePauseCategory as EventListener);
      window.removeEventListener('pulseResumeCategory', handleResumeCategory as EventListener);
    };
  }, [title]);

  // Handle scroll with throttling for smooth performance
  const handleScroll = useCallback(() => {
    if (scrollContainerRef.current) {
      setScrollTop(scrollContainerRef.current.scrollTop);
    }
  }, []);

  // Throttled scroll handler using requestAnimationFrame for better performance
  const throttledScrollHandler = useMemo(() => {
    let rafId: number | null = null;
    let lastScrollTime = 0;
    
    return () => {
      const now = performance.now();
      
      // Throttle to prevent excessive calls
      if (now - lastScrollTime < SCROLL_THROTTLE) {
        return;
      }
      
      // Cancel previous frame if still pending
      if (rafId !== null) {
        cancelAnimationFrame(rafId);
      }
      
      // Use requestAnimationFrame for smooth updates
      rafId = requestAnimationFrame(() => {
        handleScroll();
        lastScrollTime = performance.now();
        rafId = null;
      });
    };
  }, [handleScroll]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', throttledScrollHandler, { passive: true });
      return () => {
        container.removeEventListener('scroll', throttledScrollHandler);
      };
    }
  }, [throttledScrollHandler]);

  // Calculate visible items with buffer for smooth scrolling
  const visibleItems = useMemo(() => {
    if (!cards.length) return { startIndex: 0, endIndex: 0, visibleCards: [] };

    const containerHeight = listHeight;
    const startIndex = Math.max(0, Math.floor(scrollTop / ITEM_HEIGHT) - BUFFER_SIZE);
    const visibleCount = Math.ceil(containerHeight / ITEM_HEIGHT) + (2 * BUFFER_SIZE);
    const endIndex = Math.min(cards.length, startIndex + visibleCount);

    const visibleCards = cards.slice(startIndex, endIndex).map((card, index) => ({
      ...card,
      virtualIndex: startIndex + index,
      actualIndex: startIndex + index,
    }));

    return { startIndex, endIndex, visibleCards };
  }, [cards, scrollTop, listHeight]);

  const totalHeight = cards.length * ITEM_HEIGHT;
  const offsetY = visibleItems.startIndex * ITEM_HEIGHT;

  const getCategoryColor = (title: string) => {
    switch (title.toLowerCase()) {
      case 'new': return 'from-emerald-500 to-teal-600';
      case 'bonding': return 'from-blue-500 to-indigo-600';
      case 'bonded': return 'from-purple-500 to-pink-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const getCategoryIcon = (title: string) => {
    switch (title.toLowerCase()) {
      case 'new': return '🚀';
      case 'bonding': return '⚡';
      case 'bonded': return '💎';
      default: return '📊';
    }
  };

  // Skeleton component for loading state - Optimized
  const SkeletonCard = () => (
    <div className="w-full bg-gradient-to-br from-slate-800/60 to-slate-900/80 rounded-3xl p-5 border border-slate-700/40 shadow-lg animate-pulse mb-4">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-4">
          <div className="w-14 h-14 bg-gradient-to-br from-slate-600/50 to-slate-700/60 rounded-2xl animate-shimmer"></div>
          <div className="space-y-2">
            <div className="h-5 bg-gradient-to-r from-slate-600/50 to-slate-700/60 rounded-xl w-24 animate-shimmer"></div>
            <div className="h-4 bg-gradient-to-r from-slate-600/40 to-slate-700/50 rounded-lg w-16 animate-shimmer"></div>
          </div>
        </div>
        <div className="flex gap-3">
          <div className="h-6 bg-gradient-to-r from-blue-500/30 to-blue-600/50 rounded-2xl w-16 animate-shimmer"></div>
          <div className="h-6 bg-gradient-to-r from-purple-500/30 to-purple-600/50 rounded-2xl w-14 animate-shimmer"></div>
        </div>
      </div>
      <div className="space-y-3 min-w-fit">
        <div className="h-7 bg-gradient-to-r from-yellow-500/30 to-yellow-600/50 rounded-2xl w-20 animate-shimmer"></div>
        <div className="h-6 bg-gradient-to-r from-slate-500/30 to-slate-600/50 rounded-xl w-18 animate-shimmer"></div>
      </div>
    </div>
  );

  return (
    <div className="w-1/3 min-w-0 border-r border-[#181C20]/60 glass-morphism-premium flex flex-col max-h-screen relative overflow-hidden">
      {/* Background layers */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#141416] via-[#181C20]/80 to-[#141416]"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-[#181C20]/50 via-[#141416]/70 to-[#181C20]/50"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-[#00FFBD]/4 via-transparent to-[#025FDA]/4"></div>

      {/* Sticky Header */}
      <div className="sticky top-0 glass-morphism-premium z-20 border-b border-[#181C20]/70 shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-[#141416]/98 via-[#181C20]/95 to-[#141416]/98"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-transparent to-blue-500/5"></div>
        <div className="relative px-4 py-3 z-10">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              <div className={`w-8 h-8 rounded-xl bg-gradient-to-br ${getCategoryColor(title)} flex items-center justify-center text-white font-medium text-sm shadow-lg border border-white/10`}>
                <span>{getCategoryIcon(title)}</span>
              </div>
              <div>
                <h2 className="font-semibold text-base text-white font-['Inter',_'system-ui',_sans-serif] tracking-tight">{title}</h2>
                <p className="text-xs text-slate-400 font-medium">
                  {cards.length} tokens • Live updates
                </p>
              </div>
            </div>
            <button className="text-slate-400 hover:text-emerald-400 p-2 rounded-xl transition-all duration-300 hover:bg-emerald-500/10 hover:scale-105 group">
              <SlidersHorizontal size={16} className="transition-transform duration-300 group-hover:rotate-90" />
            </button>
          </div>

          {/* Enhanced Category Stats Bar */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 text-xs">
              {isPaused ? (
                <div className="flex items-center gap-1.5 px-3 py-1.5 rounded-lg bg-amber-500/10 border border-amber-500/20 animate-pulse">
                  <div className="w-2 h-2 rounded-full bg-amber-400"></div>
                  <span className="text-amber-400 font-medium text-xs">Updates Paused</span>
                </div>
              ) : (
                <>
                  <div className="flex items-center gap-1.5 px-3 py-1.5 rounded-lg bg-emerald-500/10 border border-emerald-500/20">
                    <div className="w-2 h-2 rounded-full bg-emerald-400 animate-pulse"></div>
                    <span className="text-emerald-400 font-medium text-xs">Live</span>
                  </div>
                  <div className="flex items-center gap-1.5 px-3 py-1.5 rounded-lg bg-blue-500/10 border border-blue-500/20">
                    <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                    <span className="text-blue-400 font-medium text-xs">Real-time</span>
                  </div>
                </>
              )}
            </div>
            <div className="text-xs text-slate-500 font-medium">
              {isPaused ? 'Hover paused' : 'Updated now'}
            </div>
          </div>
        </div>
      </div>

      {/* Virtualized Scrollable Content */}
      <div
        ref={scrollContainerRef}
        className="flex-1 min-h-0 overflow-y-auto pulse-scrollable pulse-container relative"
        style={{ height: listHeight }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-900/20 to-transparent pointer-events-none"></div>
        
        {loading ? (
          <div className="p-5 space-y-5">
            {Array.from({ length: 4 }).map((_, i) => <SkeletonCard key={i} />)}
          </div>
        ) : cards.length > 0 ? (
          <div className="p-3 relative" style={{ height: totalHeight }}>
            {/* Virtual container with proper offset */}
            <div
              style={{
                transform: `translateY(${offsetY}px)`,
                position: 'relative',
              }}
            >
              {visibleItems.visibleCards.map((card) => {
                const tokenWithCategory = {
                  ...card.token,
                  category: title.toLowerCase()
                };

                const isNewCategory = title.toLowerCase() === 'new';
                
                return (
                  <div
                    key={card.address || card.virtualIndex}
                    className={`${isNewCategory ? 'animate-slide-in-elegant pulse-card' : 'pulse-card'}`}
                    style={{
                      height: ITEM_HEIGHT,
                      // Preserve staggered animation for new tokens
                      ...(isNewCategory && {
                        animationDelay: `${card.actualIndex * 0.02}s`
                      })
                    }}
                  >
                    <CryptoCard
                      {...card}
                      token={tokenWithCategory}
                      amount={amount}
                      quickBuySize={quickBuySize}
                      customRows={customRows}
                      settings={settings}
                      metricsSize={metricsSize}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center p-8">
            <div className="w-24 h-24 rounded-3xl bg-gradient-to-br from-slate-700/60 to-slate-800/80 flex items-center justify-center mb-6 shadow-2xl ring-2 ring-slate-600/20 backdrop-blur-sm">
              <span className="text-4xl animate-pulse-premium">📊</span>
            </div>
            <h3 className="text-xl font-bold text-slate-200 mb-3 tracking-tight">No {title.toLowerCase()} tokens</h3>
            <p className="text-sm text-slate-400 max-w-sm mb-6 leading-relaxed">
              {title === 'New' && 'New tokens will appear here as they are discovered and added to the platform'}
              {title === 'Bonding' && 'Tokens currently in the bonding phase will be shown here with real-time progress'}
              {title === 'Bonded' && 'Successfully bonded tokens will be displayed here with full trading capabilities'}
            </p>
            <div className="flex items-center gap-3 text-sm text-slate-500 mb-6 bg-slate-800/40 px-4 py-2 rounded-2xl border border-slate-700/30">
              <div className="w-3 h-3 rounded-full bg-emerald-400 animate-pulse-premium"></div>
              <span className="font-medium">Waiting for updates...</span>
            </div>
            <button
              onClick={() => window.location.reload()}
              className="flex items-center justify-center px-6 py-3 text-sm font-semibold text-white bg-gradient-to-r from-slate-700 to-slate-600 rounded-2xl hover:from-slate-600 hover:to-slate-500 transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-105 border border-slate-600/50 hover:border-slate-500/70 backdrop-blur-sm group"
            >
              <SlidersHorizontal size={16} className="mr-2 group-hover:rotate-180 transition-transform duration-500" />
              Refresh Page
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default VirtualizedCategoryColumn;