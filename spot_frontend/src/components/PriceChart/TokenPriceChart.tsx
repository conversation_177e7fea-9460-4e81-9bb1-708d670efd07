import React, { useEffect, useRef, useState } from "react";
import {
  create<PERSON>hart,
  CrosshairMode,
  IChartApi,
  ISeriesApi,
  UTCTimestamp,
  CandlestickData,
  CandlestickSeries
} from "lightweight-charts";
import axios from "axios";
import { homeAPI } from "@/utils/api";



interface Token {
  id: string;
  network: string;
  symbol?: string;
}

interface TokenPriceChartProps {
  token: Token;
}

const TokenPriceChart: React.FC<TokenPriceChartProps> = ({ token }) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const seriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState("1h"); // Default timeframe
  const [chartCreated, setChartCreated] = useState(false);
  const widgetRef = useRef<any>(null);
  // Available timeframes from the API doc
  const timeframes = ["1s", "1min", "5min", "15min", "1h", "1d", "7d", "30d"];

  // Initialize chart with a guaranteed size
  useEffect(() => {
    const initializeChart = () => {
      if (!chartContainerRef.current || chartRef.current) return;
      
      // Set explicit dimensions for initialization
      const width = chartContainerRef.current.clientWidth || 800; // Fallback width
      const height = chartContainerRef.current.clientHeight || 400; // Fallback height
      
      const chart = createChart(chartContainerRef.current, {
        width: width,
        height: height,
        layout: {
          background: { color: "#1e1e1e" },
          textColor: "#ccc",
        },
        grid: {
          vertLines: { color: "#333" },
          horzLines: { color: "#333" },
        },
        crosshair: {
          mode: CrosshairMode.Normal,
        },
        timeScale: {
          borderColor: "#555",
          timeVisible: true,
          secondsVisible: false,
        },
      });
  
      chartRef.current = chart;
      seriesRef.current = chart.addSeries(CandlestickSeries, {
        upColor: "#26a69a", // Green for upward trends
        downColor: "#ef5350", // Red for downward trends
        borderVisible: false,
        wickUpColor: "#26a69a",
        wickDownColor: "#ef5350",
      });
      
      setChartCreated(true);
    };

    // Use ResizeObserver for more reliable sizing
    if (chartContainerRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        // Resize existing chart
        if (chartRef.current && chartContainerRef.current) {
          const width = chartContainerRef.current.clientWidth;
          const height = chartContainerRef.current.clientHeight || 400;
          
          chartRef.current.applyOptions({
            width: width,
            height: height,
          });
          chartRef.current.timeScale().fitContent();
        }
        // Initialize chart if not yet created
        else if (!chartRef.current) {
          initializeChart();
        }
      });
      
      resizeObserver.observe(chartContainerRef.current);
      
      // Also initialize on mount with a slight delay to ensure DOM is ready
      setTimeout(initializeChart, 100);
      
      return () => {
        resizeObserver.disconnect();
        if (chartRef.current) {
          chartRef.current.remove();
          chartRef.current = null;
          seriesRef.current = null;
          setChartCreated(false);
        }
      };
    }
  }, []);
  
  // Fetch and update chart data
  useEffect(() => {
    // Only fetch data if chart is created and we have valid token info
    if (!chartCreated || !token?.id || !token?.network) return;
    
    const fetchOHLC = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const network =
          token.network === "binance-smart-chain"
            ? "BNB Smart Chain (BEP20)"
            : token.network;

        const response = await homeAPI.fetchMarketInfo('chart-data', {
          address: token.id,
          network: token.network,
          timeframe:timeframe
 
        });

        const result = response.data;
        if (!result.data || !Array.isArray(result.data) || result.data.length === 0) {
          throw new Error("Invalid or empty data received from API");
        }

        const formattedData: CandlestickData[] = result.data.map((item: any) => ({
          time: Math.floor(new Date(item.time).getTime() / 1000) as UTCTimestamp,
          open: parseFloat(item.open),
          high: parseFloat(item.high),
          low: parseFloat(item.low),
          close: parseFloat(item.close),
        }));

        // Sort data by time ascending
        formattedData.sort((a, b) => (a.time as number) - (b.time as number));

        if (seriesRef.current) {
          seriesRef.current.setData(formattedData);
          
          // Fit content to visible range
          if (chartRef.current) {
            chartRef.current.timeScale().fitContent();
          }
        }
      } catch (error) {
        console.error("Failed to fetch OHLC data:", error);
        setError(error instanceof Error ? error.message : "Failed to fetch chart data");
      } finally {
        setLoading(false);
      }
    };

    fetchOHLC();
  }, [token?.id, token?.network, timeframe, chartCreated]);

  return (
    <div className="w-full h-full mx-auto  p-4 text-white shadow-md rounded flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold">{token?.symbol}/USD</h3>
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(e.target.value)}
          className="p-2 border rounded bg-gray-700 text-white"
        >
          {timeframes.map((timeframeOption) => (
            <option key={timeframeOption} value={timeframeOption}>
              {timeframeOption}
            </option>
          ))}
        </select>
      </div>
  
 
      {error && <p className="text-center text-red-500">Error: {error}</p>}
  
      {/* Chart container with explicit min-height */}
      <div className="flex-1 relative" style={{ minHeight: "400px" }}>
        <div
          ref={chartContainerRef}
          className="w-full h-full absolute inset-0"
   
        />
      </div>
    </div>
  );
};

export default TokenPriceChart;