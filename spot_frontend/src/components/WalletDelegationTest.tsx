/**
 * Test component for wallet delegation management system
 * This component can be used to test and debug the delegation functionality
 */

import React, { useState } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { useWalletDelegation } from '../hooks/useWalletDelegation';
import { 
  checkWalletDelegationStatus, 
  getStoredDefaultWalletId, 
  updateStoredDefaultWallet,
  WalletDelegationStatus 
} from '../utils/walletDelegationManager';
import { DelegationToasts } from '../utils/delegationToast';

const WalletDelegationTest: React.FC = () => {
  const { user, authenticated } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  const walletDelegation = useWalletDelegation();
  
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunningTest, setIsRunningTest] = useState(false);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    console.log(`[WalletDelegationTest] ${message}`);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const runDelegationTest = async () => {
    if (!authenticated || !user?.id) {
      addTestResult('❌ User not authenticated');
      return;
    }

    setIsRunningTest(true);
    addTestResult('🚀 Starting wallet delegation test...');

    try {
      // Test 1: Check stored wallet ID
      const storedWalletId = getStoredDefaultWalletId();
      addTestResult(`📱 Stored wallet ID: ${storedWalletId || 'None'}`);

      // Test 2: Check delegation status
      addTestResult('🔍 Checking delegation status...');
      const delegationCheck = await checkWalletDelegationStatus(user.id, storedWalletId || undefined);
      
      if (delegationCheck.success) {
        addTestResult(`✅ Delegation check successful`);
        addTestResult(`📊 Current wallet: ${delegationCheck.currentWallet?.address || 'None'}`);
        addTestResult(`🔐 Is delegated: ${delegationCheck.currentWallet?.delegated || false}`);
        addTestResult(`📥 Is imported: ${delegationCheck.currentWallet?.imported || false}`);
        addTestResult(`🎯 Needs delegation: ${delegationCheck.needsDelegation}`);
        addTestResult(`✨ Has valid delegated wallet: ${delegationCheck.hasValidDelegatedWallet}`);
        addTestResult(`💡 Recommended wallet: ${delegationCheck.recommendedWallet?.address || 'None'}`);
        addTestResult(`📈 Total wallets: ${delegationCheck.allWallets.length}`);
        
        // List all wallets
        delegationCheck.allWallets.forEach((wallet, index) => {
          const tempFlag = wallet.walletId.startsWith('temp_') ? ' (TEMP)' : '';
          addTestResult(`  ${index + 1}. ${wallet.address.slice(0, 8)}... - Delegated: ${wallet.delegated}, Imported: ${wallet.imported}${tempFlag}`);
        });
      } else {
        addTestResult(`❌ Delegation check failed: ${delegationCheck.message}`);
      }

      // Test 3: Check Privy wallets
      addTestResult(`🔗 Connected Solana wallets: ${solanaWallets.length}`);
      solanaWallets.forEach((wallet, index) => {
        addTestResult(`  ${index + 1}. ${wallet.address?.slice(0, 8)}... - Type: ${wallet.walletClientType}, Imported: ${wallet.imported}`);
      });

      // Test 4: Hook state
      addTestResult(`🎣 Hook state:`);
      addTestResult(`  - Is checking: ${walletDelegation.isChecking}`);
      addTestResult(`  - Needs delegation: ${walletDelegation.needsDelegation}`);
      addTestResult(`  - Has valid delegated wallet: ${walletDelegation.hasValidDelegatedWallet}`);
      addTestResult(`  - Current wallet: ${walletDelegation.currentWallet?.address || 'None'}`);
      addTestResult(`  - Error: ${walletDelegation.error || 'None'}`);

    } catch (error) {
      addTestResult(`❌ Test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsRunningTest(false);
      addTestResult('🏁 Test completed');
    }
  };

  const testDelegationWorkflow = async () => {
    if (!authenticated || !user?.id) {
      addTestResult('❌ User not authenticated');
      return;
    }

    addTestResult('🔄 Testing delegation workflow...');
    const result = await walletDelegation.handleDelegationWorkflow();
    
    if (result) {
      addTestResult(`✅ Delegation workflow successful: ${result.address}`);
    } else {
      addTestResult('❌ Delegation workflow failed');
    }
  };

  const testToastSystem = () => {
    addTestResult('🔔 Testing toast system...');
    
    DelegationToasts.checkingStatus();
    setTimeout(() => DelegationToasts.enablingDelegation('test123...'), 1000);
    setTimeout(() => DelegationToasts.delegationSuccess('test123...'), 2000);
    setTimeout(() => DelegationToasts.importedWalletWarning('imported123...'), 3000);
    setTimeout(() => DelegationToasts.delegationFailed('failed123...', 'Test error'), 4000);
    
    addTestResult('🔔 Toast test sequence started');
  };

  const switchToWallet = (wallet: WalletDelegationStatus) => {
    walletDelegation.switchToWallet(wallet);
    addTestResult(`🔄 Switched to wallet: ${wallet.address}`);
  };

  if (!authenticated) {
    return (
      <div className="p-4 bg-gray-800 rounded-lg text-white">
        <h3 className="text-lg font-semibold mb-2">Wallet Delegation Test</h3>
        <p className="text-gray-400">Please authenticate to test wallet delegation</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-gray-800 rounded-lg text-white max-w-4xl">
      <h3 className="text-lg font-semibold mb-4">Wallet Delegation Test Panel</h3>
      
      {/* Control Buttons */}
      <div className="flex flex-wrap gap-2 mb-4">
        <button
          onClick={runDelegationTest}
          disabled={isRunningTest}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-md text-sm"
        >
          {isRunningTest ? 'Running Test...' : 'Run Delegation Test'}
        </button>
        
        <button
          onClick={testDelegationWorkflow}
          disabled={walletDelegation.isChecking}
          className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 rounded-md text-sm"
        >
          Test Delegation Workflow
        </button>
        
        <button
          onClick={testToastSystem}
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-sm"
        >
          Test Toast System
        </button>
        
        <button
          onClick={clearResults}
          className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-md text-sm"
        >
          Clear Results
        </button>
      </div>

      {/* Current Status */}
      <div className="mb-4 p-3 bg-gray-700 rounded-md">
        <h4 className="font-semibold mb-2">Current Status</h4>
        <div className="text-sm space-y-1">
          <div>User ID: {user?.id || 'None'}</div>
          <div>Stored Wallet: {getStoredDefaultWalletId() || 'None'}</div>
          <div>Hook Status: {walletDelegation.isChecking ? 'Checking...' : 'Ready'}</div>
          <div>Needs Delegation: {walletDelegation.needsDelegation ? 'Yes' : 'No'}</div>
          <div>Current Wallet: {walletDelegation.currentWallet?.address || 'None'}</div>
          {walletDelegation.error && (
            <div className="text-red-400">Error: {walletDelegation.error}</div>
          )}
        </div>
      </div>

      {/* Available Wallets */}
      {walletDelegation.delegationStatus?.allWallets && (
        <div className="mb-4 p-3 bg-gray-700 rounded-md">
          <h4 className="font-semibold mb-2">Available Wallets</h4>
          <div className="space-y-2">
            {walletDelegation.delegationStatus.allWallets.map((wallet, index) => (
              <div key={wallet.walletId} className="flex items-center justify-between text-sm">
                <div>
                  <span className={wallet.walletId.startsWith('temp_') ? 'text-yellow-400' : 'text-white'}>
                    {wallet.address.slice(0, 12)}...
                  </span>
                  <span className="ml-2 text-gray-400">
                    {wallet.delegated ? '✅' : '❌'} 
                    {wallet.imported ? ' 📥' : ''}
                    {wallet.walletId.startsWith('temp_') ? ' 🔄' : ''}
                  </span>
                </div>
                <button
                  onClick={() => switchToWallet(wallet)}
                  className="px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs"
                >
                  Switch
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Test Results */}
      <div className="bg-black rounded-md p-3 max-h-96 overflow-y-auto">
        <h4 className="font-semibold mb-2">Test Results</h4>
        {testResults.length === 0 ? (
          <p className="text-gray-400 text-sm">No test results yet. Click "Run Delegation Test" to start.</p>
        ) : (
          <div className="space-y-1">
            {testResults.map((result, index) => (
              <div key={index} className="text-xs font-mono text-green-400">
                {result}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default WalletDelegationTest;
