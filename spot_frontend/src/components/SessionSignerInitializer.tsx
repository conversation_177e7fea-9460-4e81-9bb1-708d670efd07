import React, { useEffect, useState, useRef } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { useSessionSigners } from '../utils/sessionSigners';

/**
 * SessionSignerInitializer component
 *
 * This component automatically delegates all Solana wallets to session signers
 * without requiring user interaction. It runs when:
 * 1. A user logs in
 * 2. A new wallet is created or connected
 * 3. The component mounts if the user is already authenticated
 */
const SessionSignerInitializer: React.FC = () => {
  const { authenticated, ready, user } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  const { delegateAllWallets, resetDelegationState } = useSessionSigners();

  // Track initialization state
  const [initialized, setInitialized] = useState(false);
  const initializationInProgressRef = useRef(false);
  const lastWalletCountRef = useRef(0);

  // Initialize session signers when wallets change
  useEffect(() => {
    // Skip if not authenticated or not ready
    if (!authenticated || !ready || !user) return;

    // Skip if initialization is already in progress
    if (initializationInProgressRef.current) return;

    // Count current wallets
    const currentWalletCount = solanaWallets?.length || 0;

    // Check if wallet count has changed
    const hasWalletChanges = currentWalletCount !== lastWalletCountRef.current;

    // Skip if no wallet changes and already initialized
    if (!hasWalletChanges && initialized) return;

    // Update last wallet count
    lastWalletCountRef.current = currentWalletCount;

    // Initialize session signers
    const initializeSessionSigners = async () => {
      // Set initialization in progress
      initializationInProgressRef.current = true;

      try {
        console.log('🔄 Initializing session signers for all Solana wallets');
        
        // Add a delay to ensure wallets are fully registered with Privy
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        await delegateAllWallets();
        setInitialized(true);
        console.log('✅ Session signer initialization complete');
      } catch (error: any) {
        console.error('❌ Error initializing session signers:', error);

        // Check if it's the specific delegation error
        if (error?.message?.includes('Address to delegate is not associated with current user') || 
            error?.message?.includes('not associated with current user')) {
          console.log('🔄 Delegation error detected, resetting delegation state...');
          resetDelegationState();

          // Try again after a longer delay to ensure proper cleanup
          setTimeout(async () => {
            try {
              console.log('🔄 Retrying delegation after state reset...');
              
              // Additional delay to ensure wallets are fully ready
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              await delegateAllWallets();
              setInitialized(true);
              console.log('✅ Session signer initialization successful on retry');
            } catch (retryError) {
              console.error('❌ Session signer initialization failed on retry:', retryError);
              // Reset initialization flag to allow future attempts
              setInitialized(false);
            }
          }, 3000);
        } else {
          // Reset initialization flag for other errors
          setInitialized(false);
        }
      } finally {
        // Clear initialization in progress
        initializationInProgressRef.current = false;
      }
    };

    // Initialize session signers with a longer delay to ensure wallets are ready
    const timeoutId = setTimeout(initializeSessionSigners, 2000);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [
    authenticated,
    ready,
    user,
    solanaWallets,
    delegateAllWallets,
    resetDelegationState,
    initialized
  ]);

  // Listen for wallet-setup-complete and initialize-session-signers events
  useEffect(() => {
    const handleWalletSetupComplete = async () => {
      console.log('Wallet setup complete event received, initializing session signers');

      // Skip if not authenticated or not ready
      if (!authenticated || !ready) return;

      // Skip if initialization is already in progress
      if (initializationInProgressRef.current) return;

      // Initialize session signers with a slight delay
      setTimeout(async () => {
        try {
          initializationInProgressRef.current = true;
          await delegateAllWallets();
          setInitialized(true);
        } catch (error) {
          console.error('Error initializing session signers after wallet setup:', error);
        } finally {
          initializationInProgressRef.current = false;
        }
      }, 1500);
    };

    // Handler for the initialize-session-signers event
    const handleInitializeSessionSigners = async (event: any) => {
      console.log('Initialize session signers event received', event.detail);

      // Skip if not authenticated or not ready
      if (!authenticated || !ready) return;

      // Skip if initialization is already in progress
      if (initializationInProgressRef.current) return;

      // Initialize session signers with a slight delay
      setTimeout(async () => {
        try {
          initializationInProgressRef.current = true;
          await delegateAllWallets();
          setInitialized(true);
        } catch (error) {
          console.error('Error initializing session signers after explicit initialization request:', error);
        } finally {
          initializationInProgressRef.current = false;
        }
      }, 1000);
    };

    // Handler for wallet-session-signing-enabled event
    const handleWalletSessionSigningEnabled = async (event: any) => {
      console.log('Wallet session signing enabled event received:', event.detail);

      // Skip if not authenticated or not ready
      if (!authenticated || !ready) return;

      // Skip if initialization is already in progress
      if (initializationInProgressRef.current) return;

      // Re-initialize to ensure the newly enabled wallet is properly handled
      setTimeout(async () => {
        try {
          initializationInProgressRef.current = true;
          await delegateAllWallets();
          setInitialized(true);
          console.log('Session signers re-initialized after wallet session signing enabled');
        } catch (error) {
          console.error('Error re-initializing session signers after wallet session signing enabled:', error);
        } finally {
          initializationInProgressRef.current = false;
        }
      }, 500);
    };

    window.addEventListener('wallet-setup-complete', handleWalletSetupComplete);
    window.addEventListener('initialize-session-signers', handleInitializeSessionSigners);
    window.addEventListener('wallet-session-signing-enabled', handleWalletSessionSigningEnabled);

    return () => {
      window.removeEventListener('wallet-setup-complete', handleWalletSetupComplete);
      window.removeEventListener('initialize-session-signers', handleInitializeSessionSigners);
      window.removeEventListener('wallet-session-signing-enabled', handleWalletSessionSigningEnabled);
    };
  }, [authenticated, ready, delegateAllWallets]);

  // This component doesn't render anything
  return null;
};

export default SessionSignerInitializer;