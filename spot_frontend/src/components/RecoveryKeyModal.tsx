import React, { useState } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { IoClose } from 'react-icons/io5';

interface WalletData {
  address: string;
  isEmbedded: boolean;
  walletClientType: string;
  name: string;
}

interface RecoveryKeyModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedWallet?: WalletData;
}

const RecoveryKeyModal: React.FC<RecoveryKeyModalProps> = ({ isOpen, onClose, selectedWallet }) => {
  const { ready, authenticated } = usePrivy();
  const { exportWallet: exportSolanaWallet } = useSolanaWallets();
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exportSuccess, setExportSuccess] = useState(false);

  if (!isOpen) return null;

  // Check authentication
  const isAuthenticated = ready && authenticated;

  // Debug: Log essential information
  console.log("RecoveryKeyModal: User authenticated:", isAuthenticated);
  console.log("RecoveryKeyModal: Selected wallet:", selectedWallet?.address || 'none');

  // Validate selected wallet for export
  const canExportWallet = selectedWallet &&
    selectedWallet.isEmbedded &&
    selectedWallet.walletClientType === 'privy';

  console.log("RecoveryKeyModal: Can export wallet:", canExportWallet);

  const handleExportWallet = async () => {
    if (!isAuthenticated) {
      setError('Please authenticate first');
      return;
    }

    if (!selectedWallet) {
      setError('No wallet selected for export');
      return;
    }

    if (!canExportWallet) {
      setError('Only embedded Privy wallets can be exported. External wallets are managed by their respective applications.');
      return;
    }

    setIsExporting(true);
    setError(null);

    try {
      console.log('Exporting Solana wallet:', selectedWallet.address);
      await exportSolanaWallet({ address: selectedWallet.address });

      // Success - the Privy modal will handle showing the private key
      console.log('Wallet export initiated successfully');
      setExportSuccess(true);

      // Auto-close the modal after a short delay to let user see success state
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      console.error('Export wallet error:', err);

      // Provide more specific error messages based on the error type
      let errorMessage = 'Failed to export wallet';

      if (err instanceof Error) {
        const message = err.message.toLowerCase();

        if (message.includes('not authenticated') || message.includes('unauthorized')) {
          errorMessage = 'Authentication failed. Please log in again and try again.';
        } else if (message.includes('not found') || message.includes('wallet not found')) {
          errorMessage = 'Wallet not found. Please ensure the wallet is properly connected.';
        } else if (message.includes('permission') || message.includes('denied')) {
          errorMessage = 'Permission denied. You may not have access to export this wallet.';
        } else if (message.includes('network') || message.includes('connection')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (message.includes('embedded') || message.includes('external')) {
          errorMessage = 'This wallet cannot be exported. Only embedded Privy wallets support private key export.';
        } else {
          errorMessage = `Export failed: ${err.message}`;
        }
      }

      setError(errorMessage);
    } finally {
      setIsExporting(false);
    }
  };

  const handleClose = () => {
    setError(null);
    setExportSuccess(false);
    onClose();
  };

  return (
    <div className="fixed inset-0 flex justify-center items-center bg-black/80 z-50">
      <div className="bg-[#181C20] border border-gray-600 max-w-lg w-full p-6 rounded-xl shadow-2xl relative">
        {/* Close Button */}
        <button 
          onClick={handleClose} 
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        >
          <IoClose size={24} />
        </button>

        {/* Modal Title */}
        <div className="flex items-center space-x-3 text-white text-2xl font-bold mb-6">
          <div className="w-8 h-8 bg-[#14FFA2]/20 rounded-lg flex items-center justify-center">
            <svg 
              width="20" 
              height="20" 
              viewBox="0 0 24 24" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" 
                stroke="#14FFA2" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M9 12L11 14L15 10" 
                stroke="#14FFA2" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <h2>Recovery Key</h2>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Description */}
          <div className="text-gray-300">
            <p className="mb-4">
              Export your Solana wallet's private key to use it with other wallet applications like Phantom.
            </p>
            <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="text-yellow-500 mt-0.5 flex-shrink-0"
                >
                  <path
                    d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <div>
                  <p className="text-yellow-500 font-medium text-sm">Important Security Notice</p>
                  <p className="text-yellow-400 text-sm mt-1">
                    Never share your private key with anyone. Anyone with access to your private key can control your wallet and funds.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="text-red-500"
                >
                  <path
                    d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            </div>
          )}

          {/* Success Display */}
          {exportSuccess && (
            <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="text-green-500"
                >
                  <path
                    d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="text-green-400 text-sm">Export initiated successfully! The Privy modal will open to show your private key.</p>
              </div>
            </div>
          )}

          {/* Wallet Export Section */}
          {isAuthenticated ? (
            <div className="space-y-4">
              {selectedWallet ? (
                <div>
                  <p className="text-gray-400 text-lg mb-4">Selected wallet:</p>

                  {/* Selected Wallet Info */}
                  <div className="bg-[#1D2226] border border-gray-600 rounded-lg p-4 mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-[#9945FF] rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">SOL</span>
                      </div>
                      <div className="flex-1">
                        <div className="text-white font-medium">{selectedWallet.name}</div>
                        <div className="text-gray-400 text-sm font-mono">
                          {`${selectedWallet.address.slice(0, 8)}...${selectedWallet.address.slice(-8)}`}
                        </div>
                        <div className="text-gray-500 text-xs mt-1">
                          {selectedWallet.isEmbedded ? 'Embedded Wallet' : 'External Wallet'} •
                          {selectedWallet.walletClientType}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Export Button */}
                  {canExportWallet ? (
                    <button
                      onClick={handleExportWallet}
                      disabled={isExporting}
                      className="w-full bg-[#9945FF] hover:bg-[#8A3FE8] text-white rounded-lg p-4 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                    >
                      <div className="flex items-center justify-center space-x-2">
                        {isExporting ? (
                          <>
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                            <span>Exporting...</span>
                          </>
                        ) : (
                          <span>Export Private Key</span>
                        )}
                      </div>
                    </button>
                  ) : (
                    <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                      <div className="flex items-start space-x-2">
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          className="text-red-500 mt-0.5 flex-shrink-0"
                        >
                          <path
                            d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <div>
                          <p className="text-red-400 font-medium text-sm">Cannot Export Wallet</p>
                          <p className="text-red-300 text-sm mt-1">
                            Only embedded Privy wallets can be exported. External wallets are managed by their respective applications.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-400">No wallet selected.</p>
                  <p className="text-gray-500 text-sm mt-2">
                    Please select a wallet from the wallet management screen to export its private key.
                  </p>
                </div>
              )}

            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-400">Please authenticate to export your wallet.</p>
            </div>
          )}
        </div>

        {/* Close Button */}
        <button
          className="bg-gray-600 hover:bg-gray-500 text-white w-full py-4 mt-8 rounded-2xl text-xl font-bold transition-colors"
          onClick={handleClose}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default RecoveryKeyModal;
