import React, { useState, useEffect } from 'react';
import { calculateTokenAge, getAgeDisplayClasses, shouldUpdateLive, TokenAgeInfo } from '@/utils/tokenAge';

interface TokenAgeProps {
  createdAt: string | null | undefined;
  className?: string;
}

/**
 * TokenAge Component
 * Displays token age with live countdown for new tokens (< 1 minute)
 * and static age display for older tokens
 */
const TokenAge: React.FC<TokenAgeProps> = ({ createdAt, className = '' }) => {
  // Safety check: ensure createdAt is a string or null
  const safeCreatedAt = typeof createdAt === 'string' ? createdAt : null;
  
  const [ageInfo, setAgeInfo] = useState<TokenAgeInfo>(() => calculateTokenAge(safeCreatedAt));

  useEffect(() => {
    // Initial calculation with safe value
    setAgeInfo(calculateTokenAge(safeCreatedAt));

    // Set up interval for live updates if needed
    if (shouldUpdateLive(safeCreatedAt)) {
      const interval = setInterval(() => {
        const newAgeInfo = calculateTokenAge(safeCreatedAt);
        setAgeInfo(newAgeInfo);

        // Stop updating once token reaches 1 minute old
        if (!newAgeInfo.isLive) {
          clearInterval(interval);
        }
      }, 1000); // Update every second

      return () => clearInterval(interval);
    }
  }, [safeCreatedAt]);

  // Don't render anything if no age info or if displayText is not a string
  if (!ageInfo.displayText || typeof ageInfo.displayText !== 'string') {
    return null;
  }

  const displayClasses = getAgeDisplayClasses(ageInfo);

  return (
    <div className={`text-base ${displayClasses} ${className}`}>
      {ageInfo.displayText}
    </div>
  );
};

export default TokenAge;
