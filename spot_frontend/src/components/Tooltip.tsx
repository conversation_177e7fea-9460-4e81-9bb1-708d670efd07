import React, { ReactNode } from 'react';

interface TooltipProps {
  content: string;
  children: ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

export const Tooltip: React.FC<TooltipProps> = ({ content, children, position = 'top' }) => {
  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2',
  };

  return (
    <div className="relative group">
      {children}
      <div className={`absolute ${positionClasses[position]} opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50`}>
        <div className="bg-gray-900 text-white text-xs rounded-lg py-1.5 px-2.5 whitespace-nowrap border border-gray-700 shadow-lg">
          {content}
          <div className={`absolute w-2 h-2 bg-gray-900 border-gray-700 transform rotate-45 ${
            position === 'top' ? 'bottom-[-4px] left-1/2 -translate-x-1/2 border-r border-b' :
            position === 'bottom' ? 'top-[-4px] left-1/2 -translate-x-1/2 border-l border-t' :
            position === 'left' ? 'right-[-4px] top-1/2 -translate-y-1/2 border-t border-r' :
            'left-[-4px] top-1/2 -translate-y-1/2 border-b border-l'
          }`}></div>
        </div>
      </div>
    </div>
  );
};