import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Monitor, X, Minimize2, Maximize2, Activity, Cpu, HardDrive, Wifi, Clock, Zap } from 'lucide-react';

interface PerformanceStats {
  fps: number;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  renderTime: number;
  componentCount: number;
  networkRequests: number;
  webSocketStatus: string;
  pulseDataSize: {
    new: number;
    bonding: number;
    bonded: number;
    total: number;
  };
  lastUpdate: string;
  cpuUsage?: number;
  heapSize?: {
    used: number;
    total: number;
    limit: number;
  };
}

interface PerformanceMonitorProps {
  activeTab: string;
  pulseData?: any;
  isWebSocketConnected?: boolean;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  activeTab,
  pulseData,
  isWebSocketConnected = false
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<PerformanceStats>({
    fps: 0,
    memoryUsage: { used: 0, total: 0, percentage: 0 },
    renderTime: 0,
    componentCount: 0,
    networkRequests: 0,
    webSocketStatus: 'Disconnected',
    pulseDataSize: { new: 0, bonding: 0, bonded: 0, total: 0 },
    lastUpdate: new Date().toLocaleTimeString()
  });

  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const renderStartTimeRef = useRef(0);
  const animationFrameRef = useRef<number>();
  const networkRequestsRef = useRef(0);
  const performanceObserverRef = useRef<PerformanceObserver>();

  // FPS Calculation with error handling
  const calculateFPS = useCallback(() => {
    try {
      const now = performance.now();
      frameCountRef.current++;
      
      if (now - lastTimeRef.current >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / (now - lastTimeRef.current));
        frameCountRef.current = 0;
        lastTimeRef.current = now;
        return fps;
      }
      return stats.fps;
    } catch (err) {
      console.warn('Error calculating FPS:', err);
      return 0;
    }
  }, [stats.fps]);

  // Memory Usage Calculation with error handling
  const getMemoryUsage = useCallback(() => {
    try {
      if (typeof performance !== 'undefined' && 'memory' in performance) {
        const memory = (performance as any).memory;
        return {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024 * 100) / 100,
          percentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100),
          heapSize: {
            used: memory.usedJSHeapSize,
            total: memory.totalJSHeapSize,
            limit: memory.jsHeapSizeLimit
          }
        };
      }
    } catch (err) {
      console.warn('Error getting memory usage:', err);
    }
    
    return {
      used: 0,
      total: 0,
      percentage: 0,
      heapSize: { used: 0, total: 0, limit: 0 }
    };
  }, []);

  // Component Count with safer selectors
  const getComponentCount = useCallback(() => {
    try {
      if (typeof document === 'undefined') return 0;
      
      // Use simple, safe selectors
      const divCount = document.getElementsByTagName('div').length;
      const spanCount = document.getElementsByTagName('span').length;
      const buttonCount = document.getElementsByTagName('button').length;
      
      return divCount + spanCount + buttonCount;
    } catch (err) {
      console.warn('Error counting DOM elements:', err);
      return 0;
    }
  }, []);

  // Pulse Data Size Calculation
  const getPulseDataSize = useCallback(() => {
    try {
      if (!pulseData) {
        return { new: 0, bonding: 0, bonded: 0, total: 0 };
      }

      const newCount = Array.isArray(pulseData.new) ? pulseData.new.length : 0;
      const bondingCount = Array.isArray(pulseData.bonding) ? pulseData.bonding.length : 0;
      const bondedCount = Array.isArray(pulseData.bonded) ? pulseData.bonded.length : 0;

      return {
        new: newCount,
        bonding: bondingCount,
        bonded: bondedCount,
        total: newCount + bondingCount + bondedCount
      };
    } catch (err) {
      console.warn('Error calculating pulse data size:', err);
      return { new: 0, bonding: 0, bonded: 0, total: 0 };
    }
  }, [pulseData]);

  // Network Monitoring with better error handling
  const setupNetworkMonitoring = useCallback(() => {
    try {
      if (typeof window === 'undefined') return () => {};

      let cleanup: (() => void) | undefined;

      // Setup PerformanceObserver if available
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            try {
              const entries = list.getEntries();
              networkRequestsRef.current += entries.filter(entry => 
                entry.entryType === 'resource' || entry.entryType === 'navigation'
              ).length;
            } catch (err) {
              console.warn('Error processing performance entries:', err);
            }
          });

          observer.observe({ entryTypes: ['resource', 'navigation'] });
          performanceObserverRef.current = observer;
        } catch (err) {
          console.warn('PerformanceObserver setup failed:', err);
        }
      }

      // Monitor fetch requests
      if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
          networkRequestsRef.current++;
          return originalFetch(...args);
        };

        cleanup = () => {
          if (performanceObserverRef.current) {
            performanceObserverRef.current.disconnect();
          }
          window.fetch = originalFetch;
        };
      }

      return cleanup || (() => {});
    } catch (err) {
      console.warn('Error setting up network monitoring:', err);
      return () => {};
    }
  }, []);

  // Main update loop with comprehensive error handling
  const updateStats = useCallback(() => {
    try {
      renderStartTimeRef.current = performance.now();

      const memoryInfo = getMemoryUsage();
      const newStats: PerformanceStats = {
        fps: calculateFPS(),
        memoryUsage: {
          used: memoryInfo.used,
          total: memoryInfo.total,
          percentage: memoryInfo.percentage
        },
        renderTime: Math.round((performance.now() - renderStartTimeRef.current) * 100) / 100,
        componentCount: getComponentCount(),
        networkRequests: networkRequestsRef.current,
        webSocketStatus: isWebSocketConnected ? 'Connected' : 'Disconnected',
        pulseDataSize: getPulseDataSize(),
        lastUpdate: new Date().toLocaleTimeString(),
        heapSize: memoryInfo.heapSize
      };

      setStats(newStats);
      setError(null); // Clear any previous errors
      
      animationFrameRef.current = requestAnimationFrame(updateStats);
    } catch (err) {
      console.error('Error updating performance stats:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      
      // Continue monitoring even if there's an error
      animationFrameRef.current = requestAnimationFrame(updateStats);
    }
  }, [calculateFPS, getMemoryUsage, getComponentCount, getPulseDataSize, isWebSocketConnected]);

  // Initialize monitoring with error handling
  useEffect(() => {
    try {
      const cleanup = setupNetworkMonitoring();
      updateStats();

      return () => {
        try {
          if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current);
          }
          cleanup();
        } catch (err) {
          console.warn('Error during cleanup:', err);
        }
      };
    } catch (err) {
      console.error('Error initializing performance monitor:', err);
      setError(err instanceof Error ? err.message : 'Initialization error');
    }
  }, [setupNetworkMonitoring, updateStats]);

  // Check if performance monitoring is enabled
  const isEnabled = import.meta.env.VITE_ENABLE_PERFORMANCE_MONITOR === 'true';

  if (!isEnabled) {
    return null;
  }

  const getFPSColor = (fps: number) => {
    if (fps >= 50) return 'text-green-400';
    if (fps >= 30) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getMemoryColor = (percentage: number) => {
    if (percentage < 50) return 'text-green-400';
    if (percentage < 80) return 'text-yellow-400';
    return 'text-red-400';
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <>
      {/* Performance Monitor Toggle Button */}
      {!isVisible && (
        <button
          onClick={() => setIsVisible(true)}
          className="fixed top-4 right-4 z-50 bg-gray-800/80 backdrop-blur-sm text-white p-2 rounded-lg hover:bg-gray-700/80 transition-colors"
          title="Show Performance Monitor"
        >
          <Monitor size={20} strokeLinecap="round" strokeLinejoin="round" />
        </button>
      )}

      {/* Performance Monitor Panel */}
      {isVisible && (
        <div className={`fixed top-4 right-4 z-50 bg-gray-900/95 backdrop-blur-sm text-white rounded-lg border border-gray-700 shadow-2xl transition-all duration-300 ${
          isMinimized ? 'w-80 h-12' : 'w-96 max-h-[80vh] overflow-y-auto'
        }`}>
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b border-gray-700">
            <div className="flex items-center gap-2">
              <Activity size={16} className="text-blue-400" strokeLinecap="round" strokeLinejoin="round" />
              <span className="text-sm font-medium">Performance Monitor</span>
              <span className="text-xs text-gray-400">({activeTab})</span>
            </div>
            <div className="flex gap-1">
              <button
                onClick={() => setIsMinimized(!isMinimized)}
                className="p-1 hover:bg-gray-700 rounded transition-colors"
                title={isMinimized ? "Maximize" : "Minimize"}
              >
                {isMinimized ? 
                  <Maximize2 size={14} strokeLinecap="round" strokeLinejoin="round" /> : 
                  <Minimize2 size={14} strokeLinecap="round" strokeLinejoin="round" />
                }
              </button>
              <button
                onClick={() => setIsVisible(false)}
                className="p-1 hover:bg-gray-700 rounded transition-colors"
                title="Close"
              >
                <X size={14} strokeLinecap="round" strokeLinejoin="round" />
              </button>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-2 bg-red-900/20 border-b border-red-800 text-red-400 text-xs">
              Error: {error}
            </div>
          )}

          {/* Stats Content */}
          {!isMinimized && (
            <div className="p-3 space-y-3 text-xs">
              {/* FPS and Render Performance */}
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-800/50 p-2 rounded">
                  <div className="flex items-center gap-1 mb-1">
                    <Zap size={12} className="text-yellow-400" strokeLinecap="round" strokeLinejoin="round" />
                    <span className="text-gray-300">FPS</span>
                  </div>
                  <div className={`text-lg font-mono ${getFPSColor(stats.fps)}`}>
                    {stats.fps}
                  </div>
                </div>
                <div className="bg-gray-800/50 p-2 rounded">
                  <div className="flex items-center gap-1 mb-1">
                    <Clock size={12} className="text-blue-400" strokeLinecap="round" strokeLinejoin="round" />
                    <span className="text-gray-300">Render</span>
                  </div>
                  <div className="text-lg font-mono text-blue-400">
                    {stats.renderTime}ms
                  </div>
                </div>
              </div>

              {/* Memory Usage */}
              <div className="bg-gray-800/50 p-2 rounded">
                <div className="flex items-center gap-1 mb-2">
                  <HardDrive size={12} className="text-purple-400" strokeLinecap="round" strokeLinejoin="round" />
                  <span className="text-gray-300">Memory Usage</span>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Used:</span>
                    <span className={getMemoryColor(stats.memoryUsage.percentage)}>
                      {stats.memoryUsage.used} MB ({stats.memoryUsage.percentage}%)
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total:</span>
                    <span className="text-gray-400">{stats.memoryUsage.total} MB</span>
                  </div>
                  {stats.heapSize && stats.heapSize.used > 0 && (
                    <div className="flex justify-between">
                      <span>Heap:</span>
                      <span className="text-gray-400">{formatBytes(stats.heapSize.used)}</span>
                    </div>
                  )}
                </div>
                <div className="w-full bg-gray-700 rounded-full h-1.5 mt-2">
                  <div
                    className={`h-1.5 rounded-full transition-all ${
                      stats.memoryUsage.percentage < 50 ? 'bg-green-400' :
                      stats.memoryUsage.percentage < 80 ? 'bg-yellow-400' : 'bg-red-400'
                    }`}
                    style={{ width: `${Math.min(Math.max(stats.memoryUsage.percentage, 0), 100)}%` }}
                  />
                </div>
              </div>

              {/* Pulse Data Stats */}
              <div className="bg-gray-800/50 p-2 rounded">
                <div className="flex items-center gap-1 mb-2">
                  <Activity size={12} className="text-green-400" strokeLinecap="round" strokeLinejoin="round" />
                  <span className="text-gray-300">Pulse Data</span>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex justify-between">
                    <span>New:</span>
                    <span className="text-green-400">{stats.pulseDataSize.new}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Bonding:</span>
                    <span className="text-yellow-400">{stats.pulseDataSize.bonding}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Bonded:</span>
                    <span className="text-blue-400">{stats.pulseDataSize.bonded}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total:</span>
                    <span className="text-white font-medium">{stats.pulseDataSize.total}</span>
                  </div>
                </div>
              </div>

              {/* System Stats */}
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-800/50 p-2 rounded">
                  <div className="flex items-center gap-1 mb-1">
                    <Cpu size={12} className="text-orange-400" strokeLinecap="round" strokeLinejoin="round" />
                    <span className="text-gray-300">DOM Elements</span>
                  </div>
                  <div className="text-lg font-mono text-orange-400">
                    {stats.componentCount}
                  </div>
                </div>
                <div className="bg-gray-800/50 p-2 rounded">
                  <div className="flex items-center gap-1 mb-1">
                    <Wifi 
                      size={12} 
                      className={isWebSocketConnected ? 'text-green-400' : 'text-red-400'} 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                    />
                    <span className="text-gray-300">Network</span>
                  </div>
                  <div className="text-lg font-mono text-gray-400">
                    {stats.networkRequests}
                  </div>
                </div>
              </div>

              {/* WebSocket Status */}
              <div className="bg-gray-800/50 p-2 rounded">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">WebSocket:</span>
                  <span className={`font-medium ${isWebSocketConnected ? 'text-green-400' : 'text-red-400'}`}>
                    {stats.webSocketStatus}
                  </span>
                </div>
              </div>

              {/* Last Update */}
              <div className="text-center text-gray-500 text-xs border-t border-gray-700 pt-2">
                Last updated: {stats.lastUpdate}
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default PerformanceMonitor;
