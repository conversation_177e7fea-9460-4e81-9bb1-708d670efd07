import { useState, useEffect, useRef, useCallback } from 'react';

export interface PerformanceMetrics {
  fps: number;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  renderTime: number;
  components: number;
  networkRequests: number;
  webSocketConnected: boolean;
  lastUpdate: number;
}

export const usePerformanceMonitor = (enableMonitoring: boolean = true) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    memory: { used: 0, total: 0, percentage: 0 },
    renderTime: 0,
    components: 0,
    networkRequests: 0,
    webSocketConnected: false,
    lastUpdate: Date.now()
  });

  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const animationFrame = useRef<number>();
  const networkCounter = useRef(0);
  const renderStartTime = useRef(0);

  const measureRenderTime = useCallback(() => {
    renderStartTime.current = performance.now();
  }, []);

  const calculateMetrics = useCallback(() => {
    if (!enableMonitoring) return;

    const now = performance.now();
    frameCount.current++;

    // Calculate FPS every second
    if (now - lastTime.current >= 1000) {
      const fps = Math.round((frameCount.current * 1000) / (now - lastTime.current));
      
      // Memory usage
      let memoryInfo = { used: 0, total: 0, percentage: 0 };
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        memoryInfo = {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024 * 100) / 100,
          percentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)
        };
      }

      // Component count approximation (fixed)
      let componentCount = 0;
      try {
        // Use safer DOM queries
        componentCount = document.querySelectorAll('div, span, button, input').length;
      } catch (error) {
        console.warn('Error counting DOM elements:', error);
        componentCount = 0;
      }

      // Render time
      const renderTime = renderStartTime.current ? 
        Math.round((performance.now() - renderStartTime.current) * 100) / 100 : 0;

      setMetrics(prev => ({
        ...prev,
        fps,
        memory: memoryInfo,
        renderTime,
        components: componentCount,
        networkRequests: networkCounter.current,
        lastUpdate: now
      }));

      frameCount.current = 0;
      lastTime.current = now;
    }

    animationFrame.current = requestAnimationFrame(calculateMetrics);
  }, [enableMonitoring]);

  useEffect(() => {
    if (!enableMonitoring) return;

    // Start monitoring
    calculateMetrics();

    // Monitor network requests (with error handling)
    let originalFetch: typeof window.fetch | undefined;
    
    if (typeof window !== 'undefined' && window.fetch) {
      originalFetch = window.fetch;
      window.fetch = async (...args) => {
        networkCounter.current++;
        return originalFetch!(...args);
      };
    }

    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }
      if (originalFetch && typeof window !== 'undefined') {
        window.fetch = originalFetch;
      }
    };
  }, [enableMonitoring, calculateMetrics]);

  return {
    metrics,
    measureRenderTime,
    isEnabled: enableMonitoring
  };
};
