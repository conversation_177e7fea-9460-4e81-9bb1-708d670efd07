# Performance Monitor for Pulse Page

A comprehensive performance monitoring component that displays real-time metrics for the Pulse page including FPS, memory usage, render times, and network activity.

## Features

### 📊 Performance Metrics
- **FPS (Frames Per Second)**: Real-time frame rate monitoring
- **Memory Usage**: JavaScript heap usage with percentage and visual indicators
- **Render Time**: Component render duration in milliseconds
- **Component Count**: Estimated number of DOM elements
- **Network Requests**: Count of network requests made
- **WebSocket Status**: Real-time connection status monitoring

### 🎯 Pulse-Specific Metrics
- **Pulse Data Size**: Live count of tokens in each category (New, Bonding, Bonded)
- **Active Tab**: Current tab being displayed
- **Data Source**: Whether data is coming from WebSocket or API
- **Last Update**: Timestamp of the most recent data update

### 🎨 Visual Features
- **Color-coded Indicators**: 
  - Green: Good performance (FPS >50, Memory <50%)
  - Yellow: Warning levels (FPS 30-50, Memory 50-80%)
  - Red: Critical levels (FPS <30, Memory >80%)
- **Progress Bars**: Visual memory usage representation
- **Real-time Updates**: Metrics update every frame via requestAnimationFrame
- **Minimizable Interface**: Can be collapsed to save screen space

## Configuration

### Environment Variables

Add to your `.env` file:

```env
# Enable/disable performance monitor
VITE_ENABLE_PERFORMANCE_MONITOR=true
```

### Usage in Components

```tsx
import { PerformanceMonitor } from '@/components/PerformanceMonitor';

// In your component
<PerformanceMonitor 
  activeTab="New"
  pulseData={pulseData}
  isWebSocketConnected={isConnected}
/>
```

### Props

| Prop | Type | Description | Required |
|------|------|-------------|----------|
| `activeTab` | `string` | Current active tab name | Yes |
| `pulseData` | `any` | Pulse data object with new/bonding/bonded arrays | No |
| `isWebSocketConnected` | `boolean` | WebSocket connection status | No |

## Components

### PerformanceMonitor
Main monitoring component that displays all metrics in a draggable overlay.

### PerformanceConfig
Configuration component for toggling monitor visibility (planned feature).

### usePerformanceMonitor
Custom hook for accessing performance metrics programmatically.

## Performance Impact

The monitor is designed to have minimal performance impact:
- Uses `requestAnimationFrame` for efficient updates
- Calculates metrics only when visible
- Debounced calculations for expensive operations
- Cleanup on component unmount

## Browser Compatibility

- **Memory Metrics**: Requires Chrome/Edge (performance.memory API)
- **FPS Calculation**: All modern browsers
- **Network Monitoring**: All modern browsers with Performance Observer API

## Development vs Production

The monitor automatically checks the environment variable and only renders when enabled. This ensures zero impact on production builds when disabled.

## Troubleshooting

### Monitor Not Showing
1. Check that `VITE_ENABLE_PERFORMANCE_MONITOR=true` in your `.env` file
2. Ensure the environment variable is loaded properly
3. Restart your development server after changing `.env`

### Inaccurate Metrics
1. Memory metrics may not be available in all browsers
2. Component count is an approximation based on DOM elements
3. Network request count includes all requests, not just Pulse-related ones

## Examples

### Basic Usage
```tsx
<PerformanceMonitor activeTab="Bonding" />
```

### With Pulse Data
```tsx
<PerformanceMonitor 
  activeTab="New"
  pulseData={{
    new: [/* token array */],
    bonding: [/* token array */],
    bonded: [/* token array */]
  }}
  isWebSocketConnected={true}
/>
```

### Programmatic Access
```tsx
import { usePerformanceMonitor } from '@/components/PerformanceMonitor';

const { metrics, isEnabled } = usePerformanceMonitor(true);
console.log('Current FPS:', metrics.fps);
console.log('Memory usage:', metrics.memory.percentage + '%');
```
