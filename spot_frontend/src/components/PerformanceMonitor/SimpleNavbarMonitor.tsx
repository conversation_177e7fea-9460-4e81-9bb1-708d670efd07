import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Monitor, X, Minimize2, Maximize2, Activity, Cpu, HardDrive, Wifi, Clock, Zap } from 'lucide-react';

interface PerformanceStats {
  fps: number;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  renderTime: number;
  componentCount: number;
  networkRequests: number;
  webSocketStatus: string;
  lastUpdate: string;
  heapSize?: {
    used: number;
    total: number;
    limit: number;
  };
}

const SimpleNavbarMonitor: React.FC = () => {
  const [showPanel, setShowPanel] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [stats, setStats] = useState<PerformanceStats>({
    fps: 0,
    memoryUsage: { used: 0, total: 0, percentage: 0 },
    renderTime: 0,
    componentCount: 0,
    networkRequests: 0,
    webSocketStatus: 'Disconnected',
    lastUpdate: new Date().toLocaleTimeString()
  });

  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const renderStartTimeRef = useRef(0);
  const animationFrameRef = useRef<number>();
  const networkRequestsRef = useRef(0);

  // Check if performance monitoring is enabled
  const isEnabled = import.meta.env.VITE_ENABLE_PERFORMANCE_MONITOR === 'true';

  // Performance calculation functions
  const calculateFPS = useCallback(() => {
    try {
      const now = performance.now();
      frameCountRef.current++;
      
      if (now - lastTimeRef.current >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / (now - lastTimeRef.current));
        frameCountRef.current = 0;
        lastTimeRef.current = now;
        return fps;
      }
      return stats.fps;
    } catch (err) {
      return 0;
    }
  }, [stats.fps]);

  const getMemoryUsage = useCallback(() => {
    try {
      if (typeof performance !== 'undefined' && 'memory' in performance) {
        const memory = (performance as any).memory;
        return {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024 * 100) / 100,
          percentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100),
          heapSize: {
            used: memory.usedJSHeapSize,
            total: memory.totalJSHeapSize,
            limit: memory.jsHeapSizeLimit
          }
        };
      }
    } catch (err) {
      // Ignore error
    }
    
    return {
      used: 0,
      total: 0,
      percentage: 0,
      heapSize: { used: 0, total: 0, limit: 0 }
    };
  }, []);

  const getComponentCount = useCallback(() => {
    try {
      if (typeof document === 'undefined') return 0;
      return document.getElementsByTagName('div').length + 
             document.getElementsByTagName('span').length + 
             document.getElementsByTagName('button').length;
    } catch (err) {
      return 0;
    }
  }, []);

  const updateStats = useCallback(() => {
    try {
      renderStartTimeRef.current = performance.now();

      const memoryInfo = getMemoryUsage();
      const newStats: PerformanceStats = {
        fps: calculateFPS(),
        memoryUsage: {
          used: memoryInfo.used,
          total: memoryInfo.total,
          percentage: memoryInfo.percentage
        },
        renderTime: Math.round((performance.now() - renderStartTimeRef.current) * 100) / 100,
        componentCount: getComponentCount(),
        networkRequests: networkRequestsRef.current,
        webSocketStatus: 'Connected',
        lastUpdate: new Date().toLocaleTimeString(),
        heapSize: memoryInfo.heapSize
      };

      setStats(newStats);
      animationFrameRef.current = requestAnimationFrame(updateStats);
    } catch (err) {
      animationFrameRef.current = requestAnimationFrame(updateStats);
    }
  }, [calculateFPS, getMemoryUsage, getComponentCount]);

  useEffect(() => {
    if (showPanel) {
      updateStats();
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [showPanel, updateStats]);

  if (!isEnabled) {
    return null;
  }

  const getFPSColor = (fps: number) => {
    if (fps >= 50) return 'text-green-400';
    if (fps >= 30) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getMemoryColor = (percentage: number) => {
    if (percentage < 50) return 'text-green-400';
    if (percentage < 80) return 'text-yellow-400';
    return 'text-red-400';
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <>
      {/* Monitor Icon in Navbar */}
      <button
        onClick={() => setShowPanel(!showPanel)}
        className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
          showPanel 
            ? 'bg-blue-600 text-white' 
            : 'bg-[#181C20] hover:bg-[#252A30] text-white hover:text-blue-400'
        }`}
        title="Performance Monitor"
      >
        <Monitor size={18} />
      </button>

      {/* Performance Data Panel - Shows directly when clicked */}
      {showPanel && (
        <div className={`fixed top-4 right-4 z-[9999] bg-gray-900/95 backdrop-blur-sm text-white rounded-lg border border-gray-700 shadow-2xl transition-all duration-300 ${
          isMinimized ? 'w-80 h-12' : 'w-96 max-h-[80vh] overflow-y-auto'
        }`}>
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b border-gray-700">
            <div className="flex items-center gap-2">
              <Activity size={16} className="text-blue-400" />
              <span className="text-sm font-medium">Performance Monitor</span>
            </div>
            <div className="flex gap-1">
              <button
                onClick={() => setIsMinimized(!isMinimized)}
                className="p-1 hover:bg-gray-700 rounded transition-colors"
                title={isMinimized ? "Maximize" : "Minimize"}
              >
                {isMinimized ? <Maximize2 size={14} /> : <Minimize2 size={14} />}
              </button>
              <button
                onClick={() => setShowPanel(false)}
                className="p-1 hover:bg-gray-700 rounded transition-colors"
                title="Close"
              >
                <X size={14} />
              </button>
            </div>
          </div>

          {/* Stats Content */}
          {!isMinimized && (
            <div className="p-3 space-y-3 text-xs">
              {/* FPS and Render Performance */}
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-800/50 p-2 rounded">
                  <div className="flex items-center gap-1 mb-1">
                    <Zap size={12} className="text-yellow-400" />
                    <span className="text-gray-300">FPS</span>
                  </div>
                  <div className={`text-lg font-mono ${getFPSColor(stats.fps)}`}>
                    {stats.fps}
                  </div>
                </div>
                <div className="bg-gray-800/50 p-2 rounded">
                  <div className="flex items-center gap-1 mb-1">
                    <Clock size={12} className="text-blue-400" />
                    <span className="text-gray-300">Render</span>
                  </div>
                  <div className="text-lg font-mono text-blue-400">
                    {stats.renderTime}ms
                  </div>
                </div>
              </div>

              {/* Memory Usage */}
              <div className="bg-gray-800/50 p-2 rounded">
                <div className="flex items-center gap-1 mb-2">
                  <HardDrive size={12} className="text-purple-400" />
                  <span className="text-gray-300">Memory Usage</span>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Used:</span>
                    <span className={getMemoryColor(stats.memoryUsage.percentage)}>
                      {stats.memoryUsage.used} MB ({stats.memoryUsage.percentage}%)
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total:</span>
                    <span className="text-gray-400">{stats.memoryUsage.total} MB</span>
                  </div>
                  {stats.heapSize && stats.heapSize.used > 0 && (
                    <div className="flex justify-between">
                      <span>Heap:</span>
                      <span className="text-gray-400">{formatBytes(stats.heapSize.used)}</span>
                    </div>
                  )}
                </div>
                <div className="w-full bg-gray-700 rounded-full h-1.5 mt-2">
                  <div
                    className={`h-1.5 rounded-full transition-all ${
                      stats.memoryUsage.percentage < 50 ? 'bg-green-400' :
                      stats.memoryUsage.percentage < 80 ? 'bg-yellow-400' : 'bg-red-400'
                    }`}
                    style={{ width: `${Math.min(Math.max(stats.memoryUsage.percentage, 0), 100)}%` }}
                  />
                </div>
              </div>

              {/* System Stats */}
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-800/50 p-2 rounded">
                  <div className="flex items-center gap-1 mb-1">
                    <Cpu size={12} className="text-orange-400" />
                    <span className="text-gray-300">DOM Elements</span>
                  </div>
                  <div className="text-lg font-mono text-orange-400">
                    {stats.componentCount}
                  </div>
                </div>
                <div className="bg-gray-800/50 p-2 rounded">
                  <div className="flex items-center gap-1 mb-1">
                    <Wifi size={12} className="text-green-400" />
                    <span className="text-gray-300">Network</span>
                  </div>
                  <div className="text-lg font-mono text-gray-400">
                    {stats.networkRequests}
                  </div>
                </div>
              </div>

              {/* Last Update */}
              <div className="text-center text-gray-500 text-xs border-t border-gray-700 pt-2">
                Last updated: {stats.lastUpdate}
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default SimpleNavbarMonitor;
