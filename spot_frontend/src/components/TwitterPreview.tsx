import React from 'react';
import { ExternalLink } from 'lucide-react';

interface TwitterPreviewProps {
  username?: string;
  tokenName?: string;
}

const TwitterPreview: React.FC<TwitterPreviewProps> = ({ username, tokenName }) => {
  // Extract username from URL if provided as full URL
  const extractUsername = (input?: string) => {
    if (!input) return null;
    
    // If it's already just a username
    if (!input.includes('/')) {
      return input.startsWith('@') ? input : `@${input}`;
    }
    
    // Extract from URL
    const match = input.match(/(?:twitter\.com|x\.com)\/([^\/\?]+)/i);
    return match ? `@${match[1]}` : null;
  };

  const twitterHandle = extractUsername(username);
  const twitterUrl = username && (username.startsWith('http') ? username : `https://x.com/${username.replace('@', '')}`);

  if (!twitterHandle) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p className="text-sm">No Twitter profile available</p>
      </div>
    );
  }

  return (
    <div className="w-64 bg-black/95 border border-gray-800 rounded-lg shadow-2xl">
      {/* Header with Twitter/X branding */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <svg viewBox="0 0 24 24" className="w-5 h-5 fill-white" aria-hidden="true">
              <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path>
            </svg>
            <span className="text-white font-semibold">X</span>
          </div>
        </div>
      </div>

      {/* Profile Preview */}
      <div className="p-4">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-lg">
              {tokenName ? tokenName[0].toUpperCase() : twitterHandle[1].toUpperCase()}
            </span>
          </div>
          <div>
            <h3 className="text-white font-semibold text-sm">{tokenName || 'Token'}</h3>
            <p className="text-gray-400 text-sm">{twitterHandle}</p>
          </div>
        </div>

        {/* View on X button */}
        <a
          href={twitterUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="w-full mt-3 px-4 py-2 bg-white text-black font-medium rounded-full 
                   hover:bg-gray-100 transition-colors duration-200 
                   flex items-center justify-center gap-2 text-sm"
          onClick={(e) => e.stopPropagation()}
        >
          View on X
          <ExternalLink size={14} />
        </a>
      </div>
    </div>
  );
};

export default TwitterPreview;