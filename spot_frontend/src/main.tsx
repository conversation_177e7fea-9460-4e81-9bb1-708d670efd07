import React, { Component, ErrorInfo } from "react";
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { PrivyProvider } from '@privy-io/react-auth';
// import { SmartWalletsProvider } from '@privy-io/react-auth/smart-wallets'; // Not needed for Solana
import './index.css';
import App from './App';
// Removed EVM-related imports since we're Solana-only
// import { bsc } from 'viem/chains';
// import { addRpcUrlOverrideToChain } from '@privy-io/chains';
// import { ethers } from 'ethers';
// Removed EVM paymaster imports
// import { initializePaymasterClient } from './utils/paymasterClient';
// import { getBundlerUrl, getPaymasterUrl } from './utils/bundlerPaymasterService';

// Simple error boundary component to catch Privy initialization errors
interface ErrorBoundaryProps {
  children: React.ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundaryForPrivy extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error("Error caught by boundary:", error, errorInfo);
  }

  render(): React.ReactNode {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-[#141416] text-white flex-col">
          <h2 className="text-xl mb-4">Authentication System Error</h2>
          <p className="text-md text-red-400 mb-6">
            {this.state.error?.message || "An error occurred in the authentication system"}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
          >
            Reload Now
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Configure Privy
interface LoginParams {
  user: any;
}

const handleLogin = ({user}: LoginParams) => {
  console.log("User logged in successfully", user);

  // Dispatch an event to trigger session signer initialization
  window.dispatchEvent(new CustomEvent('initialize-session-signers', {
    detail: { userId: user.id }
  }));

  // DISABLE automatic wallet creation if DISABLE_AUTO_CONNECT flag is set
  if (window.DISABLE_AUTO_CONNECT === true) {
    console.log("Automatic wallet creation disabled via DISABLE_AUTO_CONNECT flag");
    return;
  }

  // Force smart wallet creation if needed - this helps ensure wallets are created
  try {
    // Check if wallet creation was already attempted this session
    const walletCreationAttempted = localStorage.getItem('smart_wallet_creation_attempted') === 'true';
    const hasStoredWalletAddress = !!localStorage.getItem('smart_wallet_address');

    // Only trigger creation if not previously attempted
    if (!walletCreationAttempted && !hasStoredWalletAddress) {
      console.log("First login - triggering smart wallet creation");

      // Dispatch a custom event for smart wallet initialization
      window.dispatchEvent(new CustomEvent('force-smart-wallet-creation', {
        detail: { userId: user.id }
      }));

      // Store a flag to indicate we've started smart wallet creation
      localStorage.setItem('smart_wallet_creation_triggered', 'true');
      localStorage.setItem('smart_wallet_creation_attempted', 'true');

      console.log("Smart wallet creation triggered");
    } else {
      console.log("Skipping wallet creation - already attempted or wallet exists");
    }
  } catch (error) {
    console.error("Error triggering smart wallet creation:", error);
  }
};

const handleLogout = () => {
  console.log("User logged out - clearing all local data");

  try {
    // Check if the Privy token exists before attempting to remove it
    if (localStorage.getItem("privy:token")) {
      localStorage.removeItem("privy:token");
    }

    // Clear all localStorage items related to the app
    localStorage.removeItem("selectedTokens");
    localStorage.removeItem("activeToken");
    localStorage.removeItem("phantom_wallet");
    localStorage.removeItem("walletAddresses");
    localStorage.removeItem("privy:connections");
    localStorage.removeItem("privy_solana_wallet_cache"); // Clear Privy wallet ID cache

    // Attempt to clear all Privy-related localStorage items
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('privy:') ||
          key.includes('wallet') ||
          key.includes('phantom') ||
          key.includes('tokens')) {
        console.log(`Clearing localStorage item: ${key}`);
        localStorage.removeItem(key);
      }
    });

    // Clear IndexedDB databases related to the app
    const clearIndexedDB = async () => {
      try {
        // Get all IndexedDB databases
        const databases = await window.indexedDB.databases();

        // Delete each database related to the app
        for (const db of databases) {
          if (db.name && (
              db.name.includes('privy') ||
              db.name.includes('wallet') ||
              db.name.includes('phantom') ||
              db.name.includes('local-forage'))) {
            console.log(`Deleting IndexedDB database: ${db.name}`);
            window.indexedDB.deleteDatabase(db.name);
          }
        }
      } catch (error) {
        console.error("Error clearing IndexedDB:", error);
      }
    };

    // Execute IndexedDB cleanup
    clearIndexedDB();

    // Clear any cached data in sessionStorage
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith('privy:') ||
          key.includes('wallet') ||
          key.includes('phantom')) {
        sessionStorage.removeItem(key);
      }
    });

    // Add a small delay to allow state cleanup before notifying components
    setTimeout(() => {
      // Dispatch an event to notify components about the full logout
      window.dispatchEvent(new CustomEvent('app:complete-logout'));
      console.log("Logout cleanup completed");
    }, 100);
  } catch (error) {
    console.error("Error during logout cleanup:", error);
  }
};

// BSC configuration removed - Solana only
/*
// Configure BSC with a public RPC endpoint
const bscPublicRpcUrl = 'https://bsc-dataseed.binance.org/';
const bscWithRpcOverride = addRpcUrlOverrideToChain(bsc, bscPublicRpcUrl);

// Get bundler and paymaster URLs using our centralized service
const nodeRealApiKey = import.meta.env.VITE_NODEREAL_API_KEY || '';
const pimlicoApiKey = import.meta.env.VITE_PIMLICO_API_KEY || '';

// Use NodeReal as bundler and Pimlico for paymaster and estimation APIs
const bundlerUrl = `https://bsc-mainnet.nodereal.io/v1/${nodeRealApiKey}`;
const paymasterUrl = `https://api.pimlico.io/v2/56/rpc?apikey=${pimlicoApiKey}`;
// Use Pimlico's bundler endpoint for gas estimation methods that NodeReal doesn't support
const estimationBundlerUrl = paymasterUrl;

// NodeReal configuration - allows compatibility with NodeReal-specific methods
const nodeRealConfig = {
  // Disable Pimlico-specific methods for the bundler
  disablePimlicoMethods: true, // Disable Pimlico methods for bundler operations
  // NodeReal doesn't support the PM API in the same way
  disablePaymasterApi: true, // Disable paymaster API in NodeReal, use Pimlico separately
  // Set default gas values for BSC
  defaultGasForBSC: 1000000n,
  // Configure custom RPC URL
  rpcUrl: bundlerUrl
};
*/

// Create configuration following Privy 2.0 migration guidelines
const privyConfig = {
  loginMethods: ['email','passkey'],
  // For Solana-only, we don't need supportedChains (that's for EVM)
  // supportedChains: [],
  // defaultChain is not needed for Solana-only
  solanaClusters: [
    {
      name: 'mainnet-beta',
      rpcUrl: import.meta.env.VITE_SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com'
    }
  ],
  appearance: {
    theme: 'dark',
    accentColor: '#00ff00',
    showWalletLoginButton: false,
    // Set wallet chain type to Solana only
    walletChainType: 'solana-only'
  },
  embeddedWallets: {
    createOnLogin: 'all-users',
    // Enable session signers for Solana by default
    delegateWalletsByDefault: true
  },
  // Disable smart wallets since we're only supporting Solana
  /*smartWallets: {
    createOnLogin: 'all-users',
    noPromptOnSignature: false,
    // Enable session signers for EVM smart wallets by default
    delegateWalletsByDefault: true,
    smartAccountConfig: {
      version: 'v0.7',
      eoa: 'embedded',
      factoryAddresses: {
        '56': '******************************************' // v0.7-compatible factory address for BSC
      },
    },
    // Configure Account Abstraction (ERC-4337) with Pimlico bundler + paymaster
    accountAbstraction: {
      enabled: true,
      // Primary bundler URL is NodeReal
      bundlerUrls: {
        '56': bundlerUrl, // BSC mainnet NodeReal bundler
      },
      // Pimlico for paymaster
      paymasterUrls: {
        '56': paymasterUrl, // Pimlico paymaster
      },
      // Override ERC-4337 method configuration - CRITICAL: ensure all methods go to the right provider
      estimationBundlerUrls: {
        '56': paymasterUrl, // IMPORTANT: Always use Pimlico for estimation methods
      },
      // Create a secondary bundler for sending operations directly to Pimlico
      // NodeReal doesn't support key ERC-4337 methods
      bundlerApiUrls: {
        '56': paymasterUrl, // IMPORTANT: Always use Pimlico for sending operations
      },
      overrideMethodRouting: {
        // Route all ERC-4337 methods to the appropriate provider
        '56': {
          // CRITICAL: Gas estimation must ALWAYS go to Pimlico, not NodeReal
          eth_estimateUserOperationGas: 'estimationBundler',
          // Transaction submission must also go to Pimlico
          eth_sendUserOperation: 'bundlerApi',
          // Other methods can go to either provider
          eth_supportedEntryPoints: 'estimationBundler',
          eth_chainId: 'estimationBundler',
          // These methods aren't used in our current implementation
          eth_getUserOperationByHash: 'preferred',
          eth_getUserOperationReceipt: 'preferred'
        }
      },
      // Configuration for services
      disablePimlicoPaymasterMethods: false, // Enable Pimlico methods for paymaster
      disablePimlicoMethods: false, // Enable Pimlico methods for estimation operations
      disablePaymasterApi: false, // Enable paymaster API through separate URL
      useCustomPaymasterIntegration: true, // Enable custom integration
      // Additional configuration to allow custom bundling
      useCustomBundlerIntegration: true,
      // Specify the right entry point address
      entryPointAddresses: {
        '56': '******************************************', // EntryPoint v0.7 address
      },
      // Custom paymaster integration configuration
      customIntegrationConfig: {
        bundlerProvider: 'nodereal', // Use NodeReal as bundler provider
        paymasterProvider: 'pimlico', // Use Pimlico as paymaster provider
        separateClients: true, // Use separate clients for bundler and paymaster
        estimationProvider: 'pimlico', // Use Pimlico for estimation operations
      },
      // Gas estimation options
      gasEstimationOptions: {
        '56': {
          // Conservative gas settings
          verificationGasLimit: 3500000,
          preVerificationGas: 150000,
          callGasLimit: 2000000,
          // Gas price settings
          maxFeePerGas: '0x12a05f200', // 5 Gwei in hex
          maxPriorityFeePerGas: '0x12a05f200', // 5 Gwei in hex
          fallbackGasLimit: 3000000,
          fallbackGasPrice: '0x12a05f200', // 5 Gwei in hex
        }
      }
    }
  },*/
  // appearance already defined above
} as any;

// Use a simpler approach that avoids errors
const rootElement = document.getElementById('root');
if (!rootElement) throw new Error('Root element not found');

// Add the TypeScript declaration for import.meta
declare global {
  interface ImportMeta {
    env: Record<string, string>;
  }
}

// Solana chain already defined above, removing duplicate
// const ethersProvider = new ethers.providers.JsonRpcProvider(bscPublicRpcUrl); // Not needed for Solana-only

// BSC validation function commented out since we only support Solana
/*const validateChainConfig = (chainId: number) => {
  if (chainId !== 56) {
    console.warn(`Only BSC (56) is supported for ERC-4337`);
    return;
  }

  // The structure has changed, get values from new paths
  const version = privyConfig.smartWallets.smartAccountConfig.version || 'v0.7';
  const factoryAddress = privyConfig.smartWallets.smartAccountConfig.factoryAddresses?.[chainId];
  const bundlerUrl = privyConfig.smartWallets.accountAbstraction?.bundlerUrls?.[chainId];
  const paymasterUrl = privyConfig.smartWallets.accountAbstraction?.paymasterUrls?.[chainId];
  const entryPointAddress = privyConfig.smartWallets.accountAbstraction?.entryPointAddresses?.[chainId];

  // Expected entry point address for v0.7
  const expectedEntryPoint = '******************************************';

  console.log(`ERC-4337 Configuration for BSC (chain ${chainId}):`);
  console.log(`- Version: ${version}`);
  console.log(`- Factory: ${factoryAddress}`);
  console.log(`- Bundler URL: ${bundlerUrl}`);
  console.log(`- Paymaster URL: ${paymasterUrl}`);
  console.log(`- Entry Point: ${entryPointAddress}`);

  // Validation checks
  if (!factoryAddress) {
    console.error(`BSC requires a factory address for chain ID ${chainId}`);
  }

  if (!bundlerUrl) {
    console.error(`BSC requires a bundler URL for chain ID ${chainId}`);
  }

  if (!entryPointAddress) {
    console.error(`BSC requires an entry point address for chain ID ${chainId}`);
  } else if (entryPointAddress !== expectedEntryPoint) {
    console.error(`BSC requires EntryPoint v0.7 (${expectedEntryPoint}), got ${entryPointAddress}`);
  }

  if (version !== 'v0.7') {
    console.error('BSC requires ERC-4337 version v0.7');
  }
};*/

// Validate BSC chain configuration before rendering
// validateChainConfig(bsc.id); // Commented out since we only support Solana

// Add initialization code before the rendering
// Initialize the paymaster client for gas sponsorship
// initializePaymasterClient(); // Not needed for Solana

// ERC-4337 logging removed - Solana only
/*
if (import.meta.env.MODE !== 'test') {
  const factoryAddress = privyConfig.smartWallets?.smartAccountConfig?.factoryAddresses?.['56'] || 'Not configured';
  const entryPointAddr = privyConfig.smartWallets?.accountAbstraction?.entryPointAddresses?.['56'] || 'Not configured';

  console.log('ERC-4337 Configuration for BSC (chain 56):');
  console.log('- Version: v0.7');
  console.log(`- Factory: ${factoryAddress}`);
  console.log(`- Bundler URL: ${bundlerUrl}`);
  console.log(`- Paymaster URL: ${import.meta.env.VITE_PIMLICO_API_KEY ? 'Pimlico (configured)' : 'NodeReal (fallback)'}`);
  console.log(`- Entry Point: ${entryPointAddr}`);
}
*/

createRoot(rootElement).render(
  <StrictMode>
    <ErrorBoundaryForPrivy>
      <PrivyProvider
        appId={import.meta.env.VITE_PRIVY_APP_ID || "cm8iaeayj00odvpx8lr8uao1q"}
        config={privyConfig}
        onLogin={handleLogin}
        onLogout={handleLogout}
        {...{} as any}
      >
        <App />
      </PrivyProvider>
    </ErrorBoundaryForPrivy>
  </StrictMode>
);
