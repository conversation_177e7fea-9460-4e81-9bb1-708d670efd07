can you follow the task
1. can you implement price Monitoring system in limit order service
2. first collect limit order and tp/sl order group by token address
3. using mobula websocket to subscribe token price
{
  "type": "feed",
  
  "authorization": MOBULA_API_KEY,
  "kind":"address",
 "tokens": [
      {
        "blockchain": "solana",
        "address": "A3xEazrzAEJp9nXNr3ZqH1n4gqCmEV92cA7TgdPzpump"
      },
       {
        "blockchain": "solana",
        "address": "9uKLt1HtSWh4Cyh32BL4fNE6SfKqgub5hjBg3YGYtGiM"
      }
    ]
}
MOBULA_API_KEY in .env file limit_order service
response 
{
    "chain_id": "solana:solana",
    "timestamp": 1751295003000,
    "price": 0.00004987292789761359,
    "marketDepthUSDUp": 0,
    "marketDepthUSDDown": 0,
    "volume24h": 11857.08279125008,
    "baseSymbol": "jobless",
    "quoteSymbol": "USD",
    "baseID": "solana:solana|9uKLt1HtSWh4Cyh32BL4fNE6SfKqgub5hjBg3YGYtGiM",
    "quoteID": "USD",
    "extra": {
        "priceUSD": 0.00004987292789761359,
        "quoteAssetPrice": 1,
        "blockNumber": 350213239
    },
    "timestamp_sent": 1751295005477
}
in the response baseID have i token address "solana:solana|9uKLt1HtSWh4Cyh32BL4fNE6SfKqgub5hjBg3YGYtGiM" can you remove solana:solana| in baseID you get token address so compare your token to Monitoring target price
4.can you redis cache Memory get limit order and tp/sl use mobula websocket to monitor
5. can you refer https://docs.mobula.io/indexing-stream/stream/websocket/wss-price-feed
6. if price matched or crossed use redis stream or queue to send message to solana service sell the total token tpsl_orders status to executed
7. tpsl_orders we need two more column profit_val sell token got sol in profit_val and in the sell transaction we need to store Signature like txn so sell_txn column
8. finally store to Notification 

detaild explain
          
## Task Analysis: Price Monitoring System for Limit Orders

Based on the `task.md` file, here's what I understand about the requirements:

### **Main Objective**
Implement a price monitoring system in the limit order service that monitors token prices and executes TP/SL (Take Profit/Stop Loss) orders when target prices are reached.

### **Key Requirements Breakdown:**

#### **1. Data Collection & Grouping**
- Collect limit orders and TP/SL orders
- Group them by token address for efficient monitoring

#### **2. Mobula WebSocket Integration**
- Use Mobula WebSocket API to subscribe to real-time token prices
- API Key: `MOBULA_API_KEY` (stored in limit_order service .env file)
- Reference documentation: https://docs.mobula.io/indexing-stream/stream/websocket/wss-price-feed

#### **3. WebSocket Message Format**
**Subscribe Request:**
```json
{
  "type": "feed",
  "authorization": "MOBULA_API_KEY",
  "kind": "address",
  "tokens": [
    {
      "blockchain": "solana",
      "address": "A3xEazrzAEJp9nXNr3ZqH1n4gqCmEV92cA7TgdPzpump"
    }
  ]
}
```

**Price Response:**
```json
{
  "chain_id": "solana:solana",
  "timestamp": 1751295003000,
  "price": 0.00004987292789761359,
  "baseID": "solana:solana|9uKLt1HtSWh4Cyh32BL4fNE6SfKqgub5hjBg3YGYtGiM",
  "extra": {
    "priceUSD": 0.00004987292789761359
  }
}
```

#### **4. Data Processing**
- Extract token address from `baseID` by removing `"solana:solana|"` prefix
- Compare extracted token address with monitored limit orders
- Use Redis cache for storing and retrieving limit orders and TP/SL data

#### **5. Order Execution Logic**
- Monitor price changes against target prices
- When price matches or crosses target:
  - Send message via Redis stream/queue to Solana service
  - Execute sell order for total token amount
  - Update `tpsl_orders` status to "executed"

#### **6. Database Schema Updates**
Add two new columns to `tpsl_orders` table:
- `profit_val`: Store SOL amount received from selling tokens
- `sell_txn`: Store transaction signature from the sell operation

#### **7. Notification System**
- Store execution results and notifications for completed orders

### **Technical Architecture**
```
Limit Order Service → Mobula WebSocket → Price Monitoring → Redis Cache/Queue → Solana Service → Database Updates → Notifications
```

This system creates a real-time price monitoring and automated trading execution pipeline for limit orders with proper tracking and notification capabilities.
